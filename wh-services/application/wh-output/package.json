{"name": "@sage/wh-output", "description": "Sage Wh Output", "version": "59.0.8", "author": "sage", "license": "UNLICENSED", "xtrem": {"packageName": "@sage/wh-output"}, "keywords": ["xtrem-x3-services-application-package"], "main": "build/index.js", "files": ["build", "api", "lib/pages", "lib/page-extensions", "lib/widgets", "lib/page-fragments", "lib/stickers", "lib/client-functions", "lib/shared-functions", "README.md", "CHANGELOG.md"], "typings": "build/package-definition.d.ts", "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/wh-master-data-api": "workspace:*", "@sage/wh-output-api": "workspace:*", "@sage/wh-product-data-api": "workspace:*", "@sage/wh-stock-data-api": "workspace:*", "@sage/wh-structure-api": "workspace:*", "@sage/wh-system-api": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-x3": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "pnpm run clean && pnpm run build", "clean": "rm -rf build", "generate-migrate-translations": "xtrem x3-dev generate-migrate-translations", "generate-package": "xtrem x3-dev generate-package --service-name wh", "generate-translations": "xtrem x3-dev generate-translations --service-name wh", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "start": "xtrem start", "test": "xtrem test --unit --graphql", "test:ci": "xtrem test --unit --ci", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}, "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "dependencies": {"@sage/wh-master-data": "workspace:*", "@sage/wh-product-data": "workspace:*", "@sage/wh-stock-data": "workspace:*", "@sage/wh-structure": "workspace:*", "@sage/wh-system": "workspace:*", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-x3-gateway": "workspace:*", "@sage/xtrem-x3-syracuse": "workspace:*", "@sage/xtrem-x3-system-utils": "workspace:*"}}