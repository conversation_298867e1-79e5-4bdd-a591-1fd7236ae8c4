{"name": "@sage/wh-main", "description": "WH Services main package", "xtrem": {"isMain": true}, "keywords": [], "author": "Sage", "license": "UNLICENSED", "files": ["build", "data", "sql", "routing.json"], "scripts": {"start": "xtrem start", "xtrem": "xtrem"}, "version": "59.0.8", "dependencies": {"@sage/wh-input": "workspace:*", "@sage/wh-input-output": "workspace:*", "@sage/wh-input-stock": "workspace:*", "@sage/wh-output": "workspace:*", "@sage/wh-output-stock": "workspace:*", "@sage/wh-pages": "workspace:*", "@sage/wh-stock": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-x3": "workspace:*", "@sage/xtrem-standalone": "workspace:*", "@sage/xtrem-x3-adc-ui": "workspace:*", "@sage/xtrem-x3-copilot": "workspace:*", "@sage/xtrem-x3-interop": "workspace:*", "@sage/xtrem-x3-pages-ui": "workspace:*"}}