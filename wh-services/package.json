{"name": "@sage/xtrem~wh-services", "description": "Umbrella project for Xtrem Warehouse Services", "version": "59.0.8", "license": "UNLICENSED", "private": true, "scripts": {"build:wh-services": "pnpm -sw build:wh-services", "generate": "xtrem x3-dev generate --create-main --scopes @sage --service-name wh && pnpm ts:references:fix", "generate-translations": "xtrem x3-dev generate --translations --scopes @sage --service-name wh", "lint:wh:services": "pnpm -sw lint:wh-services", "start": "pnpm --dir ./main/wh-main start", "test:wh:services": "pnpm -sw test:wh-services", "ts:references:fix": "pnpm -sw ts:references:fix", "test:functional": "xdev run lerna-cache/test-functional.sh", "test:functional:report": "xdev run lerna-cache/test-functional-report.sh"}, "devDependencies": {"@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-x3": "workspace:*"}}