{"name": "@sage/k6", "version": "7.0.8", "description": "K6 package for performance.", "private": true, "main": "index.ts", "author": "", "license": "AGPL-3.0", "bugs": {"url": "https://github.com/loadimpact/k6/issues"}, "files": ["build"], "homepage": "https://github.com/loadimpact/k6#readme", "packageManager": "pnpm@10.15.0", "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.27.0", "@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication-api": "workspace:*", "@sage/xtrem-distribution-api": "workspace:*", "@sage/xtrem-finance-data-api": "workspace:*", "@sage/xtrem-manufacturing-api": "workspace:*", "@sage/xtrem-master-data-api": "workspace:*", "@sage/xtrem-mrp-data-api": "workspace:*", "@sage/xtrem-purchasing-api": "workspace:*", "@sage/xtrem-sales-api": "workspace:*", "@sage/xtrem-stock-api": "workspace:*", "@sage/xtrem-stock-data-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-technical-data-api": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/k6": "^1.0.2", "@types/lodash": "^4.14.198", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "handlebars": "^4.7.8", "handlebars-loader": "^1.7.3", "html-webpack-plugin": "^5.5.0", "json-to-graphql-query": "^2.2.5", "lodash": "^4.17.21", "typescript": "~5.9.0", "webpack": "^5.99.0", "webpack-cli": "^6.0.1", "webpack-glob-entries": "^1.0.1"}, "dependencies": {"ts-loader": "^9.5.2"}, "scripts": {"build": "tsc -b -v .", "bundle": "webpack", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "run:bom": "pnpm bundle && k6 run dist/bill-of-material.js", "run:flow": "pnpm bundle && k6 run dist/flow.js", "run:item": "pnpm bundle && k6 run dist/item.js", "run:item-crud": "pnpm bundle && k6 run dist/item-crud.js", "run:perf": "pnpm bundle && k6 run dist/perf.js", "run:purchase-order": "pnpm bundle && k6 run dist/create-purchase-order.js", "run:purchase-order-flow": "pnpm bundle && k6 run dist/purchase-order-flow.js", "run:sales-order": "pnpm bundle && k6 run dist/sales-order.js", "run:stockReceipt": "pnpm bundle && k6 run dist/stock-receipt.js", "run:work-order": "pnpm bundle && k6 run dist/work-order.js"}}