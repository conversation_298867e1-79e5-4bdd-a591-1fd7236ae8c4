import { asyncArray } from '@sage/xtrem-async-helper';
import type { Dict, ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { isPostedInProgressError } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type {
    BankAccount,
    BaseOpenItem,
    FinanceTransactionBinding,
    PaymentTracking,
} from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type { MutationResult } from '@sage/xtrem-finance-data/build/lib/shared-functions/interfaces/common';
import { editLandedCostAllocation } from '@sage/xtrem-landed-cost/build/lib/client-functions/landed-cost-allocation-helpers';
import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    BusinessRelationType,
    Currency,
    Item,
    PaymentTerm,
    Supplier,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    convertFromTo,
    getCompanyPriceScale,
    getPurchaseUnit,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import { convertAmount, scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type { LineValidationMessage } from '@sage/xtrem-master-data/build/lib/shared-functions/interfaces/finance-integration';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import { checkStockSite } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import type {
    GraphApi,
    PurchaseDocumentStatus,
    PurchaseInvoiceBinding,
    PurchaseInvoiceLine,
    PurchaseInvoiceLineBinding,
    PurchaseInvoiceMatchingStatus,
    PurchaseInvoice as PurchaseInvoiceNode,
    PurchaseOrderLine,
    PurchaseOrderLineToPurchaseInvoiceLine,
    PurchaseOrderLineToPurchaseReceiptLine,
    PurchaseReceiptLine,
    PurchaseReceiptLineToPurchaseInvoiceLine,
} from '@sage/xtrem-purchasing-api';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import type { DocumentTax, DocumentTaxBinding, DocumentTaxInput } from '@sage/xtrem-tax-api';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/display-taxes';
import * as ui from '@sage/xtrem-ui';
import { tokens } from '@sage/xtrem-ui';
import type { PdfPluginProperties } from '@sage/xtrem-ui-plugin-pdf';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import {
    confirmDialogWithAcceptButtonText,
    getFullyPaidFilter,
    getNotFullyPaidFilter,
    getPurchasePrice,
    isPurchaseOrderLine,
    setOverwriteNote,
} from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-purchase-invoice';
import { openRecordPage, openRecordPageFromMainList } from '../client-functions/finance-integration';
import type { PurchaseInvoiceStepSequenceStatus } from '../client-functions/interfaces/step-sequence';
import { addLineFromReceipt } from '../client-functions/invoice-from-receipt';
import * as PillColorPurchase from '../client-functions/pill-color';
import * as actionFunctions from '../client-functions/purchase-invoice-actions-functions';
import { updateLineVarianceType } from '../client-functions/purchase-invoice-actions-functions';
import { dueDateDefault } from '../client-functions/purchase-invoice-credit-memo-common';
import { displayTaxes } from '../client-functions/shared/display-taxes';
import {
    calculateLinePrices,
    fetchDefaultLandedCostDocumentLine,
    isExchangeRateHidden,
} from '../client-functions/shared/page-functions';
import { isPurchaseInvoiceLineActionDisabled } from '../shared-functions/edit-rules';

@ui.decorators.page<PurchaseInvoice, PurchaseInvoiceNode>({
    title: 'Purchase invoice',
    objectTypeSingular: 'Purchase invoice',
    objectTypePlural: 'Purchase invoices',
    idField() {
        return this.number;
    },
    menuItem: purchasing,
    node: '@sage/xtrem-purchasing/PurchaseInvoice',
    module: 'purchasing',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 700,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.acceptAllVariances,
                this.post,
                this.repost,
                this.createPurchaseCreditNote,
                this.recordPayment,
            ],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.notifyBuyer,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.invoiceStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    async onLoad() {
        this.$standardCancelAction.isHidden = true;
        this.save.isHidden = true;
        this.invoiceStepSequence.statuses = this.getDisplayStatusStepSequence();
        await this.initPage();
        this._manageDisplayApplicativePageActions(false);
        this.initPosting();
        this.$.setPageClean();
        if (this.pdfSupplierInvoice.value) {
            this.pdf.value = this.pdfSupplierInvoice.value.value;
        }
        if (!this.totalAmountIncludingTax.value) {
            this.totalAmountIncludingTax.value = 0;
        }
        TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxes, this.taxEngine.value ?? '');
        this._manageHeaderProperties();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { invoiceDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-purchasing/PurchaseInvoice',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id ?? '' };
                },
            }),
            line2: ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceNode, Supplier>({
                bind: 'billBySupplier',
                title: 'Bill-by supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: { businessEntity: { _id: true } } }),
                    ui.nestedFields.technical<PurchaseInvoice, Supplier, User>({
                        bind: 'defaultBuyer',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'email' }),
                            ui.nestedFields.technical({ bind: 'firstName' }),
                            ui.nestedFields.technical({ bind: 'lastName' }),
                        ],
                    }),
                ],
            }),
            id: ui.nestedFields.text({
                bind: { billBySupplier: { id: true } },
                title: 'Bill-by supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'invoiceDate', title: 'Invoice date', isMandatory: true }),
            line3: ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceNode, Site>({
                title: 'Financial site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                tunnelPage: undefined,
                columns: [
                    ui.nestedFields.technical({ bind: 'isFinance' }),
                    ui.nestedFields.technical<PurchaseInvoice, Site, Site>({
                        node: '@sage/xtrem-system/Site',
                        bind: 'financialSite',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseInvoice, Site, Company>({
                        node: '@sage/xtrem-system/Company',
                        bind: 'legalCompany',
                        nestedFields: [
                            ui.nestedFields.technical<PurchaseInvoice, Company, BankAccount>({
                                bind: 'bankAccount',
                                node: '@sage/xtrem-finance-data/BankAccount',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: { financialSite: { _id: true } } }),
                                    ui.nestedFields.technical<PurchaseInvoice, BankAccount, Currency>({
                                        bind: 'currency',
                                        node: '@sage/xtrem-master-data/Currency',
                                        nestedFields: [
                                            ui.nestedFields.technical({ bind: '_id' }),
                                            ui.nestedFields.technical({ bind: 'id' }),
                                            ui.nestedFields.technical({ bind: 'symbol' }),
                                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                        ],
                                    }),
                                ],
                            }),
                        ],
                    }),
                ],
            }),
            line_4: ui.nestedFields.numeric<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total Including tax',
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line_5: ui.nestedFields.date({ title: 'Due date', bind: 'dueDate' }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseInvoiceDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line6: ui.nestedFields.label({
                optionType: '@sage/xtrem-purchasing/PurchaseInvoiceMatchingStatus',
                title: 'Matching status',
                bind: 'matchingStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseInvoiceMatchingStatus', rowData?.matchingStatus),
            }),
            line7: ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Currency',
                isHiddenOnMainField: true,
            }),
            line8: ui.nestedFields.numeric<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line9: ui.nestedFields.numeric<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'totalTaxAmount',
                title: 'Tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line11: ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line12: ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceNode>({
                title: 'Pay to supplier',
                bind: 'payToSupplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                isHiddenOnMainField: true,
                valueField: { businessEntity: { name: true } },
            }),
            idPayToSupplier: ui.nestedFields.text({
                bind: { payToSupplier: { id: true } },
                title: 'Pay-to supplier ID',
                isHiddenOnMainField: true,
            }),
            line13: ui.nestedFields.label<PurchaseInvoice, PurchaseInvoiceNode>({
                title: 'Supplier invoice reference',
                bind: 'supplierDocumentNumber',
                isHiddenOnMainField: true,
            }),
            line14: ui.nestedFields.date<PurchaseInvoice, PurchaseInvoiceNode>({
                title: 'Supplier document date',
                bind: 'supplierDocumentDate',
                isHiddenOnMainField: true,
            }),
            line15: ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                ],
            }),
            paymentStatus: ui.nestedFields.label<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: { paymentTracking: { status: true } },
                title: 'Payment status',
                optionType: '@sage/xtrem-finance-data/OpenItemStatus',
                isHiddenOnMainField: true,
                isHidden() {
                    return !this.$.isServiceOptionEnabled('paymentTrackingOption');
                },
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.paymentTracking?.status),
            }),
            totalPayments: ui.nestedFields.numeric<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: { paymentTracking: { amountPaid: true } },
                title: 'Total payments',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                isHidden() {
                    return !this.$.isServiceOptionEnabled('paymentTrackingOption');
                },
            }),
            netBalance: ui.nestedFields.numeric<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'netBalance',
                title: 'Net balance',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            status: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({ bind: 'status' }),
            taxCalculationStatus: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'taxCalculationStatus',
            }),
            varianceTotalAmountExcludingTax: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'varianceTotalAmountExcludingTax',
            }),
            varianceTotalTaxAmount: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'varianceTotalTaxAmount',
            }),
            financeIntegrationStatus: ui.nestedFields.technical({ bind: 'financeIntegrationStatus' }),
            varianceTotalAmountIncludingTax: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'varianceTotalAmountIncludingTax',
            }),
            totalLinesCount: ui.nestedFields.aggregate<PurchaseInvoice>({
                aggregateOn: '_id',
                aggregationMethod: 'distinctCount',
                bind: 'lines',
                isHidden: true,
            }),
            calculatedTotalRemainingQuantityToCredit: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'calculatedTotalRemainingQuantityToCredit',
            }),
            openItemSysId: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({ bind: 'openItemSysId' }),
            isOpenItemPageOptionActive: ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode>({
                bind: 'isOpenItemPageOptionActive',
            }),
        },
        optionsMenu(_graph, _storage, _queryParam, _username, _userCode, serviceOptions) {
            return Promise.resolve([
                {
                    title: 'All open statuses',
                    graphQLFilter: {
                        displayStatus: { _nin: ['posted', 'partiallyCredited', 'credited', 'partiallyPaid', 'paid'] },
                    },
                },
                { title: 'All statuses', graphQLFilter: {} },
                { title: 'Posted', graphQLFilter: { displayStatus: { _eq: 'posted' } } },
                { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
                ...(serviceOptions.paymentTrackingOption ? [getNotFullyPaidFilter()] : []),
                ...(serviceOptions.paymentTrackingOption ? [getFullyPaidFilter()] : []),
                { title: 'Posting error', graphQLFilter: { displayStatus: { _eq: 'postingError' } } },
                { title: 'Stock error', graphQLFilter: { displayStatus: { _eq: 'stockError' } } },
                { title: 'Tax calculation failed', graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } } },
                { title: 'Variance', graphQLFilter: { displayStatus: { _eq: 'variance' } } },
                { title: 'No variance', graphQLFilter: { displayStatus: { _eq: 'noVariance' } } },
                { title: 'Variance approved', graphQLFilter: { displayStatus: { _eq: 'varianceApproved' } } },
            ] as ui.containers.OptionsMenuItemType<PurchaseInvoiceNode>[]);
        },
        dropdownActions: [
            {
                title: 'Accept all variances',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(_recordId, rowItem: ui.PartialNodeWithId<PurchaseInvoiceNode>) {
                    await actionFunctions.acceptAllVariancesAction({
                        purchaseInvoicePage: this,
                        recordId: rowItem._id ?? '',
                    });
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonAcceptAllVariancesAction({
                        parameters: { status: rowItem.status, matchingStatus: rowItem.matchingStatus },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            {
                title: 'Post',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    await actionFunctions.postAction({
                        purchaseInvoicePage: this,
                        recordNumber: rowItem.number ?? '',
                        taxCalculationStatus: rowItem.taxCalculationStatus,
                        varianceTotalAmountExcludingTax: Number(rowItem.varianceTotalAmountExcludingTax),
                        varianceTotalTaxAmount: Number(rowItem.varianceTotalTaxAmount),
                        recordId,
                        lines: Number(rowItem.lines),
                    });
                },
                isHidden: (recordId, rowItem) =>
                    !!displayButtons.isHiddenButtonPostAction({
                        parameters: {
                            status: rowItem.status,
                            stockTransactionStatus: rowItem.stockTransactionStatus,
                            financeIntegrationStatus: rowItem.financeIntegrationStatus,
                            matchingStatus: rowItem.matchingStatus,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        },
                        recordId,
                        isDirty: false,
                    }) || false,
            },
            {
                title: 'Open item',
                icon: 'none',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseInvoiceNode>) {
                    await this.$.dialog.page(
                        `@sage/xtrem-finance/AccountsPayableOpenItem`,
                        { _id: rowItem.openItemSysId ?? '', fromPurchasing: true },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseInvoiceNode>) {
                    return rowItem.status !== 'posted' || !rowItem.isOpenItemPageOptionActive;
                },
            },
            {
                title: 'Record payment',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseInvoiceNode>) {
                    const paymentNumber = await openRecordPageFromMainList({
                        page: this,
                        openParams: JSON.stringify({
                            bankAccount: rowItem.site?.legalCompany?.bankAccount,
                            financialSite: rowItem.site?.isFinance ? rowItem.site : rowItem.site?.financialSite,
                            baseBusinessRelation: rowItem.billBySupplier,
                            type: 'supplier' as BusinessRelationType,
                            date: DateValue.today().toString(),
                            currency: rowItem.currency,
                            amount: rowItem.netBalance ?? 0,
                            number: rowItem.number,
                        }),
                    });
                    if (typeof paymentNumber === 'string') {
                        this.$.showToast(
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_invoice__payment_created',
                                'The following payment was created: {{paymentNumber}}.',
                                { paymentNumber },
                            ),
                            { timeout: 10000 },
                        );
                    }
                    await this.$.refreshNavigationPanel();
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseInvoiceNode>) {
                    return (
                        !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
                        displayButtons.isHiddenButtonRecordPaymentAction({
                            parameters: {
                                status: rowItem.status,
                                paymentStatus: rowItem.paymentTracking?.status,
                                taxCalculationStatus: rowItem.taxCalculationStatus,
                            },
                            recordId,
                            isDirty: false,
                        }) ||
                        false
                    );
                },
            },
            {
                icon: 'none',
                title: 'Create credit memo',
                refreshesMainList: 'list',
                async onClick(_rowId, rowItem) {
                    await actionFunctions.CreateCreditMemo({
                        purchaseInvoicePage: this,
                        recordId: rowItem._id ?? '',
                        isCalledFromRecordPage: false,
                        currency: rowItem.currency ? JSON.stringify(this.currency) : '',
                    });
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonCreatePurchaseCreditNoteAction({
                        parameters: {
                            status: rowItem.status,
                            calculatedTotalRemainingQuantityToCredit: Number(
                                rowItem.calculatedTotalRemainingQuantityToCredit,
                            ),
                        },
                        recordId,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                title: 'Send for matching',
                icon: 'email_switch',
                refreshesMainList: 'record',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseInvoiceNode>) {
                    await actionFunctions.notifyBuyer(this, {
                        recordId,
                        siteId: rowItem.site?._id,
                    });
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonNotifyBuyerAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    }) ?? false,
                isDisabled: (recordId, rowItem) =>
                    displayButtons.isDisabledButtonNotifyBuyerAction({
                        parameters: { status: rowItem.status },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem) {
                    await actionFunctions.setDimensions({
                        purchaseInvoicePage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        isRepost: this.isRepost,
                        site: rowItem.site,
                        supplier: rowItem.supplier,
                    });
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId, rowItem) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-purchasing/PurchaseInvoice',
                    });
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    }) ?? false,
            },
        ],
    },
})
export class PurchaseInvoice extends ui.Page<GraphApi> {
    isRepost: boolean;

    canEditPaymentDataAfterPost: boolean;

    canEditTaxDataAfterPost: boolean;

    wereTaxesEdited: boolean;

    displayTaxesClicked = false;

    financeIntegrationCheckResult: MutationResult | null;

    private readonly orderStepSequenceCreate = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_creation',
        'Create',
    );

    private readonly orderStepSequencePost = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_post',
        'Post',
    );

    private readonly orderStepSequencePay = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_pay',
        'Pay',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.$.isServiceOptionEnabled('paymentTrackingOption')) {
            if (this.paymentTracking?.value?.status === 'paid') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    post: 'complete',
                    pay: 'complete',
                });
            }
            if (this.paymentTracking?.value?.status === 'partiallyPaid') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    post: 'complete',
                    pay: 'current',
                });
            }
        }
        if (['inProgress', 'error'].includes(this.status.value ?? '')) {
            return this._setStepSequenceStatusObject({ create: 'complete', post: 'current' });
        }
        if (this.status.value === 'posted') {
            return this._setStepSequenceStatusObject({ create: 'complete', post: 'complete' });
        }

        return this._setStepSequenceStatusObject({ create: 'current', post: 'incomplete' });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.acceptAllVariances,
                this.post,
                this.repost,
                this.createPurchaseCreditNote,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.save.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonAcceptAllVariancesAction(isDirty);
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        this.manageDisplayButtonCreatePurchaseCreditNoteAction(isDirty);
        this.manageDisplayButtonRecordPayment(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonNotifyBuyerAction(isDirty);
        this.manageDisplayButtonSelectFromReceiptAction();
        this.manageDisplayLinePhantomRow();
        this.manageDisplayButtonGoToSysNotificationPageAction();
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: {
                status: this.status.value,
                stockTransactionStatus: this.stockTransactionStatus.value,
                financeIntegrationStatus: this.financeIntegrationStatus.value,
                matchingStatus: this.matchingStatus.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
        this.post.isDisabled = displayButtons.isDisabledButtonPostAction({
            parameters: {
                landedCostNotFullyAllocated: this.$.isServiceOptionEnabled('landedCostOption')
                    ? this.lines.value.some(rowValue => JSON.parse(rowValue.landedCostCheckResult ?? '{}').message)
                    : false,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonAcceptAllVariancesAction(isDirty: boolean) {
        this.acceptAllVariances.isHidden = displayButtons.isHiddenButtonAcceptAllVariancesAction({
            parameters: { status: this.status.value, matchingStatus: this.matchingStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCreatePurchaseCreditNoteAction(isDirty: boolean) {
        this.createPurchaseCreditNote.isHidden = displayButtons.isHiddenButtonCreatePurchaseCreditNoteAction({
            parameters: {
                status: this.status.value,
                calculatedTotalRemainingQuantityToCredit: this.calculatedTotalRemainingQuantityToCredit.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRecordPayment(isDirty: boolean) {
        this.recordPayment.isHidden =
            !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
            displayButtons.isHiddenButtonRecordPaymentAction({
                parameters: {
                    status: this.status.value,
                    paymentStatus: this.paymentTracking.value?.status ?? 'notPaid',
                    taxCalculationStatus: this.taxCalculationStatus.value,
                },
                recordId: this.$.recordId,
                isDirty,
            });
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus ?? '',
                ),
            },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
            parameters: {
                status: this.status.value,
                site: this.site.value,
                billBySupplier: this.billBySupplier.value,
            },
        });
    }

    private manageDisplayButtonNotifyBuyerAction(isDirty: boolean) {
        this.notifyBuyer.isHidden = displayButtons.isHiddenButtonNotifyBuyerAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });

        this.notifyBuyer.isDisabled = displayButtons.isDisabledButtonNotifyBuyerAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                site: this.site.value,
                billBySupplier: this.billBySupplier.value,
            },
        });
    }

    private manageDisplayButtonSelectFromReceiptAction() {
        this.selectFromPurchaseReceiptsLookup.isDisabled = displayButtons.isDisabledButtonSelectFromReceiptAction({
            parameters: {
                site: this.site.value,
                status: this.status.value,
            },
        });

        this.selectFromPurchaseReceiptsLookup.isHidden = displayButtons.isHiddenButtonSelectFromReceiptAction({
            parameters: {
                site: this.site.value,
                status: this.status.value,
            },
        });
    }

    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    private _isAddNewLine = false;

    getSerializedValues() {
        const values = this.$.values as ui.PartialNode<PurchaseInvoiceBinding>;
        if (Number(values.billByAddress?._id) < 0) {
            delete values.billByAddress?._id;
        }
        if (Number(values.payToAddress?._id) < 0) {
            delete values.payToAddress?._id;
        }
        delete values.matchingStatus;
        delete values.calculatedTotalAmountExcludingTax;
        delete values.calculatedTotalAmountExcludingTaxInCompanyCurrency;
        // TODO: labels value are not updated in this.$.value
        values.status = this.status.value as PurchaseDocumentStatus;
        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = !!this.isOverwriteNote.value;
        }
        this.getLineValues(values.lines);
        return values;
    }

    // eslint-disable-next-line class-methods-use-this
    getLineValues(lines: ui.PartialNode<PurchaseInvoiceBinding>['lines']) {
        lines?.forEach(line => {
            delete line.currency;
            delete line.quantityInStockUnit;
            delete line.origin;
            // unitToStockUnitConversionFactor is Frozen in update mode
            if (Number(line._id) > 0) {
                delete line.unitToStockUnitConversionFactor;
            }
            if (line.item?.type === 'landedCost' && Number(line._id) < 0 && line.landedCost) {
                line.landedCost._id = line._id;
            }
        });
    }

    @ui.decorators.pageAction<PurchaseInvoice>({
        title: 'Save',
        buttonType: 'primary',
        access: { bind: '$update' },
        async onClick() {
            if (!this.$.recordId) {
                this.isOverwriteNote.value = await setOverwriteNote(this, {
                    linesFromSingleDocument: this.linesFromSingleReceipt(),
                    headerNotesChanged: this.headerNotesChanged(),
                    lineNotesChanged: this.lineNotesChanged(),
                    isTransferHeaderNote: this.receiptIsTransferHeaderNote(),
                    isTransferLineNote: this.receiptsIsTransferLineNote(),
                });
            }
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            if (this.$.recordId) {
                this.financeIntegrationCheckResult = await actionFunctions.financeIntegrationCheck({
                    purchaseInvoicePage: this,
                    recordId: this.$.recordId,
                    isWarning: true,
                });
                if (
                    this.financeIntegrationCheckResult?.validationMessages?.length ||
                    this.$.isServiceOptionEnabled('landedCostOption')
                ) {
                    await this.lines.redraw();
                }
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        title: 'Post',
        isHidden: true,
        onError(error: string | (Error & { errors: Array<any> })) {
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            if (this.$.recordId) {
                this.financeIntegrationCheckResult = await actionFunctions.postAction({
                    purchaseInvoicePage: this,
                    recordNumber: this.number.value,
                    taxCalculationStatus: this.taxCalculationStatus.value,
                    varianceTotalAmountExcludingTax: Number(this.varianceTotalAmountExcludingTax.value),
                    varianceTotalTaxAmount: Number(this.varianceTotalTaxAmount.value),
                    recordId: this.$.recordId,
                    lines: this.lines.value?.length ?? 0,
                });
                await this.lines.redraw();
            }
            this._manageHeaderProperties();
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            const decimalDigits = this.currency.value?.decimalDigits || 2;
            if (
                Number(this.varianceTotalAmountExcludingTax.value) !== 0 ||
                Number(Number(this.varianceTotalTaxAmount.value).toFixed(decimalDigits)) !== 0
            ) {
                throw new Error(
                    `${ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_invoice__repost__variance',
                        'You need to resolve the total amount variances before posting. The new total supplier tax is: ',
                    )}${Number((Number(this.totalTaxAmount.value ?? 0) + Number(this.varianceTotalTaxAmount.value ?? 0)).toFixed(decimalDigits))}`,
                );
            }

            await asyncArray(this.postingDetails.value)
                .filter(document => document?.postingStatus === 'posted')
                .forEach(async postedDocument => {
                    switch (postedDocument.targetDocumentType) {
                        case 'journalEntry': {
                            await this.$.dialog.message(
                                'info',
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_invoice__repost_documents_already_posted_title',
                                    'Repost',
                                ),
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_invoice__repost_journal_entry_already_posted',
                                    'The journal entry posted successfully. You can access this document in your financial solution to take further action.',
                                ),
                            );
                            break;
                        }
                        case 'accountsPayableInvoice': {
                            await this.$.dialog.message(
                                'info',
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_invoice__repost_documents_already_posted_title',
                                    'Repost',
                                ),
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_invoice__repost_accounts_payable_invoice_already_posted',
                                    'The accounts payable invoice posted successfully. You can access this document in your financial solution to take further action.',
                                ),
                            );
                            break;
                        }
                        default: {
                            break;
                        }
                    }
                });

            const documentLines = this.lines.value
                .filter((line: PartialCollectionValueWithIds<PurchaseInvoiceLineBinding>) => Number(line._id) > 0)
                .map((line: PartialCollectionValueWithIds<PurchaseInvoiceLineBinding>) => {
                    const lineData = {
                        baseDocumentLineSysId: line._id,
                        storedAttributes: line.storedAttributes,
                        storedDimensions: line.storedDimensions,
                    };
                    return this.canEditTaxDataAfterPost && this.wereTaxesEdited
                        ? { ...lineData, uiTaxes: line.uiTaxes }
                        : { ...lineData };
                });

            this.$.loader.isHidden = false;

            const repostArgs: {
                purchaseInvoice: string;
                documentData: {
                    header: {
                        supplierDocumentNumber?: string;
                        paymentData?: {
                            supplierDocumentDate: string;
                            paymentTerm: integer | string;
                        };
                        totalTaxAmount?: number;
                        taxes?: DocumentTaxInput[];
                    };
                    lines?: {
                        baseDocumentLineSysId?: integer | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                        uiTaxes?: any;
                    }[];
                };
            } = {
                purchaseInvoice: this.$.recordId ?? '',
                documentData: {
                    header: { supplierDocumentNumber: this.supplierDocumentNumber.value ?? '' },
                    lines: documentLines,
                },
            };

            if (this.wereTaxesEdited) {
                const { taxes } = this.$.values;
                repostArgs.documentData.header.totalTaxAmount = this.totalTaxAmount.value ?? undefined;
                repostArgs.documentData.header.taxes = taxes.map((tax: DocumentTaxInput) => ({
                    ...tax,
                    document: this.$.recordId,
                }));
            }

            if (this.canEditPaymentDataAfterPost) {
                repostArgs.documentData.header.paymentData = {
                    supplierDocumentDate: this.supplierDocumentDate.value ?? '',
                    paymentTerm: this.paymentTerm?.value?._id ?? 0,
                };
            }

            const postResult = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseInvoice')
                .mutations.repost({ wasSuccessful: true, message: true }, { ...repostArgs })
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `** ${ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_invoice__repost_errors',
                        'Errors while reposting:',
                    )} **\n ${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        icon: 'search',
        title: 'Add lines from receipts',
        isHidden() {
            return displayButtons.isHiddenButtonSelectFromReceiptAction({
                parameters: {
                    site: this.site.value,
                    status: this.status.value,
                },
            });
        },
        async onClick() {
            await addLineFromReceipt(this);
        },
    })
    selectFromPurchaseReceiptsLookup: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        isTransient: true,
        isHidden: true,
        title: 'Send for matching',
        icon: 'email_switch',
        async onClick() {
            await actionFunctions.notifyBuyer(this, {
                recordId: this.$.recordId,
                siteId: this.site.value?._id,
            });
        },
    })
    notifyBuyer: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        title: 'Accept all variances',
        isHidden: true,
        async onClick() {
            this.financeIntegrationCheckResult = await actionFunctions.financeIntegrationCheck({
                purchaseInvoicePage: this,
                recordId: this.$.recordId ?? '',
                isWarning: false,
            });
            if (!this.financeIntegrationCheckResult?.validationMessages?.length) {
                await actionFunctions.acceptAllVariancesAction({
                    purchaseInvoicePage: this,
                    recordId: String(this.$.recordId),
                });
            } else {
                await this.lines.redraw();
            }
        },
    })
    acceptAllVariances: ui.PageAction;

    @ui.decorators.labelField<PurchaseInvoice>({ isHidden: true })
    financeIntegrationStatus: ui.fields.Label;

    @ui.decorators.section<PurchaseInvoice>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<PurchaseInvoice>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseInvoice>({ title: 'Information', isTitleHidden: true })
    informationSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoice>({
        title: 'Information',
        isTitleHidden: true,
        width: 'large',
        parent() {
            return this.informationSection;
        },
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.labelField<PurchaseInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax calculation status',
        bind: 'taxCalculationStatus',
        isHidden() {
            return !['notDone', 'inProgress'].includes(this.taxCalculationStatus.value ?? '');
        },
        optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
        style() {
            return PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', this.taxCalculationStatus.value);
        },
    })
    taxCalculationStatus: ui.fields.Label<TaxCalculationStatus>;

    @ui.decorators.labelField<PurchaseInvoice>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.informationBlock;
        },
        style() {
            return PillColorStock.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.labelField<PurchaseInvoice>({
        parent() {
            return this.informationBlock;
        },
        optionType: '@sage/xtrem-purchasing/PurchaseInvoiceMatchingStatus',
        title: 'Matching status',
        bind: 'matchingStatus',
        style() {
            return PillColorPurchase.getLabelColorByStatus('PurchaseInvoiceMatchingStatus', this.matchingStatus.value);
        },
    })
    matchingStatus: ui.fields.Label<PurchaseInvoiceMatchingStatus>;

    @ui.decorators.referenceField<PurchaseInvoice, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Transaction currency',
        lookupDialogTitle: 'Select currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter: { isActive: true },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCurrency();
            this.showHideColumns();
        },
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<PurchaseInvoice>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.site.value, this.billBySupplier.value);
        },
    })
    rateDescription: ui.fields.Text;

    // We need this information to decide if we should allow some properties to be changed.
    // It is hidden at the moment, but it can be made visible on nest US's for Payment Tracking
    @ui.decorators.referenceField<PurchaseInvoice, PaymentTracking>({
        node: '@sage/xtrem-finance-data/PaymentTracking',
        columns: [
            ui.nestedFields.technical({ bind: 'status' }),
            ui.nestedFields.technical({ bind: 'amountPaid' }),
            ui.nestedFields.technical({ bind: 'forcedAmountPaid' }),
            ui.nestedFields.technical({ bind: 'discountPaymentBeforeDate' }),
            ui.nestedFields.technical({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: '_id' }),
                ],
            }),
        ],
    })
    paymentTracking: ui.fields.Reference<PaymentTracking>;

    @ui.decorators.vitalPodField<PurchaseInvoice, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Bill-by address',
        width: 'small',
        onAddButtonClick() {
            if (this.billByLinkedAddress.value) {
                const { ...values } = { ...this.billByLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Bill-by address',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return this.billByAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.billByAddress.value);
                },
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.billByAddress.value);
                },
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
                onChange() {
                    // TODO: how switching titles after country changed? ==> in each pod?
                },
            }),
        ],
    })
    billByAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.section<PurchaseInvoice>({ title: 'Financial' })
    financialSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<PurchaseInvoice>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption') || this.status.value !== 'posted'
                ? [this.orderStepSequenceCreate, this.orderStepSequencePost]
                : [this.orderStepSequenceCreate, this.orderStepSequencePost, this.orderStepSequencePay];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    invoiceStepSequence: ui.fields.StepSequence;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.financialSection;
        },
        width: 'large',
    })
    financialBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.headerSection;
        },
        title: 'Supplier document information',
        isTitleHidden: true,
    })
    supplierBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.itemsSection;
        },
        width: 'small',
        title: 'Attachment: Supplier invoice',
    })
    pdfBlock: ui.containers.Block;

    @ui.decorators.referenceField<PurchaseInvoice, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Financial site',
        isMandatory: true,
        fetchesDefaults: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select financial site',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseInvoice, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseInvoice, Site, Company>({
                title: 'Company',
                valueField: 'name',
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseInvoice, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.reference<PurchaseInvoice, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        tunnelPage: '@sage/xtrem-master-data/Currency',
                        title: 'Currency',
                        bind: 'currency',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.text({ bind: 'decimalDigits', title: 'Decimal places' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseInvoice, Company, BankAccount>({
                        bind: 'bankAccount',
                        node: '@sage/xtrem-finance-data/BankAccount',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: { financialSite: { _id: true } } }),
                            ui.nestedFields.technical<PurchaseInvoice, BankAccount, Currency>({
                                bind: 'currency',
                                node: '@sage/xtrem-master-data/Currency',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: '_id' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'priceScale' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseInvoice, Site, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical<PurchaseInvoice, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
            ui.nestedFields.technical<PurchaseInvoice, Site, Site>({
                node: '@sage/xtrem-system/Site',
                bind: 'financialSite',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        isReadOnly() {
            if (this.site.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
        async onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            this.manageDisplayButtonSelectFromReceiptAction();
            if (this.site.value) {
                this.taxEngine.value =
                    this.site.value?.legalCompany?.legislation?.id === 'US'
                        ? 'genericTaxCalculation'
                        : String(this.site.value?.legalCompany?.taxEngine);
                TotalTaxCalculator.getInstance().taxEngineProperty = this.taxEngine.value;
                if (this.billBySupplier.value) {
                    this._defaultDimensionsAttributes = await initDefaultDimensions({
                        page: this,
                        dimensionDefinitionLevel: 'purchasingDirect',
                        site: this.site.value,
                        supplier: this.billBySupplier.value,
                    });
                }
            }
            this.showHideColumns();
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                site: this.site.value,
            });
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PurchaseInvoice, Supplier>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Bill-by supplier',
        lookupDialogTitle: 'Select bill-by supplier',
        minLookupCharacters: 3,
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<PurchaseInvoice, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseInvoice, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseInvoice, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<PurchaseInvoice, Supplier, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                    ui.nestedFields.technical({ bind: 'dueDateType' }),
                    ui.nestedFields.technical({ bind: 'days' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseInvoice, Supplier, User>({
                bind: 'defaultBuyer',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
        ],
        async onChange() {
            await this.$.fetchDefaults(['billByLinkedAddress', 'billByAddress', 'currency', 'paymentTerm']);
            if (this.billBySupplier.value?.businessEntity?.currency) {
                this.currency.value = this.billBySupplier.value.businessEntity.currency;
            }
            if (this.billBySupplier.value?.paymentTerm) {
                this.paymentTerm.value = this.billBySupplier.value.paymentTerm;
            }
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            this.showHideColumns();
            if (this.site.value && this.billBySupplier.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'purchasingDirect',
                    site: this.site.value,
                    supplier: this.billBySupplier.value,
                });
            }
        },
        isReadOnly() {
            if (this.billBySupplier.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
    })
    billBySupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<PurchaseInvoice, Currency>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Company currency',
        lookupDialogTitle: 'Select company currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isMandatory: true,
        fetchesDefaults: true,
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
            ui.nestedFields.technical({ bind: 'symbol' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter() {
            return { isActive: true };
        },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        onChange() {
            this.applyCompanyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
    })
    companyCurrency: ui.fields.Reference<Currency>;

    applyCurrency() {
        this.totalAmountExcludingTax.unit = this.currency.value;
    }

    private applyCompanyCurrency() {
        this.calculatedTotalAmountExcludingTaxInCompanyCurrency.unit = this.siteCurrency;
    }

    @ui.decorators.dateField<PurchaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden: true,
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.textField<PurchaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<PurchaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Invoice date',
        isReadOnly() {
            return !!this.$.recordId;
        },
        validation(value) {
            if (Date.parse(value) > Date.now()) {
                return ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__invoice_date__cannot__be__future',
                    'The invoice date cannot be later than today.',
                );
            }

            return undefined;
        },
    })
    invoiceDate: ui.fields.Date;

    @ui.decorators.labelField<PurchaseInvoice>({
        title: 'Status',
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<PurchaseInvoice>({
        title: 'Display status',
        optionType: '@sage/xtrem-purchasing/PurchaseInvoiceDisplayStatus',
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation') {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/page__at_least_one_mandatory_tax_code_not_found',
                        'At least one mandatory tax code is missing on the line.',
                    ),
                    { type: 'warning' },
                );
            }

            if (this.displayStatus.value === 'postingInProgress') {
                if (
                    await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_title',
                            'Check and update status',
                        ),
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_message',
                            'You are about to update the status.',
                        ),
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_continue',
                            'Continue',
                        ),
                    )
                ) {
                    const { newDisplayStatus } = await this.$.graph
                        .node('@sage/xtrem-purchasing/PurchaseInvoice')
                        .mutations.resynchronizeDisplayStatus(
                            { oldDisplayStatus: true, newDisplayStatus: true },
                            { purchaseInvoice: this.$.recordId ?? '' },
                        )
                        .execute();
                    if (newDisplayStatus !== 'postingInProgress') {
                        await this.$.router.refresh();
                    }
                }
            }
        },
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Total remaining quantity to credit',
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        isReadOnly: true,
        isHidden: true,
    })
    calculatedTotalRemainingQuantityToCredit: ui.fields.Numeric;

    @ui.decorators.dropdownListField<PurchaseInvoice>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Tax engine',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isHidden: true,
    })
    taxEngine: ui.fields.DropdownList;

    @ui.decorators.referenceField<PurchaseInvoice, BusinessEntityAddress>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Bill-by address',
        valueField: { name: true },
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'addressLine1' }),
            ui.nestedFields.technical({ bind: 'addressLine2' }),
            ui.nestedFields.technical({ bind: 'city' }),
            ui.nestedFields.technical({ bind: 'region' }),
            ui.nestedFields.technical({ bind: 'postcode' }),
            ui.nestedFields.technical<PurchaseInvoice, BusinessEntityAddress, Country>({
                bind: 'country',
                node: '@sage/xtrem-structure/Country',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
            ui.nestedFields.technical<PurchaseInvoice, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        orderBy: { isActive: +1 },
        filter() {
            if (this.billBySupplier.value) {
                return { isActive: true, businessEntity: { id: this.billBySupplier.value.businessEntity?.id } };
            }
            return { isActive: true };
        },
        async onChange() {
            await this.$.fetchDefaults(['billByAddress']);
        },
    })
    billByLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.tile<PurchaseInvoice>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total supplier amount incl. tax',
        bind: 'totalAmountIncludingTax',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseInvoiceExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total variance amount excl. tax',
        bind: 'varianceTotalAmountExcludingTax',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseInvoiceVarianceExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total tax variance ',
        bind: 'varianceTotalTaxAmount',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseInvoiceVarianceTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total variance amount incl. tax',
        bind: 'varianceTotalAmountIncludingTax',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseInvoiceVarianceIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Total payments',
        parent() {
            return this.tileContainer;
        },
        unit() {
            return this.currency.value;
        },
        size: 'small',
        bind: { paymentTracking: { amountPaid: true } },
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    amountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.tileContainer;
        },
        title: 'Net balance',
        size: 'small',
        unit() {
            return this.currency.value;
        },
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    netBalance: ui.fields.Numeric;

    @ui.decorators.fileField<PurchaseInvoice>({
        parent() {
            return this.pdfBlock;
        },
        fileTypes: 'application/pdf',
        text: 'Supplier invoice',
        onChange() {
            if (this.pdfSupplierInvoice.value) {
                this.pdf.value = this.pdfSupplierInvoice.value.value;
            } else {
                this.pdf.value = '';
            }
        },
    })
    pdfSupplierInvoice: ui.fields.File;

    @ui.decorators.pluginField<PurchaseInvoice, PdfPluginProperties>({
        parent() {
            return this.pdfBlock;
        },

        pluginPackage: '@sage/xtrem-ui-plugin-pdf',
        height: 800,
        isFullWidth: true,
        isTransient: true,
    })
    pdf: ui.fields.Plugin<PdfPluginProperties>;

    @ui.decorators.textField<PurchaseInvoice>({
        parent() {
            return this.supplierBlock;
        },
        width: 'small-medium',
        title: 'Supplier invoice reference',
    })
    supplierDocumentNumber: ui.fields.Text;

    @ui.decorators.dateField<PurchaseInvoice>({
        parent() {
            return this.supplierBlock;
        },
        width: 'small-medium',
        title: 'Supplier document date',
        isMandatory: true,
        validation(value) {
            if (Date.parse(value) > Date.now()) {
                return ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__supplier_document_date__cannot__be__future',
                    'The supplier document date cannot be later than today.',
                );
            }

            return undefined;
        },
        async onChange() {
            await this.$.fetchDefaults(['fxRateDate', 'companyFxRate', 'rateDescription']);
            dueDateDefault(this);
        },
    })
    supplierDocumentDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.supplierBlock;
        },
        width: 'small-medium',
        title: 'Total supplier amount excl. tax',
        scale: null,
        unit() {
            return this.currency?.value;
        },
        isMandatory: true,
        isReadOnly() {
            return this._isLineDisabled();
        },
        onChange() {
            this.totalAmountIncludingTax.value = Number(
                Number(this.totalAmountExcludingTax.value ?? 0) + Number(this.totalTaxAmount.value ?? 0),
            );
            this._computeTotalAmounts();
        },
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.supplierBlock;
        },
        width: 'small-medium',
        title: 'Total supplier tax',
        unit() {
            return this.currency?.value;
        },
        scale: null,
        onChange() {
            if (!this.totalTaxAmount.value) {
                this.totalTaxAmount.value = 0;
            }
            this.totalAmountIncludingTax.value = Number(
                Number(this.totalAmountExcludingTax.value ?? 0) + Number(this.totalTaxAmount.value ?? 0),
            );
            this._computeTotalAmounts();
        },
        isReadOnly() {
            return this._isLineDisabled();
        },
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.supplierBlock;
        },
        width: 'small-medium',
        isHidden: true,
        title: 'Total supplier amount incl. tax',
        isReadOnly: true,
        scale: null,
        unit() {
            return this.currency?.value;
        },
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.varianceBlock;
        },
        title: 'Total amount excluding tax variance',
        unit() {
            return this.currency?.value;
        },
        scale: null,
        isReadOnly: true,
        isHidden: true,
    })
    varianceTotalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.varianceBlock;
        },
        title: 'Total tax amount variance',
        unit() {
            return this.currency?.value;
        },
        isReadOnly: true,
        isHidden: true,
    })
    varianceTotalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        parent() {
            return this.varianceBlock;
        },
        title: 'Total amount including tax variance',
        unit() {
            return this.currency?.value;
        },
        scale: null,
        isReadOnly: true,
        isHidden: true,
    })
    varianceTotalAmountIncludingTax: ui.fields.Numeric;

    private _isLineDisabled() {
        return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
    }

    @ui.decorators.tableField<PurchaseInvoice, PurchaseInvoiceLineBinding>({
        title: 'Lines',
        width: 'large',
        canSelect: false,
        pageSize: 10,
        canAddNewLine: true,
        hasLineNumbers: true,
        node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        onChange() {
            this._computeTotalAmounts();
        },
        warningMessage() {
            const numberOfWarningInTable = this.lines.value.reduce(
                (count, rowValue) =>
                    count +
                    Number(!!JSON.parse(rowValue.landedCostCheckResult ?? '{}').message) +
                    (this.financeIntegrationCheckResult?.validationMessages?.filter(
                        message => message.lineNumber === +(rowValue._id ?? 0),
                    ).length ?? 0),
                0,
            );

            return PurchaseInvoice.getHeaderGridWarningMessage(numberOfWarningInTable);
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: 'landedCostCheckResult' }),
            ui.nestedFields.icon<PurchaseInvoice, PurchaseInvoiceLineBinding>({
                bind: '_id', // link to a non-transient field and not displayed in the gridRowBlock
                title: () => ' ',
                size: 'small',
                color: tokens.colorsSemanticCaution500,
                map(_value?, rowValue?) {
                    return (JSON.parse(rowValue.landedCostCheckResult ?? '{}').message ??
                        this.financeIntegrationCheckResult?.validationMessages?.some(
                            message => message.lineNumber === +(_value ?? 0),
                        ))
                        ? 'warning'
                        : '';
                },
                isHidden() {
                    return (
                        this.lines.value.every(
                            rowValue => !JSON.parse(rowValue.landedCostCheckResult ?? '{}').message,
                        ) && !this.financeIntegrationCheckResult?.validationMessages?.length
                    );
                },
                async onClick(_id, rowData) {
                    const financeIntegrationErrors = this.financeIntegrationCheckResult?.validationMessages?.filter(
                        validationMessage => validationMessage.lineNumber === +(_id ?? 0),
                    );

                    const landedCostCheckResult = JSON.parse(rowData.landedCostCheckResult);

                    if (!landedCostCheckResult && !financeIntegrationErrors) {
                        return;
                    }

                    const dialogBoxTitle = financeIntegrationErrors?.length
                        ? ui.localize(
                              '@sage/xtrem-purchasing/pages__purchase_invoice__finance_errors_title',
                              'Finance control',
                          )
                        : landedCostCheckResult.title;

                    const dialogBoxMessage = financeIntegrationErrors?.length
                        ? financeIntegrationErrors.map(validationMessage => validationMessage.message).join('\n')
                        : landedCostCheckResult.message;

                    const dialogBoxButtonText = financeIntegrationErrors?.length
                        ? ui.localize(
                              '@sage/xtrem-purchasing/pages__purchase_invoice__confirm_landed_cost',
                              'Landed cost',
                          )
                        : ui.localize('@sage/xtrem-purchasing/pages__purchase_invoice__confirm_allocate', 'Allocate');

                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            dialogBoxTitle,
                            dialogBoxMessage,
                            dialogBoxButtonText,
                        )
                    ) {
                        await this.openLandedCostPanel(rowData, financeIntegrationErrors ?? []);
                    }
                },
            }),
            ui.nestedFields.image({
                title: 'Image',
                isTitleHidden: true,
                bind: { item: { image: true } },
                width: 'small',
                height: '40px',
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, PurchaseInvoiceNode>({
                bind: 'document',
                node: '@sage/xtrem-purchasing/PurchaseInvoice',
                valueField: 'number',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceNode, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'rounding' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentLineOrigin',
                isReadOnly: true,
            }),
            ui.nestedFields.label<PurchaseInvoice, PurchaseInvoiceLine>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
                isHidden() {
                    return this.stockTransactionStatus.value
                        ? ['draft', 'completed'].includes(this.stockTransactionStatus.value)
                        : false;
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.label({
                title: 'Tax status',
                bind: 'taxCalculationStatus',
                isHidden() {
                    return (
                        this.taxCalculationStatus.value !== 'failed' || this.taxEngine.value !== 'genericTaxCalculation'
                    );
                },
                optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
                style: (_id, rowData) =>
                    PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', rowData?.taxCalculationStatus),
                async onClick(_rowId, rowItem) {
                    this.displayTaxesClicked = true;
                    await this.callDisplayTaxes(rowItem);
                    this.displayTaxesClicked = false;
                },
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, Item>({
                title: 'Item',
                lookupDialogTitle: 'Select item',
                minLookupCharacters: 3,
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                isMandatory: true,
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<PurchaseInvoice, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
                orderBy: { name: -1, stockUnit: { id: +1 } },
                isReadOnly() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData) {
                    if (!rowData?.item || !rowData.item._id) {
                        return;
                    }

                    rowData.itemDescription = rowData.item.description ? rowData.item.description : rowData.item.name;
                    rowData.unit = await getPurchaseUnit(
                        this.$.graph,
                        rowData.item._id,
                        this.billBySupplier.value?._id ?? '',
                    );
                    const quantityToConvert = rowData.quantity ?? '1';

                    if (rowData.unit?._id && rowData.item.stockUnit?._id) {
                        const conversion = await convertFromTo(
                            this.$.graph,
                            rowData.unit._id,
                            rowData.item.stockUnit._id,
                            Number(quantityToConvert),
                            rowData.item._id,
                            this.billBySupplier?.value?._id ?? '',
                            '',
                            'purchase',
                            false,
                        );
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.unitToStockUnitConversionFactor = String(conversion.conversionFactor);
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = String(conversion.convertedQuantity);
                        }
                    }

                    rowData.recipientSite =
                        (await checkStockSite(this, rowData.item, rowData.recipientSite)) ?? undefined;

                    if (rowData.item.type === 'landedCost') {
                        const defaultLandedCost = await fetchDefaultLandedCostDocumentLine(
                            this,
                            'PurchaseInvoice',
                            rowData.item._id,
                        );
                        if (defaultLandedCost) {
                            rowData.landedCost = {
                                ...defaultLandedCost,
                                // set _id otherwise we have a bug when creating several lines at once.
                                // When saving, they all have the same negative _id and it fails
                                ...(rowData._id < 0 ? { _id: rowData._id } : {}),
                                landedCostAllocationMethod: 'manual',
                            };
                        }
                    }

                    if (this.site.value && this.billBySupplier.value && rowData.item) {
                        const { storedAttributes, storedDimensions } =
                            await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                page: this,
                                _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                dimensionDefinitionLevel: 'purchasingDirect',
                                site: this.site.value,
                                supplier: this.billBySupplier.value,
                                item: rowData.item,
                            });
                        rowData.storedAttributes = storedAttributes;
                        rowData.storedDimensions = storedDimensions;
                    }

                    this.lines.addOrUpdateRecordValue(rowData);
                    await this.lines.redraw('quantity');
                    await this.lines.redraw('recipientSite');

                    await this._setPriceOrigin(rowData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
            }),
            ui.nestedFields.technical<PurchaseInvoice, PurchaseInvoiceLineBinding, Site>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, Site>({
                title: 'Recipient site',
                lookupDialogTitle: 'Select recipient site',
                minLookupCharacters: 0,
                bind: 'recipientSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                fetchesDefaults: true,
                isMandatory: true,
                isDisabled(_rowId, rowData?: ui.PartialCollectionValue<PurchaseInvoiceLineBinding>) {
                    return this._isLineDisabled() ?? rowData?.origin !== 'direct';
                },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.reference({
                        bind: { businessEntity: { country: true } },
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseInvoice, Site, Site['legalCompany']>({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
                async onChange(_rowId, rowData) {
                    if (rowData.item) {
                        await this.onChangeTaxCalculation(rowData);
                    }
                },
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, UnitOfMeasure>({
                title: 'Purchase unit',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 0,
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isMandatory: true,
                fetchesDefaults: true,
                isDisabled(_rowId, rowData?: ui.PartialCollectionValue<PurchaseInvoiceLineBinding>) {
                    return this._isLineDisabled() ?? rowData?.origin !== 'direct';
                },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                async onChange(_rowId, rowData) {
                    if (rowData?.unit && rowData?.item) {
                        const quantityToConvert = rowData.quantity ? rowData.quantity : 1;

                        const conversion = await convertFromTo(
                            this.$.graph,
                            rowData.unit._id,
                            rowData.item.stockUnit._id,
                            quantityToConvert,
                            rowData.item._id,
                            this.billBySupplier.value?._id,
                            '',
                            'purchase',
                            false,
                        );
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor;
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = conversion.convertedQuantity;
                        }
                        await this._calculatePrices(rowData);
                        this.lines.addOrUpdateRecordValue(rowData);
                        await this._setPriceOrigin(rowData);
                    }
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'quantity',
                isMandatory: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseInvoiceLine>) {
                    if (rowData?.item && Number(rowData?.quantity) >= 0 && rowData?.unit) {
                        if (rowData.origin !== 'direct') {
                            await this.changeQuantityReceipt(rowData);
                        } else {
                            await this.changedQuantity(rowData);
                        }
                    }
                    await this.onChangeTaxCalculation(rowData);
                },
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                scale: (_rowId, rowData) => rowData?.stockUnit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol ?? '',
                isHidden(_rowId, rowData) {
                    if (isPurchaseOrderLine(rowData)) {
                        return rowData?.item?.type === 'service';
                    }
                    return false;
                },
            }),
            ui.nestedFields.numeric({
                bind: 'unitToStockUnitConversionFactor',
                title: 'Stock unit conversion factor',
                width: 'large',
                isHiddenOnMainField: true,
                isReadOnly: true,
                scale: (_rowId, rowData) =>
                    rowData?.unitToStockUnitConversionFactor?.toString().split('.')[1].length ?? 2,
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                scale() {
                    return getCompanyPriceScale(this?.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData) {
                    rowData.priceOrigin = rowData.grossPrice ? 'manual' : null;
                    await this.onChangeTaxCalculation(rowData);
                    if (rowData.origin !== 'direct') {
                        rowData.varianceType = updateLineVarianceType(rowData);
                        rowData.matchingStatus = rowData.varianceType === 'noVariance' ? 'noVariance' : 'variance';
                    }
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData) {
                    await this.onChangeTaxCalculation(rowData);
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData) {
                    await this.onChangeTaxCalculation(rowData);
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Net price',
                bind: 'netPrice',
                scale() {
                    return getCompanyPriceScale(this?.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.label({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-purchasing/PriceOrigin',
                width: 'large',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, Currency>({
                title: 'Currency',
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isExcludedFromMainField: true,
                fetchesDefaults: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.numeric({ bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ bind: 'symbol', canFilter: false }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
                filter() {
                    return { isActive: true };
                },
                isReadOnly: true,
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, UnitOfMeasure>({
                minLookupCharacters: 0,
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden: (_rowId, rowData?: ui.PartialCollectionValue<PurchaseInvoiceLineBinding>) =>
                    rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'amountExcludingTax',
                width: 'small',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Tax amount',
                bind: 'taxAmount',
                width: 'small',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                bind: 'amountIncludingTax',
                width: 'large',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax company currency',
                bind: 'amountIncludingTaxInCompanyCurrency',
                width: 'small',
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                bind: 'amountExcludingTaxInCompanyCurrency',
                width: 'small',
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Tax amount adjusted',
                bind: 'taxAmountAdjusted',
                width: 'large',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({
                bind: 'landedCost',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'allocationRule' }),
                    ui.nestedFields.technical({ bind: 'allocationMethod' }),
                    ui.nestedFields.technical({ bind: 'costAmountToAllocate' }),
                    ui.nestedFields.technical({ bind: 'costAmountToAllocateInCompanyCurrency' }),
                    ui.nestedFields.technical({ bind: 'totalAmountAllocated' }),
                    ui.nestedFields.technical({
                        bind: 'allocationRuleUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
                    }),
                ],
            }),
            ui.nestedFields.technical<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseReceiptLineToPurchaseInvoiceLine
            >({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: { purchaseReceiptLine: { _id: true } } }),
                    ui.nestedFields.technical<
                        PurchaseInvoice,
                        PurchaseReceiptLineToPurchaseInvoiceLine,
                        PurchaseReceiptLine
                    >({
                        bind: 'purchaseReceiptLine',
                        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                        nestedFields: [
                            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLine, UnitOfMeasure>({
                                bind: 'unit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                            ui.nestedFields.technical({ bind: 'quantity' }),
                            ui.nestedFields.technical({ bind: 'grossPrice' }),
                            ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
                            ui.nestedFields.technical<
                                PurchaseInvoice,
                                PurchaseReceiptLine,
                                PurchaseOrderLineToPurchaseReceiptLine
                            >({
                                bind: 'purchaseOrderLine',
                                node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
                                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                            }),
                        ],
                    }),
                    ui.nestedFields.technical<
                        PurchaseInvoice,
                        PurchaseReceiptLineToPurchaseInvoiceLine,
                        PurchaseInvoiceLine
                    >({
                        bind: 'purchaseInvoiceLine',
                        node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
            }),
            ui.nestedFields.technical<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseReceiptLineToPurchaseInvoiceLine
            >({
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
                nestedFields: [ui.nestedFields.technical({ bind: 'invoicedQuantity' })],
            }),
            ui.nestedFields.reference<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseOrderLineToPurchaseInvoiceLine
            >({
                title: 'Order line',
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine',
                valueField: { purchaseOrderLine: { _id: true } },
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'ID', bind: '_id' }),
                    ui.nestedFields.reference<
                        PurchaseInvoice,
                        PurchaseOrderLineToPurchaseInvoiceLine,
                        PurchaseOrderLine
                    >({
                        title: 'Purchase order',
                        bind: 'purchaseOrderLine',
                        node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                        valueField: '_id',
                        columns: [
                            ui.nestedFields.text({ title: 'ID', bind: '_id' }),
                            ui.nestedFields.reference<PurchaseInvoice, PurchaseOrderLine, UnitOfMeasure>({
                                title: 'Purchase unit',
                                bind: 'unit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                                valueField: 'symbol',
                                isMandatory: true,
                                fetchesDefaults: true,
                                columns: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                            ui.nestedFields.numeric({
                                title: 'Ordered quantity',
                                bind: 'quantity',
                                isExcludedFromMainField: true,
                                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                            }),
                            ui.nestedFields.numeric({
                                title: 'Order unit price',
                                bind: 'grossPrice',
                                isExcludedFromMainField: true,
                                scale() {
                                    return getCompanyPriceScale(this?.site?.value?.legalCompany);
                                },
                                unit: (_rowId, rowData) => rowData?.currency,
                            }),
                            ui.nestedFields.numeric({
                                title: 'Total amount from order',
                                bind: 'amountExcludingTax',
                                isExcludedFromMainField: true,
                                unit: (_rowId, rowData) => rowData?.currency,
                            }),
                        ],
                    }),
                    ui.nestedFields.reference<
                        PurchaseInvoice,
                        PurchaseOrderLineToPurchaseInvoiceLine,
                        PurchaseInvoiceLine
                    >({
                        title: 'Purchase invoice',
                        bind: 'purchaseInvoiceLine',
                        node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                        valueField: '_id',
                        columns: [ui.nestedFields.text({ title: 'ID', bind: '_id' })],
                    }),
                ],
            }),
            ui.nestedFields.reference<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseReceiptLineToPurchaseInvoiceLine
            >({
                title: 'Received quantity',
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
                valueField: { purchaseReceiptLine: { quantity: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseOrderLineToPurchaseInvoiceLine
            >({
                title: 'Ordered quantity',
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine',
                valueField: { purchaseOrderLine: { quantity: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseReceiptLineToPurchaseInvoiceLine
            >({
                title: 'Receipt unit price',
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
                valueField: { purchaseReceiptLine: { grossPrice: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseReceiptLineToPurchaseInvoiceLine
            >({
                title: 'Total amount from receipt',
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
                valueField: { purchaseReceiptLine: { amountExcludingTax: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseOrderLineToPurchaseInvoiceLine
            >({
                title: 'Order unit price',
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine',
                valueField: { purchaseOrderLine: { grossPrice: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseInvoice,
                PurchaseInvoiceLineBinding,
                PurchaseOrderLineToPurchaseInvoiceLine
            >({
                title: 'Total amount from order',
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine',
                valueField: { purchaseOrderLine: { amountExcludingTax: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Matching status',
                bind: 'matchingStatus',
                optionType: '@sage/xtrem-purchasing/PurchaseInvoiceMatchingStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseInvoiceMatchingStatus', rowData?.matchingStatus),
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.label({
                bind: 'varianceType',
                title: 'Variance type',
                optionType: '@sage/xtrem-purchasing/PurchaseInvoiceVarianceType',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<PurchaseInvoice, PurchaseInvoiceLineBinding, User>({
                title: 'Variance approver',
                bind: 'varianceApprover',
                node: '@sage/xtrem-system/User',
                tunnelPage: '@sage/xtrem-authorization/User',
                valueField: 'displayName',
                isReadOnly: true,
                isHiddenOnMainField: true,
                columns: [ui.nestedFields.text({ title: 'ID', bind: 'displayName' })],
            }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical({ bind: 'taxDate' }),

            // TODO: how to display a TextStream property type ?
            // ui.nestedFields.richTextField({
            //     title: 'Variance text',
            //     bind: 'varianceText',
            //     isHidden: true,
            //     isExcludedFromMainField: true,
            // }),
        ],
        dropdownActions: [
            {
                icon: 'none',
                title: 'Landed cost',
                isHidden(_rowId, rowData) {
                    return !this.$.isServiceOptionEnabled('landedCostOption') || rowData.item?.type !== 'landedCost';
                },
                isDisabled() {
                    return this.$.isDirty ?? this.isRepost;
                },
                async onClick(_rowId, rowData) {
                    await this.openLandedCostPanel(
                        rowData,
                        this.financeIntegrationCheckResult?.validationMessages?.filter(
                            validationMessage => validationMessage.lineNumber === +(_rowId ?? 0),
                        ) ?? [],
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'document_tick',
                title: 'Accept variance',
                isDisabled(_rowId, rowData) {
                    return rowData?.matchingStatus !== 'variance' || this.$.isDirty;
                },
                async onClick(_rowId, rowData) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_title',
                                'Accept variance for this invoice line',
                            ),
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_content',
                                'You are about to approve the variance for this invoice line.',
                            ),
                            ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
                        )
                    ) {
                        this.$.loader.isHidden = false;
                        await this.approveVarianceLine(true, this.$.recordId ?? '', rowData._id);
                        this.$.loader.isHidden = true;
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            {
                                editable:
                                    !isPurchaseInvoiceLineActionDisabled(this.status.value ?? '', '', 'dimensions') ||
                                    this.isRepost,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'none',
                title: 'Tax details',
                isHidden: (_rowId, rowItem) => !rowItem.uiTaxes,
                async onClick(_rowId, rowItem) {
                    if (rowItem.uiTaxes) {
                        await this.callDisplayTaxes(rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                async onClick(rowId, rowItem) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_invoice__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_invoice__line_delete_action_dialog_content',
                                'You are about to delete this purchase invoice line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        await TotalTaxCalculator.getInstance().updateTaxData(JSON.parse(rowItem.uiTaxes ?? '{}'), null);
                        await TotalTaxCalculator.getInstance().updateTaxDetails(
                            this.taxes,
                            this.totalTaxAmountAdjusted,
                            this.calculatedTotalTaxAmount,
                        );
                        this.taxCalculationStatus.value = 'notDone';
                        this._computeTotalAmounts();
                        recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                    }
                },
                isHidden() {
                    return this._isLineDisabled();
                },
            },
        ],
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Lines with variances', graphQLFilter: { matchingStatus: { _eq: 'variance' } } },
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        addItemActions() {
            return [this.selectFromPurchaseReceiptsLookup];
        },
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.selectReceiptSecondaryButton],
            });
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description' }),
            line2Right: ui.nestedFields.numeric({
                bind: 'amountIncludingTax',
                title: 'Amount',
                canFilter: false,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (+(recordValue?._id ?? 0) < 0) {
                    return ui.localize('@sage/xtrem-purchasing/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'none',
                    title: 'Landed cost',
                    isHidden(_rowId, rowData) {
                        return (
                            !this.$.isServiceOptionEnabled('landedCostOption') || rowData.item?.type !== 'landedCost'
                        );
                    },
                    isDisabled() {
                        return this.$.isDirty;
                    },
                    async onClick(_rowId, rowData) {
                        await this.openLandedCostPanel(
                            rowData,
                            this.financeIntegrationCheckResult?.validationMessages?.filter(
                                validationMessage => validationMessage.lineNumber === +(_rowId ?? 0),
                            ) ?? [],
                        );
                    },
                },

                {
                    icon: 'document_tick',
                    title: 'Accept variance',
                    isDisabled: (_rowId, rowData) => rowData?.matchingStatus !== 'variance',
                    async onClick(_rowId, rowData) {
                        if (
                            await confirmDialogWithAcceptButtonText(
                                this,
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_title',
                                    'Accept variance for this invoice line',
                                ),
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_content',
                                    'You are about to approve the variance for this invoice line.',
                                ),
                                ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
                            )
                        ) {
                            this.$.loader.isHidden = false;
                            await this.approveVarianceLine(true, this.$.recordId ?? '', rowData._id);
                            this.$.loader.isHidden = true;
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                { documentLine: rowData },
                                {
                                    editable:
                                        !isPurchaseInvoiceLineActionDisabled(
                                            this.status?.value ?? '',
                                            '',
                                            'dimensions',
                                        ) || this.isRepost,
                                },
                            ),
                        );
                    },
                },
                {
                    icon: 'none',
                    title: 'Tax details',
                    isHidden: (_rowId, rowItem) => !rowItem.uiTaxes,
                    async onClick(_rowId, rowItem) {
                        if (rowItem.uiTaxes) {
                            await this.callDisplayTaxes(rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden() {
                        return this._isLineDisabled();
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';

                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<PurchaseInvoiceLineBinding>,
                    );
                }

                await TotalTaxCalculator.getInstance().updateTaxDetails(
                    this.taxes,
                    this.totalTaxAmountAdjusted,
                    this.calculatedTotalTaxAmount,
                );
                recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                this._computeTotalAmounts();
            },
            async onRecordOpened(_id, recordValue) {
                this._isAddNewLine = true;
                if (recordValue) {
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';

                    if (
                        +recordValue._id < 0 &&
                        recordValue.origin === 'direct' &&
                        (this._defaultDimensionsAttributes.dimensions !== '{}' ||
                            this._defaultDimensionsAttributes.attributes !== '{}')
                    ) {
                        const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                            {
                                site: this.site.value,
                                currency: this.currency.value,
                                origin: 'direct',
                                recipientSite:
                                    this.site.value &&
                                    this.site.value.isFinance &&
                                    (this.site.value.isPurchase ?? this.site.value.isInventory)
                                        ? this.site.value
                                        : null,
                            } as ui.PartialNodeWithId<PurchaseInvoiceLine>,
                            this._defaultDimensionsAttributes,
                        );

                        recordValue.storedAttributes = line.storedAttributes ?? '';
                        recordValue.storedDimensions = line.storedDimensions ?? '';

                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<PurchaseInvoiceLineBinding>,
                        );
                    }
                }
                this._loadMatchingQuantityPod(_id, recordValue);
                this._loadMatchingPricePod(_id, recordValue);

                await this._loadRelatedPurchaseReceiptLines(_id);
                await this._loadRelatedPurchaseOrderLines(_id);
                this._filterInvoicingInProgressReceiptLines(_id);
                this._showHideReceiptsSection();
            },
            layout() {
                return {
                    general: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            itemBlock: { fields: ['item', 'origin', 'itemDescription', 'recipientSite'] },
                            purchase: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_purchase',
                                    'Purchase',
                                ),
                                fields: ['unit', 'quantity'],
                            },
                            stock: {
                                isHidden: (_id, recordValue) => !recordValue?.item?.isStockManaged,
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: { fields: ['grossPrice', 'discount', 'charge', 'netPrice'] },
                            mainBlock2: { fields: ['priceOrigin'] },
                            totals: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['amountExcludingTax', 'taxAmount', 'amountIncludingTax'],
                            },
                            totals2: {
                                fields: ['amountExcludingTaxInCompanyCurrency', 'amountIncludingTaxInCompanyCurrency'],
                            },
                        },
                    },
                    matching: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_matching', 'Matching'),
                        blocks: {
                            varianceBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_variance',
                                    'Variance',
                                ),
                                fields: ['matchingStatus', 'varianceType'],
                            },
                            varianceApproverBlock: { fields: ['varianceApprover'] },
                            matchingQuantityBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_quantity',
                                    'Quantity',
                                ),
                                fields: [this.matchingQuantity],
                            },
                            matchingPriceBlock: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_price', 'Price'),
                                fields: [this.matchingPrice],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.purchaseReceiptLines.value.length === 0;
                        },
                        blocks: { receiptBlock: { fields: [this.purchaseReceiptLines] } },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: { notesBlock: { fields: [this.internalNoteLine] } },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<PurchaseInvoiceLine>;

    @ui.decorators.pageAction<PurchaseInvoice>({
        title: 'Record payment',
        isHidden: true,
        async onClick() {
            if (this.$.recordId) {
                const paymentNumber = await openRecordPage(this);
                if (typeof paymentNumber === 'string') {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__payment_created',
                            'The following payment was created: {{paymentNumber}}.',
                            { paymentNumber },
                        ),
                        { timeout: 10000 },
                    );
                }
                await this.$.router.refresh();
                await this.$.refreshNavigationPanel();
            }
        },
    })
    recordPayment: ui.PageAction;

    public _computeTotalAmounts() {
        const { totalAmountExcludingTax, totalAmountExcludingTaxInCompanyCurrency } = this.lines.value.reduce(
            (amounts: { totalAmountExcludingTax: number; totalAmountExcludingTaxInCompanyCurrency: number }, line) => {
                amounts.totalAmountExcludingTax += Number(line.amountExcludingTax ?? 0);
                amounts.totalAmountExcludingTaxInCompanyCurrency += Number(
                    line.amountExcludingTaxInCompanyCurrency ?? 0,
                );
                return amounts;
            },
            { totalAmountExcludingTax: 0, totalAmountExcludingTaxInCompanyCurrency: 0 },
        );

        this.calculatedTotalAmountExcludingTax.value = totalAmountExcludingTax;
        this.calculatedTotalAmountIncludingTax.value = Number(
            this.calculatedTotalAmountExcludingTax.value + (this.totalTaxAmountAdjusted.value ?? 0),
        );
        this.calculatedTotalAmountExcludingTaxInCompanyCurrency.value = totalAmountExcludingTaxInCompanyCurrency;
        this.calculatedTotalAmountIncludingTaxInCompanyCurrency.value = convertAmount(
            Number(this.calculatedTotalAmountIncludingTax.value),
            this.companyFxRate.value ?? 0,
            this.companyFxRateDivisor.value ?? 0,
            this.currency.value?.decimalDigits ?? 2,
            this.siteCurrency?.decimalDigits ?? 2,
        );

        if ((this.totalAmountIncludingTax.value ?? 0) + this.calculatedTotalAmountExcludingTax.value > 0) {
            this.varianceTotalAmountExcludingTax.value = Number(
                (this.calculatedTotalAmountExcludingTax.value ?? 0) - (this.totalAmountExcludingTax.value ?? 0),
            );
        }

        if (Number(this.totalTaxAmount.value ?? 0) + (this.totalTaxAmountAdjusted.value ?? 0) >= 0) {
            this.varianceTotalTaxAmount.value = Number(
                (this.totalTaxAmountAdjusted.value ?? 0) - (this.totalTaxAmount.value ?? 0),
            );
        }

        this.varianceTotalAmountIncludingTax.value = Number(
            (this.varianceTotalTaxAmount.value ?? 0) + (this.varianceTotalAmountExcludingTax.value ?? 0),
        );
    }

    private _loadMatchingQuantityPod(_id: string, rowData: any) {
        this.matchingQuantity.value = {
            unit: rowData.unit,
            invoiceLineOrigin: rowData.origin,
            quantity: rowData.purchaseReceiptLine?.purchaseReceiptLine?.quantity,
            receivedQuantityVariance: rowData.purchaseReceiptLine?.purchaseReceiptLine?.quantity
                ? String(+rowData.quantity - +rowData.purchaseReceiptLine.purchaseReceiptLine.quantity)
                : '0',
            orderedQuantity: rowData.purchaseOrderLine?.purchaseOrderLine?.quantity,
            orderedQuantityVariance: rowData.purchaseOrderLine?.purchaseOrderLine?.quantity
                ? String(+rowData.quantity - +rowData.purchaseOrderLine.purchaseOrderLine.quantity)
                : '0',
        };
    }

    private async _loadRelatedPurchaseOrderLines(invoiceLineId: string) {
        const oldIsDirty = this.$.isDirty;
        const filter = { _and: [{}] };
        filter._and.push({ purchaseInvoiceLine: { document: { _id: { _eq: this.$.recordId } } } });
        if (invoiceLineId) {
            filter._and.push({ purchaseInvoiceLine: { _id: invoiceLineId } });
        }
        const resultOrderLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseOrderLine: {
                                _id: true,
                                document: { _id: true, number: true },
                                status: true,
                                quantity: true,
                                grossPrice: true,
                            },
                            invoicedQuantity: true,
                            unit: { _id: true, id: true, symbol: true, decimalDigits: true },
                        },
                        { filter },
                    ),
                )
                .execute(),
        );
        this.purchaseOrderLine.value.forEach(line => this.purchaseOrderLine.removeRecord(line._id));
        resultOrderLines.forEach(row => {
            this.purchaseOrderLine.addOrUpdateRecordValue(row);
        });
        this.purchaseOrderLine.isHidden = this.purchaseOrderLine.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    private _loadMatchingPricePod(_id: string, rowData: any) {
        this.matchingPrice.value = {
            currency: rowData?.currency,
            invoiceLineOrigin: rowData.origin,
            grossPrice: Number(rowData.purchaseReceiptLine?.purchaseReceiptLine?.grossPrice),
            receiptPriceVariance: rowData.purchaseReceiptLine?.purchaseReceiptLine?.grossPrice
                ? String(
                      Number(rowData.grossPrice) - Number(rowData.purchaseReceiptLine?.purchaseReceiptLine?.grossPrice),
                  )
                : '0',
            totalTaxExcludedAmount: rowData.purchaseReceiptLine?.purchaseReceiptLine?.totalTaxExcludedAmount
                ? Number(rowData.purchaseReceiptLine?.purchaseReceiptLine?.totalTaxExcludedAmount)
                : '0',
            receiptTotalTaxExcludedAmountVariance: rowData.purchaseReceiptLine?.purchaseReceiptLine
                ?.totalTaxExcludedAmount
                ? String(
                      Number(rowData.amountExcludingTax) -
                          Number(rowData.purchaseReceiptLine?.purchaseReceiptLine?.totalTaxExcludedAmount),
                  )
                : '0',
            orderUnitPrice: Number(rowData.purchaseOrderLine?.purchaseOrderLine?.grossPrice),
            orderPriceVariance: rowData.purchaseOrderLine
                ? String(Number(rowData.grossPrice) - Number(rowData.purchaseOrderLine?.purchaseOrderLine?.grossPrice))
                : '0',
            orderTotalTaxExcludedAmount: rowData.purchaseOrderLine?.purchaseOrderLine?.amountExcludingTax
                ? Number(rowData.purchaseOrderLine.purchaseOrderLine.amountExcludingTax)
                : '0',
            orderTotalTaxExcludedAmountVariance: rowData.purchaseOrderLine?.purchaseOrderLine?.amountExcludingTax
                ? String(
                      Number(rowData.amountExcludingTax) -
                          Number(rowData.purchaseOrderLine?.purchaseOrderLine?.amountExcludingTax),
                  )
                : '0',
        };
    }

    @ui.decorators.vitalPodField<PurchaseInvoice, PurchaseReceiptLine>({
        title: 'Quantity',
        width: 'large',
        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
        isTransient: true,
        isTitleHidden: true,
        columns: [
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLine, UnitOfMeasure>({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'invoiceLineOrigin' as any }),
            ui.nestedFields.numeric<PurchaseInvoice, PurchaseReceiptLine>({
                title: 'Quantity received',
                bind: 'quantity',
                isReadOnly: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.label({
                title: 'Received quantity variance',
                bind: 'receivedQuantityVariance' as any, // Hack for transient input
                isTransient: true,
                isTitleHidden: true,
                borderColor: ui.tokens.colorsYang100,
                color: value => (value === '0' ? ui.tokens.colorsYang100 : ui.tokens.colorsSemanticNegative500),
            }),
            ui.nestedFields.numeric<PurchaseInvoice, PurchaseReceiptLine>({
                title: 'Quantity ordered',
                bind: 'orderedQuantity' as any, // Hack for transient input
                isReadOnly: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.label({
                title: 'Ordered quantity variance',
                bind: 'orderedQuantityVariance' as any, // Hack for transient input
                isTransient: true,
                isTitleHidden: true,
                borderColor: ui.tokens.colorsYang100,
                color: value => (value === '0' ? ui.tokens.colorsYang100 : ui.tokens.colorsSemanticNegative500),
            }),
        ],
    })
    matchingQuantity: ui.fields.VitalPod;

    @ui.decorators.vitalPodField<PurchaseInvoice, PurchaseReceiptLine>({
        title: 'Price',
        width: 'large',
        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
        isTransient: true,
        isTitleHidden: true,
        columns: [
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLine, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'rounding' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'invoiceLineOrigin' as any }),
            ui.nestedFields.numeric<PurchaseInvoice, PurchaseReceiptLine>({
                title: 'Receipt unit price',
                bind: 'grossPrice',
                isReadOnly: true,
                scale() {
                    return getCompanyPriceScale(this?.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.label({
                title: 'Receipt price variance',
                bind: 'receiptPriceVariance' as any, // Hack for transient input
                isTransient: true,
                isTitleHidden: true,
                borderColor: ui.tokens.colorsYang100,
                color: value => (value === '0' ? ui.tokens.colorsYang100 : ui.tokens.colorsSemanticNegative500),
            }),
            ui.nestedFields.numeric<PurchaseInvoice, PurchaseReceiptLine>({
                title: 'Total amount from receipt',
                bind: 'totalTaxExcludedAmount' as any, // Hack for transient input
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.label({
                title: 'Receipt total tax excluded amount variance',
                bind: 'receiptTotalTaxExcludedAmountVariance' as any, // Hack for transient input
                isTransient: true,
                isExcludedFromMainField: true,
                isTitleHidden: true,
                borderColor: ui.tokens.colorsYang100,
                color: value => (value === '0' ? ui.tokens.colorsYang100 : ui.tokens.colorsSemanticNegative500),
            }),
            ui.nestedFields.numeric<PurchaseInvoice, PurchaseReceiptLine>({
                title: 'Order unit price',
                bind: 'orderUnitPrice' as any, // Hack for transient input
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.label({
                title: 'Order price variance',
                bind: 'orderPriceVariance' as any, // Hack for transient input
                isTransient: true,
                isTitleHidden: true,
                borderColor: ui.tokens.colorsYang100,
                color: value => (value === '0' ? ui.tokens.colorsYang100 : ui.tokens.colorsSemanticNegative500),
            }),
            ui.nestedFields.numeric<PurchaseInvoice, PurchaseReceiptLine>({
                title: 'Total amount from order',
                bind: 'orderTotalTaxExcludedAmount' as any, // Hack for transient input
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.label({
                title: 'Order total tax excluded amount variance',
                bind: 'orderTotalTaxExcludedAmountVariance' as any, // Hack for transient input
                isTransient: true,
                isTitleHidden: true,
                borderColor: ui.tokens.colorsYang100,
                color: value => (value === '0' ? ui.tokens.colorsYang100 : ui.tokens.colorsSemanticNegative500),
            }),
        ],
    })
    matchingPrice: ui.fields.VitalPod;

    @ui.decorators.pageAction<PurchaseInvoice>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    /**
     * Handles the visibility of buttons in the dropdown based on selected options.
     * - When "Financial Site" is selected:
     *   - Hides the "Add Line in Panel" option as it's not supported in Sage UI library.
     *   - Displays only the "Add Lines from Orders" button.
     * - When both "Bill-by Supplier" and "Financial Site" are selected:
     *   - Displays both "Add Panel Option" and "Add Lines from Orders" in the dropdown.
     */

    @ui.decorators.pageAction<PurchaseInvoice>({
        icon: 'search',
        title: 'Add lines from receipts',
        isHidden() {
            return !(this.site.value && !this.billBySupplier.value);
        },
        async onClick() {
            await addLineFromReceipt(this);
        },
    })
    selectReceiptSecondaryButton: ui.PageAction;

    @ui.decorators.referenceField<PurchaseInvoice, Supplier>({
        parent() {
            return this.financialBlock;
        },
        title: 'Pay to supplier',
        lookupDialogTitle: 'Select pay to supplier',
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'Country', bind: { businessEntity: { country: { name: true } } } }),
            ui.nestedFields.technical<PurchaseInvoice, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseInvoice, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseInvoice, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<PurchaseInvoice, Supplier, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
        ],
        isReadOnly() {
            return !!this.$.recordId;
        },
        async onChange() {
            await this.$.fetchDefaults(['payToLinkedAddress', 'payToAddress']);
        },
    })
    payToSupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<PurchaseInvoice, BusinessEntityAddress>({
        parent() {
            return this.financialBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Pay-to address',
        valueField: { name: true },
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'addressLine1' }),
            ui.nestedFields.technical({ bind: 'addressLine2' }),
            ui.nestedFields.technical({ bind: 'city' }),
            ui.nestedFields.technical({ bind: 'region' }),
            ui.nestedFields.technical({ bind: 'postcode' }),
            ui.nestedFields.technical<PurchaseInvoice, BusinessEntityAddress, Country>({
                bind: 'country',
                node: '@sage/xtrem-structure/Country',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
            }),
            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
            ui.nestedFields.technical<PurchaseInvoice, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        orderBy: { isActive: +1 },
        filter() {
            return {
                isActive: true,
                ...(this.payToSupplier.value?.businessEntity?.id
                    ? { businessEntity: { id: this.payToSupplier.value.businessEntity.id } }
                    : {}),
            };
        },
        async onChange() {
            await this.$.fetchDefaults(['payToAddress']);
        },
    })
    payToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.vitalPodField<PurchaseInvoice, Address>({
        parent() {
            return this.financialSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Pay-to address',
        width: 'small',
        onAddButtonClick() {
            if (this.payToLinkedAddress.value) {
                const { ...values } = { ...this.payToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Pay-to address',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return this.payToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.payToAddress.value);
                },
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.payToAddress.value);
                },
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
        ],
    })
    payToAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.tableField<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine>({
        title: 'Purchase receipt to purchase invoice line', // Receipt lines
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        isHidden: true,
        node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
        orderBy: { _sortValue: +1 },
        onChange() {
            this.prevPurchaseReceiptLines.forEach(line => {
                const currentLine = this.purchaseReceiptLines.getRecordValue(line._id);
                if (currentLine && currentLine.invoicedQuantity !== line.invoicedQuantity) {
                    const actualOnInvoiceQuantity = +line.invoicedQuantity;
                    const newOnInvoiceQuantity = +(currentLine.invoicedQuantity ?? 0);
                    const relatedInvoiceLine = this.lines.getRecordValue(currentLine.purchaseInvoiceLine?._id ?? '');
                    if (relatedInvoiceLine && relatedInvoiceLine.purchaseReceiptLine) {
                        const isRelatedReceiptLine =
                            relatedInvoiceLine.purchaseReceiptLine === currentLine.purchaseReceiptLine?._id;
                        if (isRelatedReceiptLine) {
                            // If the current onInvoiceQuantity is greater than the remainingQuantity from the receipt line,
                            // We force the onInvoiceQuantity to the remainingQuantity
                            // and the remaining quantity to be invoiced is set to 0 (done in the map event of that property)
                            const newAvailableQtyToInvoice = +this._onChangeInvoicedQuantityPurchaseReceiptLine(
                                currentLine,
                                +this.invoicedQuantityPurchaseReceiptLine[+currentLine._id],
                            );
                            // Warning message displayed by the event on invoicedQuantity property
                            if (+newAvailableQtyToInvoice < 0) {
                                currentLine.invoicedQuantity = String(
                                    +(currentLine.invoicedQuantity ?? 0) + +newAvailableQtyToInvoice,
                                );
                            }
                            // User enters a qty > remaining qty to invoice. This generates an invoice line qty for the new qty,
                            // but the receipt related invoiced qty is forced to remaining qty to invoice.
                            // Now, if the user changes the req qty to < remaining qty to invoice, we have to reset the invoice line qty
                            relatedInvoiceLine.quantity =
                                +(relatedInvoiceLine.quantity ?? 0) >
                                +(relatedInvoiceLine.purchaseReceiptLine.invoicedQuantity ?? 0)
                                    ? relatedInvoiceLine.purchaseReceiptLine.invoicedQuantity
                                    : relatedInvoiceLine.quantity;

                            relatedInvoiceLine.purchaseReceiptLine.invoicedQuantity = currentLine.invoicedQuantity;
                        }
                        relatedInvoiceLine.quantity = String(
                            Number.parseInt(relatedInvoiceLine.quantity ?? '', 10) -
                                (actualOnInvoiceQuantity - newOnInvoiceQuantity),
                        );
                        this.lines.setRecordValue(relatedInvoiceLine);
                    }
                    // updating the technical property that allows to detect changes
                    this.prevPurchaseReceiptLines.splice(
                        this.prevPurchaseReceiptLines.findIndex(elt => elt._id === line._id),
                        1,
                    );
                    this.prevPurchaseReceiptLines.push(currentLine);
                    // The pod 'invoicedQuantity' is not refreshed
                    this.purchaseReceiptLines.setRecordValue(currentLine);
                }
            });
            // TODO: CPO remove this; not needed
            // this._stockSiteId = await this._setStockSiteId();
            // this._availableReceiptLines = await this._getAvailableReceiptLines();
            // await this._enableSelectFromReceiptAction();
        },
        columns: [
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine, PurchaseInvoiceLine>({
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.link({
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                isTransient: true,
                title: 'Receipt number',
                isFullWidth: true,
                // isHidden: true, // TODO: see why the link is not clickable when readOnly ???
                map: (_value, rowData) => String(rowData.purchaseReceiptLine.document.number),
                page: '@sage/xtrem-purchasing/PurchaseReceipt',
                queryParameters: (_value, rowData) => ({ _id: rowData?.purchaseReceiptLine.document._id ?? '' }),
            }),
            ui.nestedFields.label({
                title: 'Receipt status',
                bind: { purchaseReceiptLine: { status: true } },
                isTitleHidden: true,
                isTransient: true,
                // TODO: https://jira.sage.com/browse/XT-719 (solution 1) enhancement request for map event
                map(value, rowData) {
                    switch (rowData.purchaseReceiptLine.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseReceiptLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseReceiptLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                isFullWidth: true,
                bind: 'invoicedQuantity',
                isDisabled: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                onClick(rowId: any, rowData: any) {
                    this.invoicedQuantityPurchaseReceiptLine[rowId] =
                        this.invoicedQuantityPurchaseReceiptLine[rowId] ?? rowData.purchaseReceiptLine.invoicedQuantity;
                },
                onChange(rowId, rowData) {
                    this._onChangeInvoicedQuantityPurchaseReceiptLine(
                        rowData,
                        this.invoicedQuantityPurchaseReceiptLine[rowId],
                    );
                },
            }),
            ui.nestedFields.label({
                title: 'Remaining quantity to invoice',
                isFullWidth: true,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                isTransient: true,
                bind: '_id',
                map(rowId, rowData) {
                    let availableQuantity = '0';

                    if (rowData.purchaseReceiptLine.status !== 'closed') {
                        const invoicedQuantity = +rowData.purchaseReceiptLine.invoicedQuantity;
                        const additionalInvoicedQuantity =
                            this.invoicedQuantityPurchaseReceiptLine.length >= +rowId &&
                            this.invoicedQuantityPurchaseReceiptLine[rowId]
                                ? +rowData.invoicedQuantity - +this.invoicedQuantityPurchaseReceiptLine[rowId]
                                : 0;

                        const totalInvoicedQuantity = invoicedQuantity + additionalInvoicedQuantity;
                        const remainingQuantity = +rowData.purchaseReceiptLine.quantity - totalInvoicedQuantity;

                        availableQuantity = Math.max(remainingQuantity, 0).toString();
                    }
                    return availableQuantity;
                },
            }),
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine, PurchaseReceiptLine>({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                isTransient: true,
                nestedFields: [
                    ui.nestedFields.technical({ bind: { document: { date: true } } }),
                    ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'invoicedQuantity' }),
                    ui.nestedFields.technical({ bind: 'remainingQuantityToInvoice' }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                    ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical<
                        PurchaseInvoice,
                        PurchaseReceiptLine,
                        PurchaseOrderLineToPurchaseReceiptLine
                    >({
                        bind: 'purchaseOrderLine',
                        node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: { purchaseOrderLine: { document: { number: true } } } }),
                            ui.nestedFields.technical<
                                PurchaseInvoice,
                                PurchaseOrderLineToPurchaseReceiptLine,
                                PurchaseOrderLine
                            >({
                                bind: 'purchaseOrderLine',
                                node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                                nestedFields: [ui.nestedFields.technical({ bind: { document: { number: true } } })],
                            }),
                        ],
                    }),
                    ui.nestedFields.image({ bind: { item: { image: true } }, isHidden: true }),
                ],
            }),
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    purchaseReceiptLines: ui.fields.Table<PurchaseReceiptLineToPurchaseInvoiceLine>;

    @ui.decorators.podCollectionField<PurchaseInvoice, PurchaseOrderLineToPurchaseInvoiceLine>({
        title: 'Purchase order line to purchase invoice line',
        isTitleHidden: true,
        isHidden: true,
        isTransient: true,
        bind: 'purchaseOrderLine',
        node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                bind: '_id',
                isTransient: true,
                title: 'Order',
                isFullWidth: true,
                map: (_value, rowData) => String(rowData.purchaseOrderLine.document.number),
                page: '@sage/xtrem-purchasing/PurchaseOrder',
                queryParameters: (_value, rowData) => ({ _id: rowData?.purchaseOrderLine.document._id ?? '' }),
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: { purchaseOrderLine: { status: true } },
                isTitleHidden: true,
                isTransient: true,
                map(value, rowData) {
                    switch (rowData.purchaseOrderLine.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseOrderLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseOrderLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Order quantity',
                isFullWidth: true,
                isDisabled: true,
                bind: 'invoicedQuantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.technical<PurchaseInvoice, PurchaseOrderLineToPurchaseInvoiceLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    purchaseOrderLine: ui.fields.PodCollection<PurchaseOrderLineToPurchaseInvoiceLine>;

    prevPurchaseReceiptLines: any[];

    /**
     *  invoicedQuantity of the PurchaseReceiptLine - Old Values
     */
    invoicedQuantityPurchaseReceiptLine: number[] = [];

    /**
     * PodCollection
     * purchaseReceiptLine For creation
     */
    @ui.decorators.podCollectionField<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine>({
        title: 'Purchase receipt line to purchase invoice line',
        isTitleHidden: true,
        isTransient: true,
        node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine',
        orderBy: { _sortValue: +1 },
        onChange() {
            this.prevPurchaseReceiptLineToPurchaseInvoiceLines.forEach(line => {
                const currentLine = this.purchaseReceiptLineToPurchaseInvoiceLines.getRecordValue(line._id);
                if (currentLine && currentLine.invoicedQuantity !== line.invoicedQuantity) {
                    const actualOnInvoiceQuantity = +line.invoicedQuantity;
                    const newOnInvoiceQuantity = +(currentLine.invoicedQuantity ?? 0);
                    const relatedInvoiceLine = this.lines.getRecordValue(currentLine.purchaseInvoiceLine?._id ?? '');
                    if (relatedInvoiceLine && relatedInvoiceLine.purchaseReceiptLine) {
                        const isRelatedReceiptLine =
                            relatedInvoiceLine.purchaseReceiptLine === currentLine.purchaseReceiptLine?._id || '';
                        if (isRelatedReceiptLine) {
                            // If the current onInvoiceQuantity is greater than the remainingQuantity from the receipt line,
                            // We force the onInvoiceQuantity to the remainingQuantity
                            // and the remaining quantity to be invoiced is set to 0 (done in the map event of that property)
                            const newAvailableQtyToInvoice = +this._onChangeInvoicedQuantityPurchaseReceiptLine(
                                currentLine,
                                0,
                            );
                            // Warning message displayed by the event on invoicedQuantity property
                            if (+newAvailableQtyToInvoice < 0) {
                                currentLine.invoicedQuantity =
                                    currentLine.purchaseReceiptLine?.remainingQuantityToInvoice;
                            }
                            // User enters a qty > remaining qty to invoice. This generates an invoice line qty for the new qty,
                            // but the receipt related invoiced qty is forced to remaining qty to invoice.
                            // Now, if the user changes the req qty to < remaining qty to invoice, we have to reset the invoice line qty
                            relatedInvoiceLine.quantity =
                                +(relatedInvoiceLine.quantity ?? 0) >
                                +(relatedInvoiceLine.purchaseReceiptLine.invoicedQuantity ?? 0)
                                    ? relatedInvoiceLine.purchaseReceiptLine.invoicedQuantity
                                    : relatedInvoiceLine.quantity;
                            relatedInvoiceLine.purchaseReceiptLine.invoicedQuantity = currentLine.invoicedQuantity;
                        }
                        relatedInvoiceLine.quantity = String(
                            Number.parseInt(relatedInvoiceLine.quantity ?? '', 10) -
                                (actualOnInvoiceQuantity - newOnInvoiceQuantity),
                        );
                        this.lines.setRecordValue(relatedInvoiceLine);
                    }
                    // updating the technical property that allows to detect changes
                    this.prevPurchaseReceiptLineToPurchaseInvoiceLines.splice(
                        this.prevPurchaseReceiptLineToPurchaseInvoiceLines.findIndex(elt => elt._id === line._id),
                        1,
                    );
                    this.prevPurchaseReceiptLineToPurchaseInvoiceLines.push(currentLine);
                    // updating the technical property that contains all invoicing in progress lines
                    // (used for filtering the ones related to the current invoice line when displaying)
                    this.allInProgressReceiptLineToInvoiceLines.splice(
                        this.allInProgressReceiptLineToInvoiceLines.findIndex(elt => elt._id === line._id),
                        1,
                    );
                    this.allInProgressReceiptLineToInvoiceLines.push(currentLine);
                    // The pod 'invoicedQuantity' is not refreshed
                    this.purchaseReceiptLineToPurchaseInvoiceLines.setRecordValue(currentLine);
                }
            });
            // TODO: CPO to be removed; not needed anymore
            // this._stockSiteId = await this._setStockSiteId();
            // this._availableReceiptLines = await this._getAvailableReceiptLines();
            // await this._enableSelectFromReceiptAction();
        },
        canAddRecord: false,
        canRemoveRecord: false,
        columns: [
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine, PurchaseInvoiceLine>({
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.link({
                title: 'Receipt',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                isTransient: true,
                isFullWidth: true,
                // isHidden: true, // TODO: see why the link is not clickable when readOnly ???
                map: (_fieldValue, rowData) => rowData.purchaseReceiptLine.document.number,
                page: '@sage/xtrem-purchasing/PurchaseReceipt',
                queryParameters: (_value, rowData) => ({ _id: rowData?.purchaseReceiptLine.document._id ?? '' }),
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: '_id',
                isTitleHidden: true,
                isTransient: true,
                // TODO: https://jira.sage.com/browse/XT-719 (solution 1) enhancement request for map event
                map(value, rowData) {
                    switch (rowData.purchaseReceiptLine.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseReceiptLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseReceiptLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Receipt quantity',
                isFullWidth: true,
                bind: 'invoicedQuantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                onChange(_rowId, rowData) {
                    this._onChangeInvoicedQuantityPurchaseReceiptLine(rowData, 0);
                },
            }),
            ui.nestedFields.label({
                title: 'Available quantity to invoice',
                isFullWidth: true,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                bind: '_id',
                map: (_rowId, rowData) =>
                    rowData.purchaseReceiptLine.status !== 'closed'
                        ? Math.max(
                              +rowData.purchaseReceiptLine.quantity -
                                  (+rowData.purchaseReceiptLine.invoicedQuantity + +rowData.invoicedQuantity),
                              0,
                          ).toString()
                        : '0',
            }),
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine, PurchaseReceiptLine>({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                isTransient: true,
                nestedFields: [
                    ui.nestedFields.technical({ bind: { document: { date: true } } }),
                    ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical({ bind: 'invoicedQuantity' }),
                    ui.nestedFields.technical({ bind: 'quantity' }),
                    ui.nestedFields.technical({ bind: 'remainingQuantityToInvoice' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                    ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.image({ bind: { item: { image: true } }, isHidden: true }),
                    ui.nestedFields.technical<
                        PurchaseInvoice,
                        PurchaseReceiptLine,
                        PurchaseOrderLineToPurchaseReceiptLine
                    >({
                        bind: 'purchaseOrderLine',
                        node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: { purchaseOrderLine: { document: { number: true } } } }),
                            ui.nestedFields.technical<
                                PurchaseInvoice,
                                PurchaseOrderLineToPurchaseReceiptLine,
                                PurchaseOrderLine
                            >({
                                bind: 'purchaseOrderLine',
                                node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                                nestedFields: [ui.nestedFields.technical({ bind: { document: { number: true } } })],
                            }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<PurchaseInvoice, PurchaseReceiptLineToPurchaseInvoiceLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            // TODO: having 2 bind-ed properties for the same 'invoicedQuantityInStockUnit' (this one and the progress bar) generates an error (see XT-4956).
            // ui.nestedFields.numeric({
            //     title: 'Invoiced quantity in stock unit',
            //     bind: 'invoicedQuantityInStockUnit',
            //     isReadOnly: true,
            //     isHidden: true,
            //     scale(rowId: any, rowData: any) {
            //         return rowData?.stockUnit?.decimalDigits ?? 2;
            //     },
            //     postfix(rowId: any, rowData: any) {
            //         return rowData?.stockUnit?.id ?? '';
            //     },
            // }),
            ui.nestedFields.technical({ bind: 'invoicedQuantityInStockUnit', isTransient: true }),
            // ui.nestedFields.link({
            //     bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
            //     isTransient: true,
            //     title: 'Purchase order',
            //     isTitleHidden: false,
            //     isFullWidth: true,
            //     isHidden(value, rowData) {
            //         return !rowData.purchaseReceiptLine?.purchaseOrderLine?.purchaseOrderLine?.document?._id;
            //     },
            //     map(value: any, rowData: any) {
            //         return String(
            //             rowData.purchaseReceiptLine?.purchaseOrderLine?.purchaseOrderLine?.document?.number,
            //         );
            //     },
            //     onClick(_id, rowData: any) {
            //         this.$.router.goTo('@sage/xtrem-purchasing/PurchaseOrder', {
            //             _id: rowData.purchaseReceiptLine?.purchaseOrderLine?.purchaseOrderLine?.document?._id,
            //         });
            //     },
            // }),
        ],
    })
    purchaseReceiptLineToPurchaseInvoiceLines: ui.fields.PodCollection<PurchaseReceiptLineToPurchaseInvoiceLine>;

    /**
     * Technical property that allows to handle user changes for invoicing in progress
     */
    prevPurchaseReceiptLineToPurchaseInvoiceLines: any[];

    /**
     * Technical property that contains all invoicing in progress lines
     * (used for filtering the ones related to the current invoice line when displaying)
     */
    allInProgressReceiptLineToInvoiceLines: any[];

    @ui.decorators.section<PurchaseInvoice>({ title: 'Totals' })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.totalsSection;
        },
        title: 'Calculated amounts',
        width: 'large',
        isTitleHidden: true,
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Adjusted tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        isHidden: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountIncludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.tableField<PurchaseInvoice, DocumentTaxBinding>({
        title: 'Summary by tax',
        width: 'large',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-tax/DocumentTax',
        orderBy: { _sortValue: +1, tax: +1 },
        parent() {
            return this.totalsSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({ title: 'Category', bind: 'taxCategory', isReadOnly: true }),
            ui.nestedFields.text({ title: 'Tax', bind: 'tax', isReadOnly: true }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxableAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({ title: 'Rate', postfix: '%', scale: 2, bind: 'taxRate', isReadOnly: true }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Adjusted amount',
                size: 'small',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmountAdjusted',
                onChange() {
                    this.totalTaxAmountAdjusted.value = this.taxes.value.reduce(
                        (amount: number, line) =>
                            Number(amount + Number(!line.isReverseCharge ? (line.taxAmountAdjusted ?? 0) : 0)),
                        0,
                    );
                    this._computeTotalAmounts();
                    TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxes, this.taxEngine.value ?? '');
                    this.wereTaxesEdited = true;
                },
                isReadOnly() {
                    return (
                        ['posted', 'inProgress', 'error'].includes(this.status.value ?? '') &&
                        !this.canEditTaxDataAfterPost
                    );
                },
            }),
            ui.nestedFields.checkbox({
                title: 'Reverse charge',
                bind: 'isReverseCharge',
                isReadOnly: true,
                size: 'small',
            }),
            ui.nestedFields.technical({ bind: { taxCategoryReference: { _id: true } } }),
            ui.nestedFields.technical({ bind: { taxReference: { _id: true } } }),
        ],
    })
    taxes: ui.fields.Table<DocumentTax>;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.totalsSection;
        },
        title: 'Amount in company currency',
        width: 'large',
    })
    totalsSectionCompanyCurrencyDetailsBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.totalsSection;
        },
        title: 'Variances',
        isHidden: true,
    })
    varianceBlock: ui.containers.Block;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<PurchaseInvoice>({
        title: 'Posting',
        isHidden() {
            return !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseInvoice, FinanceTransactionBinding>({
        title: 'Results',
        isTitleHidden: true,
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page(_value, rowData) {
                    switch (rowData?.targetDocumentType) {
                        case 'journalEntry':
                            return '@sage/xtrem-finance/JournalEntry';
                        case 'accountsPayableInvoice':
                            return '@sage/xtrem-finance/AccountsPayableInvoice';
                        default:
                            return '@sage/xtrem-finance/AccountsPayableInvoice';
                    }
                },
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.targetDocumentSysId ?? '',
                        number: rowData?.targetDocumentNumber ?? '',
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                page: '',
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isHidden: false,
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationApp' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationAppUrl' }),
            ui.nestedFields.technical({ bind: 'documentSysId' }),
        ],
        onRowClick(_id, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<PurchaseInvoice>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<PurchaseInvoice>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'purchaseInvoice',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        title: 'Create credit memo',
        isHidden: true,
        async onClick() {
            await actionFunctions.CreateCreditMemo({
                purchaseInvoicePage: this,
                recordId: this.$.recordId ?? null,
                isCalledFromRecordPage: true,
                currency: this.currency.value ? JSON.stringify(this.currency.value) : '',
            });
        },
    })
    createPurchaseCreditNote: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.displayStatus.value !== 'posted';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines, isSourceDocumentLine: true },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoice>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.displayStatus.value !== 'stockError';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    @ui.decorators.section<PurchaseInvoice>({ title: 'Payments' })
    paymentsSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoice>({
        parent() {
            return this.paymentsSection;
        },
        width: 'extra-large',
        title: 'Payments',
        isTitleHidden: true,
    })
    paymentsBlock: ui.containers.Block;

    @ui.decorators.referenceField<PurchaseInvoice, PaymentTerm>({
        parent() {
            return this.paymentsBlock;
        },
        lookupDialogTitle: 'Select payment term',
        minLookupCharacters: 0,
        filter: { businessEntityType: { _in: ['supplier', 'all'] } },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'dueDateType' }),
            ui.nestedFields.technical({ bind: 'days' }),
        ],
        isReadOnly() {
            return this._isLineDisabled() && !this.canEditPaymentDataAfterPost;
        },
        onChange() {
            dueDateDefault(this);
        },
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.dateField<PurchaseInvoice>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Due date',
        isReadOnly() {
            return this._isLineDisabled();
        },
    })
    dueDate: ui.fields.Date;

    @ui.decorators.tableField<PurchaseInvoice, BaseOpenItem>({
        bind: { apOpenItems: true },
        isHidden: true,
        node: '@sage/xtrem-finance-data/BaseOpenItem',
        columns: [
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.technical({ bind: { documentNumber: true } }),
        ],
    })
    apOpenItems: ui.fields.Table<BaseOpenItem>;

    @ui.decorators.checkboxField<PurchaseInvoice>({ isHidden: true })
    isOpenItemPageOptionActive: ui.fields.Checkbox;

    @ui.decorators.linkField<PurchaseInvoice>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Open item',
        width: 'small',
        isTransient: true,
        async onClick() {
            await this.$.dialog.page(
                `@sage/xtrem-finance/AccountsPayableOpenItem`,
                { _id: this.apOpenItems.value?.at(0)?._id ?? '', fromPurchasing: true },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
        map() {
            return this.apOpenItems.value?.at(0)?.documentNumber ?? '';
        },
        isHidden() {
            return this.status.value !== 'posted' || !this.isOpenItemPageOptionActive.value;
        },
        isDisabled() {
            return this.$.queryParameters.fromFinance?.toString() === 'true';
        },
    })
    documentNumberLink: ui.fields.Link;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Total amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        bind: { paymentTracking: { amountPaid: true } },
        width: 'small',
        isHidden() {
            return this.status.value !== 'posted' || !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    totalAmountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseInvoice>({
        title: 'Forced amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        isReadOnly: true,
        width: 'small',
        bind: { paymentTracking: { forcedAmountPaid: true } },
        isHidden() {
            return (
                this.status.value !== 'posted' ||
                !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
                !this.forcedAmountPaid.value ||
                this.forcedAmountPaid.value === 0
            );
        },
    })
    forcedAmountPaid: ui.fields.Numeric;

    @ui.decorators.fragmentFields<PurchaseInvoice>({
        isTitleHidden: true,
        parent() {
            return this.paymentsBlock;
        },
        fragment: '@sage/xtrem-distribution/PaymentLine',
    })
    payments: ui.containers.FragmentFields;

    @ui.decorators.section<PurchaseInvoice>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoice>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
        isDisabled() {
            return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<PurchaseInvoice>({
        parent() {
            return this.noteBlock;
        },
        width: 'large',
        isFullWidth: true,
        helperText: 'Notes display on internal documents.',
        title: 'Internal notes',
        capabilities: validCapabilities,
        isDisabled() {
            return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseInvoice>({
        parent() {
            return this.noteBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseInvoice>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        width: 'large',
        isDisabled() {
            return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
        },
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseInvoice>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        width: 'large',
        isDisabled() {
            return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
        },
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.richTextField<PurchaseInvoice>({
        width: 'large',
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
        },
    })
    internalNoteLine: ui.fields.RichText;

    async initPage() {
        const oldIsDirty = this.$.isDirty;
        this.financeIntegrationCheckResult = null;

        this.billByAddress.isReadOnly = true;
        if (this.currency.value) {
            this.applyCurrency();
        }
        if (this.purchaseReceiptLineToPurchaseInvoiceLines?.value.length) {
            this.purchaseReceiptLineToPurchaseInvoiceLines?.value.forEach(elt =>
                this.purchaseReceiptLineToPurchaseInvoiceLines.removeRecord(elt._id),
            );
        }
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
        this.purchaseReceiptLineToPurchaseInvoiceLines.isHidden = true;

        if (this.$.recordId) {
            await this._loadRelatedPurchaseReceiptLines('');
            if (this.status.value === 'closed') {
                this.billByAddress.isDisabled = true;
            } else {
                this.billByAddress.isDisabled = false;
            }
            this.documentNumberLink.value = this.apOpenItems.value?.at(0)?.documentNumber ?? '';
        } else {
            this.invoiceDate.value = DateValue.today().toString();
        }
        if (this.site.value && this.billBySupplier.value) {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                site: this.site.value,
                supplier: this.billBySupplier.value,
            });
        }
        this.allInProgressReceiptLineToInvoiceLines = [];
        this.varianceBlock.isHidden = true;
        this.showHideColumns();
    }

    initPosting() {
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message ?? '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
        this.isRepost = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        this.canEditPaymentDataAfterPost =
            this.isRepost && (this.paymentTracking.value?.status ?? 'notPaid') === 'notPaid';
        this.canEditTaxDataAfterPost = this.canEditPaymentDataAfterPost && this.taxEngine.value !== 'avalaraAvaTax';
    }

    /**
     * @param rowData : Price can be 0. return - 1 when record not found.
     */
    private async _setPriceOrigin(rowData: ui.PartialCollectionValue<PurchaseInvoiceLineBinding>) {
        if (rowData?.item && Number(rowData?.quantity) >= 0 && rowData?.unit) {
            const purchasePrice = await getPurchasePrice(
                this.$.graph,
                this.site.value?._id ?? '',
                this.billBySupplier.value?._id ?? '',
                rowData?.currency?._id ?? '',
                rowData.item._id ?? '',
                rowData.unit._id ?? '',
                parseFloat(rowData.quantity ?? ''),
                new Date(this.invoiceDate.value ?? ''),
                false,
            );
            rowData.grossPrice = String(purchasePrice.price);
            rowData.priceOrigin = purchasePrice.priceOrigin ?? 'manual';
            await this._calculatePrices(rowData);
            this.lines.addOrUpdateRecordValue(rowData);
        }
    }

    /**
     *  Receipt lines functions
     *
     */
    private async _loadRelatedPurchaseReceiptLines(invoiceLineId: string) {
        const oldIsDirty = this.$.isDirty;
        const filter = { _and: [{}] };
        filter._and.push({ purchaseInvoiceLine: { document: { _id: { _eq: this.$.recordId } } } });
        if (invoiceLineId) {
            filter._and.push({ purchaseInvoiceLine: { _id: invoiceLineId } });
        }
        const resultReceiptLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseInvoiceLine: { _id: true, document: { _id: true } },
                            purchaseReceiptLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true, date: true },
                                status: true,
                                item: { _id: true, name: true },
                                quantity: true,
                                grossPrice: true,
                                unit: { id: true, symbol: true, decimalDigits: true },
                                currency: { id: true, decimalDigits: true, symbol: true },
                                invoicedQuantity: true,
                                invoicedQuantityInStockUnit: true,
                                remainingQuantityToInvoice: true,
                                remainingQuantityInStockUnit: true,
                                purchaseOrderLine: {
                                    purchaseOrderLine: { _id: true, document: { _id: true, number: true } },
                                },
                            },
                            invoicedQuantity: true,
                            invoicedQuantityInStockUnit: true,
                            unit: { _id: true, id: true, symbol: true, decimalDigits: true },
                            stockUnit: { _id: true, id: true, symbol: true, decimalDigits: true },
                        },
                        { filter },
                    ),
                )
                .execute(),
        );
        this.purchaseReceiptLines.value.forEach(line => this.purchaseReceiptLines.removeRecord(line._id));
        resultReceiptLines.forEach(row => {
            // TODO: update the row invoicedQuantity with the one from the related invoice line one (due to possible update in the meanwhile on the page)
            const invoiceLine = this.lines.getRecordValue(row.purchaseInvoiceLine._id);
            if (invoiceLine?.purchaseReceiptLine) {
                const isRelatedReqLine =
                    invoiceLine.purchaseReceiptLine.purchaseInvoiceLine === +row.purchaseInvoiceLine._id &&
                    +(invoiceLine.purchaseReceiptLine.purchaseInvoiceLine.purchaseReceiptLine ?? 0) ===
                        +row.purchaseReceiptLine._id;
                if (isRelatedReqLine) {
                    row.invoicedQuantity = invoiceLine.purchaseReceiptLine.invoicedQuantity ?? '';
                }
            }
            this.purchaseReceiptLines.addOrUpdateRecordValue(row);
        });
        this.purchaseReceiptLines.isHidden = this.purchaseReceiptLines.value.length === 0;

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    private _filterInvoicingInProgressReceiptLines(invoiceLineId: string) {
        this.allInProgressReceiptLineToInvoiceLines.forEach(line => {
            if (line.purchaseInvoiceLine._id !== invoiceLineId) {
                if (
                    this.purchaseReceiptLineToPurchaseInvoiceLines.value.find(
                        progressLine => progressLine._id === line._id,
                    )
                ) {
                    this.purchaseReceiptLineToPurchaseInvoiceLines.removeRecord(line._id);
                }
            } else {
                this.purchaseReceiptLineToPurchaseInvoiceLines.addOrUpdateRecordValue(line);
            }
        });
    }

    // TODO: Keep it; to uncomment when needed
    // private _clearFilterInvoicingInProgressReceiptLines() {
    //     this.allInProgressReceiptLineToInvoiceLines.forEach(line =>
    //         this.purchaseReceiptLineToPurchaseInvoiceLines.addOrUpdateRecordValue(line),
    //     );
    // }

    // private _deleteFromInvoicingInProgressReceiptLines(invoiceLineId: string) {
    //     this.allInProgressReceiptLineToInvoiceLines = this.allInProgressReceiptLineToInvoiceLines.filter(
    //         line => line.purchaseInvoiceLine._id !== invoiceLineId,
    //     );
    //     this.purchaseReceiptLineToPurchaseInvoiceLines.value
    //         .filter(progressLine => progressLine.purchaseInvoiceLine._id === invoiceLineId)
    //         .forEach(progressLine => this.purchaseReceiptLineToPurchaseInvoiceLines.removeRecord(progressLine._id));
    // }

    // private _clearInvoicingInProgressReceiptLines() {
    //     this.allInProgressReceiptLineToInvoiceLines = [];
    //     this.purchaseReceiptLineToPurchaseInvoiceLines.value.forEach(progressLine =>
    //         this.purchaseReceiptLineToPurchaseInvoiceLines.removeRecord(progressLine._id),
    //     );
    //     this._showHideReceiptsSection();
    // }

    private _showHideReceiptsSection() {
        this.purchaseReceiptLines.isHidden = !this.purchaseReceiptLines.value.length;
        this.purchaseOrderLine.isHidden = !this.purchaseOrderLine.value.length;
        this.purchaseReceiptLineToPurchaseInvoiceLines.isHidden =
            !this.purchaseReceiptLineToPurchaseInvoiceLines.value.length;
    }

    /**
     * @param previousQty : firstValue present in invoicedQuantity ( for modification of InvoicedQuantity in PurchaseReceiptLine )
     * must be 0 when creating
     */
    private _onChangeInvoicedQuantityPurchaseReceiptLine(rowData: any, previousQty: number) {
        const availableQuantityToInvoice =
            +rowData.purchaseReceiptLine.quantity -
            (+rowData.purchaseReceiptLine.invoicedQuantity - previousQty + +rowData.invoicedQuantity);

        if (availableQuantityToInvoice < 0) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisitionLine_invoicedQuantity_to_much',
                    'You are invoicing ({{alreadyInvoiced}}{{unit}})  when the requisition was generated for {{remainingQuantityd}}{{unit}}.',
                    {
                        remainingQuantity: rowData.purchaseReceiptLine.quantity,
                        alreadyInvoiced:
                            +rowData.purchaseReceiptLine.invoicedQuantity + +rowData.invoicedQuantity - previousQty,
                        unit: rowData?.unit?.id,
                    },
                ),
                { type: 'warning' },
            );
        }
        return availableQuantityToInvoice;
    }

    async approveVarianceLine(approve: boolean, purchaseInvoiceId: string, purchaseInvoiceLineId: string) {
        const isAcceptedLine = await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseInvoiceLine')
            .mutations.acceptAllVariancesLine(true, {
                purchaseInvoiceId,
                purchaseInvoiceLineId,
                approve,
            })
            .execute();

        if (isAcceptedLine) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__line_matching_status_updated',
                    'Matching status updated.',
                ),
                { type: 'success' },
            );
            this.$.setPageClean();
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseInvoice`, { _id: this.$.recordId ?? '' });
        }
    }

    private async _calculatePrices(
        rowData: ui.PartialCollectionValue<PurchaseInvoiceLine>,
        taxDate = this.supplierDocumentDate.value,
    ) {
        if (rowData.recipientSite?._id) {
            const prices = await calculateLinePrices({
                grossPrice: Number(rowData.grossPrice),
                charge: Number(rowData.charge),
                discount: Number(rowData.discount),
                netPriceScale: getCompanyPriceScale(this.site.value?.legalCompany),
                quantity: Number(rowData.quantity),
                amountExcludingTax: Number(rowData.amountExcludingTax),
                amountIncludingTax: Number(rowData.amountIncludingTax),
                taxAmount: Number(rowData.taxAmount),
                taxAmountAdjusted: Number(rowData.taxAmountAdjusted),
                rateMultiplication: this.companyFxRate.value ?? 0,
                rateDivision: this.companyFxRateDivisor.value ?? 1,
                fromDecimals: this.currency.value?.decimalDigits ?? 2,
                toDecimals: this.siteCurrency.decimalDigits,
                taxes: {
                    site: rowData.recipientSite?._id,
                    businessPartner: this.billBySupplier.value?._id,
                    item: rowData.item?._id,
                    currency: this.currency.value?._id,
                    lineNodeName: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                    taxEngine: this.taxEngine.value ?? '',
                    uiTaxes: rowData.uiTaxes ?? '',
                    graphObject: this.$.graph,
                    taxDate: taxDate ?? '',
                },
            });
            rowData.netPrice = String(prices.netPrice);
            rowData.taxAmount = String(prices.taxAmount);
            rowData.taxAmountAdjusted = String(prices.taxAmountAdjusted);
            rowData.uiTaxes = prices.uiTaxes;
            rowData.taxDate = this.invoiceDate.value ?? undefined;
            rowData.amountExcludingTax = String(prices.amountExcludingTax);
            rowData.amountIncludingTax = String(prices.amountIncludingTax);
            rowData.amountExcludingTaxInCompanyCurrency = String(prices.amountExcludingTaxInCompanyCurrency);
            rowData.amountIncludingTaxInCompanyCurrency = String(prices.amountIncludingTaxInCompanyCurrency);
            await TotalTaxCalculator.getInstance().updateTaxDetails(
                this.taxes,
                this.totalTaxAmountAdjusted,
                this.calculatedTotalTaxAmount,
            );
        }
    }

    private async callDisplayTaxes(rowItem: ui.PartialCollectionValue<PurchaseInvoiceLine>, updateTaxDetails = true) {
        const result = await displayTaxes(
            this,
            rowItem,
            {
                currency: this.currency.value ?? {},
                taxAmount: +(rowItem.taxAmount ?? 0),
                amountExcludingTax: +(rowItem.amountExcludingTax ?? 0),
                amountIncludingTax: +(rowItem.amountIncludingTax ?? 0),
                documentType: DocumentTypeEnum.purchaseInvoiceLine,
                taxDate: rowItem.taxDate,
                legislation: this.site.value?.legalCompany?.legislation,
                editable: !isPostedInProgressError(this.status.value) || this.canEditTaxDataAfterPost,
                taxAmountAdjusted: +(rowItem.taxAmountAdjusted ?? 0),
                destinationCountry: this.billByLinkedAddress.value?.country ?? {},
                originCountry: this.site.value?.primaryAddress?.country ?? {},
                validTypes: ['purchasing', 'purchasingAndSales'],
            },
            updateTaxDetails,
        );

        if (result) {
            this.wereTaxesEdited = true;
            rowItem.amountIncludingTaxInCompanyCurrency = String(
                convertAmount(
                    Number(this.lines.getRecordValue(rowItem._id ?? '')?.amountIncludingTax),
                    this.companyFxRate.value ?? 0,
                    this.companyFxRateDivisor.value ?? 1,
                    this.currency.value?.decimalDigits ?? 2,
                    this.siteCurrency.decimalDigits,
                ),
            );
            this.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                amountIncludingTaxInCompanyCurrency: rowItem.amountIncludingTaxInCompanyCurrency,
            });
            if (updateTaxDetails) {
                this._computeTotalAmounts();
            }
        }
    }

    get siteCurrency() {
        const currency = this.site.value?.legalCompany?.currency;
        return {
            id: currency?.id ?? '',
            _id: currency?._id ?? '',
            decimalDigits: currency?.decimalDigits ?? 2,
            symbol: currency?.symbol ?? '',
        };
    }

    async changedQuantity(rowData: ui.PartialCollectionValue<PurchaseInvoiceLine>) {
        const conversion = await convertFromTo(
            this.$.graph,
            rowData.unit?._id ?? '',
            rowData.item?.stockUnit?._id ?? '',
            +(rowData?.quantity ?? 0),
            rowData.item?._id,
            this.billBySupplier.value?._id,
            '',
            'purchase',
            false,
        );
        rowData.quantityInStockUnit = String(conversion.convertedQuantity);
        rowData.unitToStockUnitConversionFactor = String(conversion.conversionFactor);
        if ((rowData.origin ?? 'direct') === 'direct') {
            await this._setPriceOrigin(rowData);
        } else {
            await this._calculatePrices(rowData);
            this.lines.addOrUpdateRecordValue(rowData);
        }
    }

    async changeQuantityReceipt(rowData: ui.PartialCollectionValue<PurchaseInvoiceLine>) {
        if (rowData.purchaseReceiptLine) {
            const purchaseReceiptLineQuantity = rowData.purchaseReceiptLine?.purchaseReceiptLine?.quantity
                ? +rowData.purchaseReceiptLine.purchaseReceiptLine.quantity
                : 0;
            const quantity = rowData.quantity ? +rowData.quantity : 0;
            if (quantity < purchaseReceiptLineQuantity) {
                if (
                    !(await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__partial_confirm_dialog_title',
                            'Confirm partial invoicing',
                        ),
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__partial_confirm_dialog_content',
                            'The invoice quantity is less than the receipt quantity.',
                        ),
                        ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                    ))
                ) {
                    rowData.quantity = rowData.quantityInStockUnit?.toString();
                } else {
                    await this.changedQuantity(rowData);
                }
            }

            if (quantity > purchaseReceiptLineQuantity) {
                if (
                    !(await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__greater_dialog_title',
                            'Confirm invoicing',
                        ),
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_invoice__greater_dialog_content',
                            'The invoiced quantity is greater than the receipt quantity.',
                        ),
                        ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                    ))
                ) {
                    rowData.quantity = rowData.quantityInStockUnit?.toString();
                } else {
                    await this.changedQuantity(rowData);
                }
            }
        } else {
            await this.changedQuantity(rowData);
        }
        PurchaseInvoice.updateVarianceType(rowData);
        this.lines.addOrUpdateRecordValue(rowData);
    }

    static updateVarianceType(rowData: ui.PartialCollectionValue<PurchaseInvoiceLine>) {
        rowData.varianceType = updateLineVarianceType(rowData);
        rowData.matchingStatus = rowData.varianceType === 'noVariance' ? 'noVariance' : 'variance';
    }

    private async onChangeTaxCalculation(rowData: ui.PartialCollectionValue<PurchaseInvoiceLine>) {
        await this._calculatePrices(rowData);
        this.lines.addOrUpdateRecordValue(rowData);
        this._computeTotalAmounts();
    }

    showHideColumns() {
        if (isExchangeRateHidden(this.currency.value, this.site.value, this.billBySupplier.value)) {
            this.rateDescription.isHidden = true;
            this.lines.hideColumn('amountExcludingTaxInCompanyCurrency');
            this.lines.hideColumn('amountIncludingTaxInCompanyCurrency');
        } else {
            this.lines.showColumn('amountExcludingTaxInCompanyCurrency');
            this.lines.showColumn('amountIncludingTaxInCompanyCurrency');
            this.rateDescription.isHidden = false;
        }
    }

    _setStepSequenceStatusObject(stepSequenceValues: PurchaseInvoiceStepSequenceStatus): Dict<ui.StepSequenceStatus> {
        const payStepSequence = stepSequenceValues.pay ? { [this.orderStepSequencePay]: stepSequenceValues.pay } : {};
        return {
            [this.orderStepSequenceCreate]: stepSequenceValues.create,
            [this.orderStepSequencePost]: stepSequenceValues.post,
            ...(payStepSequence as Dict<ui.StepSequenceStatus>),
        };
    }

    receiptIsTransferHeaderNote() {
        return this.lines.value.some(
            line => line.purchaseReceiptLine?.purchaseReceiptLine?.document?.isTransferHeaderNote === true,
        );
    }

    receiptsIsTransferLineNote() {
        return this.lines.value.some(
            line => line.purchaseReceiptLine?.purchaseReceiptLine?.document?.isTransferLineNote === true,
        );
    }

    linesFromSingleReceipt() {
        const orderLineNumbers = this.lines.value.map(
            line => line.purchaseReceiptLine?.purchaseReceiptLine?.document?.number,
        );
        return orderLineNumbers.every(number => number === orderLineNumbers[0]);
    }

    lineNotesChanged() {
        return this.lines.value.some(line => line.internalNote?.value);
    }

    headerNotesChanged() {
        return this.internalNote.value !== '';
    }

    async openLandedCostPanel(
        rowData: ui.PartialCollectionValue<PurchaseInvoiceLineBinding>,
        lineValidationFinanceErrors?: LineValidationMessage[],
    ) {
        if (!rowData.landedCost?._id) {
            return;
        }

        const allocationModification = await editLandedCostAllocation(
            this,
            {
                isEditable: !isPurchaseInvoiceLineActionDisabled(this.status.value ?? '', '', 'landedCost'),
                company: this.site.value?.legalCompany?._id ?? '',
                site: this.site.value?._id ?? '',
                currency: this.currency.value?._id ?? '',
                companyCurrency: this.companyCurrency.value?._id ?? '',
                currencyConversion: {
                    rate: this.companyFxRate.value ?? 1,
                    rateDivisor: this.companyFxRateDivisor.value ?? 1,
                    currencyDecimals: MasterDataUtils.getScaleValue(2, rowData?.currency?.decimalDigits),
                    companyCurrencyDecimals: MasterDataUtils.getScaleValue(
                        2,
                        this.companyCurrency.value?.decimalDigits,
                    ),
                },
                landedCostDocumentLine: rowData.landedCost._id,
                allocatingDocumentLine: rowData._id ?? '0',
            },
            lineValidationFinanceErrors,
        );
        if (allocationModification?.modified) {
            await this.lines.refreshRecord(rowData._id ?? '0');
            if (
                rowData.landedCostCheckResult !== this.lines.getRecordValue(rowData._id ?? '0')?.landedCostCheckResult
            ) {
                // redraw is needed to display or hide the warning icon column
                await this.lines.redraw();
                // as the warning status has changed, the display of the 'Post' button must be updated
                this.manageDisplayButtonPostAction(this.$.isDirty);
            }
        }
    }

    static getHeaderGridWarningMessage(numberOfWarningInTable: number): string {
        return numberOfWarningInTable
            ? ui.localize(
                  '@sage/xtrem-purchasing/pages__purchase_invoice___header_grid_warning_message',
                  'Refer to the warning on the line for details ({{numberOfWarningInTable}}).',
                  { numberOfWarningInTable },
              )
            : '';
    }

    private _manageHeaderProperties() {
        const isDisabled = ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
        this.supplierDocumentNumber.isDisabled = this.isRepost ? false : isDisabled;
        this.supplierDocumentDate.isDisabled = this.canEditPaymentDataAfterPost ? false : isDisabled;
        this.totalAmountExcludingTax.isDisabled = isDisabled;
        this.totalTaxAmount.isDisabled = this.canEditTaxDataAfterPost ? false : isDisabled;
        this.dueDate.isDisabled = isDisabled;
    }
}
