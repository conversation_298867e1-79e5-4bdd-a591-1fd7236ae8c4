import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { FilteredUsers } from '@sage/xtrem-master-data/lib/client-functions/interfaces/document';
import { loadApprovers } from '@sage/xtrem-master-data/lib/client-functions/user';
import type { GraphApi, PurchaseCreditMemo as PurchaseCreditMemoNode } from '@sage/xtrem-purchasing-api';
import type { User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { isPurchaseCreditMemoLineActionDisabled } from '../shared-functions/edit-rules';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    CreditMemoPageParameter,
    CreditMemoPostingParameter,
    GetPurchaseCreditMemoLinesParameter,
    PostParameter,
    SetDimensionActionParameter,
    UpdateStatusParameter,
} from './interfaces/purchase-credit-memo-actions-functions';

async function getPurchaseCreditMemoLinesForDimensions(parameters: GetPurchaseCreditMemoLinesParameter) {
    return extractEdges(
        await parameters.purchaseCreditMemoPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseCreditMemoLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                        status: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseCreditMemoLines = await getPurchaseCreditMemoLinesForDimensions({
        purchaseCreditMemoPage: parameters.purchaseCreditMemoPage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = purchaseCreditMemoLines.filter(
        line =>
            !isPurchaseCreditMemoLineActionDisabled(parameters.status || '', line.status || '', 'dimensions') ||
            parameters.isRepost,
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            if (!dataDetermined) {
                const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                    page: parameters.purchaseCreditMemoPage,
                    site: parameters.site || null,
                    supplier: parameters.supplier || null,
                });
                const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                    await getDimensionsForPurchaseLines({
                        line: lineToSetDimensions,
                        purchasePage: parameters.purchaseCreditMemoPage,
                        defaultDimensionsAttributes,
                        defaultedFromItem,
                    });
                if (!resultDataDetermined) {
                    break;
                }
                dimensionsToSet = resultDimensionsToSet;
                attributesToSet = resultAttributesToSet;
                dataDetermined = resultDataDetermined;
            }
        }
        if (dataDetermined) {
            await parameters.purchaseCreditMemoPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseCreditMemoLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.purchaseCreditMemoPage.$.showToast(
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo__apply_dimensions_success',
            'Dimensions applied.',
        ),
        { type: 'success' },
    );
}

async function getDisplayStatus(parameters: CreditMemoPageParameter) {
    const purchasingCreditMemo = extractEdges(
        await parameters.purchaseCreditMemoPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseCreditMemo')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as PurchaseCreditMemoNode[];
    return purchasingCreditMemo[0].displayStatus;
}

async function checkForUpdate(parameters: CreditMemoPageParameter) {
    let refreshCounter: number;
    refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            purchaseCreditMemoPage: parameters.purchaseCreditMemoPage,
            recordId: parameters.recordId,
        });
        if (displayStatus === 'posted') {
            await parameters.purchaseCreditMemoPage.$.router.refresh(true);
            await parameters.purchaseCreditMemoPage.$.refreshNavigationPanel();
            parameters.purchaseCreditMemoPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 10) {
            // TODO: XT-79005
            // eslint-disable-next-line
            setTimeout(checkForDisplayStatus, 1000);
        }
    };
    await checkForDisplayStatus();
}

export async function creditMemoPostingAction(parameters: CreditMemoPostingParameter) {
    // Disable post button so the user cannot post twice
    parameters.purchaseCreditMemoPage.post.isDisabled = true;
    parameters.purchaseCreditMemoPage.$.loader.isHidden = false;

    const postResult = await parameters.purchaseCreditMemoPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseCreditMemo')
        .mutations.post(
            {
                wasSuccessful: true,
                message: true,
            },
            { creditMemo: parameters.recordId },
        )
        .execute();

    if (!postResult.wasSuccessful) {
        parameters.purchaseCreditMemoPage.$.showToast(
            `**${ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_credit_memo__post_errors',
                'Posting errors:',
            )}**\n${postResult.message}`,
            { type: 'error', timeout: 20000 },
        );
    } else {
        parameters.purchaseCreditMemoPage.$.showToast(postResult.message, { type: 'success' });
    }
    await checkForUpdate({ purchaseCreditMemoPage: parameters.purchaseCreditMemoPage, recordId: parameters.recordId });
    parameters.purchaseCreditMemoPage.$.loader.isHidden = true;
}

export async function postAction(parameters: PostParameter) {
    if (parameters.lines === 0) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_credit_memo__post__no_line',
                'You need to add lines before posting.',
            ),
        );
    }
    if (parameters.varianceTotalAmountIncludingTax?.toString() !== '0') {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_credit_memo__post__variance',
                'You need to resolve the total amount variances before posting.',
            ),
        );
    }
    if (parameters.taxCalculationStatus === 'failed') {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_credit_memo__post__tax_calculation_failed',
                'You need to resolve tax calculation issues before posting.',
            ),
        );
    }
    if (parameters.taxCalculationStatus === 'done' && parameters.varianceTotalAmountIncludingTax.toString() === '0') {
        if (
            await confirmDialogWithAcceptButtonText(
                parameters.purchaseCreditMemoPage,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__post_action_dialog_title',
                    'Confirm posting',
                ),
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__post_action_dialog_content',
                    'You are about to post this purchase credit memo.',
                ),
                ui.localize('@sage/xtrem-purchasing/pages-confirm-post', 'Post'),
            )
        ) {
            await creditMemoPostingAction({
                purchaseCreditMemoPage: parameters.purchaseCreditMemoPage,
                recordId: parameters.recordId,
                isCalledFromRecordPage: parameters.isCalledFromRecordPage,
            });
        }
    }
}

export async function updateCreditMemoStatus(parameters: UpdateStatusParameter) {
    if (parameters.status === 'inProgress' && parameters.stockTransactionStatus === 'draft') {
        if (
            await confirmDialogWithAcceptButtonText(
                parameters.purchaseCreditMemoPage,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_title',
                    'Check and update status',
                ),
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_message',
                    'You are about to correct this shipment status back to Draft.',
                ),
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_continue',
                    'Continue',
                ),
            )
        ) {
            const purchaseCreditMemo = parameters.recordId;
            return parameters.purchaseCreditMemoPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseCreditMemo')
                .mutations.synchronizeDisplayStatus(true, { purchaseCreditMemo })
                .execute();
        }
    }

    return false;
}

export async function notifyBuyer(
    page: ui.Page<GraphApi>,
    parameters: {
        recordId: string | undefined;
        siteId: string | undefined;
    },
) {
    if (parameters.siteId && parameters.recordId) {
        const localizedResults = getLocalizedCreditMemoBuyerSendActions();
        const defaultApprover: ExtractEdgesPartial<User> | FilteredUsers | null = null;
        const substituteApprover: ExtractEdgesPartial<User> | FilteredUsers | null = null;
        const usersList = await loadApprovers(page);

        const result = (await page.$.dialog.page(
            '@sage/xtrem-master-data/RequestApprovalDialog',
            {
                _id: parameters.recordId ?? '',
                users: JSON.stringify(usersList),
                defaultApprover: JSON.stringify(defaultApprover),
                substituteApprover: JSON.stringify(substituteApprover),
                nodeName: JSON.stringify('@sage/xtrem-purchasing/PurchaseCreditMemo'),
                dialogTitle: JSON.stringify(localizedResults.dialogTitle),
                confirmTitle: JSON.stringify(localizedResults.confirmTitle),
                confirmMessage: JSON.stringify(localizedResults.confirmMessage),
                resultMessage: JSON.stringify(localizedResults.resultMessage),
                errorMessage: JSON.stringify(localizedResults.errorMessage),
                emailNotSent: JSON.stringify(localizedResults.emailNotSent),
                selectButtonTitle: JSON.stringify(localizedResults.selectButtonTitle),
                emailHelperText: JSON.stringify(localizedResults.emailHelperText),
                isApproval: false,
            },
            { resolveOnCancel: true },
        )) as { isSent: boolean };

        if (result && result.isSent) {
            await page.$.refreshNavigationPanel();
            if (page.$.recordId) await page.$.router.refresh();
        }
    }
}

function getLocalizedCreditMemoBuyerSendActions() {
    return {
        dialogTitle: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__send_approval_request_dialog_title',
            'Buyer notification',
        ),
        confirmTitle: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__send_approval_request_confirm_title',
            'Buyer notification',
        ),
        confirmMessage: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__send_approval_request_dialog_content',
            'You are about to send the buyer notification email.',
        ),
        resultMessage: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_sent_to_approval',
            'Buyer notification email sent to: ',
        ),
        errorMessage: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_exception_request',
            'Could not send buyer notification email.',
        ),
        emailNotSent: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_not_sent',
            'The buyer notification cannot be sent by email.',
        ),
        selectButtonTitle: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__select_button_title',
            'Select buyer',
        ),
        emailHelperText: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_helper_text',
            'A buyer notification for this purchase credit memo is sent to this address.',
        ),
    };
}
