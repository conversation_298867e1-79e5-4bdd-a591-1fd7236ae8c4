import { asyncArray } from '@sage/xtrem-async-helper';
import { withoutEdges } from '@sage/xtrem-client';
import { loadApprovers } from '@sage/xtrem-master-data/lib/client-functions/user';
import type { GraphApi, PurchaseReceipt } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isPurchaseOrderLineActionDisabled } from '../shared-functions/edit-rules';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    ApproveActionParameters,
    ApproveOrRejectActionParameters,
    CloseActionParameters,
    ConfirmActionParameters,
    CreatePurchaseReceiptActionParameters,
    RejectActionParameters,
    RequestApprovalParameters,
    SetDimensionActionParameter,
    SiteApprover,
} from './interfaces/purchase-order-actions-functions';
import { getLinesForDimensions } from './manage-dimensions';

const continueText = ui.localize('@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-continue', 'Continue');
const cancelText = ui.localize('@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-cancel', 'Cancel');

async function openAssignmentDialog(page: ui.Page<GraphApi>, dialogTitle: string, dialogText: string) {
    if (
        await page.$.dialog
            .confirmation('warn', dialogTitle, dialogText, {
                acceptButton: { text: continueText },
                cancelButton: { text: cancelText },
            })
            .then(() => true)
            .catch(() => false)
    ) {
        return true;
    }
    return false;
}

export function isPurchaseOrderAssignmentCheckDelete(purchaseOrderPage: ui.Page, orderAssignmentLinked: boolean) {
    if (orderAssignmentLinked) {
        return openAssignmentDialog(
            purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_title',
                'Delete purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_content',
                'When you delete this purchase order, the links to the original orders are also deleted.',
            ),
        );
    }
    return true;
}

export function isPurchaseOrderAssignmentCheckClose(purchaseOrderPage: ui.Page, orderAssignmentLinked: boolean) {
    if (orderAssignmentLinked) {
        return openAssignmentDialog(
            purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_title',
                'Close purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_content',
                'When you close this purchase order, the links to the sales orders remain.',
            ),
        );
    }
    return true;
}

export async function confirmAction(parameters: ConfirmActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_title', 'Confirm purchase order'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_content',
                'You are about to set this purchase order to "Confirmed"',
            ),
            ui.localize('@sage/xtrem-purchasing/pages_confirm_button', 'Confirm'),
        ))
    ) {
        parameters.purchaseOrderPage.$.loader.isHidden = false;
        const purchaseOrder = await parameters.purchaseOrderPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .mutations.confirm(
                { approvalStatus: true },
                {
                    document: parameters.purchaseOrderPage.$.recordId || '',
                    confirmStatusToUpdate: parameters.isConfirmed ? 'confirmed' : 'rejected',
                },
            )
            .execute();

        if (purchaseOrder.approvalStatus === 'confirmed') {
            parameters.purchaseOrderPage.$.showToast(
                ui.localize('@sage/xtrem-purchasing/pages__purchase_order__confirm_status_updated', 'Status updated.'),
                { type: 'success' },
            );
            if (parameters.isCalledFromRecordPage && parameters.recordId) {
                parameters.purchaseOrderPage.$.setPageClean();
                parameters.purchaseOrderPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                    _id: parameters.recordId,
                });
            }
        }

        parameters.purchaseOrderPage.$.loader.isHidden = true;
    }
}

async function createPurchaseReceipt(
    parameters: CreatePurchaseReceiptActionParameters,
): Promise<{ message: string; type: 'success' | 'error' }> {
    const receipts = (await parameters.purchaseOrderPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseOrder')
        .mutations.createPurchaseReceipt({ number: true }, { document: parameters.recordId })
        .execute()) as PurchaseReceipt[];

    if (receipts?.length > 0) {
        return {
            message: ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__receipt_created',
                'Receipt created: {{receiptNumbers}}.',
                { receiptNumbers: receipts.map(receipt => receipt.number).join('\n') },
            ),
            type: 'success',
        };
    }
    return {
        message: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order__receipt_not_created',
            'Could not create receipt.',
        ),
        type: 'error',
    };
}

export async function createPurchaseReceiptAction(parameters: CreatePurchaseReceiptActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_title',
                'Confirm receipt creation',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_content',
                'You are about to create a receipt from this purchase order.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
        ))
    ) {
        parameters.purchaseOrderPage.$.loader.isHidden = false;

        const { message, type } = await createPurchaseReceipt(parameters);
        parameters.purchaseOrderPage.$.showToast(message, { type });

        if (parameters.isCalledFromRecordPage && parameters.recordId) {
            parameters.purchaseOrderPage.$.setPageClean();
            parameters.purchaseOrderPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                _id: parameters.recordId,
            });
        }

        parameters.purchaseOrderPage.$.loader.isHidden = true;
    }
}

async function approve(parameters: ApproveOrRejectActionParameters) {
    parameters.purchaseOrderPage.$.loader.isHidden = false;

    await parameters.purchaseOrderPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseOrder')
        .mutations.approve(
            { approvalStatus: true, status: true },
            {
                document: parameters.purchaseOrderPage.$.recordId ?? parameters.recordId,
                approvalStatusToUpdate: parameters.isApproved ? 'approved' : 'rejected',
            },
        )
        .execute();

    parameters.purchaseOrderPage.$.showToast(
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order__approval_status_updated',
            'Approval status updated.',
        ),
        { type: 'success' },
    );
    if (parameters.isCalledFromRecordPage && parameters.recordId) {
        parameters.purchaseOrderPage.$.setPageClean();
        await parameters.purchaseOrderPage.$.router.refresh(true);
        await parameters.purchaseOrderPage.$.refreshNavigationPanel();
    }

    parameters.purchaseOrderPage.$.loader.isHidden = true;
}

export async function approveAction(parameters: ApproveActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_title', 'Confirm approval'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_content',
                'You are about to approve this order.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
        ))
    ) {
        await approve(parameters);
    }
}

export async function rejectAction(parameters: RejectActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_title', 'Confirm rejection'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_content',
                'You are about to reject this order.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-reject', 'Reject'),
        ))
    ) {
        await approve({
            isCalledFromRecordPage: parameters.isCalledFromRecordPage,
            purchaseOrderPage: parameters.purchaseOrderPage,
            recordNumber: parameters.recordNumber,
            isApproved: !parameters.isRejected,
            recordId: parameters.recordId,
        });
    }
}

export async function closeAction(parameters: CloseActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_title',
                'Confirm close purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_content',
                'You are about to change the status of this order to closed. You can reopen this order later.',
            ),
            ui.localize('@sage/xtrem-purchasing/closeOrder____title', 'Close order'),
        ))
    ) {
        parameters.purchaseOrderPage.$.loader.isHidden = false;

        try {
            const purchaseOrder = await parameters.purchaseOrderPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.close({ status: true }, { purchaseOrder: parameters.recordId ?? '' })
                .execute();

            if (purchaseOrder.status === 'closed') {
                parameters.purchaseOrderPage.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__purchase_order_closed',
                        'The purchase order is closed.',
                    ),
                    { type: 'success' },
                );
                if (parameters.isCalledFromRecordPage && parameters.recordId) {
                    parameters.purchaseOrderPage.$.setPageClean();
                    parameters.purchaseOrderPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                        _id: parameters.recordId,
                    });
                }
            }
        } catch (error) {
            // TODO: wait for XT-62376 to be fixed and then move this call in 'onError' instead of using try/catch
            await parameters.purchaseOrderPage.$.processServerErrors(error);
        }
        parameters.purchaseOrderPage.$.loader.isHidden = true;
    }
}

export async function openAction(parameters: CloseActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__reopen_order_dialog_title',
                'Confirm reopen purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__reopen_order_dialog_content',
                'You are about to reopen this order.',
            ),
            ui.localize('@sage/xtrem-purchasing/openOrder____title', 'Reopen order'),
        ))
    ) {
        parameters.purchaseOrderPage.$.loader.isHidden = false;

        try {
            const isOpened = await parameters.purchaseOrderPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.open(true, { purchaseOrder: parameters.recordId || '' })
                .execute();

            if (isOpened) {
                parameters.purchaseOrderPage.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__purchase_order_opened',
                        'The purchase order is open.',
                    ),
                    { type: 'success' },
                );
                if (parameters.isCalledFromRecordPage && parameters.recordId) {
                    parameters.purchaseOrderPage.$.setPageClean();
                    parameters.purchaseOrderPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                        _id: parameters.recordId,
                    });
                }
            }
        } catch (error) {
            // TODO: wait for XT-62376 to be fixed and then move this call in 'onError' instead of using try/catch
            await parameters.purchaseOrderPage.$.processServerErrors(error);
        }
        parameters.purchaseOrderPage.$.loader.isHidden = true;
    }
}

export async function getPurchaseOrderSiteApprovers(
    page: ui.Page<GraphApi>,
    siteId: string,
): Promise<SiteApprover | null> {
    const siteApprovers: SiteApprover[] = withoutEdges(
        await page.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        id: true,
                        purchaseOrderDefaultApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                        purchaseOrderSubstituteApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    {
                        filter: {
                            _id: siteId,
                        },
                    },
                ),
            )
            .execute(),
    );

    if (siteApprovers.length > 0) {
        return siteApprovers[0];
    }

    return null;
}

export async function submitForApproval(
    page: ui.Page<GraphApi>,
    parameters: {
        recordId: string | undefined;
        siteId: string | undefined;
        throwError: boolean;
    },
) {
    if (parameters.throwError) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order_submit_for_approval__tax_calculation_failed',
                'You need to resolve tax calculation issues before submitting for approval.',
            ),
        );
    }

    if (parameters.siteId && parameters.recordId) {
        const localizedResults = getLocalizeOrderApprovalSendActions();
        const siteApprovers = await getPurchaseOrderSiteApprovers(page, parameters.siteId);
        const usersList = await loadApprovers(page);
        const result = (await page.$.dialog.page(
            '@sage/xtrem-master-data/RequestApprovalDialog',
            {
                _id: parameters.recordId ?? '',
                users: JSON.stringify(usersList),
                defaultApprover: JSON.stringify(siteApprovers?.purchaseOrderDefaultApprover ?? null),
                substituteApprover: JSON.stringify(siteApprovers?.purchaseOrderSubstituteApprover ?? null),
                nodeName: localizedResults?.nodeName ?? null,
                isApproval: localizedResults?.isApproval ?? true,
                confirmTitle: JSON.stringify(localizedResults?.confirmTitle ?? ''),
            },
            { resolveOnCancel: true },
        )) as { isSent: boolean };

        if (result && result.isSent) {
            await page.$.refreshNavigationPanel();
            if (page.$.recordId) await page.$.router.refresh();
        }
    }
}

export function getLocalizeOrderApprovalSendActions() {
    return {
        nodeName: JSON.stringify('@sage/xtrem-purchasing/PurchaseOrder'),
        isApproval: true,
        confirmTitle: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order__request_approval_dialog__send_approval_request_confirm_title',
            'Purchase order approval request',
        ),
    };
}

export async function isConfirmedRequestApprovalAction(parameters: RequestApprovalParameters) {
    if (
        parameters.isGrossPriceMissing &&
        !(await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__no_price_title', 'Confirm price'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__no_price',
                'You are about to submit the purchase order for approval. Some lines have no unit price.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-submit', 'Submit'),
        ))
    ) {
        return false;
    }
    return true;
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseOrderLines = await getLinesForDimensions({
        purchaseOrderPage: parameters.purchaseOrderPage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = purchaseOrderLines.filter(
        line =>
            !isPurchaseOrderLineActionDisabled(
                parameters.status || '',
                line.status || '',
                'dimensions',
                parameters.approvalStatus || '',
            ) || parameters.isRepost,
    );

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.purchaseOrderPage,
                site: parameters.site || null,
                supplier: parameters.supplier || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForPurchaseLines({
                    line: lineToSetDimensions,
                    purchasePage: parameters.purchaseOrderPage,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }

        if (dataDetermined) {
            await parameters.purchaseOrderPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrderLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }

        return loopResponse;
    });

    parameters.purchaseOrderPage.$.showToast(
        ui.localize('@sage/xtrem-purchasing/pages__purchase_order__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}
