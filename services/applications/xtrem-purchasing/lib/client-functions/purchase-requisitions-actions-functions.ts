import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import { Decimal } from '@sage/xtrem-decimal';
import {
    convertFromTo,
    getCompanyPriceScale,
    getPurchaseUnit,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { loadApprovers } from '@sage/xtrem-master-data/lib/client-functions/user';
import type { GraphApi, PurchaseRequisitionLine } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isPurchaseRequisitionLineActionDisabled } from '../shared-functions/edit-rules';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getPurchasePrice,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    ActionsParameter,
    ApplyDefaultSupplierActionParameter,
    ApproveRejectActionParameter,
    CalculatePricesParameter,
    CalculatePricesResult,
    ConfirmActionParameters,
    CreateActionParameter,
    FilterLinesParameter,
    GetDefaultSupplierParameter,
    SetDimensionActionParameter,
    SiteApprover,
} from './interfaces/purchase-requisition-actions-functions';

async function getDefaultSupplier(parameters: GetDefaultSupplierParameter) {
    const { item, site, purchaseRequisitionPage } = parameters;
    const defaultSupplier = await purchaseRequisitionPage.$.graph
        .node('@sage/xtrem-master-data/Supplier')
        .queries.getDefaultSupplier(
            {
                _id: true,
                businessEntity: {
                    id: true,
                    name: true,
                    currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                },
            },
            { item, site },
        )
        .execute();

    return defaultSupplier || {};
}

function filterLinesForDefaultSupplier(parameters: FilterLinesParameter) {
    return parameters.lines.filter(
        line =>
            !line.supplier?._id &&
            !isPurchaseRequisitionLineActionDisabled(parameters.status, line.status || '', 'applyDefaultSuppliers'),
    );
}

export function calculatePrices(parameters: CalculatePricesParameter): CalculatePricesResult {
    const { grossPrice, charge, discount, quantity, legalCompany, netPrice, totalTaxExcludedAmount } = parameters;
    if (parameters.netPrice !== undefined && parameters.netPrice >= 0) {
        const scale = getCompanyPriceScale(legalCompany);
        const chargeAmount = Decimal.roundAt((grossPrice * charge || 0) / 100, scale);
        const discountAmount = Decimal.roundAt((grossPrice * discount || 0) / 100, scale);
        const netUnitPrice = Decimal.roundAt(grossPrice + chargeAmount - discountAmount, scale);

        return {
            resultNetPrice: netUnitPrice.toString(),
            resultTotalTaxExcludedAmount: (netUnitPrice * quantity).toString(),
        };
    }
    return {
        resultNetPrice: netPrice.toString(),
        resultTotalTaxExcludedAmount: totalTaxExcludedAmount.toString(),
    };
}

export async function applyDefaultSupplier(parameters: ApplyDefaultSupplierActionParameter) {
    const linesWithoutSupplier = filterLinesForDefaultSupplier({ lines: parameters.lines, status: parameters.status });
    await Promise.all(
        linesWithoutSupplier.map(async lineWithoutSupplier => {
            if (lineWithoutSupplier.item?._id && lineWithoutSupplier.site?._id) {
                lineWithoutSupplier.supplier = await getDefaultSupplier({
                    purchaseRequisitionPage: parameters.purchaseRequisitionPage,
                    item: lineWithoutSupplier.item._id,
                    site: lineWithoutSupplier.site._id,
                });
            }
            if (lineWithoutSupplier.supplier?._id) {
                lineWithoutSupplier.currency = lineWithoutSupplier.supplier.businessEntity?.currency;
                lineWithoutSupplier.unit = await getPurchaseUnit(
                    parameters.purchaseRequisitionPage.$.graph,
                    lineWithoutSupplier.item?._id || '',
                    lineWithoutSupplier.supplier._id,
                );
                const quantityToConvert = lineWithoutSupplier.quantity ? Number(lineWithoutSupplier.quantity) : 1;
                const conversion = await convertFromTo(
                    parameters.purchaseRequisitionPage.$.graph,
                    lineWithoutSupplier.unit?._id || '',
                    lineWithoutSupplier.item?.stockUnit?._id || '',
                    quantityToConvert,
                    lineWithoutSupplier.item?._id,
                    lineWithoutSupplier.supplier?._id,
                    '',
                    'purchase',
                    false,
                );
                if (conversion.conversionFactor === 0) {
                    parameters.purchaseRequisitionPage.$.showToast(
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_requisition__no_coefficient_exists',
                            'No coefficient exists for the selected purchase unit and the item stock unit, please add the coefficient for the units.',
                        ),
                        { type: 'error' },
                    );
                }

                const { resultNetPrice, resultTotalTaxExcludedAmount } = calculatePrices({
                    netPrice: Number(lineWithoutSupplier.netPrice),
                    grossPrice: Number(lineWithoutSupplier.grossPrice),
                    charge: lineWithoutSupplier.charge ? Number(lineWithoutSupplier.charge) : 0,
                    discount: lineWithoutSupplier.discount ? Number(lineWithoutSupplier.discount) : 0,
                    quantity: Number(lineWithoutSupplier.quantity),
                    totalTaxExcludedAmount: Number(lineWithoutSupplier.totalTaxExcludedAmount),
                    legalCompany: parameters.legalCompany,
                });
                lineWithoutSupplier.netPrice = resultNetPrice;
                lineWithoutSupplier.totalTaxExcludedAmount = resultTotalTaxExcludedAmount;
                const purchasePrice = await getPurchasePrice(
                    parameters.purchaseRequisitionPage.$.graph,
                    lineWithoutSupplier.site?._id || '',
                    lineWithoutSupplier.supplier?._id,
                    lineWithoutSupplier.currency?._id || '',
                    lineWithoutSupplier.item?._id || '',
                    lineWithoutSupplier.unit?._id || '',
                    parseFloat(lineWithoutSupplier.quantity || '0'),
                    new Date(parameters.requestDate),
                    false,
                );
                lineWithoutSupplier.grossPrice = String(purchasePrice.price);
                if (purchasePrice.priceOrigin) {
                    lineWithoutSupplier.priceOrigin = purchasePrice.priceOrigin;
                }
                if (!parameters.isCalledFromRecordPage) {
                    await parameters.purchaseRequisitionPage.$.graph
                        .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
                        .mutations.applyDefaultSupplier(true, {
                            purchaseRequisitionLine: lineWithoutSupplier._id,
                            supplier: lineWithoutSupplier.supplier._id,
                            quantity: Number(lineWithoutSupplier.quantity),
                            grossPrice: Number(lineWithoutSupplier.grossPrice),
                            netPrice: Number(lineWithoutSupplier.netPrice),
                            totalTaxExcludedAmount: Number(lineWithoutSupplier.totalTaxExcludedAmount),
                            priceOrigin: lineWithoutSupplier.priceOrigin,
                        })
                        .execute();
                }
            }
            return lineWithoutSupplier;
        }),
    );
    if (!parameters.isCalledFromRecordPage) {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__apply_default_supplier_success',
                'Default supplier applied',
            ),
            { type: 'success' },
        );
    }
    await parameters.purchaseRequisitionPage.$.router.refresh();
    await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
    return linesWithoutSupplier as ui.PartialNodeWithId<PurchaseRequisitionLine>[];
}

export async function getPurchaseRequisitionLines(parameters: ActionsParameter) {
    return extractEdges(
        await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        site: { _id: true },
                        _sortValue: true,
                        document: {
                            _id: true,
                        },
                        item: {
                            _id: true,
                            name: true,
                            id: true,
                            stockUnit: {
                                _id: true,
                                id: true,
                            },
                            type: true,
                        },
                        netPrice: true,
                        totalTaxExcludedAmount: true,
                        status: true,
                        grossPrice: true,
                        priceOrigin: true,
                        quantity: true,
                        supplier: { _id: true },
                        currency: {
                            _id: true,
                            name: true,
                            id: true,
                            decimalDigits: true,
                            rounding: true,
                        },
                    },

                    {
                        filter: {
                            document: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function confirmAction(parameters: ConfirmActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_title',
                'Confirm purchase requisition',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_content',
                'You are about to set this purchase requisition to "Confirmed"',
            ),
            ui.localize('@sage/xtrem-purchasing/pages_confirm_button', 'Confirm'),
        ))
    ) {
        parameters.purchaseRequisitionPage.$.loader.isHidden = false;
        const purchaseRequisition = await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisition')
            .mutations.confirm({ approvalStatus: true }, { document: parameters.recordId || '' })
            .execute();
        if (purchaseRequisition && purchaseRequisition.approvalStatus === 'confirmed') {
            parameters.purchaseRequisitionPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__confirm_status_updated',
                    'Status updated.',
                ),
                { type: 'success' },
            );
        }
        await parameters.purchaseRequisitionPage.$.router.refresh();
        await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
        parameters.purchaseRequisitionPage.$.loader.isHidden = true;
    }
}

async function getPurchaseRequisitionLinesForDimensions(parameters: ActionsParameter) {
    return extractEdges(
        await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        status: true,
                    },

                    {
                        filter: {
                            document: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseRequisitionLines = await getPurchaseRequisitionLinesForDimensions({
        purchaseRequisitionPage: parameters.purchaseRequisitionPage,
        recordId: parameters.recordId,
    });
    const linesToSetDimensions = purchaseRequisitionLines.filter(
        line => !isPurchaseRequisitionLineActionDisabled(parameters.status || '', line.status || '', 'dimensions'),
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            if (!dataDetermined) {
                const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                    page: parameters.purchaseRequisitionPage,
                    site: parameters.site || null,
                    supplier: parameters.supplier || null,
                });
                const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                    await getDimensionsForPurchaseLines({
                        line: lineToSetDimensions,
                        purchasePage: parameters.purchaseRequisitionPage,
                        defaultDimensionsAttributes,
                        defaultedFromItem,
                    });
                if (!resultDataDetermined) {
                    break;
                }
                dimensionsToSet = resultDimensionsToSet;
                attributesToSet = resultAttributesToSet;
                dataDetermined = resultDataDetermined;
            }
        }
        if (dataDetermined) {
            await parameters.purchaseRequisitionPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.purchaseRequisitionPage.$.showToast(
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__apply_dimensions_success',
            'Dimensions applied.',
        ),
        { type: 'success' },
    );
}

async function approveOrReject(parameters: ApproveRejectActionParameter) {
    parameters.purchaseRequisitionPage.$.loader.isHidden = false;
    if (
        await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisition')
            .mutations.approve(true, {
                document: parameters.purchaseRequisitionPage.$.recordId ?? parameters.recordId,
                approve: parameters.approve,
            })
            .execute()
    ) {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__approval_status_updated',
                'Approval status updated.',
            ),
            { type: 'success' },
        );
    }
    await parameters.purchaseRequisitionPage.$.router.refresh();
    await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
    parameters.purchaseRequisitionPage.$.loader.isHidden = true;
}

export async function approve(parameters: ApproveRejectActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_title', 'Confirm approval'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_content',
                'You are about to approve this requisition.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
        )
    ) {
        await approveOrReject(parameters);
    }
}
export async function reject(parameters: ApproveRejectActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_title', 'Confirm rejection'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_content',
                'You are about to reject this requisition.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-reject', 'Reject'),
        )
    ) {
        await approveOrReject(parameters);
    }
}

export async function close(parameters: ActionsParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_title',
                'Confirm status change',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_content',
                'You are about to change the status of this requisition to closed. You cannot undo this change.',
            ),
            ui.localize('@sage/xtrem-purchasing/closeRequisition____title', 'Close requisition'),
        )
    ) {
        parameters.purchaseRequisitionPage.$.loader.isHidden = false;

        if (
            await parameters.purchaseRequisitionPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisition')
                .mutations.close(true, { purchaseRequisition: parameters.recordId || '' })
                .execute()
        ) {
            parameters.purchaseRequisitionPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__status_updated',
                    'Requisition status updated.',
                ),
                { type: 'success' },
            );
        } else {
            parameters.purchaseRequisitionPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__status_not_updated',
                    'Unable to update status.',
                ),
                { type: 'error' },
            );
        }
        await parameters.purchaseRequisitionPage.$.router.refresh();
        await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
        parameters.purchaseRequisitionPage.$.loader.isHidden = true;
    }
}

export async function getPurchaseRequisitionSiteApprovers(
    page: ui.Page<GraphApi>,
    siteId: string,
): Promise<SiteApprover | null> {
    const siteApprovers: SiteApprover[] = withoutEdges(
        await page.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        id: true,
                        purchaseRequisitionDefaultApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                        purchaseRequisitionSubstituteApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    {
                        filter: {
                            _id: siteId,
                        },
                    },
                ),
            )
            .execute(),
    );

    if (siteApprovers.length > 0) {
        return siteApprovers[0];
    }

    return null;
}

export async function submitForApproval(
    page: ui.Page<GraphApi>,
    parameters: {
        recordId: string | undefined;
        siteId: string | undefined;
    },
) {
    if (parameters.siteId && parameters.recordId) {
        const localizedResults = getLocalizeRequisitionApprovalSendActions();
        const siteApprovers = await getPurchaseRequisitionSiteApprovers(page, parameters.siteId);
        const usersList = await loadApprovers(page);
        const result = (await page.$.dialog.page(
            '@sage/xtrem-master-data/RequestApprovalDialog',
            {
                _id: parameters.recordId ?? '',
                users: JSON.stringify(usersList),
                defaultApprover: JSON.stringify(siteApprovers?.purchaseRequisitionDefaultApprover ?? null),
                substituteApprover: JSON.stringify(siteApprovers?.purchaseRequisitionSubstituteApprover ?? null),
                nodeName: localizedResults?.nodeName ?? null,
                isApproval: localizedResults?.isApproval ?? true,
                confirmTitle: JSON.stringify(localizedResults?.confirmTitle ?? ''),
            },
            { resolveOnCancel: true },
        )) as { isSent: boolean };

        if (result && result.isSent) {
            await page.$.refreshNavigationPanel();
            if (page.$.recordId) await page.$.router.refresh();
        }
    }
}

export function getLocalizeRequisitionApprovalSendActions() {
    return {
        nodeName: JSON.stringify('@sage/xtrem-purchasing/PurchaseRequisition'),
        isApproval: true,
        confirmTitle: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__request_approval_dialog__send_approval_request_confirm_title',
            'Purchase requisition approval request',
        ),
    };
}

function confirmPurchaseOrderCreation(purchaseRequisitionPage: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        purchaseRequisitionPage,
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_title',
            'Confirm purchase order creation',
        ),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_content',
            'You are about to create a purchase order from this requisition.',
        ),
        ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
    );
}

/** Only lines containing a supplier are included */
function onlyLinesWithSupplier(purchaseRequisitionPage: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        purchaseRequisitionPage,
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_title',
            'Confirm purchase order creation',
        ),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_lines_without_supplier',
            'Only lines containing a supplier are included.',
        ),
        ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
    );
}

export async function createPurchaseOrderAction(parameters: CreateActionParameter) {
    const { lines } = parameters;

    if (!(await confirmPurchaseOrderCreation(parameters.purchaseRequisitionPage))) {
        return;
    }

    if (lines.length && lines.every(row => !row.supplier?._id && row.status === 'pending')) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition_select_supplier',
                'You need to select a supplier before creating an order.',
            ),
        );
    }
    if (lines.some(row => !row.supplier?._id && row.status === 'pending')) {
        if (!(await onlyLinesWithSupplier(parameters.purchaseRequisitionPage))) {
            return;
        }
        return;
    }

    parameters.purchaseRequisitionPage.$.loader.isHidden = false;
    const orders = (await parameters.purchaseRequisitionPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseRequisition')
        .mutations.createPurchaseOrders({ number: true }, { document: parameters.recordId })
        .execute()) as { number: string }[];

    if (orders.length) {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__purchase_order_created',
                'Purchase order(s) created: {{orderNumbers}}.',
                {
                    orderNumbers: orders.map(order => order.number).join('\n'),
                },
            ),
            { type: 'success' },
        );
    } else {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__Purchase_order_not_created',
                'Could not create purchase order.',
            ),
            { type: 'error' },
        );
    }

    parameters.purchaseRequisitionPage.$.setPageClean();

    await parameters.purchaseRequisitionPage.$.router.refresh();
    await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
    parameters.purchaseRequisitionPage.$.loader.isHidden = true;
}
