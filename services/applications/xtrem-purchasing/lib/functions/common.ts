import type { Context, NodeQueryFilter, OperationGrant } from '@sage/xtrem-core';
import { NodeStatus, date } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremPurchasing from '../index';

const { BusinessEntity, Supplier, Currency, UnitOfMeasure, ItemSite, Item, ItemSupplierPrice, ItemSiteSupplier } =
    xtremMasterData.nodes;
const { Legislation, Country } = xtremStructure.nodes;
const { Company, User } = xtremSystem.nodes;
const { Tax } = xtremTax.nodes;
const { AttachmentAssociation } = xtremUpload.nodes;

export const commonPurchasingActivities: OperationGrant[] = [
    {
        operations: ['lookup'],
        on: [
            () => BusinessEntity,
            () => Supplier,
            () => Legislation,
            () => Country,
            () => Company,
            () => Currency,
            () => ItemSite,
            () => Item,
            () => ItemSupplierPrice,
            () => ItemSiteSupplier,
            () => AttachmentAssociation,
            () => User,
        ],
    },
    { operations: ['getDefaultSupplier'], on: [() => Supplier] },
    { operations: ['getPurchaseUnit', 'convertFromTo'], on: [() => UnitOfMeasure] },
    { operations: ['getPurchasePrice'], on: [() => ItemSupplierPrice] },
    { operations: ['getTaxValues'], on: [() => Tax] },
    ...xtremFinanceData.functions.dimensionsAndAttributesOperations,
];
/**
 * Verifies if the current date input is later than today for the purchase documents
 * @param nodeStatus current status of the node(this.$.status)
 * @param val date input
 * @param oldNodeDate date before the change
 * @returns Boolean
 */
export function isDateLaterThanToday(nodeStatus: NodeStatus, val: date, oldNodeDate = date.today()) {
    return (
        (nodeStatus === NodeStatus.added || (nodeStatus === NodeStatus.modified && val.compare(oldNodeDate) !== 0)) &&
        val.compare(date.today()) > 0
    );
}

export async function getCurrentHeaderNote(
    instance: xtremPurchasing.interfaces.PurchaseDocumentNotePropagation,
): Promise<xtremPurchasing.interfaces.CurrentNote> {
    return {
        isOverwrite: await instance.isOverwriteNote,
        internalNote: await instance.internalNote,
        isExternalNote: await instance.isExternalNote,
        externalNote: await instance.externalNote,
    };
}

export async function setHeaderInternalNote(
    instance: xtremPurchasing.interfaces.PurchaseDocumentNotePropagation,
    linkedDocumentArray: xtremPurchasing.interfaces.LinkedPurchaseDocument[],
    currentNote: xtremPurchasing.interfaces.CurrentNote,
) {
    await instance.$.set({
        internalNote:
            currentNote.internalNote.toString() && !currentNote.isOverwrite
                ? currentNote.internalNote
                : await linkedDocumentArray[0].internalNote,
    });
}

export async function setHeaderSetupNote(
    instance: xtremPurchasing.interfaces.PurchaseDocumentNotePropagation,
    isTransferHeaderNote: boolean,
    isTransferLineNote: boolean,
) {
    await instance.$.set({ isTransferHeaderNote, isTransferLineNote });
}

export async function getCurrentLineNote(
    instance: xtremPurchasing.interfaces.PurchaseDocumentLineNotePropagation,
): Promise<xtremPurchasing.interfaces.CurrentNote> {
    return {
        isOverwrite: await (await instance.document).isOverwriteNote,
        internalNote: await instance.internalNote,
        isExternalNote: await instance.isExternalNote,
        externalNote: await instance.externalNote,
    };
}

export async function setLineInternalNote(
    instance: xtremPurchasing.interfaces.PurchaseDocumentLineNotePropagation,
    linkedDocument: xtremPurchasing.interfaces.LinkedPurchaseDocumentLine,
    currentNote: xtremPurchasing.interfaces.CurrentNote,
) {
    await instance.$.set({
        internalNote:
            currentNote.internalNote.toString() && !currentNote.isOverwrite
                ? currentNote.internalNote
                : await linkedDocument.internalNote,
    });
}

/**
 * Checks if there are purchase documents connected to a given item-site preventing item-site and item-site-cost from
 * deleting it.
 * @param context current context
 * @param item Item to be selected
 * @param site Site to be selected
 * @returns Boolean
 */
export async function doPurchaseDocumentsExist(
    context: Context,
    item: xtremMasterData.nodes.Item,
    site: xtremSystem.nodes.Site,
): Promise<boolean> {
    // Check for possible purchase orders for this item-site.
    if (
        await context.query(xtremPurchasing.nodes.PurchaseOrderLine, {
            filter: { _and: [{ item }, { stockSite: site }] },
            first: 1,
        }).length
    )
        return true; // Found at least 1.

    // Check for possible purchase invoices for this item-site.
    if (
        await context.query(xtremPurchasing.nodes.PurchaseInvoiceLine, {
            filter: { _and: [{ item }, { recipientSite: site }] },
            first: 1,
        }).length
    )
        return true; // Found at least 1.

    // Check for possible purchase requisitions for this item-site.
    if (
        await context.query(xtremPurchasing.nodes.PurchaseRequisitionLine, {
            filter: { item, site },
            first: 1,
        }).length
    )
        return true; // Found at least 1.

    // Check for possible purchase receipts for this item-site.
    if (
        await context.query(xtremPurchasing.nodes.PurchaseReceiptLine, {
            filter: { _and: [{ item }, { stockSite: site }] },
            first: 1,
        }).length
    )
        return true; // Found at least 1.

    return false; // None found.
}

/**
 * Queries PurchaseInvoice and PurchaseCreditMemo for supplierDocumentNumber for the same billBySupplier
 * @param context current context
 * @param document Object with supplierDocumentNumber, supplierId and nodeToQuery(node factory name)
 * @returns number
 */
export function queryCountSupplierDocumentNumberBySupplier(
    context: Context,
    document: xtremPurchasing.interfaces.DocumentForSupplierDocumentNumberValidation,
): Promise<number> {
    const basicFilter: NodeQueryFilter<
        xtremPurchasing.nodes.PurchaseCreditMemo | xtremPurchasing.nodes.PurchaseInvoice
    > = {
        billBySupplier: document.supplierId,
        supplierDocumentNumber: document.supplierDocumentNumber,
    };
    const queryCountFilter: NodeQueryFilter<
        xtremPurchasing.nodes.PurchaseCreditMemo | xtremPurchasing.nodes.PurchaseInvoice
    > = document.currentDocumentSysId ? { ...basicFilter, _id: { _ne: document.currentDocumentSysId } } : basicFilter;
    return context.queryCount(
        document.nodeToQuery === 'PurchaseCreditMemo'
            ? xtremPurchasing.nodes.PurchaseCreditMemo
            : xtremPurchasing.nodes.PurchaseInvoice,
        { filter: queryCountFilter },
    );
}
