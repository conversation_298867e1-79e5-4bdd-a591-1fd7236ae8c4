import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import { commonPurchasingActivities } from '../functions/common';
import { PurchaseRequisition, PurchaseRequisitionLine } from '../nodes/index';

const commonOperations: OperationGrant[] = [...commonPurchasingActivities];

const approveOperations: OperationGrant[] = [
    ...commonOperations,
    {
        operations: ['read'],
        on: [() => PurchaseRequisition],
    },
];

const manageOperations: OperationGrant[] = [
    ...commonOperations,
    {
        operations: [
            'read',
            'create',
            'update',
            'delete',
            'sendApprovalRequestMail',
            'sendRequestChangesMail',
            'close',
            'confirm',
        ],
        on: [() => PurchaseRequisition],
    },
    {
        operations: ['getFilteredList', 'closeLine', 'setDimension', 'applyDefaultSupplier'],
        on: [() => PurchaseRequisitionLine],
    },
];

export const purchaseRequisition = new Activity({
    description: 'Purchase requisition',
    node: () => PurchaseRequisition,
    __filename,
    permissions: ['read', 'approve', 'manage'],
    operationGrants: {
        read: commonOperations,
        approve: approveOperations,
        manage: manageOperations,
    },
});
