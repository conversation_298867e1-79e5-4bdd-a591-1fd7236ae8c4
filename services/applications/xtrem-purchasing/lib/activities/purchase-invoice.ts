import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import { commonPurchasingActivities } from '../functions/common';
import * as xtremPurchasing from '../index';

export const purchaseInvoice = new Activity({
    description: 'Purchase invoice',
    node: () => xtremPurchasing.nodes.PurchaseInvoice,
    __filename,
    permissions: ['read', 'manage', 'acceptAllVariances', 'post'],

    operationGrants: {
        read: [
            { operations: ['lookup'], on: [() => xtremPurchasing.nodes.PurchaseOrder] },
            { operations: ['lookup'], on: [() => xtremPurchasing.nodes.PurchaseReceipt] },
            { operations: ['queryAllocations'], on: [() => xtremLandedCost.nodes.LandedCostDocumentLine] },
            ...commonPurchasingActivities,
        ],
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'sendNotificationToBuyerMail',
                    'getRateOrReverseRate',
                    'getRateOrReverseDivisor',
                    'rateDescription',
                    'financeIntegrationCheck',
                    'resynchronizeDisplayStatus',
                    'sendApprovalRequestMail',
                ],
                on: [() => xtremPurchasing.nodes.PurchaseInvoice],
            },
            {
                operations: ['lookup', 'updateLineTo'],
                on: [() => xtremPurchasing.nodes.PurchaseOrder],
            },
            {
                operations: ['lookup', 'updateLineTo'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },

            {
                operations: ['createPurchaseInvoice'],
                on: [() => xtremPurchasing.nodes.PurchaseReturn],
            },
            {
                operations: ['calculateLineTaxes', 'setDimension', 'acceptAllVariancesLine'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoiceLine],
            },
            {
                operations: ['queryAllocations', 'updateAllocations'],
                on: [() => xtremLandedCost.nodes.LandedCostDocumentLine],
            },
            ...commonPurchasingActivities,
        ],
        acceptAllVariances: [
            { operations: ['read', 'financeIntegrationCheck'], on: [() => xtremPurchasing.nodes.PurchaseInvoice] },
            ...commonPurchasingActivities,
            { operations: ['lookup'], on: [() => xtremPurchasing.nodes.PurchaseOrder] },
            { operations: ['lookup'], on: [() => xtremPurchasing.nodes.PurchaseReceipt] },
            { operations: ['queryAllocations'], on: [() => xtremLandedCost.nodes.LandedCostDocumentLine] },
        ],
        post: [
            {
                operations: ['repost', 'read', 'resynchronizeDisplayStatus', 'resendNotificationForFinance'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoice],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            { operations: ['lookup'], on: [() => xtremPurchasing.nodes.PurchaseOrder] },
            { operations: ['lookup'], on: [() => xtremPurchasing.nodes.PurchaseReceipt] },
            {
                operations: ['getPostingStatusData'],
                on: [() => xtremFinanceData.nodes.FinanceTransaction],
            },
            { operations: ['queryAllocations'], on: [() => xtremLandedCost.nodes.LandedCostDocumentLine] },
            ...commonPurchasingActivities,
        ],
    },
});
