{"@sage/xtrem-purchasing/activity__purchase_credit_memo__name": "Purchase credit memo", "@sage/xtrem-purchasing/activity__purchase_invoice__name": "Purchase invoice", "@sage/xtrem-purchasing/activity__purchase_order__name": "Purchase order", "@sage/xtrem-purchasing/activity__purchase_receipt__name": "Purchase receipt", "@sage/xtrem-purchasing/activity__purchase_requisition__name": "Purchase requisition", "@sage/xtrem-purchasing/activity__purchase_return__name": "Purchase return", "@sage/xtrem-purchasing/cant_approve_order_wrong_taxCalculationStatus": "The tax calculation is {{taxCalculationStatus}}, the order cannot be approved.", "@sage/xtrem-purchasing/cant_post_credit_memo_when_taxCalculationStatus_is_not_done": "The tax calculation is not {{done}}, the credit memo cannot be posted.", "@sage/xtrem-purchasing/cant_post_invoice_when_taxCalculationStatus_is_not_done": "The tax calculation is not {{done}}, the invoice cannot be posted.", "@sage/xtrem-purchasing/cant_post_receipt_wrong_taxCalculationStatus": "The tax calculation is {{taxCalculationStatus}}, the receipt cannot be posted.", "@sage/xtrem-purchasing/closeOrder____title": "Close order", "@sage/xtrem-purchasing/closeRequisition____title": "Close requisition", "@sage/xtrem-purchasing/closeReturn____title": "Close return", "@sage/xtrem-purchasing/credit_note_from_return__dialog_title": "Select return lines", "@sage/xtrem-purchasing/credit_note_from_return_select": "Select", "@sage/xtrem-purchasing/data_types__price_origin_enum__name": "Price origin enum", "@sage/xtrem-purchasing/data_types__property_data_type_purchasing__name": "Property data type purchasing", "@sage/xtrem-purchasing/data_types__purchase_credit_memo_default__name": "Purchase credit memo default", "@sage/xtrem-purchasing/data_types__purchase_credit_memo_display_status_enum__name": "Purchase credit memo display status enum", "@sage/xtrem-purchasing/data_types__purchase_credit_memo_status_enum__name": "Purchase credit memo status enum", "@sage/xtrem-purchasing/data_types__purchase_document_approval_status_enum__name": "Purchase document approval status enum", "@sage/xtrem-purchasing/data_types__purchase_document_line_origin_enum__name": "Purchase document line origin enum", "@sage/xtrem-purchasing/data_types__purchase_document_status_enum__name": "Purchase document status enum", "@sage/xtrem-purchasing/data_types__purchase_document_type_enum__name": "Purchase document type enum", "@sage/xtrem-purchasing/data_types__purchase_invoice_default__name": "Purchase invoice default", "@sage/xtrem-purchasing/data_types__purchase_invoice_display_status_enum__name": "Purchase invoice display status enum", "@sage/xtrem-purchasing/data_types__purchase_invoice_matching_status_enum__name": "Purchase invoice matching status enum", "@sage/xtrem-purchasing/data_types__purchase_invoice_status_enum__name": "Purchase invoice status enum", "@sage/xtrem-purchasing/data_types__purchase_invoice_variance_type_enum__name": "Purchase invoice variance type enum", "@sage/xtrem-purchasing/data_types__purchase_order_approval_status_enum__name": "Purchase order approval status enum", "@sage/xtrem-purchasing/data_types__purchase_order_confirm_status_enum__name": "Purchase order confirm status enum", "@sage/xtrem-purchasing/data_types__purchase_order_default__name": "Purchase order default", "@sage/xtrem-purchasing/data_types__purchase_order_display_status_enum__name": "Purchase order display status enum", "@sage/xtrem-purchasing/data_types__purchase_order_invoice_status_enum__name": "Purchase order invoice status enum", "@sage/xtrem-purchasing/data_types__purchase_order_receipt_status_enum__name": "Purchase order receipt status enum", "@sage/xtrem-purchasing/data_types__purchase_receipt_default__name": "Purchase receipt default", "@sage/xtrem-purchasing/data_types__purchase_receipt_display_status_enum__name": "Purchase receipt display status enum", "@sage/xtrem-purchasing/data_types__purchase_receipt_invoice_status_enum__name": "Purchase receipt invoice status enum", "@sage/xtrem-purchasing/data_types__purchase_receipt_return_status_enum__name": "Purchase receipt return status enum", "@sage/xtrem-purchasing/data_types__purchase_requisition_default__name": "Purchase requisition default", "@sage/xtrem-purchasing/data_types__purchase_requisition_display_status_enum__name": "Purchase requisition display status enum", "@sage/xtrem-purchasing/data_types__purchase_requisition_order_status_enum__name": "Purchase requisition order status enum", "@sage/xtrem-purchasing/data_types__purchase_return_credit_status_enum__name": "Purchase return credit status enum", "@sage/xtrem-purchasing/data_types__purchase_return_default__name": "Purchase return default", "@sage/xtrem-purchasing/data_types__purchase_return_display_status_enum__name": "Purchase return display status enum", "@sage/xtrem-purchasing/data_types__purchase_return_invoice_status_enum__name": "Purchase return invoice status enum", "@sage/xtrem-purchasing/data_types__purchase_return_shipping_status_enum__name": "Purchase return shipping status enum", "@sage/xtrem-purchasing/data_types__supplier_document_number_data_type__name": "Supplier document number data type", "@sage/xtrem-purchasing/data_types__test_text_stream_type__name": "Test text stream type", "@sage/xtrem-purchasing/data_types__unbilled_account_payable_status_enum__name": "Unbilled account payable status enum", "@sage/xtrem-purchasing/edit-create-line": "Add new line", "@sage/xtrem-purchasing/enums__price_origin__manual": "Manual", "@sage/xtrem-purchasing/enums__price_origin__supplierPriceList": "Supplier price list", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__paid": "Paid", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__partiallyPaid": "Partially paid", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__posted": "Posted", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__postingError": "Posting error", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__stockError": "Stock error", "@sage/xtrem-purchasing/enums__purchase_credit_memo_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__error": "Error", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__inProgress": "In progress", "@sage/xtrem-purchasing/enums__purchase_credit_memo_status__posted": "Posted", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__approved": "Approved", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__changeRequested": "Change requested", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__confirmed": "Confirmed", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__pendingApproval": "Pending approval", "@sage/xtrem-purchasing/enums__purchase_document_approval_status__rejected": "Rejected", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__direct": "Direct", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseCreditMemo": "Purchase credit memo", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseInvoice": "Purchase invoice", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseReceipt": "Purchase receipt", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseRequisition": "Purchase requisition", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseReturn": "Purchase return", "@sage/xtrem-purchasing/enums__purchase_document_line_origin__purchaseSuggestion": "Purchase suggestion", "@sage/xtrem-purchasing/enums__purchase_document_status__closed": "Closed", "@sage/xtrem-purchasing/enums__purchase_document_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_document_status__error": "Error", "@sage/xtrem-purchasing/enums__purchase_document_status__inProgress": "In progress", "@sage/xtrem-purchasing/enums__purchase_document_status__pending": "Pending", "@sage/xtrem-purchasing/enums__purchase_document_status__posted": "Posted", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseCreditMemo": "Purchase credit memo", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseInvoice": "Purchase invoice", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseReceipt": "Purchase receipt", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseRequisition": "Purchase requisition", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseReturn": "Purchase return", "@sage/xtrem-purchasing/enums__purchase_document_type__purchaseSuggestion": "Purchase suggestion", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__credited": "Credited", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__noVariance": "No variance", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__paid": "Paid", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__partiallyCredited": "Partially credited", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__partiallyPaid": "Partially paid", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__posted": "Posted", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__postingError": "Posting error", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__stockError": "Stock error", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/enums__purchase_invoice_display_status__varianceApproved": "<PERSON><PERSON><PERSON> approved", "@sage/xtrem-purchasing/enums__purchase_invoice_matching_status__noVariance": "No variance", "@sage/xtrem-purchasing/enums__purchase_invoice_matching_status__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/enums__purchase_invoice_matching_status__varianceApproved": "<PERSON><PERSON><PERSON> approved", "@sage/xtrem-purchasing/enums__purchase_invoice_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_invoice_status__error": "Error", "@sage/xtrem-purchasing/enums__purchase_invoice_status__inProgress": "In progress", "@sage/xtrem-purchasing/enums__purchase_invoice_status__posted": "Posted", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__noVariance": "No variance", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__price": "Price", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__quantity": "Quantity", "@sage/xtrem-purchasing/enums__purchase_invoice_variance_type__quantityAndPrice": "Quantity and price", "@sage/xtrem-purchasing/enums__purchase_order_approval_status__approved": "Approved", "@sage/xtrem-purchasing/enums__purchase_order_approval_status__rejected": "Rejected", "@sage/xtrem-purchasing/enums__purchase_order_confirm_status__confirmed": "Confirmed", "@sage/xtrem-purchasing/enums__purchase_order_confirm_status__rejected": "Rejected", "@sage/xtrem-purchasing/enums__purchase_order_display_status__approved": "Approved", "@sage/xtrem-purchasing/enums__purchase_order_display_status__closed": "Closed", "@sage/xtrem-purchasing/enums__purchase_order_display_status__confirmed": "Confirmed", "@sage/xtrem-purchasing/enums__purchase_order_display_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_order_display_status__partiallyReceived": "Partially received", "@sage/xtrem-purchasing/enums__purchase_order_display_status__pendingApproval": "Pending approval", "@sage/xtrem-purchasing/enums__purchase_order_display_status__received": "Received", "@sage/xtrem-purchasing/enums__purchase_order_display_status__rejected": "Rejected", "@sage/xtrem-purchasing/enums__purchase_order_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-purchasing/enums__purchase_order_invoice_status__invoiced": "Invoiced", "@sage/xtrem-purchasing/enums__purchase_order_invoice_status__notInvoiced": "Not invoiced", "@sage/xtrem-purchasing/enums__purchase_order_invoice_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-purchasing/enums__purchase_order_receipt_status__notReceived": "Not received", "@sage/xtrem-purchasing/enums__purchase_order_receipt_status__partiallyReceived": "Partially received", "@sage/xtrem-purchasing/enums__purchase_order_receipt_status__received": "Received", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__closed": "Closed", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__error": "Error", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__invoiced": "Invoiced", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__partiallyReturned": "Partially returned", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__received": "Received", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__returned": "Returned", "@sage/xtrem-purchasing/enums__purchase_receipt_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-purchasing/enums__purchase_receipt_invoice_status__invoiced": "Invoiced", "@sage/xtrem-purchasing/enums__purchase_receipt_invoice_status__notInvoiced": "Not invoiced", "@sage/xtrem-purchasing/enums__purchase_receipt_invoice_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-purchasing/enums__purchase_receipt_return_status__notReturned": "Not returned", "@sage/xtrem-purchasing/enums__purchase_receipt_return_status__partiallyReturned": "Partially returned", "@sage/xtrem-purchasing/enums__purchase_receipt_return_status__returned": "Returned", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__approved": "Approved", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__closed": "Closed", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__confirmed": "Confirmed", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__ordered": "Ordered", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__partiallyOrdered": "Partially ordered", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__pendingApproval": "Pending approval", "@sage/xtrem-purchasing/enums__purchase_requisition_display_status__rejected": "Rejected", "@sage/xtrem-purchasing/enums__purchase_requisition_order_status__notOrdered": "Not ordered", "@sage/xtrem-purchasing/enums__purchase_requisition_order_status__ordered": "Ordered", "@sage/xtrem-purchasing/enums__purchase_requisition_order_status__partiallyOrdered": "Partially ordered", "@sage/xtrem-purchasing/enums__purchase_return_credit_status__credited": "Credited", "@sage/xtrem-purchasing/enums__purchase_return_credit_status__notCredited": "Not credited", "@sage/xtrem-purchasing/enums__purchase_return_credit_status__partiallyCredited": "Partially credited", "@sage/xtrem-purchasing/enums__purchase_return_display_status__approved": "Approved", "@sage/xtrem-purchasing/enums__purchase_return_display_status__closed": "Closed", "@sage/xtrem-purchasing/enums__purchase_return_display_status__confirmed": "Confirmed", "@sage/xtrem-purchasing/enums__purchase_return_display_status__draft": "Draft", "@sage/xtrem-purchasing/enums__purchase_return_display_status__error": "Error", "@sage/xtrem-purchasing/enums__purchase_return_display_status__pendingApproval": "Pending approval", "@sage/xtrem-purchasing/enums__purchase_return_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-purchasing/enums__purchase_return_display_status__rejected": "Rejected", "@sage/xtrem-purchasing/enums__purchase_return_display_status__returned": "Returned", "@sage/xtrem-purchasing/enums__purchase_return_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-purchasing/enums__purchase_return_invoice_status__invoiced": "Invoiced", "@sage/xtrem-purchasing/enums__purchase_return_invoice_status__notInvoiced": "Not invoiced", "@sage/xtrem-purchasing/enums__purchase_return_invoice_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-purchasing/enums__purchase_return_shipping_status__notShipped": "Not shipped", "@sage/xtrem-purchasing/enums__purchase_return_shipping_status__partiallyShipped": "Partially shipped", "@sage/xtrem-purchasing/enums__purchase_return_shipping_status__shipped": "Shipped", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__completed": "Completed", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__draft": "Draft", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__error": "Error", "@sage/xtrem-purchasing/enums__unbilled_account_payable_status__inProgress": "In progress", "@sage/xtrem-purchasing/functions__purchase__credit_memo__buyer_notification_subject": "Purchase credit memo {{purchaseCreditMemoNumber}}: Matching request", "@sage/xtrem-purchasing/functions__purchase__invoice__buyer_notification_subject": "[Purchase invoice to be matched - {{purchaseInvoiceNumber}}]", "@sage/xtrem-purchasing/functions__purchase__order__approval_email_subject": "This purchase order needs approval: [Purchase order {{number}}]", "@sage/xtrem-purchasing/functions__purchase__order__request_changes_email_subject": "Changes requested for this purchase order: [Purchase order {{number}}]", "@sage/xtrem-purchasing/functions__purchase__requisition__approval_email_subject": "[Purchase requisition {{purchaseRequisitionNumber}}] approval request", "@sage/xtrem-purchasing/functions__purchase__requisition__request_changes_email_subject": "[Purchase requisition {{purchaseRequisitionNumber}}] changes requested", "@sage/xtrem-purchasing/functions__purchase_document_common__check_taxes_editable": "You can only change taxes on a draft document.", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_close_landed_costs_exist": "You cannot close an order line that is not received and that has landed costs attached.", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_close_non_posted_receipts_exist": "You cannot close an order line with associated receipts that are not posted. Check the following purchase receipts: {{receiptNumbers}}.", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_delete_landed_costs_exist": "You cannot delete this order line, as landed cost allocations exist on it. Check the following associated invoices:\n\n{{invoiceNumbers}}", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_reopen_landed_costs_exist": "You cannot open an order line that has landed costs attached.", "@sage/xtrem-purchasing/functions__purchase_order_lib__control_reopen_non_posted_receipts_exist": "You cannot open an order line with associated receipts that are not posted. Check the following purchase receipts: {{receiptNumbers}}.", "@sage/xtrem-purchasing/functions__send_mail__for_approval__approved": "The document is already approved or rejected.", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order__error_updating_status_order_not_closable": "Unable to update status on already closed, received, pending approval order.", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order__status_updated": "Order status updated.", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__error_updating_status_order_line_not_closable": "Unable to update status on already closed, received, pending approval order line.", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__error_updating_status_order_line_not_openable": "Unable to update status on already opened, received, invoiced line.", "@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__status_updated": "Order line status updated.", "@sage/xtrem-purchasing/functions-purchase-order-lib-purchase-order-created": "{{currentItem}} stock is below reordered point so purchase order was created.", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__error_updating_status": "Unable to update status. {{errorMessage}}", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__error_updating_status_Requisition_not_closable": "Unable to update status on already closed, received, pending approval requisition.", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__status_updated": "Requisition status updated.", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition_line__error_updating_status": "Unable to update line status. {{errorMessage}}", "@sage/xtrem-purchasing/functions-purchase-Requisition-lib_purchase_requisition_line__error_updating_status_Requisition_line_not_closable": "Unable to update status on already closed, received, pending approval requisition line.", "@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition_line__status_updated": "Requisition line status updated.", "@sage/xtrem-purchasing/invoice__from_receipts_dialog_title": "Select receipt lines", "@sage/xtrem-purchasing/invoice__from_receipts_select": "Select", "@sage/xtrem-purchasing/invoice_to_credit_memo__confirm__lower_greater_quantity": "You are about to create order lines with a quantity different from the invoiced quantity.", "@sage/xtrem-purchasing/invoice_to_credit_memo__confirm_greater_quantity": "You are about to create a credit memo for more than the invoiced quantity.", "@sage/xtrem-purchasing/invoice_to_credit_memo__confirm_lower_quantity": "You are about to create a credit memo for less than the invoiced quantity.", "@sage/xtrem-purchasing/invoice_to_credit_memo__create_purchase_dialog_title": "Confirm credit memo quantity", "@sage/xtrem-purchasing/invoice_to_credit_memo__dialog_title": "Select invoice lines", "@sage/xtrem-purchasing/invoice_to_credit_memo__error_zero_or_negative_quantity": "You need to enter a quantity.", "@sage/xtrem-purchasing/invoice_to_credit_memo__error_zero_or_negative_unit_price": "You need to enter a unit price.", "@sage/xtrem-purchasing/invoice_to_credit_memo_select": "Select", "@sage/xtrem-purchasing/is_active_dimension_inactive": "The {{dimension}} dimension or \"{{dimensionType}}\" dimension type is inactive.", "@sage/xtrem-purchasing/menu_item__purchase-inquiries": "Purchasing inquiries", "@sage/xtrem-purchasing/missing_purchase_order_reference": "The purchase order reference is missing.", "@sage/xtrem-purchasing/missing_purchase_receipt_reference": "The purchase receipt reference is missing.", "@sage/xtrem-purchasing/node__purchase_credit_memo__resend_notification_for_finance": "Regenerating finance notification for purchase credit memo number: {{documentNumber}}.", "@sage/xtrem-purchasing/node__purchase_invoice__resend_notification_for_finance": "Regenerating finance notification for purchase invoice number: {{documentNumber}}.", "@sage/xtrem-purchasing/node__purchase_order_bulk_print_report_name": "Purchase order", "@sage/xtrem-purchasing/node__purchase_return__resend_notification_for_finance": "Resending finance notification for purchase return: {{purchaseReturnNumber}}.", "@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_approval_workflow": "You cannot use this status if the approval workflow is disabled.", "@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_confirmed_workflow": "You cannot use this status if the approval workflow is enabled.", "@sage/xtrem-purchasing/node_site_extension_check_if_any_pending": "You need to approve or reject pending documents before disabling the approval process.", "@sage/xtrem-purchasing/node-extensions__item_extension__property__purchaseDocuments": "Purchase documents", "@sage/xtrem-purchasing/node-extensions__payment_tracking_extension__property__discountPaymentBeforeDate": "Discount payment before date", "@sage/xtrem-purchasing/node-extensions__site_extension__property__isPurchaseOrderApprovalManaged": "Is purchase order approval managed", "@sage/xtrem-purchasing/node-extensions__site_extension__property__isPurchaseRequisitionApprovalManaged": "Is purchase requisition approval managed", "@sage/xtrem-purchasing/node-extensions__site_extension__property__isPurchaseReturnApprovalManaged": "Is purchase return approval managed", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseOrderDefaultApprover": "Purchase order default approver", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseOrderSubstituteApprover": "Purchase order substitute approver", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseRequisitionDefaultApprover": "Purchase requisition default approver", "@sage/xtrem-purchasing/node-extensions__site_extension__property__purchaseRequisitionSubstituteApprover": "Purchase requisition substitute approver", "@sage/xtrem-purchasing/node-extensions__supplier_extension__property__defaultBuyer": "Default buyer", "@sage/xtrem-purchasing/node-extensions__supplier_extension__property__purchaseOrders": "Purchase orders", "@sage/xtrem-purchasing/nodes__base_purchase_document__approval_status_change_forbidden": "You can only change the approval status for Draft documents.", "@sage/xtrem-purchasing/nodes__base_purchase_document__deletion_forbidden_reason_status": "The current purchase document has the {{currentStatus}} status. You cannot delete it.", "@sage/xtrem-purchasing/nodes__base_purchase_document__id_already_exists": "A sequence number cannot be allocated because the document number already exits: {{document}}", "@sage/xtrem-purchasing/nodes__base_purchase_document__line__item_modify": "The item: {{currentItem}}, cannot be edited.", "@sage/xtrem-purchasing/nodes__base_purchase_document__line__no__receiving_site": "No {{siteId}} {{site}} receiving site for the {{item}} item site.", "@sage/xtrem-purchasing/nodes__base_purchase_document__line__no_inventory_site": "No stock site for the company. {{legalCompanyName}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document__node_name": "Base purchase document", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__businessRelation": "Business relation", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__displayStatus": "Display status", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__externalNote": "External note", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__internalNote": "Internal note", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__isExternalNote": "Is external note", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__paymentTerm": "Payment term", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__status": "Status", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__base_purchase_document__property__supplier": "Supplier", "@sage/xtrem-purchasing/nodes__base_purchase_document__reiving_site_legal_company": "The receiving site: {{site}}, does not belong to the same company as the header: {{company}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document__same_company_site_stock_site": "The site and the stock site need to have the same company.", "@sage/xtrem-purchasing/nodes__base_purchase_document__supplier_document_date": "The date cannot be later than the current date.", "@sage/xtrem-purchasing/nodes__base_purchase_document__supplier_document_number_already_exists": "Supplier document number already exists.", "@sage/xtrem-purchasing/nodes__base_purchase_document__tax_type_validation": "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.", "@sage/xtrem-purchasing/nodes__base_purchase_document__update_not_allowed_status_posted": "You cannot update a document that was posted: document {{ number}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document_cannot_approve_document": "No draft or pendingApproval document.", "@sage/xtrem-purchasing/nodes__base_purchase_document_credit_memo__date": "The date cannot be later than the document date.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__approval_status_change_forbidden": "You can only change the approval status of Draft lines.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__bad_conversion_factor": "The converted quantity: {{convertedQuantity}}, from quantity: {{quantity}} in unit: {{unit}}\n             to quantity in stock unit: {{stockUnit}}, using the conversion factor: {{conversionFactor}},\n             is different than the current quantity in stock unit: {{quantityInStockUnit}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_site_record": "The item: {{currentItem}}, is not managed for the site: {{currentSite}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_supplier_record": "The item: {{currentItem}}, is not managed for the supplier: {{currentSupplier}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_supplier_site_record": "The item:{{currentItem}}, is not managed for the site: {{currentSite}}, and supplier: {{currentSupplier}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__node_name": "Base purchase document line", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__amountExcludingTax": "Amount excluding tax", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__amountExcludingTaxInCompanyCurrency": "Amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__charge": "Charge", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__company": "Company", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__companyCurrency": "Company currency", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__discount": "Discount", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__document": "Document", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__externalNote": "External note", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__grossPrice": "Gross price", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__internalNote": "Internal note", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__isExternalNote": "Is external note", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__itemDescription": "Item description", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__itemSupplier": "Item supplier", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__lineStatus": "Line status", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__netPrice": "Net price", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__priceOrigin": "Price origin", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__purchaseUnit": "Purchase unit", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__quantity": "Quantity", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__status": "Status", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__stockSiteLinkedAddress": "Stock site linked address", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__supplier": "Supplier", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__supplierName": "Supplier name", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__same_company_site_stock_site": "The line site and the line stock site need to have the same company.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line__same_site_header_and_line": "The line site and the header site need to be the same.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_deletion_forbidden_reason_status": "The line cannot be deleted. The document status is: {{currentStatus}}.", "@sage/xtrem-purchasing/nodes__base_purchase_document_line_item_may_not_have_type_landed_cost": "A landed cost item cannot be added to the document: {{documentName}}.", "@sage/xtrem-purchasing/nodes__different_bill_by_supplier": "billBySupplier", "@sage/xtrem-purchasing/nodes__different_currency": "currency", "@sage/xtrem-purchasing/nodes__different_purchase_unit": "purchase unit", "@sage/xtrem-purchasing/nodes__different_receiving_site_address": "receiving site address", "@sage/xtrem-purchasing/nodes__different_site": "site", "@sage/xtrem-purchasing/nodes__different_supplier": "supplier", "@sage/xtrem-purchasing/nodes__different_unit_conversion_factor": "unit conversion factor", "@sage/xtrem-purchasing/nodes__differentProperties_item": "item", "@sage/xtrem-purchasing/nodes__item-site__failed_deletion_impossible_if_documents": "Delete not allowed. Purchasing documents exist for this item-site.", "@sage/xtrem-purchasing/nodes__item-site-cost__failed_deletion_impossible_if_documents": "Delete not allowed. Purchase documents exist for this item-site.", "@sage/xtrem-purchasing/nodes__landed_cost_allocation__stock_transaction_status_not_completed": "The stock status of all purchase receipt lines allocated to landed cost needs to be Completed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_post_credit_memo_when_status_is_not_draft": "The status is not {{draft}}, the credit memo cannot be posted.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_post_credit_memo_when_totals_not_equal": "The user entered total tax excluded amount is not equal to the sum of the lines.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_repost_purchasing_credit_memo_when_status_is_not_failed": "You can only repost a purchase credit memo if the status is 'Failed.'", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__deletion_forbidden_reason_status": "A document with this status cannot be deleted: {{number}}, {{status}}", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__document_was_posted": "The purchase credit memo was posted.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__line__no_financial_site": "No financial site on legal company. {{legalCompany}}", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__enforceStatusPosted": "Enforce status posted", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__enforceStatusPosted__failed": "Enforce status posted failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__enforceStatusPosted__parameter__creditMemo": "Credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__financeIntegrationCheck__parameter__creditMemo": "Credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__post": "Post", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__post__failed": "Post failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__post__parameter__creditMemo": "Credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__repost": "Repost", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__repost__failed": "Repost failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__repost__parameter__documentData": "Document data", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__repost__parameter__purchaseCreditMemo": "Purchase credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__resendNotificationForFinance__parameter__purchaseCreditMemo": "Purchase credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__sendNotificationToBuyerMail": "Send notification to buyer mail", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__sendNotificationToBuyerMail__failed": "Send notification to buyer mail failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__sendNotificationToBuyerMail__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__sendNotificationToBuyerMail__parameter__user": "User", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__synchronizeDisplayStatus": "Synchronize display status", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__synchronizeDisplayStatus__failed": "Synchronize display status failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__mutation__synchronizeDisplayStatus__parameter__purchaseCreditMemo": "Purchase credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__node_name": "Purchase credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__posted": "The purchase credit memo has been posted.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__apOpenItems": "Ap open items", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billByAddress": "Bill by address", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billByContact": "Bill by contact", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billByLinkedAddress": "Bill by linked address", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__businessRelation": "Business relation", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountExcludingTax": "Calculated total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountExcludingTaxInCompanyCurrency": "Calculated total amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountIncludingTax": "Calculated total amount including tax", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalAmountIncludingTaxInCompanyCurrency": "Calculated total amount including tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalExemptAmount": "Calculated total exempt amount", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalTaxableAmount": "Calculated total taxable amount", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalTaxAmount": "Calculated total tax amount", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__calculatedTotalTaxAmountAdjusted": "Calculated total tax amount adjusted", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__creditMemoDate": "Credit memo date", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__date": "Date", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__displayStatus": "Display status", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__documentDate": "Document date", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__dueDate": "Due date", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__fxRateDate": "Fx rate date", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__isOpenItemPageOptionActive": "Is open item page option active", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__matchingUser": "Matching user", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__openItemSysId": "Open item sys id", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__page": "Page", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__paymentTerm": "Payment term", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToAddress": "Pay to address", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToContact": "Pay to contact", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToLinkedAddress": "Pay to linked address", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__payToSupplier": "Pay to supplier", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__pdfSupplierCreditMemo": "Pdf supplier credit memo", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__postingDetails": "Posting details", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__reason": "Reason", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__supplierDocumentDate": "Supplier document date", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__totalTaxAmountAdjusted": "Total tax amount adjusted", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__varianceTotalAmountExcludingTax": "Variance total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__varianceTotalAmountIncludingTax": "Variance total amount including tax", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__property__varianceTotalTaxAmount": "Variance total tax amount", "@sage/xtrem-purchasing/nodes__purchase_credit_memo__update_forbidden_credit_memo_posted": "You cannot update the credit memo. It is already posted.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__mutation__calculateLineTaxes": "Calculate line taxes", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__mutation__calculateLineTaxes__failed": "Calculate line taxes failed.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__mutation__calculateLineTaxes__parameter__data": "Data", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__new_credit_memo_line_not_allowed": "You are not allowed to add new lines if credit memo is in a posted status.", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__node_name": "Purchase credit memo line", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__canHaveLandedCost": "Can have landed cost", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__consumptionLinkedAddress": "Consumption linked address", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__grossPrice": "Gross price", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__itemDescription": "Item description", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__origin": "Origin", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__priceOrigin": "Price origin", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__providerLinkedAddress": "Provider linked address", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__purchaseInvoiceLine": "Purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__purchaseReturnLine": "Purchase return line", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__receiptNumber": "Receipt number", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__receiptSysId": "Receipt sys id", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__recipientSite": "Recipient site", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__signedAmountExcludingTax": "Signed amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__signedAmountExcludingTaxInCompanyCurrency": "Signed amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__signedQuantity": "Signed quantity", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__sourceDocumentSysId": "Source document sys id", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__taxDate": "Tax date", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__uPurchaseUnit": "U purchase unit", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__property__uStockUnit": "U stock unit", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__node_name": "Purchase credit memo line discount charge", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_credit_memo_line_discount_charge__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_document__deletion_forbidden_reason_status": "The current purchase document cannot be deleted.", "@sage/xtrem-purchasing/nodes__purchase_document_line__is_inventory_is_purchasing": "The receiving site needs to be either stock or purchasing.", "@sage/xtrem-purchasing/nodes__purchase_document_line__item_must_be_same": "The item cannot be changed.", "@sage/xtrem-purchasing/nodes__purchase_document_line__no_purchase_unit_conversion_coefficient": "The current purchase unit has no conversion factor for the previous purchase unit of the line.", "@sage/xtrem-purchasing/nodes__purchase_document_line__no_stock_unit_conversion_coefficient": "The current purchase unit has no conversion factor for the item stock unit.", "@sage/xtrem-purchasing/nodes__purchase_document_line__purchase_unit_must_be_same": "The purchase unit cannot be changed.", "@sage/xtrem-purchasing/nodes__purchase_document_line__receipt_site_must_be_same_as_receipt": "The receiving site needs to be the same as the receipt site linked to invoice line.", "@sage/xtrem-purchasing/nodes__purchase_invoice__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_invoice__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_invoice__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_when_receipt_line_not_completed": "The purchase invoice cannot be posted. The stock status of all related purchase receipt lines needs to be Completed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_when_totals_not_equal": "The user entered total tax excluded amount is not equal to the sum of the lines.", "@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_whitout_lines": "You need to add lines to the purchase invoice before you can post it.", "@sage/xtrem-purchasing/nodes__purchase_invoice__information_payment_tracking": "Update the open items from the AP/AR aging balance", "@sage/xtrem-purchasing/nodes__purchase_invoice__invalid_variance_approval": "Invalid status for approval.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances": "Accept all variances", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances__failed": "Accept all variances failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__acceptAllVariances__parameter__matchingStatusToUpdate": "Matching status to update", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice": "Create credit memo from invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__failed": "Create credit memo from invoice failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__reasonCode": "Reason code", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__supplierDocumentDate": "Supplier document date", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__createCreditMemoFromInvoice__parameter__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__financeIntegrationCheck__parameter__invoice": "Invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__post": "Post", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__post__failed": "Post failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__post__parameter__purchaseInvoice": "Purchase invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__repost": "Repost", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__repost__failed": "Repost failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__repost__parameter__documentData": "Document data", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__repost__parameter__purchaseInvoice": "Purchase invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resendNotificationForFinance__parameter__purchaseInvoice": "Purchase invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resynchronizeDisplayStatus": "Resynchronize display status", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resynchronizeDisplayStatus__failed": "Resynchronize display status failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__resynchronizeDisplayStatus__parameter__purchaseInvoice": "Purchase invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__sendNotificationToBuyerMail": "Send notification to buyer mail", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__sendNotificationToBuyerMail__failed": "Send notification to buyer mail failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__sendNotificationToBuyerMail__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_invoice__mutation__sendNotificationToBuyerMail__parameter__user": "User", "@sage/xtrem-purchasing/nodes__purchase_invoice__node_name": "Purchase invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__payment_tracking": "Payment tracking service option activated", "@sage/xtrem-purchasing/nodes__purchase_invoice__posted": "The purchase invoice posted.", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__apOpenItems": "Ap open items", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billByAddress": "Bill by address", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billByContact": "Bill by contact", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billByLinkedAddress": "Bill by linked address", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__businessRelation": "Business relation", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountExcludingTax": "Calculated total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountExcludingTaxInCompanyCurrency": "Calculated total amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountIncludingTax": "Calculated total amount including tax", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalAmountIncludingTaxInCompanyCurrency": "Calculated total amount including tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalExemptAmount": "Calculated total exempt amount", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalRemainingQuantityToCredit": "Calculated total remaining quantity to credit", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalTaxableAmount": "Calculated total taxable amount", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalTaxAmount": "Calculated total tax amount", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__calculatedTotalTaxAmountAdjusted": "Calculated total tax amount adjusted", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__companyCurrency": "Company currency", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__date": "Date", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__displayStatus": "Display status", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__documentDate": "Document date", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__dueDate": "Due date", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__financeStatus": "Finance status", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__fxRateDate": "Fx rate date", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__invoiceDate": "Invoice date", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__isOpenItemPageOptionActive": "Is open item page option active", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__matchingStatus": "Matching status", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__matchingUser": "Matching user", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__openItemSysId": "Open item sys id", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__page": "Page", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__paymentTerm": "Payment term", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToAddress": "Pay to address", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToContact": "Pay to contact", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToLinkedAddress": "Pay to linked address", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__payToSupplier": "Pay to supplier", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__pdfSupplierInvoice": "Pdf supplier invoice", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__postingDetails": "Posting details", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__returnLinkedAddress": "Return linked address", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__supplierDocumentDate": "Supplier document date", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__totalAmountIncludingTax": "Total amount including tax", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__totalTaxAmountAdjusted": "Total tax amount adjusted", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__transactionCurrency": "Transaction currency", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__varianceTotalAmountExcludingTax": "Variance total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__varianceTotalAmountIncludingTax": "Variance total amount including tax", "@sage/xtrem-purchasing/nodes__purchase_invoice__property__varianceTotalTaxAmount": "Variance total tax amount", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor": "Get rate or reverse divisor", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor__failed": "Get rate or reverse divisor failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor__parameter__destinationCurrencyId": "Destination currency id", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor__parameter__rateDate": "Rate date", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseDivisor__parameter__sourceCurrencyId": "Source currency id", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate": "Get rate or reverse rate", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate__failed": "Get rate or reverse rate failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate__parameter__destinationCurrencyId": "Destination currency id", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate__parameter__rateDate": "Rate date", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__getRateOrReverseRate__parameter__sourceCurrencyId": "Source currency id", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__isStatusValidForVarianceApproval": "Is status valid for variance approval", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__isStatusValidForVarianceApproval__failed": "Is status valid for variance approval failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__isStatusValidForVarianceApproval__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription": "Rate description", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__failed": "Rate description failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__companyFxRate": "Company fx rate", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__companyFxRateDivisor": "Company fx rate divisor", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__destinationCurrencyId": "Destination currency id", "@sage/xtrem-purchasing/nodes__purchase_invoice__query__rateDescription__parameter__sourceCurrencyId": "Source currency id", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__allocated_purchase_receipt_line_not_completed": "The purchase receipt lines allocated to landed cost need to have the stock status Completed.", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__allocated_receipt_lines": "Purchase receipts allocated but not completed", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__insufficient_allocations": "Allocated amount insufficient", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__invalid_variance_approval": "Invalid approval status", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__landed_cost_not_fully_allocated": "The total allocated amount ({{currencySymbol}}{{allocatedAmount}}) cannot be less than the landed cost amount to allocate ({{currencySymbol}}{{amountToAllocate}}).", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__landed_cost_zero_allocation": "You need to enter an allocated amount greater than zero for each allocated document.", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__missing_amount_allocation": "Allocated amount missing", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine": "Accept all variances line", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine__failed": "Accept all variances line failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine__parameter__approve": "Approve", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine__parameter__purchaseInvoiceId": "Purchase invoice id", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__acceptAllVariancesLine__parameter__purchaseInvoiceLineId": "Purchase invoice line id", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__calculateLineTaxes": "Calculate line taxes", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__calculateLineTaxes__failed": "Calculate line taxes failed.", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__mutation__calculateLineTaxes__parameter__data": "Data", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__node_name": "Purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__only__applicative_users": "Only applicative users can approve variances.", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__canHaveLandedCost": "Can have landed cost", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__consumptionLinkedAddress": "Consumption linked address", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__creditedQuantity": "Credited quantity", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__landedCostCheckResult": "Landed cost check result", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__matchingStatus": "Matching status", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__origin": "Origin", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__priceOrigin": "Price origin", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__providerLinkedAddress": "Provider linked address", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseCreditMemoLines": "Purchase credit memo lines", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseReceiptLine": "Purchase receipt line", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__purchaseReturnLines": "Purchase return lines", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__recipientSite": "Recipient site", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__remainingQuantityToCredit": "Remaining quantity to credit", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__sourceDocumentSysId": "Source document sys id", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__taxAmount": "Tax amount", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__taxDate": "Tax date", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__varianceApprover": "Variance approver", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__varianceText": "Variance text", "@sage/xtrem-purchasing/nodes__purchase_invoice_line__property__varianceType": "Variance type", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__node_name": "Purchase invoice line to purchase credit memo line", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoAmount": "Credit memo amount", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoQuantity": "Credit memo quantity", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoQuantityInStockUnit": "Credit memo quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__creditMemoUnitPrice": "Credit memo unit price", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "Purchase credit memo line", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__purchaseInvoiceLine": "Purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__same_properties_invoice_to_credit_memo": "The purchase credit memo must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase invoice.", "@sage/xtrem-purchasing/nodes__purchase_order__already_exist_order_with_same_supplier_order_reference": "This order reference already exists for this supplier.", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders": "Create test purchase orders", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__failed": "Create test purchase orders failed.", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__fixedNumberOfLines": "Fixed number of lines", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__itemId": "Item id", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__itemQuantity": "Item quantity", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__numberOfLinesPerOrder": "Number of lines per order", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__orderNumberRoot": "Order number root", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__orderQuantity": "Order quantity", "@sage/xtrem-purchasing/nodes__purchase_order__asyncMutation__createTestPurchaseOrders__parameter__supplierId": "Supplier id", "@sage/xtrem-purchasing/nodes__purchase_order__bulkMutation__massApproval": "Mass approval", "@sage/xtrem-purchasing/nodes__purchase_order__bulkMutation__massApproval__failed": "Mass approval failed.", "@sage/xtrem-purchasing/nodes__purchase_order__bulkMutation__printBulk": "Print bulk", "@sage/xtrem-purchasing/nodes__purchase_order__bulkMutation__printBulk__failed": "Print bulk failed.", "@sage/xtrem-purchasing/nodes__purchase_order__cannot_close_order_because_of_a_link": "Remove the supply order links before closing the purchase order.", "@sage/xtrem-purchasing/nodes__purchase_order__cannot_create_receipt": "You need to remove the inactive items before you change the document status.", "@sage/xtrem-purchasing/nodes__purchase_order__invalid_approval_status": "Invalid status for approval.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__afterPrintPurchaseOrder": "After print purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__afterPrintPurchaseOrder__failed": "After print purchase order failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__afterPrintPurchaseOrder__parameter__order": "Order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve": "Approve", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve__failed": "Approve failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve__parameter__approvalStatusToUpdate": "Approval status to update", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__approve__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__beforePrintPurchaseOrder": "Before print purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__beforePrintPurchaseOrder__failed": "Before print purchase order failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__beforePrintPurchaseOrder__parameter__order": "Order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__close": "Close", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__close__failed": "Close failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__close__parameter__controlOrderLinks": "Control order links", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__close__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__close__parameter__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__closeLine": "Close line", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__closeLine__failed": "Close line failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__closeLine__parameter__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm": "Confirm", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm__failed": "Confirm failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm__parameter__confirmStatusToUpdate": "Confirm status to update", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__confirm__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseOrderReplenishment": "Create purchase order replenishment", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseOrderReplenishment__failed": "Create purchase order replenishment failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseOrderReplenishment__parameter__data": "Data", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseReceipt": "Create purchase receipt", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseReceipt__failed": "Create purchase receipt failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__createPurchaseReceipt__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__financeIntegrationCheck__parameter__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__open": "Open", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__open__failed": "Open failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__open__parameter__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail": "Print purchase order and email", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__failed": "Print purchase order and email failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactEmail": "Contact email", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactFirstName": "Contact first name", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactLastName": "Contact last name", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__contactTitle": "Contact title", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__printPurchaseOrderAndEmail__parameter__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__reopenLine": "Reopen line", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__reopenLine__failed": "Reopen line failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__reopenLine__parameter__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost": "Repost", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__failed": "Repost failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__financeTransaction": "Finance transaction", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__orderLines": "Order lines", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__repost__parameter__saveOnly": "Save only", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__sendRequestChangesMail": "Send request changes mail", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__sendRequestChangesMail__failed": "Send request changes mail failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__sendRequestChangesMail__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__sendRequestChangesMail__parameter__user": "User", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__setIsSentPurchaseOrder": "Set is sent purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__setIsSentPurchaseOrder__failed": "Set is sent purchase order failed.", "@sage/xtrem-purchasing/nodes__purchase_order__mutation__setIsSentPurchaseOrder__parameter__purchaseOrder": "Purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__node_name": "Purchase order", "@sage/xtrem-purchasing/nodes__purchase_order__order_date_cannot_be_future": "The order date cannot be later than today.", "@sage/xtrem-purchasing/nodes__purchase_order__print_error": "You need to correct the order details before you can print the order.", "@sage/xtrem-purchasing/nodes__purchase_order__print_tax_calculation_failed": "The tax calculation for this order failed. You can print this order after the tax details are corrected.", "@sage/xtrem-purchasing/nodes__purchase_order__property__approvalStatus": "Approval status", "@sage/xtrem-purchasing/nodes__purchase_order__property__approvalUrl": "Approval url", "@sage/xtrem-purchasing/nodes__purchase_order__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__purchase_order__property__businessRelation": "Business relation", "@sage/xtrem-purchasing/nodes__purchase_order__property__changeRequestedDescription": "Change requested description", "@sage/xtrem-purchasing/nodes__purchase_order__property__companyCurrency": "Company currency", "@sage/xtrem-purchasing/nodes__purchase_order__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_order__property__date": "Date", "@sage/xtrem-purchasing/nodes__purchase_order__property__defaultBuyer": "Default buyer", "@sage/xtrem-purchasing/nodes__purchase_order__property__deliveryMode": "Delivery mode", "@sage/xtrem-purchasing/nodes__purchase_order__property__displayStatus": "Display status", "@sage/xtrem-purchasing/nodes__purchase_order__property__documentDate": "Document date", "@sage/xtrem-purchasing/nodes__purchase_order__property__earliestExpectedDate": "Earliest expected date", "@sage/xtrem-purchasing/nodes__purchase_order__property__fxRateDate": "Fx rate date", "@sage/xtrem-purchasing/nodes__purchase_order__property__invoiceStatus": "Invoice status", "@sage/xtrem-purchasing/nodes__purchase_order__property__isApprovalManaged": "Is approval managed", "@sage/xtrem-purchasing/nodes__purchase_order__property__isClosedOrReceived": "Is closed or received", "@sage/xtrem-purchasing/nodes__purchase_order__property__isGrossPriceMissing": "Is gross price missing", "@sage/xtrem-purchasing/nodes__purchase_order__property__isOrderAssignmentLinked": "Is order assignment linked", "@sage/xtrem-purchasing/nodes__purchase_order__property__isPurchaseOrderSuggestion": "Is purchase order suggestion", "@sage/xtrem-purchasing/nodes__purchase_order__property__jsonAggregateLandedCostTypes": "Json aggregate landed cost types", "@sage/xtrem-purchasing/nodes__purchase_order__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__purchase_order__property__orderDate": "Order date", "@sage/xtrem-purchasing/nodes__purchase_order__property__page": "Page", "@sage/xtrem-purchasing/nodes__purchase_order__property__paymentTerm": "Payment term", "@sage/xtrem-purchasing/nodes__purchase_order__property__receiptStatus": "Receipt status", "@sage/xtrem-purchasing/nodes__purchase_order__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_order__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_order__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierAddress": "Supplier address", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierContact": "Supplier contact", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierLinkedAddress": "Supplier linked address", "@sage/xtrem-purchasing/nodes__purchase_order__property__supplierOrderReference": "Supplier order reference", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalAmountIncludingTaxInCompanyCurrency": "Total amount including tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_order__property__totalQuantityToReceiveInStockUnit": "Total quantity to receive in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order__property__transactionCurrency": "Transaction currency", "@sage/xtrem-purchasing/nodes__purchase_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_order_line__expected_receipt_date_to_order_date": "The expected receipt date must be later than the order date.", "@sage/xtrem-purchasing/nodes__purchase_order_line__mutation__calculateLineTaxes": "Calculate line taxes", "@sage/xtrem-purchasing/nodes__purchase_order_line__mutation__calculateLineTaxes__failed": "Calculate line taxes failed.", "@sage/xtrem-purchasing/nodes__purchase_order_line__mutation__calculateLineTaxes__parameter__data": "Data", "@sage/xtrem-purchasing/nodes__purchase_order_line__node_name": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__amountForLandedCostAllocation": "Amount for landed cost allocation", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__assignments": "Assignments", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__canHaveLandedCostLine": "Can have landed cost line", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__changeRequestedDescription": "Change requested description", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__expectedReceiptDate": "Expected receipt date", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__invoicedQuantityInStockUnit": "Invoiced quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__isPurchaseOrderSuggestion": "Is purchase order suggestion", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__isUsingFunctionToClose": "Is using function to close", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__lineInvoiceStatus": "Line invoice status", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__lineReceiptStatus": "Line receipt status", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__numberOfPurchaseInvoiceLines": "Number of purchase invoice lines", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__numberOfPurchaseReceiptLines": "Number of purchase receipt lines", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__origin": "Origin", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__postedQuantityReceivedInStockUnit": "Posted quantity received in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__purchaseInvoiceLines": "Purchase invoice lines", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__purchaseReceiptLines": "Purchase receipt lines", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__purchaseRequisitionLines": "Purchase requisition lines", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityInStockUnitForLandedCostAllocation": "Quantity in stock unit for landed cost allocation", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityReceivedInProgress": "Quantity received in progress", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityToReceive": "Quantity to receive", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__quantityToReceiveInStockUnit": "Quantity to receive in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__receivedQuantity": "Received quantity", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__receivedQuantityInStockUnit": "Received quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__receivedQuantityProgress": "Received quantity progress", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingAmountToReceiveExcludingTax": "Remaining amount to receive excluding tax", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingAmountToReceiveExcludingTaxInCompanyCurrency": "Remaining amount to receive excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingQuantityToInvoice": "Remaining quantity to invoice", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingQuantityToInvoiceOnOrder": "Remaining quantity to invoice on order", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__remainingQuantityToProcessForLandedCost": "Remaining quantity to process for landed cost", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__stockSiteAddress": "Stock site address", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__stockSiteContact": "Stock site contact", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__taxDate": "Tax date", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uDemandOrderLine": "U demand order line", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uDemandOrderLineLink": "U demand order line link", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uDemandOrderQuantity": "U demand order quantity", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__uSupplyOrderQuantity": "U supply order quantity", "@sage/xtrem-purchasing/nodes__purchase_order_line__property__workInProgress": "Work in progress", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlCloseLine": "Control close line", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlCloseLine__failed": "Control close line failed.", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlCloseLine__parameter__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlDelete": "Control delete", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlDelete__failed": "Control delete failed.", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__controlDelete__parameter__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime": "Get purchase lead time", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime__failed": "Get purchase lead time failed.", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime__parameter__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_order_line__query__getPurchaseLeadTime__parameter__supplier": "Supplier", "@sage/xtrem-purchasing/nodes__purchase_order_line__total_quantity_from_orders_equal_line_quantity": "The total received quantity is greater then the order line quantity.", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__node_name": "Purchase order line to purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedAmount": "Invoiced amount", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedQuantityInStockUnit": "Invoiced quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__invoicedUnitPrice": "Invoiced unit price", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "Purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_invoice_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__node_name": "Purchase order line to purchase receipt line", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__purchaseReceiptLine": "Purchase receipt line", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__receivedQuantity": "Received quantity", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__receivedQuantityInStockUnit": "Received quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_order_line_to_purchase_receipt_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_order_purchase_invoice_line__deletion_forbidden_reason_status": "The invoice line cannot be deleted as it is in a posted status.", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_invoice__same_properties_invoice_to_order": "The purchase invoice must have the same values for the following properties: \n {{differentProperties}}", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_completed": "A purchase receipt line of the current purchase order line is completed. You cannot add additional receipt lines to this purchase order line.", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_convert_draft": "You need to post all receipt lines before you can define this line as complete.", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_draft": "You need to post all receipt lines before you can add a complete receipt line.", "@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__same_properties_order_to_receipt": "The purchase receipt must have the same values for the following properties: \n {{differentProperties}}", "@sage/xtrem-purchasing/nodes__purchase_receipt__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_receipt__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_receipt__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_receipt__invoices_must_be_posted_first": "You need to post all the landed cost purchase invoices associated with the purchase order lines received before you can post the receipt.\n\n{{invoices}}", "@sage/xtrem-purchasing/nodes__purchase_receipt__line__no_inventory_site": "No stock site for the company: {{legalCompanyName}}.", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__beforePrintPurchaseReceipt": "Before print purchase receipt", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__beforePrintPurchaseReceipt__failed": "Before print purchase receipt failed.", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__beforePrintPurchaseReceipt__parameter__receipt": "Receipt", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__createPurchaseReturns": "Create purchase returns", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__createPurchaseReturns__failed": "Create purchase returns failed.", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__createPurchaseReturns__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__financeIntegrationCheck__parameter__purchaseReceipt": "Purchase receipt", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__financeIntegrationCheck__parameter__receiptNumber": "Receipt number", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost": "Repost", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__failed": "Repost failed.", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__financeTransaction": "Finance transaction", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__landedCostControl": "Landed cost control", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__purchaseReceipt": "Purchase receipt", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__receiptLines": "Receipt lines", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__repost__parameter__saveOnly": "Save only", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-purchasing/nodes__purchase_receipt__mutation__resendNotificationForFinance__parameter__purchaseReceipt": "Purchase receipt", "@sage/xtrem-purchasing/nodes__purchase_receipt__node_name": "Purchase receipt", "@sage/xtrem-purchasing/nodes__purchase_receipt__only_inventory_sites_allowed": "The current site must be a stock site.", "@sage/xtrem-purchasing/nodes__purchase_receipt__print_error": "You need to correct the receipt details before you can print the receipt.", "@sage/xtrem-purchasing/nodes__purchase_receipt__print_tax_calculation_failed": "You need to resolve tax calculation issues before you can print the receipt.", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__businessRelation": "Business relation", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__carrier": "Carrier", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__companyCurrency": "Company currency", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__date": "Date", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__displayStatus": "Display status", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__documentDate": "Document date", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__fxRateDate": "Fx rate date", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__invoiceStatus": "Invoice status", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__isStockDetailRequired": "Is stock detail required", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__jsonAggregateLandedCostTypes": "Json aggregate landed cost types", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__paymentTerm": "Payment term", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__purchaseReturns": "Purchase returns", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__receiptDate": "Receipt date", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__receivingAddress": "Receiving address", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__returnAddress": "Return address", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__returnStatus": "Return status", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_receipt__property__totalAmountIncludingTaxInCompanyCurrency": "Total amount including tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__mutation__calculateLineTaxes": "Calculate line taxes", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__mutation__calculateLineTaxes__failed": "Calculate line taxes failed.", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__mutation__calculateLineTaxes__parameter__data": "Data", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__node_name": "Purchase receipt line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__canHaveLandedCostLine": "Can have landed cost line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__charge": "Charge", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__completed": "Completed", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__discount": "Discount", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__financialSite": "Financial site", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__grossPrice": "Gross price", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__invoicedQuantityInStockUnit": "Invoiced quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__jsonStockDetails": "Json stock details", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__lineInvoiceStatus": "Line invoice status", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__lineReturnStatus": "Line return status", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__lineStatus": "Line status", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__numberOfPurchaseInvoiceLines": "Number of purchase invoice lines", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__orderCost": "Order cost", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__origin": "Origin", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__priceOrigin": "Price origin", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__purchaseInvoiceLines": "Purchase invoice lines", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__purchaseReturnLines": "Purchase return lines", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__quantity": "Quantity", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__remainingQuantityInStockUnit": "Remaining quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__remainingQuantityToInvoice": "Remaining quantity to invoice", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__remainingReturnQuantity": "Remaining return quantity", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__returnAddress": "Return address", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__returnedQuantity": "Returned quantity", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__returnedQuantityInStockUnit": "Returned quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockDetails": "Stock details", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockDetailStatus": "Stock detail status", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockMovements": "Stock movements", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockSiteLinkedAddress": "Stock site linked address", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__transientPurchaseOrderLine": "Transient purchase order line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__property__workInProgress": "Work in progress", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__total_invoiced_exceeds_lines_received_quantity": "The total invoiced quantity is greater than the received stock quantity.", "@sage/xtrem-purchasing/nodes__purchase_receipt_line__total_returned_exceeds_lines_received_quantity": "The total of returned quantity must be equal or less than the received stock quantity.", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__node_name": "Purchase receipt line to purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__amountExcludingTax": "Amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__invoicedQuantityInStockUnit": "Invoiced quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__price": "Price", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "Purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__purchaseReceiptLine": "Purchase receipt line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_invoice_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__node_name": "Purchase receipt line to purchase return line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__purchaseReceiptLine": "Purchase receipt line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__purchaseReturnLine": "Purchase return line", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__returnedQuantity": "Returned quantity", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__returnedQuantityInStockUnit": "Returned quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__provided_stock_quantity_error": "The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.", "@sage/xtrem-purchasing/nodes__purchase_receipt_post__already_done": "The purchase receipt was already posted.", "@sage/xtrem-purchasing/nodes__purchase_receipt_post__stock_details_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-purchasing/nodes__purchase_receipt_post__tax_calculation_failed": "You need to correct lines that failed the tax calculation before you can post.", "@sage/xtrem-purchasing/nodes__purchase_receipt_purchase_invoice_line__deletion_forbidden_reason_status": "The purchase invoice line cannot be deleted. The invoice is already posted", "@sage/xtrem-purchasing/nodes__purchase_receipt_purchase_invoice_line__stock_transaction_status_not_completed": "The stock status of the purchase receipt line needs to be Completed.", "@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_invoice__same_properties_invoice_to_receipt": "The purchase invoice must have the same values for the following properties: \n {{differentProperties}}", "@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_return__receipt_line_status_error": "You need to select a purchase receipt line that is at Pending, In progress or Closed status.", "@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_return__receipt_status_error": "You need to select a purchase receipt that is at Pending, In progress or Closed status.", "@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_return__same_properties_receipt_to_return": "The purchase return must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase receipt.", "@sage/xtrem-purchasing/nodes__purchase_requisition__approval_status_change_forbidden": "The approval status can only be changed for \"Draft\" documents", "@sage/xtrem-purchasing/nodes__purchase_requisition__approved_status__line_control": "The purchase requisition is approved. You cannot add or delete a line. You can only edit certain fields.", "@sage/xtrem-purchasing/nodes__purchase_requisition__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_requisition__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_requisition__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_requisition__cannot_update_incorrect_status_approval_workflow": "You cannot use this status if the approval workflow is disabled", "@sage/xtrem-purchasing/nodes__purchase_requisition__cannot_update_incorrect_status_confirmation_workflow": "You cannot use this status if the approval workflow is enabled", "@sage/xtrem-purchasing/nodes__purchase_requisition__invalid_status": "Invalid status for approval.", "@sage/xtrem-purchasing/nodes__purchase_requisition__item_mandatory_on_lines": "The purchase requisition can be ordered only if all lines have an associated item.", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__approve": "Approve", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__approve__failed": "Approve failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__approve__parameter__approve": "Approve", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__approve__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__close": "Close", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__close__failed": "Close failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__close__parameter__purchaseRequisition": "Purchase requisition", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__confirm": "Confirm", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__confirm__failed": "Confirm failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__confirm__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__confirm__parameter__isSafeToRetry": "Is safe to retry", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__createPurchaseOrders": "Create purchase orders", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__createPurchaseOrders__failed": "Create purchase orders failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__createPurchaseOrders__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__sendRequestChangesMail": "Send request changes mail", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__sendRequestChangesMail__failed": "Send request changes mail failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__sendRequestChangesMail__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_requisition__mutation__sendRequestChangesMail__parameter__user": "User", "@sage/xtrem-purchasing/nodes__purchase_requisition__node_name": "Purchase requisition", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__approvalStatus": "Approval status", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__approvalUrl": "Approval url", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__changeRequestedDescription": "Change requested description", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__date": "Date", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__displayStatus": "Display status", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__documentDate": "Document date", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__internalNote": "Internal note", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isApplyDefaultSupplierHidden": "Is apply default supplier hidden", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isCreateOrderLinesHidden": "Is create order lines hidden", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isSetDimensionHidden": "Is set dimension hidden", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__orderStatus": "Order status", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__page": "Page", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__receivingSite": "Receiving site", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__requestDate": "Request date", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__requester": "Requester", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_requisition__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_requisition__request_date_cannot_be_future": "The request date cannot be later than today.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__bad_conversion_factor": "The converted quantity {{convertedQuantity}} from {{quantity}} in {{unit}} to quantity in {{stockUnit}} using the conversion factor {{conversionFactor}} is different than the current quantity in stock unit {{quantityInStockUnit}}.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__gross_price_currency_mandatory": "The gross price currency is mandatory.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier": "Apply default supplier", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__failed": "Apply default supplier failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__grossPrice": "Gross price", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__netPrice": "Net price", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__priceOrigin": "Price origin", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__purchaseRequisitionLine": "Purchase requisition line", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__quantity": "Quantity", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__supplier": "Supplier", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__applyDefaultSupplier__parameter__totalTaxExcludedAmount": "Total tax excluded amount", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__closeLine": "Close line", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__closeLine__failed": "Close line failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__mutation__closeLine__parameter__purchaseRequisitionLine": "Purchase requisition line", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__need_by_date_inferior_to_request_date": "The need by date should not be before the requisition request date", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__no_purchase_unit_conversion_coefficient": "The current purchase unit has no conversion factor for the previous purchase unit of the line.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__node_name": "Purchase requisition line", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__analyticalData": "Analytical data", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__approvalStatus": "Approval status", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__changeRequestedDescription": "Change requested description", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__charge": "Charge", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__discount": "Discount", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__discountCharges": "Discount charges", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__grossPrice": "Gross price", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__itemSite": "Item site", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__itemSiteSupplier": "Item site supplier", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__itemSupplier": "Item supplier", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__lineOrderStatus": "Line order status", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__lineStatus": "Line status", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__needByDate": "Need by date", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__netPrice": "Net price", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__orderedPercentage": "Ordered percentage", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__orderedQuantity": "Ordered quantity", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__orderedQuantityInStockUnit": "Ordered quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__priceOrigin": "Price origin", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__purchaseOrderLines": "Purchase order lines", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantity": "Quantity", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantityToOrder": "Quantity to order", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__quantityToOrderInStockUnit": "Quantity to order in stock unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__receivingSite": "Receiving site", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__requestedItemDescription": "Requested item description", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__stockSiteLinkedAddress": "Stock site linked address", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__supplier": "Supplier", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__totalTaxExcludedAmount": "Total tax excluded amount", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__purchase_unit_mandatory_if_no_item": "The purchase unit is mandatory if no item is provided.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList": "Get filtered list", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList__failed": "Get filtered list failed.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList__parameter__currencyId": "Currency id", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList__parameter__siteId": "Site id", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__query__getFilteredList__parameter__supplierId": "Supplier id", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__requested_item_description_mandatory": "Requested item description is mandatory if no item provided!", "@sage/xtrem-purchasing/nodes__purchase_requisition_line__total_quantity_from_orders_equal_line_quantity": "The total ordered quantity is greater then the requisition line quantity.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_delete__line": "You cannot delete the purchase requisition line.", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__node_name": "Purchase requisition line discount charge", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_discount_charge__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__node_name": "Purchase requisition line to purchase order line", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__orderedQuantity": "Ordered quantity", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__orderedQuantityInStockUnit": "Ordered quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__purchaseRequisitionLine": "Purchase requisition line", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__provided_stock_quantity_error": "The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_currency": "currency", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_item": "item", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_purchase_unit": "purchase unit", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_supplier": "supplier", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_unit_conversion_factor": "unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__error_updating_requisition_status": "Fail updating the status on the requisition line: {{errors}}", "@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__same_properties_order_to_receipt": "The purchase order needs to have the same values for the following properties: {{differentProperties}}", "@sage/xtrem-purchasing/nodes__purchase_return__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_return__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_return__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_return__cant_repost_purchase_return_when_status_is_not_failed": "You can only repost a purchase return if the status is 'Failed.'", "@sage/xtrem-purchasing/nodes__purchase_return__deletion_forbidden_reason_status": "The current purchase return cannot be deleted.", "@sage/xtrem-purchasing/nodes__purchase_return__document_was_posted": "The purchase return was posted.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__approve": "Approve", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__approve__failed": "Approve failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__approve__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__approve__parameter__toBeApproved": "To be approved", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__close": "Close", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__close__failed": "Close failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__close__parameter__returnDoc": "Return doc", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__confirm": "Confirm", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__confirm__failed": "Confirm failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__confirm__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__createPurchaseInvoice": "Create purchase invoice", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__createPurchaseInvoice__failed": "Create purchase invoice failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__createPurchaseInvoice__parameter__returnDoc": "Return doc", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__financeIntegrationCheck": "Finance integration check", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__financeIntegrationCheck__failed": "Finance integration check failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__financeIntegrationCheck__parameter__purchaseReturn": "Purchase return", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__post": "Post", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__post__failed": "Post failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__post__parameter__purchaseReturn": "Purchase return", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__repost": "Repost", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__repost__failed": "Repost failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__repost__parameter__documentLines": "Document lines", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__repost__parameter__purchaseReturn": "Purchase return", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__resendNotificationForFinance": "Resend notification for finance", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__resendNotificationForFinance__failed": "Resend notification for finance failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__resendNotificationForFinance__parameter__purchaseReturn": "Purchase return", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__submitForApproval": "Submit for approval", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__submitForApproval__failed": "Submit for approval failed.", "@sage/xtrem-purchasing/nodes__purchase_return__mutation__submitForApproval__parameter__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_return__node_name": "Purchase return", "@sage/xtrem-purchasing/nodes__purchase_return__post__allocation_status": "You need to allocate stock to all lines before you can post.", "@sage/xtrem-purchasing/nodes__purchase_return__property__allocationStatus": "Allocation status", "@sage/xtrem-purchasing/nodes__purchase_return__property__approvalStatus": "Approval status", "@sage/xtrem-purchasing/nodes__purchase_return__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__purchase_return__property__businessRelation": "Business relation", "@sage/xtrem-purchasing/nodes__purchase_return__property__changeRequestDescription": "Change request description", "@sage/xtrem-purchasing/nodes__purchase_return__property__companyCurrency": "Company currency", "@sage/xtrem-purchasing/nodes__purchase_return__property__creditStatus": "Credit status", "@sage/xtrem-purchasing/nodes__purchase_return__property__date": "Date", "@sage/xtrem-purchasing/nodes__purchase_return__property__displayStatus": "Display status", "@sage/xtrem-purchasing/nodes__purchase_return__property__documentDate": "Document date", "@sage/xtrem-purchasing/nodes__purchase_return__property__effectiveDate": "Effective date", "@sage/xtrem-purchasing/nodes__purchase_return__property__fxRateDate": "Fx rate date", "@sage/xtrem-purchasing/nodes__purchase_return__property__invoiceStatus": "Invoice status", "@sage/xtrem-purchasing/nodes__purchase_return__property__isApprovalManaged": "Is approval managed", "@sage/xtrem-purchasing/nodes__purchase_return__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__purchase_return__property__postingDetails": "Posting details", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnItems": "Return items", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnRequestDate": "Return request date", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnSite": "Return site", "@sage/xtrem-purchasing/nodes__purchase_return__property__returnToAddress": "Return to address", "@sage/xtrem-purchasing/nodes__purchase_return__property__shippingStatus": "Shipping status", "@sage/xtrem-purchasing/nodes__purchase_return__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_return__property__siteAddress": "Site address", "@sage/xtrem-purchasing/nodes__purchase_return__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_return__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__purchase_return__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-purchasing/nodes__purchase_return__property__supplierAddress": "Supplier address", "@sage/xtrem-purchasing/nodes__purchase_return__property__supplierDocumentNumber": "Supplier document number", "@sage/xtrem-purchasing/nodes__purchase_return__property__supplierReturnReference": "Supplier return reference", "@sage/xtrem-purchasing/nodes__purchase_return__property__totalAmountExcludingTax": "Total amount excluding tax", "@sage/xtrem-purchasing/nodes__purchase_return__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_return__property__totalAmountIncludingTaxInCompanyCurrency": "Total amount including tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_return__property__transactionCurrency": "Transaction currency", "@sage/xtrem-purchasing/nodes__purchase_return_line__allocated_exceeds_returned_quantity": "The allocated quantity must be equal or less than the returned stock quantity.", "@sage/xtrem-purchasing/nodes__purchase_return_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_return_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_return_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_return_line__bad_conversion_factor": "The quantity in stock unit ({{quantityInStockUnit}}) does not match the quantity converted from {{quantity}}\n            {{unit}} to {{convertedQuantity}} {{stockUnit}} with a conversion factor of {{conversionFactor}}.", "@sage/xtrem-purchasing/nodes__purchase_return_line__expected_return_date_inferior_to_return_date": "The expected return date should not be before the return date", "@sage/xtrem-purchasing/nodes__purchase_return_line__node_name": "Purchase return line", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__allocationStatus": "Allocation status", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__approvalStatus": "Approval status", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__calculatedTotalAmountExcludingTaxInCompanyCurrency": "Calculated total amount excluding tax in company currency", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__computedAttributes": "Computed attributes", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__creditedQuantity": "Credited quantity", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__creditedQuantityInStockUnit": "Credited quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__document": "Document", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__expectedReturnDate": "Expected return date", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__invoicedQuantityInStockUnit": "Invoiced quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__lineCreditStatus": "Line credit status", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__lineInvoiceStatus": "Line invoice status", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__origin": "Origin", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__purchaseCreditMemoLines": "Purchase credit memo lines", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__purchaseInvoiceLines": "Purchase invoice lines", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__purchaseReceiptLine": "Purchase receipt line", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityToInvoice": "Quantity to invoice", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__quantityToInvoiceInStockUnit": "Quantity to invoice in stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__reason": "Reason", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__remainingQuantityToCredit": "Remaining quantity to credit", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__shippedStatus": "Shipped status", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__sourceDocumentNumber": "Source document number", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__sourceDocumentType": "Source document type", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__status": "Status", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockAllocations": "Stock allocations", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockDetails": "Stock details", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockMovements": "Stock movements", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__storedAttributes": "Stored attributes", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__storedDimensions": "Stored dimensions", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__taxDate": "Tax date", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__totalTaxExcludedAmount": "Total tax excluded amount", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__uiTaxes": "Ui taxes", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_return_line__property__workInProgress": "Work in progress", "@sage/xtrem-purchasing/nodes__purchase_return_line__total_invoiced_exceeds_lines_returned_quantity": "The total of invoiced quantity must be equal or less than the returned stock quantity.", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__node_name": "Purchase return line to purchase credit memo line", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoAmount": "Credit memo amount", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoQuantity": "Credit memo quantity", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoQuantityInStockUnit": "Credit memo quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__creditMemoUnitPrice": "Credit memo unit price", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__purchaseCreditMemoLine": "Purchase credit memo line", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__purchaseReturnLine": "Purchase return line", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_credit_memo_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__node_name": "Purchase return line to purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__invoicedQuantityInStockUnit": "Invoiced quantity in stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__purchaseInvoiceLine": "Purchase invoice line", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__purchaseReturnLine": "Purchase return line", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__purchaseUnitToStockUnitConversionFactor": "Purchase unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__stockUnit": "Stock unit", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__unit": "Unit", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__provided_stock_quantity_error": "The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.", "@sage/xtrem-purchasing/nodes__purchase_return_status_draft": "Cannot revert purchase return status back to draft.", "@sage/xtrem-purchasing/nodes__purchase_return_status_inconsistent": "You cannot change the status of the purchase return to the status you selected.", "@sage/xtrem-purchasing/nodes__purchase_return_to_purchase_credit_memo__same_properties_return_to_credit_memo": "The purchase credit memo must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase return.", "@sage/xtrem-purchasing/nodes__purchase_return_to_purchase_invoice__same_properties_return_to_invoice": "The purchase invoice must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase return.", "@sage/xtrem-purchasing/nodes__purchase-invoice__cant_create_credit_memo": "You need to post the purchase invoice before you create the credit memo.", "@sage/xtrem-purchasing/nodes__purchase-invoice__cant_repost_purchasing_invoice_when_status_is_not_failed": "You can only repost a purchase invoice if the status is 'Failed.'", "@sage/xtrem-purchasing/nodes__purchase-invoice__document_was_posted": "The purchase invoice posted.", "@sage/xtrem-purchasing/nodes__purchase-order__cant_repost_purchase_order_when_status_is_not_failed": "You can only repost a purchase order if the status is 'Failed' or 'Not recorded.'", "@sage/xtrem-purchasing/nodes__purchase-order__document_was_posted": "The purchase order was posted.", "@sage/xtrem-purchasing/nodes__purchase-order__document_was_saved": "The purchase order was saved.", "@sage/xtrem-purchasing/nodes__purchase-receipt__cant_repost_purchase_receipt_when_status_is_not_failed": "You can only repost a purchase receipt if the status is 'Failed' or 'Not recorded.'", "@sage/xtrem-purchasing/nodes__purchase-receipt__document_was_posted": "The purchase receipt posted.", "@sage/xtrem-purchasing/nodes__purchase-receipt__document_was_saved": "The purchase receipt was saved.", "@sage/xtrem-purchasing/nodes__site_extension__check_pending_purchase_requisitions": "Pending purchase requisitions need to be handled before disabling the approval process", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__unbilledAccountPayableInquiry": "Unbilled account payable inquiry", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__unbilledAccountPayableInquiry__failed": "Unbilled account payable inquiry failed.", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__asyncMutation__unbilledAccountPayableInquiry__parameter__userId": "User id", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__node_name": "Unbilled account payable input set", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__asOfDate": "As of date", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__company": "Company", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__executionDate": "Execution date", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__fromSupplier": "From supplier", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__lines": "Lines", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__sites": "Sites", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__status": "Status", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__toSupplier": "To supplier", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_input_set__property__user": "User", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__node_name": "Unbilled account payable result line", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__account": "Account", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__accountItem": "Account item", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__billBySupplier": "Bill by supplier", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__company": "Company", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__companyCurrency": "Company currency", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__creditedQuantity": "Credited quantity", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__documentDate": "Document date", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__financialSite": "Financial site", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__inputSet": "Input set", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableAmount": "Invoice receivable amount", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableAmountInCompanyCurrency": "Invoice receivable amount in company currency", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableAmountInCompanyCurrencyAtAsOfDate": "Invoice receivable amount in company currency at as of date", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__invoiceReceivableQuantity": "Invoice receivable quantity", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__netPrice": "Net price", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__purchaseUnit": "Purchase unit", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__quantity": "Quantity", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__receiptInternalId": "Receipt internal id", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__receiptNumber": "Receipt number", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__returnedQuantity": "Returned quantity", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__stockSite": "Stock site", "@sage/xtrem-purchasing/nodes__unbilled_account_payable_result_line__property__supplier": "Supplier", "@sage/xtrem-purchasing/nodes__unbilled_account_payable-input-set__success_message": "Success", "@sage/xtrem-purchasing/nodes__unbilled_account_payable-input-set__success_notification_title": "Unbilled accounts payable calculation complete", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__node_name": "Work in progress purchase order line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__actualQuantity": "Actual quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentId": "Document id", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentLine": "Document line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentNumber": "Document number", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__documentType": "Document type", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__endDate": "End date", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__expectedQuantity": "Expected quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__originDocumentType": "Origin document type", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__purchaseOrderLine": "Purchase order line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__startDate": "Start date", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_order_line__property__status": "Status", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__node_name": "Work in progress purchase receipt line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__actualQuantity": "Actual quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentId": "Document id", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentLine": "Document line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentNumber": "Document number", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__documentType": "Document type", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__endDate": "End date", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__expectedQuantity": "Expected quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__originDocumentType": "Origin document type", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__purchaseReceiptLine": "Purchase receipt line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__startDate": "Start date", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_receipt_line__property__status": "Status", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__node_name": "Work in progress purchase return line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__actualQuantity": "Actual quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentId": "Document id", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentLine": "Document line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentNumber": "Document number", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__documentType": "Document type", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__endDate": "End date", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__expectedQuantity": "Expected quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__originDocumentType": "Origin document type", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__purchaseReturnLine": "Purchase return line", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__site": "Site", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__startDate": "Start date", "@sage/xtrem-purchasing/nodes__work_in_progress_purchase_return_line__property__status": "Status", "@sage/xtrem-purchasing/nodes_different_supplier_address": "supplier address", "@sage/xtrem-purchasing/nodes_new_invoice_line_not_allowed": "You are not allowed to add new lines if invoice is in a posted status.", "@sage/xtrem-purchasing/openOrder____title": "Reopen order", "@sage/xtrem-purchasing/order_from_requisition_dialog_title": "Select requisition lines", "@sage/xtrem-purchasing/order_from_requisition_select": "Select", "@sage/xtrem-purchasing/package__name": "Sage xtrem purchasing", "@sage/xtrem-purchasing/page__at_least_one_mandatory_tax_code_not_found": "At least one mandatory tax code is missing on the line.", "@sage/xtrem-purchasing/page__purchase_credit_memo__at_least_one_mandatory_tax_code_not_found": "At least one mandatory tax code is missing on the line.", "@sage/xtrem-purchasing/page__reorder_purchase_order_panel__expected_date_exceed": "The expected date is later than the shipping date.", "@sage/xtrem-purchasing/page__reorder_purchase_order_panel__lower-than-min-quantity": "The purchase quantity is lower than the minimum quantity ({{supplierMinimumQuantity}}).", "@sage/xtrem-purchasing/page__reorder_purchase_order_panel__mandatory_supplier": "You need to add a supplier.", "@sage/xtrem-purchasing/page__reorder_purchase_order_panel__purchase_quantity_mandatory": "Purchase quantity is mandatory.", "@sage/xtrem-purchasing/page-extensions__item_extension__documentSection____title": "Documents", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title___constructor": "Type", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__displayStatus": "Document status", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__number": "Document number", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__orderDate": "Document date", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__site__id": "Document site ID", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__site__name": "Document site", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__supplier__id": "Supplier ID", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__document__supplier__name": "Supplier", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__expectedReceiptDate": "Expected receipt date", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__lineInvoiceStatus": "Invoice status", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__lineReceiptStatus": "Receipt status", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__quantity": "Ordered quantity", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__quantityToReceive": "Remaining quantity", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__status": "Line status", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__stockSite__id": "Stock site ID", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____columns__title__stockSite__name": "Stock site", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title": "All statuses", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title__2": "Draft", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title__3": "Pending", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____optionsMenu__title__4": "In progress", "@sage/xtrem-purchasing/page-extensions__item_extension__itemDocuments____title": "Documents", "@sage/xtrem-purchasing/page-extensions__landed_cost_allocation_panel_extension__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-purchasing/page-extensions__landed_cost_allocation_panel_extension__selectFromPurchaseOrder____title": "Add lines from orders", "@sage/xtrem-purchasing/page-extensions__landed_cost_allocation_panel_extension__selectFromPurchaseReceipt____title": "Add lines from receipts", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__displayStatus": "Status", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__receivedQuantity": "Received quantity", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__supplier__name": "Supplier", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__documents____levels__columns__title__supplierDocumentNumber": "Supplier packing slip", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__number": "Number", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__receiptStatus": "Receipt status", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__site": "Site", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____columns__title__supplier__name": "Supplier", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____lookupDialogTitle": "Select purchase order", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseOrder____title": "Purchase order", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__displayStatus": "Status", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__number": "Number", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__site": "Site", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____columns__title__supplier__name": "Supplier", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____lookupDialogTitle": "Select purchase receipt", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceipt____title": "Purchase receipt", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__displayStatus": "Status", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__number": "Number", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__site": "Site", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__supplier__name": "Supplier", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____columns__title__supplierDocumentNumber": "Supplier packing slip", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____lookupDialogTitle": "Select supplier packing slip", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__purchaseReceiptPackingSlip____title": "Supplier packing slip", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-purchasing/page-extensions__landed_cost_document_line_pick_list_extension__supplier____title": "Supplier", "@sage/xtrem-purchasing/page-extensions__site_extension____navigationPanel__listItem__line11__title": "Default buyer", "@sage/xtrem-purchasing/page-extensions__site_extension__defaultApproverBlock____title": "Purchasing approval", "@sage/xtrem-purchasing/page-extensions__site_extension__isPurchaseOrderApprovalManaged____title": "Order", "@sage/xtrem-purchasing/page-extensions__site_extension__isPurchaseRequisitionApprovalManaged____title": "Requisition", "@sage/xtrem-purchasing/page-extensions__site_extension__isPurchaseReturnApprovalManaged____title": "Return", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____columns__title__email": "Email", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____columns__title__firstName": "First name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderDefaultApprover____title": "Approver", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____columns__title__email": "Email", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____columns__title__firstName": "First name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseOrderSubstituteApprover____title": "Substitute approver", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____columns__title__email": "Email", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____columns__title__firstName": "First name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionDefaultApprover____title": "Approver", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____columns__title__email": "Email", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____columns__title__firstName": "First name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/page-extensions__site_extension__purchaseRequisitionSubstituteApprover____title": "Substitute approver", "@sage/xtrem-purchasing/page-extensions__supplier_extension____navigationPanel__listItem__line11__title": "Default buyer", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____columns__title__email": "Email", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____columns__title__firstName": "First name", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____lookupDialogTitle": "Select default buyer", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyer____title": "Default buyer", "@sage/xtrem-purchasing/page-extensions__supplier_extension__defaultBuyerBlock____title": "Purchasing", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__approvalStatus": "Approval status", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__number": "Number", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__orderDate": "Date", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__paymentTerm__name": "Payment term", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__status": "Status", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__stockSite__name": "Stock site", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____columns__title__totalAmountExcludingTax": "Amount", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____dropdownActions__title": "View order", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchaseOrders____title": "Orders", "@sage/xtrem-purchasing/page-extensions__supplier_extension__purchasingSection____title": "Purchasing", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list___no_results": "No results found.", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list__search": "Search", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__order_number": "Order number", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__ordered_quantity": "Ordered quantity", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__receipt_number": "Receipt number", "@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__received_quantity": "Received quantity", "@sage/xtrem-purchasing/pages__invoice_from_receipt__error_zero_or_negative_price": "You need to enter a unit price greater than zero.", "@sage/xtrem-purchasing/pages__invoice_from_receipt__error_zero_or_negative_quantity": "You need to enter a quantity greater than zero.", "@sage/xtrem-purchasing/pages__invoice_to_receipt__warning_different_information": "Some lines were not added as they have different bill by supplier details.", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title": "Post", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__2": "Open item", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__3": "Send for matching", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__4": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__dropdownActions__title__5": "Delete", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__id__title": "Bill-by supplier ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__idPayToSupplier__title": "Pay-to supplier ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line_4__title": "Total Including tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line_5__title": "Due date", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line12__title": "Pay to supplier", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line13__title": "Supplier document reference", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line14__title": "Supplier document date", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__rounding": "Rounding", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line15__title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line2__title": "Bill-by supplier", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line2Right__title": "Credit memo date", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line3__title": "Financial site", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line6__title": "Reason", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line8__title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__line9__title": "Tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__netBalance__title": "Net balance", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__paymentStatus__title": "Payment status", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_credit_memo____navigationPanel__listItem__totalPayments__title": "Total payments", "@sage/xtrem-purchasing/pages__purchase_credit_memo____objectTypePlural": "Purchase credit memos", "@sage/xtrem-purchasing/pages__purchase_credit_memo____objectTypeSingular": "Purchase credit memo", "@sage/xtrem-purchasing/pages__purchase_credit_memo____title": "Purchase credit memo", "@sage/xtrem-purchasing/pages__purchase_credit_memo__amountPaid____title": "Total payments", "@sage/xtrem-purchasing/pages__purchase_credit_memo__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__concatenatedAddress": "Bill-by address", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____dropdownActions__title": "Replace", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByAddress____title": "Bill-by address", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__country": "Country", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____columns__title__region": "Region", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billByLinkedAddress____title": "Bill-by address", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__lookupDialogTitle__businessEntity__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__nestedFields__primaryAddress__title": "Phone number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____lookupDialogTitle": "Select bill-by supplier", "@sage/xtrem-purchasing/pages__purchase_credit_memo__billBySupplier____title": "Bill-by supplier", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountIncludingTax____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalAmountIncludingTaxInCompanyCurrency____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__calculatedTotalTaxAmount____title": "Tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyCurrency____lookupDialogTitle": "Select company currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyCurrency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyCurrency____title": "Company currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyFxRate____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_credit_memo__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-purchasing/pages__purchase_credit_memo__credit_memo_date__cannot__be__future": "The credit memo date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__creditMemoDate____title": "Credit memo date", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__currency____title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__defaultDimension____title": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_credit_memo__displayStatus____title": "Display status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__documentNumberLink____title": "Open item", "@sage/xtrem-purchasing/pages__purchase_credit_memo__dueDate____title": "Due date", "@sage/xtrem-purchasing/pages__purchase_credit_memo__financialSection____title": "Financial", "@sage/xtrem-purchasing/pages__purchase_credit_memo__forcedAmountPaid____title": "Forced amount paid", "@sage/xtrem-purchasing/pages__purchase_credit_memo__from_invoice__greater_dialog_content": "You are about to create a credit memo quantity larger than the invoice quantity.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__from_invoice__smaller_dialog_content": "You are about to create a credit memo quantity smaller than the invoice quantity.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__fxRateDate____title": "Exchange rate date", "@sage/xtrem-purchasing/pages__purchase_credit_memo__goToSysNotificationPage____title": "Retry", "@sage/xtrem-purchasing/pages__purchase_credit_memo__headerSection____title": "Header section", "@sage/xtrem-purchasing/pages__purchase_credit_memo__informationBlock____title": "Information", "@sage/xtrem-purchasing/pages__purchase_credit_memo__informationSection____title": "Information", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNote____title": "Internal notes", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__internalNoteLine____title": "Internal line notes", "@sage/xtrem-purchasing/pages__purchase_credit_memo__itemsSection____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__2": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__3": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__4": "Rounding", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__currency__id__title__5": "Symbol", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__columns__unit__symbol__title": "Symbol", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__columns__unit__symbol__title__2": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title___id": "ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title___id__2": "ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__amountExcludingTax": "Total amount from order", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__amountExcludingTaxInCompanyCurrency": "Total amount from order company currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__grossPrice": "Order unit price", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__quantity": "Ordered quantity", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__columns__title__unit__symbol": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__title": "ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__title__2": "Purchase credit memo", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__purchaseInvoiceLine__purchaseInvoiceLine___id__title__3": "Purchase credit memo line", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__recipientSite__title": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__recipientSite__title__2": "ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__recipientSite__title__3": "Tax ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__stockUnit__symbol__title__2": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__unit__symbol__title": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__columns__unit__symbol__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__recipientSite": "Select recipient site", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__unit__symbol": "Select purchase unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__lookupDialogTitle__uPurchaseUnit__name": "Select unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__postfix__charge": "%", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__postfix__discount": "%", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title": "", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__amountIncludingTaxInCompanyCurrency": "Total including tax company currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__charge": "Charge", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__discount": "Discount", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__item__id": "Item ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__netPrice": "Net price", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__origin": "Origin", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine___id": "Invoice line", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__amountExcludingTax": "Total amount from credit memo", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__amountExcludingTaxInCompanyCurrency": "Total amount from credit memo", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__grossPrice": "Invoice unit price", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseInvoiceLine__purchaseInvoiceLine__quantity": "Invoiced quantity", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseReturnLine__purchaseReturnLine__amountExcludingTax": "Total amount from return", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseReturnLine__purchaseReturnLine__grossPrice": "Return unit price", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__purchaseReturnLine__purchaseReturnLine__quantity": "Received quantity", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__quantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__recipientSite": "Recipient site", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__taxAmount": "Total tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__taxAmountAdjusted": "Tax amount adjusted", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__taxCalculationStatus": "Tax status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__unit__symbol": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__uPurchaseUnit__name": "Unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____columns__title__uStockUnit__name": "Unit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____dropdownActions__title": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____dropdownActions__title__2": "Tax details", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____dropdownActions__title__3": "Delete", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____inlineActions__title": "Open line panel", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____mobileCard__line2__title": "Description", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____mobileCard__title__title": "Product", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____sidebar__headerDropdownActions__title": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____sidebar__headerDropdownActions__title__2": "Tax details", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____sidebar__headerDropdownActions__title__3": "Delete", "@sage/xtrem-purchasing/pages__purchase_credit_memo__lines____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_credit_memo__netBalance____title": "Net balance", "@sage/xtrem-purchasing/pages__purchase_credit_memo__notesBlock____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_credit_memo__notesSection____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_credit_memo__notifyBuyer____title": "Send for matching", "@sage/xtrem-purchasing/pages__purchase_credit_memo__number____title": "Number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__paymentsBlock____title": "Payments", "@sage/xtrem-purchasing/pages__purchase_credit_memo__paymentsSection____title": "Payments", "@sage/xtrem-purchasing/pages__purchase_credit_memo__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__concatenatedAddress": "Pay-to address", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____dropdownActions__title": "Replace", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____dropdownActions__title__2": "Edit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____dropdownActions__title__3": "Read-only", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToAddress____title": "Pay-to address", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____columns__title__region": "Region", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToLinkedAddress____title": "Pay-to address", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____lookupDialogTitle": "Select pay to supplier", "@sage/xtrem-purchasing/pages__purchase_credit_memo__payToSupplier____title": "Pay to supplier", "@sage/xtrem-purchasing/pages__purchase_credit_memo__pdfBlock____title": "Attachment: Supplier credit memo", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post____title": "Post", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post__no_line": "You need to add lines before posting.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post__tax_calculation_failed": "You need to resolve tax calculation issues before posting.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post__variance": "You need to resolve the total amount variances before posting.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post_action_dialog_content": "You are about to post this purchase credit memo.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post_action_dialog_title": "Confirm posting", "@sage/xtrem-purchasing/pages__purchase_credit_memo__post_errors": "Posting errors:", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__financeIntegrationAppRecordId__2": "Accounting integration reference", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingDetails____title": "Results", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingMessageBlock____title": "Error details", "@sage/xtrem-purchasing/pages__purchase_credit_memo__postingSection____title": "Posting", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoExcludingTaxVariance____title": "Total variance amount excl. tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoIncludingTax____title": "Total credit memo amount incl. tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoIncludingTaxVariance____title": "Total variance amount incl. tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseCreditMemoTaxVariance____title": "Total tax variance", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__creditedQuantity": "Quantity in purchase unit ", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__link": "Invoice number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__remainingQuantity": "Remaining quantity to credit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____columns__title__status": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseInvoiceLines____title": "Purchase invoice lines", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__creditedQuantity": "Quantity in purchase unit ", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__link": "Return number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__linkReceipt": "Purchase receipt number", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__remainingQuantity": "Remaining quantity to credit", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____columns__title__status": "Return status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__purchaseReturnLines____title": "Purchase return lines", "@sage/xtrem-purchasing/pages__purchase_credit_memo__rateDescription____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_credit_memo__reason____lookupDialogTitle": "Select reason", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost____title": "Repost", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost__variance": "You need to resolve the total amount variances before posting. The new total supplier tax is: ", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_accounts_payable_invoice_already_posted": "The accounts payable credit memo posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_documents_already_posted_title": "Repost", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_errors": "Errors while reposting:", "@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_journal_entry_already_posted": "The journal entry posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_continue": "Continue", "@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_message": "You are about to correct this shipment status back to Draft.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__resync_display_status_title": "Check and update status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__save____title": "Save", "@sage/xtrem-purchasing/pages__purchase_credit_memo__save_warnings": "Warnings while saving:", "@sage/xtrem-purchasing/pages__purchase_credit_memo__selectFromInvoice____title": "Add lines from invoices", "@sage/xtrem-purchasing/pages__purchase_credit_memo__selectFromReturn____title": "Add lines from returns", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__title__id": "ID ", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____lookupDialogTitle": "Select financial site", "@sage/xtrem-purchasing/pages__purchase_credit_memo__site____title": "Financial site", "@sage/xtrem-purchasing/pages__purchase_credit_memo__status____title": "Status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_creation": "Create", "@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_pay": "Pay", "@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_posting": "Post", "@sage/xtrem-purchasing/pages__purchase_credit_memo__stockTransactionStatus____title": "Stock status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplier_document_date__cannot__be__future": "The supplier document date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplierBlock____title": "Supplier document information", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplierDocumentDate____title": "Supplier document date", "@sage/xtrem-purchasing/pages__purchase_credit_memo__supplierDocumentNumber____title": "Supplier credit memo reference", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxCalculationStatus____title": "Tax calculation status", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxEngine____title": "Tax engine", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__postfix__taxRate": "%", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__isReverseCharge": "Reverse charge", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__tax": "Tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxAmount": "Amount", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxAmountAdjusted": "Adjusted amount", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxCategory": "Category", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____columns__title__taxRate": "Rate", "@sage/xtrem-purchasing/pages__purchase_credit_memo__taxes____title": "Summary by tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalAmountExcludingTax____title": "Total credit memo amount excl. tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalAmountIncludingTax____title": "Supplier total including tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalAmountPaid____title": "Total amount paid", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalsSection____title": "Totals", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalsSectionCompanyCurrencyDetailsBlock____title": "Calculated amounts company currency", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalsSectionTaxTotalsBlock____title": "Calculated amounts", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalTaxAmount____title": "Total credit memo tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__totalTaxAmountAdjusted____title": "Adjusted tax", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceBlock____title": "Variances", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceTotalAmountExcludingTax____title": "Total amount excluding tax variance", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceTotalAmountIncludingTax____title": "Total amount including tax variance", "@sage/xtrem-purchasing/pages__purchase_credit_memo__varianceTotalTaxAmount____title": "Total tax amount variance", "@sage/xtrem-purchasing/pages__purchase_credit_memo_from_return__greater_dialog_content": "You are about to create a credit memo quantity larger than the return quantity", "@sage/xtrem-purchasing/pages__purchase_credit_memo_partial__from_return_dialog_content": "You are about to create a credit memo quantity smaller than the return quantity.", "@sage/xtrem-purchasing/pages__purchase_credit_memo_partial_confirm__partial_dialog_title": "Confirm credit memo", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_exception_request": "Could not send buyer notification email.", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_helper_text": "A buyer notification for this purchase credit memo is sent to this address.", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_not_sent": "The buyer notification cannot be sent by email.", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__email_sent_to_approval": "Buyer notification email sent to: ", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__select_button_title": "Select buyer", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__send_approval_request_confirm_title": "Buyer notification", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__send_approval_request_dialog_content": "You are about to send the buyer notification email.", "@sage/xtrem-purchasing/pages__purchase_credit_memo_request_approval_dialog__send_approval_request_dialog_title": "Buyer notification", "@sage/xtrem-purchasing/pages__purchase_credit-memo__line_delete_action_dialog_content": "You are about to delete this purchase creit memo line. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_credit-memo__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title": "Accept all variances", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__2": "Post", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__3": "Open item", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__4": "Record payment", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__5": "Create credit memo", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__6": "Send for matching", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__7": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__dropdownActions__title__8": "Delete", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__id__title": "Bill-by supplier ID", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__idPayToSupplier__title": "Pay-to supplier ID", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line_4__title": "Total Including tax", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line_5__title": "Due date", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line12__title": "Pay to supplier", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line13__title": "Supplier invoice reference", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line14__title": "Supplier document date", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__columns__title__rounding": "Rounding", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line15__title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line2__title": "Bill-by supplier", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line2Right__title": "Invoice date", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line3__title": "Financial site", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line6__title": "Matching status", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line7__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line8__title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__line9__title": "Tax", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__netBalance__title": "Net balance", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__paymentStatus__title": "Payment status", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_invoice____navigationPanel__listItem__totalPayments__title": "Total payments", "@sage/xtrem-purchasing/pages__purchase_invoice____objectTypePlural": "Purchase invoices", "@sage/xtrem-purchasing/pages__purchase_invoice____objectTypeSingular": "Purchase invoice", "@sage/xtrem-purchasing/pages__purchase_invoice____title": "Purchase invoice", "@sage/xtrem-purchasing/pages__purchase_invoice___header_grid_warning_message": "Refer to the warning on the line for details ({{numberOfWarningInTable}}).", "@sage/xtrem-purchasing/pages__purchase_invoice__acceptAllVariances____title": "Accept all variances", "@sage/xtrem-purchasing/pages__purchase_invoice__amountPaid____title": "Total payments", "@sage/xtrem-purchasing/pages__purchase_invoice__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-purchasing/pages__purchase_invoice__approve_dialog_content": "You are about to approve all variances for this invoice.", "@sage/xtrem-purchasing/pages__purchase_invoice__approve_dialog_title": "Accept all variances for this invoice", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__concatenatedAddress": "Bill-by address", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__billByAddress____title": "Bill-by address", "@sage/xtrem-purchasing/pages__purchase_invoice__billByLinkedAddress____title": "Bill-by address", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____lookupDialogTitle": "Select bill-by supplier", "@sage/xtrem-purchasing/pages__purchase_invoice__billBySupplier____title": "Bill-by supplier", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountIncludingTax____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalAmountIncludingTaxInCompanyCurrency____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalRemainingQuantityToCredit____title": "Total remaining quantity to credit", "@sage/xtrem-purchasing/pages__purchase_invoice__calculatedTotalTaxAmount____title": "Tax", "@sage/xtrem-purchasing/pages__purchase_invoice__companyCurrency____lookupDialogTitle": "Select company currency", "@sage/xtrem-purchasing/pages__purchase_invoice__companyCurrency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_invoice__companyCurrency____title": "Company currency", "@sage/xtrem-purchasing/pages__purchase_invoice__companyFxRate____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_invoice__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-purchasing/pages__purchase_invoice__confirm_allocate": "Allocate", "@sage/xtrem-purchasing/pages__purchase_invoice__confirm_landed_cost": "Landed cost", "@sage/xtrem-purchasing/pages__purchase_invoice__createPurchaseCreditNote____title": "Create credit memo", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_invoice__currency____title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_invoice__defaultDimension____title": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_invoice__displayStatus____title": "Display status", "@sage/xtrem-purchasing/pages__purchase_invoice__documentNumberLink____title": "Open item", "@sage/xtrem-purchasing/pages__purchase_invoice__dueDate____title": "Due date", "@sage/xtrem-purchasing/pages__purchase_invoice__finance_errors": "Errors:", "@sage/xtrem-purchasing/pages__purchase_invoice__finance_errors_title": "Finance control", "@sage/xtrem-purchasing/pages__purchase_invoice__financialSection____title": "Financial", "@sage/xtrem-purchasing/pages__purchase_invoice__forcedAmountPaid____title": "Forced amount paid", "@sage/xtrem-purchasing/pages__purchase_invoice__fxRateDate____title": "Exchange rate date", "@sage/xtrem-purchasing/pages__purchase_invoice__goToSysNotificationPage____title": "Retry", "@sage/xtrem-purchasing/pages__purchase_invoice__greater_dialog_content": "The invoiced quantity is greater than the receipt quantity.", "@sage/xtrem-purchasing/pages__purchase_invoice__greater_dialog_title": "Confirm invoicing", "@sage/xtrem-purchasing/pages__purchase_invoice__headerSection____title": "Header section", "@sage/xtrem-purchasing/pages__purchase_invoice__informationBlock____title": "Information", "@sage/xtrem-purchasing/pages__purchase_invoice__informationSection____title": "Information", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNote____title": "Internal notes", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_invoice__internalNoteLine____title": "Internal line notes", "@sage/xtrem-purchasing/pages__purchase_invoice__invoice_date__cannot__be__future": "The invoice date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_invoice__invoiceDate____title": "Invoice date", "@sage/xtrem-purchasing/pages__purchase_invoice__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_invoice__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_invoice__itemsSection____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_content": "You are about to approve the variance for this invoice line.", "@sage/xtrem-purchasing/pages__purchase_invoice__line_approve_dialog_title": "Accept variance for this invoice line", "@sage/xtrem-purchasing/pages__purchase_invoice__line_delete_action_dialog_content": "You are about to delete this purchase invoice line. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_invoice__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-purchasing/pages__purchase_invoice__line_matching_status_updated": "Matching status updated.", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__columns__unit__symbol__title": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__columns__unit__symbol__title__2": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title___id": "ID", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title___id__2": "ID", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__amountExcludingTax": "Total amount from order", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__grossPrice": "Order unit price", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__quantity": "Ordered quantity", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__columns__title__unit__symbol": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__title": "ID", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__title__2": "Purchase order", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__purchaseOrderLine__purchaseOrderLine___id__title__3": "Purchase invoice", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__recipientSite__title": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__recipientSite__title__2": "ID", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__stockUnit__symbol__title__2": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__unit__symbol__title": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__unit__symbol__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__columns__varianceApprover__displayName__title": "ID", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__lookupDialogTitle__recipientSite": "Select recipient site", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__lookupDialogTitle__unit__symbol": "Select unit", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__postfix__charge": "%", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__postfix__discount": "%", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__amountIncludingTaxInCompanyCurrency": "Total including tax company currency", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__charge": "Charge", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__discount": "Discount", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__item__id": "Item ID", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__item__image": "Image", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__matchingStatus": "Matching status", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__netPrice": "Net price", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__origin": "Origin", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine___id": "Order line", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine__amountExcludingTax": "Total amount from order", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine__grossPrice": "Order unit price", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseOrderLine__purchaseOrderLine__quantity": "Ordered quantity", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseReceiptLine__purchaseReceiptLine__amountExcludingTax": "Total amount from receipt", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseReceiptLine__purchaseReceiptLine__grossPrice": "Receipt unit price", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__purchaseReceiptLine__purchaseReceiptLine__quantity": "Received quantity", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__quantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__recipientSite": "Recipient site", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__taxAmount": "Tax amount", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__taxAmountAdjusted": "Tax amount adjusted", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__taxCalculationStatus": "Tax status", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__unit__symbol": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__varianceApprover__displayName": "Variance approver", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____columns__title__varianceType": "Variance type", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title": "Landed cost", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__2": "Accept variance", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__3": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__4": "Tax details", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____dropdownActions__title__5": "Delete", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____inlineActions__title": "Open line panel", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____mobileCard__line2__title": "Description", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____mobileCard__title__title": "Product", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____optionsMenu__title": "All statuses", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____optionsMenu__title__2": "Lines with variances", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title": "Landed cost", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__2": "Accept variance", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__3": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__4": "Tax details", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____sidebar__headerDropdownActions__title__5": "Delete", "@sage/xtrem-purchasing/pages__purchase_invoice__lines____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title": "Receipt price variance", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__2": "Total amount from receipt", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__3": "Receipt total tax excluded amount variance", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__4": "Order unit price", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__5": "Order price variance", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__6": "Total amount from order", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__7": "Order total tax excluded amount variance", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____columns__title__grossPrice": "Receipt unit price", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingPrice____title": "Price", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title": "Received quantity variance", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title__2": "Quantity ordered", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title__3": "Ordered quantity variance", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____columns__title__quantity": "Quantity received", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingQuantity____title": "Quantity", "@sage/xtrem-purchasing/pages__purchase_invoice__matchingStatus____title": "Matching status", "@sage/xtrem-purchasing/pages__purchase_invoice__netBalance____title": "Net balance", "@sage/xtrem-purchasing/pages__purchase_invoice__noteBlock____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_invoice__notesSection____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_invoice__notifyBuyer____title": "Send for matching", "@sage/xtrem-purchasing/pages__purchase_invoice__number____title": "Number", "@sage/xtrem-purchasing/pages__purchase_invoice__partial_confirm_dialog_content": "The invoice quantity is less than the receipt quantity.", "@sage/xtrem-purchasing/pages__purchase_invoice__partial_confirm_dialog_title": "Confirm partial invoicing", "@sage/xtrem-purchasing/pages__purchase_invoice__payment_created": "The following payment was created: {{paymentNumber}}.", "@sage/xtrem-purchasing/pages__purchase_invoice__paymentsBlock____title": "Payments", "@sage/xtrem-purchasing/pages__purchase_invoice__paymentsSection____title": "Payments", "@sage/xtrem-purchasing/pages__purchase_invoice__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__concatenatedAddress": "Pay-to address", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__payToAddress____title": "Pay-to address", "@sage/xtrem-purchasing/pages__purchase_invoice__payToLinkedAddress____title": "Pay-to address", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____columns__title__businessEntity__name__2": "ID", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____lookupDialogTitle": "Select pay to supplier", "@sage/xtrem-purchasing/pages__purchase_invoice__payToSupplier____title": "Pay to supplier", "@sage/xtrem-purchasing/pages__purchase_invoice__pdfBlock____title": "Attachment: Supplier invoice", "@sage/xtrem-purchasing/pages__purchase_invoice__post____title": "Post", "@sage/xtrem-purchasing/pages__purchase_invoice__post__no_line": "You need to add lines before posting.", "@sage/xtrem-purchasing/pages__purchase_invoice__post__tax_calculation_failed": "You need to resolve tax calculation issues before posting.", "@sage/xtrem-purchasing/pages__purchase_invoice__post__variance": "You need to resolve the total amount variances before posting.", "@sage/xtrem-purchasing/pages__purchase_invoice__post_action_dialog_content": "You are about to post this purchase invoice.", "@sage/xtrem-purchasing/pages__purchase_invoice__post_action_dialog_title": "Confirm posting", "@sage/xtrem-purchasing/pages__purchase_invoice__post_errors": "Posting errors:", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__financeIntegrationAppRecordId__2": "Accounting integration reference", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-purchasing/pages__purchase_invoice__postingDetails____title": "Results", "@sage/xtrem-purchasing/pages__purchase_invoice__postingMessageBlock____title": "Error details", "@sage/xtrem-purchasing/pages__purchase_invoice__postingSection____title": "Posting", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceExcludingTax____title": "Total supplier amount incl. tax", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceVarianceExcludingTax____title": "Total variance amount excl. tax", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceVarianceIncludingTax____title": "Total variance amount incl. tax", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseInvoiceVarianceTax____title": "Total tax variance ", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____columns__title___id": "Order", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____columns__title__invoicedQuantity": "Order quantity", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____columns__title__purchaseOrderLine__status": "Status", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseOrderLine____title": "Purchase order line to purchase invoice line", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__nestedFields__unit__title": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title___id": "Receipt number", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title___id__2": "Remaining quantity to invoice", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title__invoicedQuantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____columns__title__purchaseReceiptLine__status": "Receipt status", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLines____title": "Purchase receipt to purchase invoice line", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title___id": "Receipt", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title___id__2": "Status", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title___id__3": "Available quantity to invoice", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____columns__title__invoicedQuantity": "Receipt quantity", "@sage/xtrem-purchasing/pages__purchase_invoice__purchaseReceiptLineToPurchaseInvoiceLines____title": "Purchase receipt line to purchase invoice line", "@sage/xtrem-purchasing/pages__purchase_invoice__rateDescription____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_invoice__recordPayment____title": "Record payment", "@sage/xtrem-purchasing/pages__purchase_invoice__repost____title": "Repost", "@sage/xtrem-purchasing/pages__purchase_invoice__repost__variance": "You need to resolve the total amount variances before posting. The new total supplier tax is: ", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_accounts_payable_invoice_already_posted": "The accounts payable invoice posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_documents_already_posted_title": "Repost", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_errors": "Errors while reposting:", "@sage/xtrem-purchasing/pages__purchase_invoice__repost_journal_entry_already_posted": "The journal entry posted successfully. You can access this document in your financial solution to take further action.", "@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_continue": "Continue", "@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_message": "You are about to update the status.", "@sage/xtrem-purchasing/pages__purchase_invoice__resync_display_status_title": "Check and update status", "@sage/xtrem-purchasing/pages__purchase_invoice__save____title": "Save", "@sage/xtrem-purchasing/pages__purchase_invoice__save_warnings": "Warnings while saving:", "@sage/xtrem-purchasing/pages__purchase_invoice__selectFromPurchaseReceiptsLookup____title": "Add lines from receipts", "@sage/xtrem-purchasing/pages__purchase_invoice__selectReceiptSecondaryButton____title": "Add lines from receipts", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__columns__legalCompany__name__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__columns__legalCompany__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__title__id": "ID ", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__purchase_invoice__site____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_invoice__site____lookupDialogTitle": "Select financial site", "@sage/xtrem-purchasing/pages__purchase_invoice__site____title": "Financial site", "@sage/xtrem-purchasing/pages__purchase_invoice__status____title": "Status", "@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_creation": "Create", "@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_pay": "Pay", "@sage/xtrem-purchasing/pages__purchase_invoice__step_sequence_post": "Post", "@sage/xtrem-purchasing/pages__purchase_invoice__stockTransactionStatus____title": "Stock status", "@sage/xtrem-purchasing/pages__purchase_invoice__supplier_document_date__cannot__be__future": "The supplier document date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_invoice__supplierBlock____title": "Supplier document information", "@sage/xtrem-purchasing/pages__purchase_invoice__supplierDocumentDate____title": "Supplier document date", "@sage/xtrem-purchasing/pages__purchase_invoice__supplierDocumentNumber____title": "Supplier invoice reference", "@sage/xtrem-purchasing/pages__purchase_invoice__taxCalculationStatus____title": "Tax calculation status", "@sage/xtrem-purchasing/pages__purchase_invoice__taxEngine____title": "Tax engine", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__postfix__taxRate": "%", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__isReverseCharge": "Reverse charge", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__tax": "Tax", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxAmount": "Amount", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxAmountAdjusted": "Adjusted amount", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxCategory": "Category", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____columns__title__taxRate": "Rate", "@sage/xtrem-purchasing/pages__purchase_invoice__taxes____title": "Summary by tax", "@sage/xtrem-purchasing/pages__purchase_invoice__to_credit_memo_fail": "This purchase credit memo cannot be created.", "@sage/xtrem-purchasing/pages__purchase_invoice__to_credit_memo_success": "Purchase credit memo created.", "@sage/xtrem-purchasing/pages__purchase_invoice__totalAmountExcludingTax____title": "Total supplier amount excl. tax", "@sage/xtrem-purchasing/pages__purchase_invoice__totalAmountIncludingTax____title": "Total supplier amount incl. tax", "@sage/xtrem-purchasing/pages__purchase_invoice__totalAmountPaid____title": "Total amount paid", "@sage/xtrem-purchasing/pages__purchase_invoice__totalsSection____title": "Totals", "@sage/xtrem-purchasing/pages__purchase_invoice__totalsSectionCompanyCurrencyDetailsBlock____title": "Amount in company currency", "@sage/xtrem-purchasing/pages__purchase_invoice__totalsSectionTaxTotalsBlock____title": "Calculated amounts", "@sage/xtrem-purchasing/pages__purchase_invoice__totalTaxAmount____title": "Total supplier tax", "@sage/xtrem-purchasing/pages__purchase_invoice__totalTaxAmountAdjusted____title": "Adjusted tax", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceBlock____title": "Variances", "@sage/xtrem-purchasing/pages__purchase_invoice__variances_status_updated": "Variance status updated.", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceTotalAmountExcludingTax____title": "Total amount excluding tax variance", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceTotalAmountIncludingTax____title": "Total amount including tax variance", "@sage/xtrem-purchasing/pages__purchase_invoice__varianceTotalTaxAmount____title": "Total tax amount variance", "@sage/xtrem-purchasing/pages__purchase_invoice_attachment____title": "Attachment", "@sage/xtrem-purchasing/pages__purchase_invoice_attachment__mainSection____title": "General", "@sage/xtrem-purchasing/pages__purchase_invoice_attachment__validate____title": "OK", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyCurrencyId__title": "Company currency ID", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyCurrencyName__title": "Company currency", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyId__title": "Company ID", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__companyName__title": "Company", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__currencyId__title": "Currency ID", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__documentDate__title": "Document date", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__documentLineType__title": "Document line type", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__netPrice__title": "Net price", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__signedAmountExcludingTax__title": "Amount", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__signedAmountExcludingTaxInCompanyCurrency__title": "Amount in company currency", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__siteId__title": "Financial site ID", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__siteName__title": "Financial site", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__stockSiteId__title": "Recipient site ID", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__stockSiteName__title": "Recipient site", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__supplierId__title": "Supplier ID", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__supplierName__title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__title__title": "Document number", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__listItem__unit__title": "Unit", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____navigationPanel__optionsMenu__title": "Invoice and credit memo lines", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____objectTypePlural": "Purchase invoice and credit memo lines", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____objectTypeSingular": "Purchase invoice and credit memo line", "@sage/xtrem-purchasing/pages__purchase_invoice_credit_memo_line_inquiry____title": "Purchase invoice line", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__email_exception_request": "Could not send buyer notification email.", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__email_helper_text": "A buyer notification for this purchase invoice is sent to this address.", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__email_not_sent": "The buyer notification cannot be sent by email.", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__email_sent_to_approval": "Buyer notification email sent to: ", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__select_button_title": "Select buyer", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__send_approval_request_confirm_title": "Buyer notification", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__send_approval_request_dialog_content": "You are about to send the buyer notification email.", "@sage/xtrem-purchasing/pages__purchase_invoice_request_approval_dialog__send_approval_request_dialog_title": "Buyer notification", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel____title": "Create purchase credit memo", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__cancel____title": "Cancel", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__create____title": "Create", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__creditNoteSection____title": "Create purchase credit memo", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__reasonCreditNote____lookupDialogTitle": "Select reason", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__reasonCreditNote____title": "Reason", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__supplierDocumentDateCreditNote____title": "Supplier document date", "@sage/xtrem-purchasing/pages__purchase_invoice_to_credit_note_panel__totalAmountExcludingTaxCreditNote____title": "Total amount excluding tax", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__bulkActions__title": "Print", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__approve": "Approve", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__closeOrder": "Close order", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__confirm": "Confirm", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__createReceipt": "Create receipt", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__delete": "Delete", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__print": "Print", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__reject": "Reject", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__reopenOrder": "Reopen order", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__send": "Send", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__setDimensions": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__dropdownActions__title__submitForApproval": "Submit for approval", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__inlineActions__title__duplicate": "Duplicate", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__id__title": "Supplier ID", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line_4__title": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line_5__title": "Earliest expected date", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line1__title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line12__title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line13__title": "Tax", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__columns__title__rounding": "Rounding", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line14__title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line2Right__title": "Order date", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line3__title": "Purchasing site", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line6__title": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line8__title": "Printed", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__line9__title": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__10": "Closed", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__11": "Tax calculation failed", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__4": "Pending approval", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__5": "Approved", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__6": "Confirmed", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__7": "Partially received", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__8": "Received", "@sage/xtrem-purchasing/pages__purchase_order____navigationPanel__optionsMenu__title__9": "Rejected", "@sage/xtrem-purchasing/pages__purchase_order____objectTypePlural": "Purchase orders", "@sage/xtrem-purchasing/pages__purchase_order____objectTypeSingular": "Purchase order", "@sage/xtrem-purchasing/pages__purchase_order____title": "Purchase order", "@sage/xtrem-purchasing/pages__purchase_order__amountsInCompanyCurrencyBlock____title": "Amounts company currency", "@sage/xtrem-purchasing/pages__purchase_order__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-purchasing/pages__purchase_order__approval_status_updated": "Approval status updated.", "@sage/xtrem-purchasing/pages__purchase_order__approvalStatus____title": "Approval status", "@sage/xtrem-purchasing/pages__purchase_order__approve____title": "Approve", "@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_content": "You are about to approve this order.", "@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_title": "Confirm approval", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____lookupDialogTitle": "Select supplier", "@sage/xtrem-purchasing/pages__purchase_order__businessRelation____title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_order__changeRequestedDescription____title": "Change requested", "@sage/xtrem-purchasing/pages__purchase_order__changeRequestTextSection____title": "Requested changes", "@sage/xtrem-purchasing/pages__purchase_order__close____title": "Close order", "@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_content": "You are about to change the status of this order to closed. You can reopen this order later.", "@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_title": "Confirm close purchase order", "@sage/xtrem-purchasing/pages__purchase_order__companyCurrency____lookupDialogTitle": "Select company currency", "@sage/xtrem-purchasing/pages__purchase_order__companyCurrency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_order__companyCurrency____title": "Company currency", "@sage/xtrem-purchasing/pages__purchase_order__companyFxRate____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_order__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-purchasing/pages__purchase_order__confirm____title": "Confirm", "@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_content": "You are about to set this purchase order to \"Confirmed\"", "@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_title": "Confirm purchase order", "@sage/xtrem-purchasing/pages__purchase_order__confirm_status_updated": "Status updated.", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__email": "Email", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__firstName": "First name", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/pages__purchase_order__contacts____columns__title__title": "Title", "@sage/xtrem-purchasing/pages__purchase_order__contactSelectionBlock____title": "", "@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_content": "You are about to create a receipt from this purchase order.", "@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_title": "Confirm receipt creation", "@sage/xtrem-purchasing/pages__purchase_order__createPurchaseReceipt____title": "Create receipt", "@sage/xtrem-purchasing/pages__purchase_order__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_order__currency____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_order__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-purchasing/pages__purchase_order__currency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_order__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____columns__title__email": "Email", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____columns__title__firstName": "First name", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____lookupDialogTitle": "Select default buyer", "@sage/xtrem-purchasing/pages__purchase_order__defaultBuyer____title": "Default buyer", "@sage/xtrem-purchasing/pages__purchase_order__defaultDimension____title": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_order__deletePurchaseOrder____title": "Delete", "@sage/xtrem-purchasing/pages__purchase_order__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-purchasing/pages__purchase_order__deliveryMode____title": "Delivery mode", "@sage/xtrem-purchasing/pages__purchase_order__displayStatus____title": "Display status", "@sage/xtrem-purchasing/pages__purchase_order__earliestExpectedDateTile____title": "Earliest expected date", "@sage/xtrem-purchasing/pages__purchase_order__email_not_sent": "Could not send request email.", "@sage/xtrem-purchasing/pages__purchase_order__email_sent": "<PERSON><PERSON> sent.", "@sage/xtrem-purchasing/pages__purchase_order__emailAddress____helperText": "A purchase order will be sent to this address.", "@sage/xtrem-purchasing/pages__purchase_order__emailAddress____title": "Email", "@sage/xtrem-purchasing/pages__purchase_order__emailAddressChanges____helperText": "A request for changes will be sent to this address", "@sage/xtrem-purchasing/pages__purchase_order__emailAddressChanges____title": "To", "@sage/xtrem-purchasing/pages__purchase_order__emailFirstName____title": "First name", "@sage/xtrem-purchasing/pages__purchase_order__emailLastName____title": "Last name", "@sage/xtrem-purchasing/pages__purchase_order__emailTitle____title": "Title", "@sage/xtrem-purchasing/pages__purchase_order__externalNote____helperText": "Notes display on supplier documents.", "@sage/xtrem-purchasing/pages__purchase_order__externalNote____title": "Supplier notes", "@sage/xtrem-purchasing/pages__purchase_order__externalNoteLine____helperText": "Notes display on supplier documents.", "@sage/xtrem-purchasing/pages__purchase_order__externalNoteLine____title": "Supplier line notes", "@sage/xtrem-purchasing/pages__purchase_order__financialBlock____title": "Financial", "@sage/xtrem-purchasing/pages__purchase_order__financialSection____title": "Financial", "@sage/xtrem-purchasing/pages__purchase_order__fxRateDate____title": "Exchange rate date", "@sage/xtrem-purchasing/pages__purchase_order__headerSection____title": "Header section", "@sage/xtrem-purchasing/pages__purchase_order__informationSection____title": "Information", "@sage/xtrem-purchasing/pages__purchase_order__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_order__internalNote____title": "Internal notes", "@sage/xtrem-purchasing/pages__purchase_order__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_order__internalNoteLine____title": "Internal line notes", "@sage/xtrem-purchasing/pages__purchase_order__invoiceStatus____title": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_order__isExternalNote____title": "Add notes to the supplier document", "@sage/xtrem-purchasing/pages__purchase_order__isExternalNoteLine____title": "Add notes to the supplier document", "@sage/xtrem-purchasing/pages__purchase_order__isPrinted____title": "Printed", "@sage/xtrem-purchasing/pages__purchase_order__isSent____title": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_order__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_order__itemsSection____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_order__just_save": "Do you want to repost now or after you edited dimensions on other source documents?", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____columns__title__actualAllocatedCostAmountInCompanyCurrency": "Actual allocated cost amount in company currency", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____columns__title__actualCostAmountInCompanyCurrency": "Actual cost amount in company currency", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____columns__title__landedCostType": "Type", "@sage/xtrem-purchasing/pages__purchase_order__landedCosts____title": "Summary by landed cost type", "@sage/xtrem-purchasing/pages__purchase_order__landedCostsSection____title": "Landed costs", "@sage/xtrem-purchasing/pages__purchase_order__landedCostsSectionBlock____title": "Total in company currency", "@sage/xtrem-purchasing/pages__purchase_order__line_close_action_dialog_content": "You are about to close this purchase order line.", "@sage/xtrem-purchasing/pages__purchase_order__line_close_action_dialog_title": "Confirm update", "@sage/xtrem-purchasing/pages__purchase_order__line_delete_action_dialog_content": "You are about to delete this purchase order line. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_order__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-purchasing/pages__purchase_order__line_open_action_dialog_content": "You are about to reopen this purchase order line.", "@sage/xtrem-purchasing/pages__purchase_order__line_reopen_action_dialog_title": "Confirm reopen purchase order line", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__columns__title__id": "ID", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__title": "Name", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__title__2": "ID", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__site__title__3": "Company", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__stockSite__title": "Name", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__stockSite__title__2": "ID", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__stockSite__title__3": "Company", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__unit__name__title": "Name", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__columns__unit__name__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__site": "Select purchasing site", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__stockSite": "Select receiving site", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__lookupDialogTitle__unit__name": "Select unit", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__postfix__charge": "%", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__postfix__discount": "%", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__amountIncludingTaxInCompanyCurrency": "Total including tax company currency", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__changeRequestedDescription": "Change requested", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__charge": "Charge", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__discount": "Discount", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__expectedReceiptDate": "Expected receipt date", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__isExternalNote": "Add notes to the supplier document", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__item__id": "Item ID", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__item__status": "Item status", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineInvoiceStatus": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__lineReceiptStatus": "Receipt status", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__netPrice": "Net price", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__origin": "Origin", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityReceivedInProgress": "Quantity received in progress", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityToReceive": "Remaining quantity to receive", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__quantityToReceiveInStockUnit": "Quantity to receive in stock unit", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__receivedQuantity": "Quantity received", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__receivedQuantityProgress": "Receipt progression", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__site": "Purchasing site", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__status": "Status", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__stockSite": "Receiving site", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__stockSiteLinkedAddress__name": "Receiving address", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__taxAmount": "Total tax", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__taxCalculationStatus": "Tax status", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__uDemandOrderLineLink": "Assigned to", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__unit__name": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_order__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__assignOrder": "Assign order", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__close": "Close", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__delete": "Delete", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__dimensions": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__landedCosts": "Landed costs", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__priceList": "Price list", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__projectedStock": "Projected stock", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__Reopen": "Reopen", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__taxDetails": "Tax details", "@sage/xtrem-purchasing/pages__purchase_order__lines____dropdownActions__title__updatePrice": "Update price", "@sage/xtrem-purchasing/pages__purchase_order__lines____inlineActions__title__openLinePanel": "Open line panel", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__line2__title": "Description", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__title__title": "Product", "@sage/xtrem-purchasing/pages__purchase_order__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_order__lines____optionsMenu__title": "All statuses", "@sage/xtrem-purchasing/pages__purchase_order__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title": "Close", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__2": "Update price", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__3": "Assign order", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__4": "Projected stock", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__5": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__6": "Price list", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__7": "Tax details", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__8": "Landed costs", "@sage/xtrem-purchasing/pages__purchase_order__lines____sidebar__headerDropdownActions__title__9": "Delete", "@sage/xtrem-purchasing/pages__purchase_order__lines____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_order__minimumOrderAmount____title": "Minimum order amount", "@sage/xtrem-purchasing/pages__purchase_order__no_price": "You are about to submit the purchase order for approval. Some lines have no unit price.", "@sage/xtrem-purchasing/pages__purchase_order__no_price_title": "Confirm price", "@sage/xtrem-purchasing/pages__purchase_order__noteBlock____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_order__notesSection____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_order__number____title": "Number", "@sage/xtrem-purchasing/pages__purchase_order__order_date_cannot_be_future": "The order date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_order__orderDate____title": "Order date", "@sage/xtrem-purchasing/pages__purchase_order__paymentTerm____columns__title__description": "Description", "@sage/xtrem-purchasing/pages__purchase_order__paymentTerm____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-purchasing/pages__purchase_order__print____title": "Print", "@sage/xtrem-purchasing/pages__purchase_order__printed": "The purchase order was printed.", "@sage/xtrem-purchasing/pages__purchase_order__purchase_order_closed": "The purchase order is closed.", "@sage/xtrem-purchasing/pages__purchase_order__purchase_order_opened": "The purchase order is open.", "@sage/xtrem-purchasing/pages__purchase_order__purchase_order_status_update_fail": "Update Purchase order status failed - Purchase order status is not \"pending approval\".", "@sage/xtrem-purchasing/pages__purchase_order__purchase_order_status_update_success": "Update Purchase order status done. You will be redirected to the Purchase order page.", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title___id": "Invoice number", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title__invoicedQuantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title__invoicedUnitPrice": "Unit price", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____columns__title__purchaseInvoiceLine__document__status": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_order__purchaseInvoiceLines____title": "Purchase order line to purchase invoice line", "@sage/xtrem-purchasing/pages__purchase_order__purchaseOrderExcludingValue____title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_order__purchaseOrderValue____title": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____columns__title___id": "Receipt number", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____columns__title__purchaseReceiptLine__status": "Receipt status", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____columns__title__receivedQuantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_order__purchaseReceiptLines____title": "Purchase order line to purchase receipt line", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title___id": "Requisition", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title___id__2": "Available quantity to order", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title__orderedQuantity": "Requisition quantity", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____columns__title__purchaseRequisitionLine__status": "Status", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLines____title": "Purchase requisition line to purchase order line", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title___id": "Requisition", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title___id__2": "Available quantity to order", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title__orderedQuantity": "Requisition quantity", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title__orderedQuantityInStockUnit": "Order progression", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____columns__title__purchaseRequisitionLine__status": "Status", "@sage/xtrem-purchasing/pages__purchase_order__purchaseRequisitionLineToPurchaseOrderLines____title": "Purchase requisition line to purchase order line", "@sage/xtrem-purchasing/pages__purchase_order__rateDescription____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_order__receipt_created": "Receipt created: {{receiptNumbers}}.", "@sage/xtrem-purchasing/pages__purchase_order__receipt_not_created": "Could not create receipt.", "@sage/xtrem-purchasing/pages__purchase_order__receiptStatus____title": "Receipt status", "@sage/xtrem-purchasing/pages__purchase_order__reject____title": "Reject", "@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_content": "You are about to reject this order.", "@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_title": "Confirm rejection", "@sage/xtrem-purchasing/pages__purchase_order__reopen_order_dialog_content": "You are about to reopen this order.", "@sage/xtrem-purchasing/pages__purchase_order__reopen_order_dialog_title": "Confirm reopen purchase order", "@sage/xtrem-purchasing/pages__purchase_order__reopenOrder____title": "Reopen order", "@sage/xtrem-purchasing/pages__purchase_order__repost": "Repost", "@sage/xtrem-purchasing/pages__purchase_order__repost____title": "Repost", "@sage/xtrem-purchasing/pages__purchase_order__request_approval_dialog__send_approval_request_confirm_title": "Purchase order approval request", "@sage/xtrem-purchasing/pages__purchase_order__requestApproval____title": "Submit for approval", "@sage/xtrem-purchasing/pages__purchase_order__requestChanges____title": "Request changes", "@sage/xtrem-purchasing/pages__purchase_order__requestChangesSection____title": "Request changes", "@sage/xtrem-purchasing/pages__purchase_order__save____title": "Save", "@sage/xtrem-purchasing/pages__purchase_order__save_only": "Save without posting", "@sage/xtrem-purchasing/pages__purchase_order__save_warnings": "Warnings while saving:", "@sage/xtrem-purchasing/pages__purchase_order__select_bill_to_contact_button_text": "Select supplier contact", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__email": "Email", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__firstName": "First name", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/pages__purchase_order__selectedContact____columns__title__title": "Title", "@sage/xtrem-purchasing/pages__purchase_order__selectFromRequisition____title": "Add lines from requisitions", "@sage/xtrem-purchasing/pages__purchase_order__send_change_request_button_text": "Send", "@sage/xtrem-purchasing/pages__purchase_order__send_order_button_text": "Send", "@sage/xtrem-purchasing/pages__purchase_order__send_order_dialog_content": "You are about to send the purchase order email.", "@sage/xtrem-purchasing/pages__purchase_order__send_order_dialog_title": "Purchase order email sending confirmation", "@sage/xtrem-purchasing/pages__purchase_order__sendEmail____title": "Send", "@sage/xtrem-purchasing/pages__purchase_order__sendEmailBlock____title": "To", "@sage/xtrem-purchasing/pages__purchase_order__sendEmailSection____title": "Send purchase order", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__columns__legalCompany__name__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__columns__legalCompany__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__title__id": "ID ", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__purchase_order__site____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__site____lookupDialogTitle": "Select purchase site", "@sage/xtrem-purchasing/pages__purchase_order__site____title": "Purchasing site", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____addButtonText": "Add site address", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____dropdownActions__title__edit": "Edit", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____dropdownActions__title__readOnly": "Read-only", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____dropdownActions__title__replace": "Replace", "@sage/xtrem-purchasing/pages__purchase_order__siteAddress____title": "Site address", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__columns__country__title": "Name", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__isActive": "Active", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____columns__title__region": "Region", "@sage/xtrem-purchasing/pages__purchase_order__siteBusinessEntityAddress____title": "Site address", "@sage/xtrem-purchasing/pages__purchase_order__status____title": "Status", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_approving": "Approve", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_confirm": "Confirm", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_creation": "Create", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_invoicing": "Invoice", "@sage/xtrem-purchasing/pages__purchase_order__step_sequence_receiving": "Receive", "@sage/xtrem-purchasing/pages__purchase_order__stockSite____lookupDialogTitle": "Select receiving site", "@sage/xtrem-purchasing/pages__purchase_order__stockSite____title": "Receiving site", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____addButtonText": "Add site address", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____dropdownActions__title__edit": "Edit", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____dropdownActions__title__readOnly": "Read-only", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____dropdownActions__title__replace": "Replace", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteAddress____title": "Receiving site address", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__columns__country__title": "Region label", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__columns__country__title__2": "ZIP label", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____columns__title__region": "Region", "@sage/xtrem-purchasing/pages__purchase_order__stockSiteLinkedAddress____title": "Receiving site address", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____addButtonText": "Add supplier address", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__columns__country__title": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__lookupDialogTitle__country": "Select country", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____dropdownActions__title__edit": "Edit", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____dropdownActions__title__readOnly": "Read-only", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____dropdownActions__title__replace": "Replace", "@sage/xtrem-purchasing/pages__purchase_order__supplierAddress____title": "Supplier address", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__columns__country__title": "Name", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__columns__country__title__2": "ISO 3166-1 alpha-2 code", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__addressLine1": "Address line 1", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__addressLine2": "Address line 2", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__city": "City", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__isActive": "Active", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____columns__title__region": "Region", "@sage/xtrem-purchasing/pages__purchase_order__supplierLinkedAddress____title": "Supplier address", "@sage/xtrem-purchasing/pages__purchase_order__taxCalculationStatus____title": "Tax calculation status", "@sage/xtrem-purchasing/pages__purchase_order__taxEngine____title": "Tax engine", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__postfix__taxRate": "%", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__isReverseCharge": "Reverse charge", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__tax": "Tax", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxAmount": "Amount", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxCategory": "Category", "@sage/xtrem-purchasing/pages__purchase_order__taxes____columns__title__taxRate": "Rate", "@sage/xtrem-purchasing/pages__purchase_order__taxes____title": "Summary by tax", "@sage/xtrem-purchasing/pages__purchase_order__totalActualLandedCostsInCompanyCurrency____title": "Actual landed costs", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountIncludingTax____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_order__totalAmountIncludingTaxInCompanyCurrency____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_order__totalsBlock____title": "Totals", "@sage/xtrem-purchasing/pages__purchase_order__totalsSection____title": "Totals", "@sage/xtrem-purchasing/pages__purchase_order__totalTaxAmount____title": "Tax", "@sage/xtrem-purchasing/pages__purchase_order__totalTaxAmountAdjusted____title": "Total tax adjusted", "@sage/xtrem-purchasing/pages__purchase_order__update_price": "Recalculate", "@sage/xtrem-purchasing/pages__purchase_order__warning_dialog_content": "You are about to update the purchase order sent to the supplier.", "@sage/xtrem-purchasing/pages__purchase_order__warning_dialog_title": "Warning update", "@sage/xtrem-purchasing/pages__purchase_order_approval____title": "Purchase order approval", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel____title": "Assigned orders", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignedQuantity____title": "Assigned quantity", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__demandDocumentLine__documentNumber": "Order", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__demandType": "Assigned to", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__demandWorkInProgress__expectedQuantity": "Quantity on order", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__quantityInStockUnit": "Assigned quantity", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____columns__title__quantityNotAssigned": "Quantity not assigned ", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____dropdownActions__title": "Delete", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__assignmentLines____title": "Assigned orders", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__cancel____title": "Cancel", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__componentBlock____title": "General", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__confirm____title": "Save", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__remainingQuantity____title": "Remaining quantity", "@sage/xtrem-purchasing/pages__purchase_order_assignment_details_panel__requiredQuantity____title": "Required quantity", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__assigned_quantity_higher_than_purchase_order_line_quantity_in_stock_unit": "You have assigned more than the purchase quantity. You need to reduce the assigned quantity.", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_on_order": "You have assigned more than the quantity on order. You need to reduce the assigned quantity.", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line_deletion_dialog_content": "You are about to delete the link to the order.", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line_deletion_dialog_title": "Delete assigned line", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line-cancel": "Cancel", "@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line-delete": "Delete", "@sage/xtrem-purchasing/pages__purchase_order_line_close_action_allowed_line_closed": "The purchase order line is closed.", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__dropdownActions__title": "Receipt lines", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__dropdownActions__title__2": "Invoice lines", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__amountExcludingTax__title": "Ordered amount", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__amountExcludingTaxInCompanyCurrency__title": "Ordered amount in company currency", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyCurrencyId__title": "Company currency ID", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyCurrencyName__title": "Company currency", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyId__title": "Company ID", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__companyName__title": "Company", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__currencyId__title": "Currency ID", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__expectedReceiptDate__title": "Expected receipt date", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__invoiceStatus__title": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__itemId__title": "Item Id", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__lineReceiptStatus__title": "Receipt status", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__netPrice__title": "Net price", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__orderDate__title": "Order date", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__purchaseOrderNumber__title": "Purchase order", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__quantityToReceive__title": "Remaining quantity", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__remainingAmountToReceiveExcludingTax__title": "Remaining amount", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__remainingAmountToReceiveExcludingTaxInCompanyCurrency__title": "Remaining amount in company currency", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__siteId__title": "Purchasing site ID", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__siteName__title": "Purchasing site", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierId__title": "Supplier ID", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierItem__title": "Supplier item ID", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierItemCode__title": "Supplier item code", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierItemName__title": "Supplier item name", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__supplierName__title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____navigationPanel__listItem__unit__title": "Unit", "@sage/xtrem-purchasing/pages__purchase_order_line_inquiry____title": "Purchase order line", "@sage/xtrem-purchasing/pages__purchase_order_line_open_action_allowed_line_opened": "The purchase order line is reopened.", "@sage/xtrem-purchasing/pages__purchase_order_line_panel__quantity_in_purchase_unit_negative_value": "The quantity in purchase unit cannot be less than or equal to 0.", "@sage/xtrem-purchasing/pages__purchase_order_send__email_cannot_be_sent": "The purchase order cannot be sent by email.", "@sage/xtrem-purchasing/pages__purchase_order_send__email_exception": "Could not send purchase order email. ({{exception}})", "@sage/xtrem-purchasing/pages__purchase_order_send__email_sent": "Purchase order sent to {{email}}.", "@sage/xtrem-purchasing/pages__purchase_order_send__invalid-email": "Email address incorrect: {{email}}", "@sage/xtrem-purchasing/pages__purchase_order_send__order_cannot_be_sent": "The purchase order has been sent but due to a technical error the 'Sent' checkbox could not be updated.", "@sage/xtrem-purchasing/pages__purchase_order_submit_for_approval__tax_calculation_failed": "You need to resolve tax calculation issues before submitting for approval.", "@sage/xtrem-purchasing/pages__purchase_order_suggestion____title": "Purchase order suggestion", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__businessRelation____lookupDialogTitle": "Select supplier", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__businessRelation____title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__creation__success": "{{num}} purchase order(s) updated", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__creation__success_multi": "{{num}} purchase orders updated", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__criteriaBlock____title": "Selection criteria", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__issueDateFrom____title": "Order date from", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__issueDateTo____title": "Order date to", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemFrom____columns__title__category__name": "Category", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemFrom____lookupDialogTitle": "Select item from", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemFrom____title": "Item from", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemTo____columns__title__category__name": "Category", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemTo____lookupDialogTitle": "Select item to", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__itemTo____title": "Item to", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__mainBlock____title": "Purchase order suggestions", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__mainSection____title": "General", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__document__number": "Purchase order number", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__expectedReceiptDate": "Expected receipt date", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__grossPrice": "Price", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__item__id": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__item__name": "Item name", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__itemDescription": "Item description", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__quantity": "Quantity", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__supplierName": "Supplier", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__orderLines____columns__title__unit__name": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__stockSite____lookupDialogTitle": "Select site", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__stockSite____placeholder": "Select site", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__stockSite____title": "Site", "@sage/xtrem-purchasing/pages__purchase_order_suggestion__updatePurchaseOrder____title": "Validate", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm__lower_greater_quantity": "You are about to create one or more receipt lines with a quantity less or greater than the ordered quantity.", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm_greater_quantity": "You are about to create one or more receipt lines with a quantity greater than the ordered quantity.", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm_lower_quantity": "You are about to create one or more receipt lines with a quantity less than the ordered quantity.", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__create_purchase_dialog_title": "Confirm receipt quantity", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__warning_over_ordered": "You have over ordered this line.", "@sage/xtrem-purchasing/pages__purchase_order_table_panel__warning_under_ordered": "You have ordered less than needed for this line.", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title": "Post stock", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__2": "Create return", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__3": "Print", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__4": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__dropdownActions__title__5": "Delete", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__id__title": "Supplier ID", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line_4__title": "Receiving site", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line_5__title": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line10__title": "Supplier packing slip", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line11__title": "Carrier", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line2__title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line2Right__title": "Receipt date", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__columns__title__rounding": "Rounding", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line6__title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line7__title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__line8__title": "Tax", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__10": "Partially returned", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__11": "Returned", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__12": "Closed", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__4": "Tax calculation failed", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__5": "Posting in progress", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__6": "Error", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__7": "Received", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__8": "Partially invoiced", "@sage/xtrem-purchasing/pages__purchase_receipt____navigationPanel__optionsMenu__title__9": "Invoiced", "@sage/xtrem-purchasing/pages__purchase_receipt____objectTypePlural": "Purchase receipts", "@sage/xtrem-purchasing/pages__purchase_receipt____objectTypeSingular": "Purchase receipt", "@sage/xtrem-purchasing/pages__purchase_receipt____title": "Purchase receipt", "@sage/xtrem-purchasing/pages__purchase_receipt__actualOnReceiptQuantity____title": "Actual quantity", "@sage/xtrem-purchasing/pages__purchase_receipt__amountsInCompanyCurrencyBlock____title": "Amounts company currency", "@sage/xtrem-purchasing/pages__purchase_receipt__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____lookupDialogTitle": "Select supplier", "@sage/xtrem-purchasing/pages__purchase_receipt__businessRelation____title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_receipt__carriageBlock____title": "Carriage", "@sage/xtrem-purchasing/pages__purchase_receipt__carrier____lookupDialogTitle": "Select carrier", "@sage/xtrem-purchasing/pages__purchase_receipt__carrier____title": "Carrier", "@sage/xtrem-purchasing/pages__purchase_receipt__companyCurrency____lookupDialogTitle": "Select currency (company)", "@sage/xtrem-purchasing/pages__purchase_receipt__companyCurrency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_receipt__companyCurrency____title": "Currency (company)", "@sage/xtrem-purchasing/pages__purchase_receipt__companyFxRate____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_receipt__companyFxRateDivisor____title": "Exchange rate divisor", "@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_content": "You are about to create a return from this purchase receipt.", "@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_lines_without_supplier": "Only lines containing a supplier are included.", "@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_title": "Confirm return creation", "@sage/xtrem-purchasing/pages__purchase_receipt__createPurchaseReturn____title": "Create return", "@sage/xtrem-purchasing/pages__purchase_receipt__currency____lookupDialogTitle": "Select currency (transaction)", "@sage/xtrem-purchasing/pages__purchase_receipt__currency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_receipt__currency____title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_receipt__customSave____title": "Save", "@sage/xtrem-purchasing/pages__purchase_receipt__date____title": "Receipt date", "@sage/xtrem-purchasing/pages__purchase_receipt__defaultDimension____title": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_receipt__delete_confirmation": "Record deleted", "@sage/xtrem-purchasing/pages__purchase_receipt__displayStatus____title": "Display status", "@sage/xtrem-purchasing/pages__purchase_receipt__financialBlock____title": "Financial", "@sage/xtrem-purchasing/pages__purchase_receipt__financialSection____title": "Financial", "@sage/xtrem-purchasing/pages__purchase_receipt__fxRateDate____title": "Exchange rate date", "@sage/xtrem-purchasing/pages__purchase_receipt__goToSysNotificationPage____title": "Retry", "@sage/xtrem-purchasing/pages__purchase_receipt__headerSection____title": "General", "@sage/xtrem-purchasing/pages__purchase_receipt__informationBlock____title": "Information", "@sage/xtrem-purchasing/pages__purchase_receipt__informationSection____title": "Information", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNote____title": "Internal notes", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_receipt__internalNoteLine____title": "Internal line notes", "@sage/xtrem-purchasing/pages__purchase_receipt__invoiceStatus____title": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_receipt__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_receipt__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_receipt__itemsSection____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_receipt__just_save": "Do you want to repost now or after you edited dimensions on other source documents?", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCosts____columns__title__actualCostAmountInCompanyCurrency": "Actual cost amount in company currency", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCosts____columns__title__landedCostType": "Type", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCosts____title": "Summary by landed cost type", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCostsSection____title": "Landed costs", "@sage/xtrem-purchasing/pages__purchase_receipt__landedCostsSectionBlock____title": "Total in company currency", "@sage/xtrem-purchasing/pages__purchase_receipt__line_delete_action_dialog_content": "You are about to delete this purchase receipt line. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_receipt__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_content": "You are about to post this purchase receipt with lines that have values equal to zero.", "@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_title": "Confirm posting of lines with values equal to zero", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title": "Name", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__2": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__3": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__4": "Rounding", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__currency__id__title__5": "Symbol", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__stockUnit__symbol__title": "Symbol", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__stockUnit__symbol__title__2": "Name", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__title__expirationDate": "Expiration date", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__title__item__name": "Item name", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__unit__symbol__title": "Name", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__columns__unit__symbol__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle": "Select location", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__2": "Select status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__3": "Select lot", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__lookupDialogTitle__unit__symbol": "Select unit", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__nestedFields__purchaseOrderLine__columns__columns__document__number__title": "Number", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__nestedFields__purchaseOrderLine__title": "Purchase receipt line", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__nestedFields__purchaseOrderLine__title__2": "Received quantity", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__postfix__charge": "%", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__postfix__discount": "%", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title": "Purchase order link", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__2": "Location", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__3": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__4": "Lot", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__5": "Expiration date", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__actualLandedCostInCompanyCurrency": "Actual landed cost in company currency", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__amountIncludingTax": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__amountIncludingTaxInCompanyCurrency": "Total including tax company currency", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__charge": "Charge", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__discount": "Discount", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__item__id": "Item ID", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineInvoiceStatus": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__lineReturnStatus": "Return status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__netPrice": "Net price", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__origin": "Origin", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__purchaseOrderLine__purchaseReceiptLine__quantity": "Received quantity", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__quantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__status": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__stockDetailStatus": "Stock detail status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__stockTransactionStatus": "Stock status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__taxAmount": "Total tax", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__taxCalculationStatus": "Tax status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__unit__symbol": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title": "Stock details", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__3": "Tax details", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__4": "Landed costs", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__5": "Delete", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____dropdownActions__title__updatePrice": "Update price", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____inlineActions__title": "Open line panel", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__line2__title": "Description", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__title__title": "Product", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____optionsMenu__title": "All statuses", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____optionsMenu__title__3": "Stock details required", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title": "Stock details", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__2": "Update price", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__3": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__4": "Tax details", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__5": "Landed costs", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____sidebar__headerDropdownActions__title__6": "Delete", "@sage/xtrem-purchasing/pages__purchase_receipt__lines____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_receipt__newOnReceiptQuantity____title": "New quantity", "@sage/xtrem-purchasing/pages__purchase_receipt__notesBlock____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_receipt__notesSection____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_receipt__number____title": "Number", "@sage/xtrem-purchasing/pages__purchase_receipt__onReceiptQuantityChangeSection____title": "On order quantity change", "@sage/xtrem-purchasing/pages__purchase_receipt__post____title": "Post stock", "@sage/xtrem-purchasing/pages__purchase_receipt__post_from_main_row": "Receipt posted", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__documentNumber": "Document number", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__documentType": "Document type", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____columns__title__status": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt__postingDetails____title": "Results", "@sage/xtrem-purchasing/pages__purchase_receipt__postingMessageBlock____title": "Error details", "@sage/xtrem-purchasing/pages__purchase_receipt__postingSection____title": "Posting", "@sage/xtrem-purchasing/pages__purchase_receipt__print____title": "Print", "@sage/xtrem-purchasing/pages__purchase_receipt__printed": "The purchase receipt was printed.", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____columns__title___id": "Invoice number", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____columns__title__invoicedQuantity": "Quantity invoiced", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____columns__title__purchaseInvoiceLine__document__status": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseInvoiceLines____title": "Purchase receipt line to purchase invoice line", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title": "Received order", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title__2": "Quantity to receive", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title__3": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__columns__purchaseOrderLine__document__orderDate__title__4": "Gross price", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__title___id": "Available quantity to receive", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__title__purchaseOrderLine__status": "Order line status", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____columns__title__purchaseReceiptLine__quantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseOrderLines____title": "Purchase order line to purchase receipt line", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReceiptLineCount____title": "Number of items", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____columns__title___id": "Return number", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____columns__title__purchaseReturnLine__status": "Return status", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____columns__title__returnedQuantity": "Quantity returned", "@sage/xtrem-purchasing/pages__purchase_receipt__purchaseReturnLines____title": "Purchase receipt line to purchase return line", "@sage/xtrem-purchasing/pages__purchase_receipt__rateDescription____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_receipt__receipt_date_cannot_be_future": "The receipt date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingAddress____columns__title__concatenatedAddress": "Receiving address", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingAddress____title": "Receiving address", "@sage/xtrem-purchasing/pages__purchase_receipt__receivingSection____title": "Receiving", "@sage/xtrem-purchasing/pages__purchase_receipt__repost": "Repost", "@sage/xtrem-purchasing/pages__purchase_receipt__repost____title": "Repost", "@sage/xtrem-purchasing/pages__purchase_receipt__repost_errors": "Errors occurred while reposting:", "@sage/xtrem-purchasing/pages__purchase_receipt__return_created": "Purchase return created: {{returnNumbers}}.", "@sage/xtrem-purchasing/pages__purchase_receipt__return_not_created": "The record was not created.", "@sage/xtrem-purchasing/pages__purchase_receipt__returnAddress____columns__title__concatenatedAddress": "Return-to address", "@sage/xtrem-purchasing/pages__purchase_receipt__returnAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_receipt__returnAddress____title": "Return to address", "@sage/xtrem-purchasing/pages__purchase_receipt__returnsBlock____title": "Returns", "@sage/xtrem-purchasing/pages__purchase_receipt__returnsSection____title": "Returns", "@sage/xtrem-purchasing/pages__purchase_receipt__returnStatus____title": "Return status", "@sage/xtrem-purchasing/pages__purchase_receipt__save____title": "Save", "@sage/xtrem-purchasing/pages__purchase_receipt__save_only": "Save without posting", "@sage/xtrem-purchasing/pages__purchase_receipt__save_warnings": "Warnings while saving:", "@sage/xtrem-purchasing/pages__purchase_receipt__selectFromPurchaseOrderLookup____title": "Add lines from orders", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__columns__legalCompany__name__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__columns__legalCompany__name__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__columns__legalCompany__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__id": "ID ", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_receipt__site____columns__title__primaryAddress___id": "Primary address", "@sage/xtrem-purchasing/pages__purchase_receipt__site____lookupDialogTitle": "Select receiving site", "@sage/xtrem-purchasing/pages__purchase_receipt__site____title": "Receiving site", "@sage/xtrem-purchasing/pages__purchase_receipt__status____title": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_creation": "Create", "@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_invoicing": "Invoice", "@sage/xtrem-purchasing/pages__purchase_receipt__step_sequence_return": "Return", "@sage/xtrem-purchasing/pages__purchase_receipt__stockTransactionStatus____title": "Stock status", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierAddress____columns__title__concatenatedAddress": "Supplier address", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierAddress____title": "Supplier address", "@sage/xtrem-purchasing/pages__purchase_receipt__supplierDocumentNumber____title": "Supplier packing slip", "@sage/xtrem-purchasing/pages__purchase_receipt__taxCalculationStatus____title": "Tax calculation status", "@sage/xtrem-purchasing/pages__purchase_receipt__taxEngine____title": "Tax engine", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__postfix__taxRate": "%", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__isReverseCharge": "Reverse charge", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__tax": "Tax", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxableAmount": "Taxable base", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxAmount": "Amount", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxCategory": "Category", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____columns__title__taxRate": "Rate", "@sage/xtrem-purchasing/pages__purchase_receipt__taxes____title": "Summary by tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalActualLandedCostsInCompanyCurrency____title": "Actual landed costs", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountIncludingTax____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalAmountIncludingTaxInCompanyCurrency____title": "Including tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalExcludingTaxValue____title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalIncludingTaxValue____title": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalsBlock____title": "Totals", "@sage/xtrem-purchasing/pages__purchase_receipt__totalsSection____title": "Totals", "@sage/xtrem-purchasing/pages__purchase_receipt__totalTaxAmount____title": "Tax", "@sage/xtrem-purchasing/pages__purchase_receipt__totalTaxAmountAdjusted____title": "Total tax adjusted", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__dropdownActions__title": "Invoice lines", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__amountExcludingTax__title": "Received amount", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__amountExcludingTaxInCompanyCurrency__title": "Received amount in company currency", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyCurrencyId__title": "Company currency ID", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyCurrencyName__title": "Company currency", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyId__title": "Company ID", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__companyName__title": "Company", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__currencyId__title": "Currency ID", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__date__title": "Receipt date", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__invoicedQuantity__title": "Invoiced quantity", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__invoiceStatus__title": "Invoice status", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__itemName__title": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__lineReturnStatus__title": "Return status", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__netPrice__title": "Net price", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__purchaseOrderNumber__title": "Purchase receipt", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__quantity__title": "Quantity", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__remainingQuantityToInvoice__title": "Remaining quantity to invoice", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__remainingReturnQuantity__title": "Remaining quantity to return", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__returnedQuantity__title": "Returned quantity", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__siteId__title": "Purchasing site ID", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__siteName__title": "Purchasing site", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__status__title": "Status", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__stockSiteId__title": "Receiving site ID", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__stockSiteName__title": "Receiving site", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierId__title": "Supplier ID", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierItem__title": "Supplier item", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierItemCode__title": "Supplier item code", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__supplierName__title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____navigationPanel__listItem__unit__title": "Unit", "@sage/xtrem-purchasing/pages__purchase_receipt_line_inquiry____title": "Purchase receipt line", "@sage/xtrem-purchasing/pages__purchase_receipt_post__stock_details_required": "You need to enter stock details for all lines before you can post.", "@sage/xtrem-purchasing/pages__purchase_receipt_post__tax_calculation_failed": "You need to correct lines that failed the tax calculation before you can post.", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title": "Create order", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__2": "Close requisition", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__3": "Submit for approval", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__4": "Confirm", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__5": "Approve", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__6": "Reject", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__7": "Apply default supplier", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__8": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__dropdownActions__title__9": "Delete", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__inlineActions__title": "Duplicate", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__line_4__title": "Site", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__line2__title": "Requester", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__line2Right__title": "Requisition date", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__10": "Confirmed", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__4": "Pending approval", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__5": "Approved", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__6": "Partially ordered", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__7": "Ordered", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__8": "Rejected", "@sage/xtrem-purchasing/pages__purchase_requisition____navigationPanel__optionsMenu__title__9": "Closed", "@sage/xtrem-purchasing/pages__purchase_requisition____objectTypePlural": "Purchase requisitions", "@sage/xtrem-purchasing/pages__purchase_requisition____objectTypeSingular": "Purchase requisition", "@sage/xtrem-purchasing/pages__purchase_requisition____title": "Purchase requisition", "@sage/xtrem-purchasing/pages__purchase_requisition__apply_default_supplier_success": "Default supplier applied", "@sage/xtrem-purchasing/pages__purchase_requisition__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-purchasing/pages__purchase_requisition__applyDefaultSuppliers____title": "Apply default supplier", "@sage/xtrem-purchasing/pages__purchase_requisition__approval_status_updated": "Approval status updated.", "@sage/xtrem-purchasing/pages__purchase_requisition__approvalStatus____title": "Approval status", "@sage/xtrem-purchasing/pages__purchase_requisition__approve____title": "Approve", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_content": "You are about to approve this requisition.", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_title": "Confirm approval", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_supplier_content": "You have selected a supplier that is not referenced for this item.", "@sage/xtrem-purchasing/pages__purchase_requisition__approve_supplier_title": "Confirm supplier", "@sage/xtrem-purchasing/pages__purchase_requisition__changeRequestTextSection____title": "Requested changes", "@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_content": "You are about to change the status of this requisition to closed. You cannot undo this change.", "@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_title": "Confirm status change", "@sage/xtrem-purchasing/pages__purchase_requisition__closeRequisition____title": "Close requisition", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm____title": "Confirm", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_content": "You are about to set this purchase requisition to \"Confirmed\"", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_title": "Confirm purchase requisition", "@sage/xtrem-purchasing/pages__purchase_requisition__confirm_status_updated": "Status updated.", "@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_content": "You are about to create a purchase order from this requisition.", "@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_lines_without_supplier": "Only lines containing a supplier are included.", "@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_title": "Confirm purchase order creation", "@sage/xtrem-purchasing/pages__purchase_requisition__createPurchaseOrder____title": "Create order", "@sage/xtrem-purchasing/pages__purchase_requisition__default_supplier": "<PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_requisition__defaultDimension____title": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_requisition__displayStatus____title": "Display status", "@sage/xtrem-purchasing/pages__purchase_requisition__earliestExpectedDate____title": "Earliest required date", "@sage/xtrem-purchasing/pages__purchase_requisition__email_not_sent": "Could not send request email.", "@sage/xtrem-purchasing/pages__purchase_requisition__email_sent": "<PERSON><PERSON> sent.", "@sage/xtrem-purchasing/pages__purchase_requisition__emailAddressChanges____helperText": "A request for changes will be sent to this address", "@sage/xtrem-purchasing/pages__purchase_requisition__emailAddressChanges____title": "To", "@sage/xtrem-purchasing/pages__purchase_requisition__headerSection____title": "General", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNote____title": "Internal notes", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_requisition__internalNoteLine____title": "Internal line notes", "@sage/xtrem-purchasing/pages__purchase_requisition__isTransferHeaderNote____title": "Repeat the document notes on new documents", "@sage/xtrem-purchasing/pages__purchase_requisition__isTransferLineNote____title": "Repeat all line notes", "@sage/xtrem-purchasing/pages__purchase_requisition__item_mandatory": "There is at least one line where the item or item description should be referenced.", "@sage/xtrem-purchasing/pages__purchase_requisition__itemsSection____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_requisition__line_close_action_dialog_content": "You are about to close this purchase requisition line. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_requisition__line_close_action_dialog_title": "Confirm update", "@sage/xtrem-purchasing/pages__purchase_requisition__line_delete_action_dialog_content": "You are about to delete this purchase requisition line. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_requisition__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__currency__name__title": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__currency__name__title__2": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__currency__name__title__3": "Symbol", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__columns__title__symbol__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__item__name__title__2": "Image", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__stockUnit__name__title": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__stockUnit__name__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__supplier__title": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__supplier__title__2": "ID", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__unit__name__title": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__columns__unit__name__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__currency__name": "Select currency", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__stockUnit__name": "Select unit", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__lookupDialogTitle__unit__name": "Select unit", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__postfix__charge": "%", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__postfix__discount": "%", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__approvalStatus": "Approval status", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__charge": "Charge", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__currency__name": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__discount": "Discount", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__item__id": "Item ID", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__lineOrderStatus": "Order status", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__needByDate": "Required date", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__netPrice": "Net price", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__orderedPercentage": "Order progress", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__orderedQuantity": "Ordered quantity", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__quantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__quantityToOrder": "Quantity to order", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__requestedItemDescription": "Item description", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__site__businessEntity__name": "Site", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__status": "Status", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__stockUnit__name": "Stock unit", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__totalTaxExcludedAmount": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__unit__name": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____dropdownActions__title": "Close", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____dropdownActions__title__2": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____dropdownActions__title__3": "Delete", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____dropdownActions__title__updatePrice": "Update price", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____inlineActions__title": "Open line panel", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__line2__title": "Description", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__line2Right__title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__title__title": "Product", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____optionsMenu__title": "All statuses", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____sidebar__headerDropdownActions__title": "Close", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____sidebar__headerDropdownActions__title__2": "Update price", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____sidebar__headerDropdownActions__title__3": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____sidebar__headerDropdownActions__title__4": "Delete", "@sage/xtrem-purchasing/pages__purchase_requisition__lines____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_requisition__no_coefficient_exists": "No coefficient exists for the selected purchase unit and the item stock unit, please add the coefficient for the units.", "@sage/xtrem-purchasing/pages__purchase_requisition__noteSection____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_requisition__number____title": "Number", "@sage/xtrem-purchasing/pages__purchase_requisition__orderStatus____title": "Order status", "@sage/xtrem-purchasing/pages__purchase_requisition__other_supplier": "Other", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_order_created": "Purchase order(s) created: {{orderNumbers}}.", "@sage/xtrem-purchasing/pages__purchase_requisition__Purchase_order_not_created": "Could not create purchase order.", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_requisition_status_update_fail": "Update Purchase requisition status failed - Purchase requisition status is not \"pending approval\".", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_requisition_status_update_success": "Update Purchase requisition status done. You will be redirected to the Purchase requisition page.", "@sage/xtrem-purchasing/pages__purchase_requisition__purchase_unit_mandatory": "Purchase unit is mandatory for item {item}.", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title___id": "Order number", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title__orderedQuantity": "Quantity ordered", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title__purchaseOrderLine__document__orderDate": "Order date", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____columns__title__purchaseOrderLine__status": "Order status", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseOrderLines____title": "Purchase requisition line to purchase order line", "@sage/xtrem-purchasing/pages__purchase_requisition__purchaseRequisitionLineCount____title": "Number of items", "@sage/xtrem-purchasing/pages__purchase_requisition__referenced_supplier": "Referenced", "@sage/xtrem-purchasing/pages__purchase_requisition__reject____title": "Reject", "@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_content": "You are about to reject this requisition.", "@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_title": "Confirm rejection", "@sage/xtrem-purchasing/pages__purchase_requisition__request_approval_dialog__send_approval_request_confirm_title": "Purchase requisition approval request", "@sage/xtrem-purchasing/pages__purchase_requisition__request_date_cannot_be_future": "The request date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_requisition__requestApproval____title": "Submit for approval", "@sage/xtrem-purchasing/pages__purchase_requisition__requestChanges____title": "Request changes", "@sage/xtrem-purchasing/pages__purchase_requisition__requestChangesSection____title": "Request changes", "@sage/xtrem-purchasing/pages__purchase_requisition__requestDate____title": "Requisition date", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____columns__title__email": "Email", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____columns__title__firstName": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____columns__title__lastName": "Last name", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____lookupDialogTitle": "Select requester", "@sage/xtrem-purchasing/pages__purchase_requisition__requester____title": "Requester", "@sage/xtrem-purchasing/pages__purchase_requisition__required_date_cannot_be_before_requisition_date": "You need to select a required date that is on or after the requisition date.", "@sage/xtrem-purchasing/pages__purchase_requisition__save____title": "Save", "@sage/xtrem-purchasing/pages__purchase_requisition__select_supplier_link_text": "Select supplier", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__selectedSupplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__purchase_requisition__send_change_request_button_text": "Send", "@sage/xtrem-purchasing/pages__purchase_requisition__site____columns__title__id": "ID ", "@sage/xtrem-purchasing/pages__purchase_requisition__site____columns__title__legalCompany__description": "Company", "@sage/xtrem-purchasing/pages__purchase_requisition__site____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__site____lookupDialogTitle": "Select site", "@sage/xtrem-purchasing/pages__purchase_requisition__site____title": "Purchasing site", "@sage/xtrem-purchasing/pages__purchase_requisition__status____title": "Status", "@sage/xtrem-purchasing/pages__purchase_requisition__status_not_updated": "Unable to update status.", "@sage/xtrem-purchasing/pages__purchase_requisition__status_updated": "Requisition status updated.", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_approving": "Approve", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_confirm": "Confirm", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_creation": "Create", "@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_order": "Order", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__id": "ID", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__purchaseLeadTime": "Lead time", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____columns__title__type": "Type", "@sage/xtrem-purchasing/pages__purchase_requisition__suppliers____title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_requisition_approval____title": "Purchase requisition approval", "@sage/xtrem-purchasing/pages__purchase_requisition_approval__headerSection____title": "General", "@sage/xtrem-purchasing/pages__purchase_requisition_select_supplier": "You need to select a supplier before creating an order.", "@sage/xtrem-purchasing/pages__purchase_requisitionLine_invoicedQuantity_to_much": "You are invoicing ({{alreadyInvoiced}}{{unit}})  when the requisition was generated for {{remainingQuantityd}}{{unit}}.", "@sage/xtrem-purchasing/pages__purchase_requisitionLine_orderedQuantity_to_much": "You are ordering ({{alreadyOrdered}}{{unit}})  when the requisition was generated for {{quantityToOrdered}}{{unit}}.", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title": "Post stock", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__2": "Close return", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__3": "Submit for approval", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__4": "Confirm", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__5": "Approve", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__6": "Reject", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__7": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__dropdownActions__title__8": "Delete", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__id__title": "Supplier ID", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line_4__title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line_5__title": "Shipping status", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line2__title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line2Right__title": "Return request date", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line3__title": "Return site", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line6__title": "Allocation status", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line7__title": "Return items to supplier", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__columns__title__rounding": "Rounding", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line8__title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__line9__title": "Supplier reference", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__title__title": "Number", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__listItem__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__10": "Error", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__11": "Returned", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__12": "Closed", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__2": "All statuses", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__3": "Draft", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__4": "Pending approval", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__5": "Approved", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__6": "Confirmed", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__7": "Rejected", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__8": "Tax calculation failed", "@sage/xtrem-purchasing/pages__purchase_return____navigationPanel__optionsMenu__title__9": "Posting in progress", "@sage/xtrem-purchasing/pages__purchase_return____objectTypePlural": "Purchase returns", "@sage/xtrem-purchasing/pages__purchase_return____objectTypeSingular": "Purchase return", "@sage/xtrem-purchasing/pages__purchase_return____title": "Purchase return", "@sage/xtrem-purchasing/pages__purchase_return__allocationStatus____title": "Allocation status", "@sage/xtrem-purchasing/pages__purchase_return__apply_dimensions_success": "Dimensions applied.", "@sage/xtrem-purchasing/pages__purchase_return__approval_status_not_updated": "Unable to update approval status.", "@sage/xtrem-purchasing/pages__purchase_return__approval_status_updated": "Approval status updated.", "@sage/xtrem-purchasing/pages__purchase_return__approvalStatus____title": "Approval status", "@sage/xtrem-purchasing/pages__purchase_return__approvalStatusHidden____title": "Approval status", "@sage/xtrem-purchasing/pages__purchase_return__approve____title": "Approve", "@sage/xtrem-purchasing/pages__purchase_return__approve_dialog_content": "You are about to approve this return.", "@sage/xtrem-purchasing/pages__purchase_return__approve_dialog_title": "Confirm approval", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____lookupDialogTitle": "Select supplier", "@sage/xtrem-purchasing/pages__purchase_return__businessRelation____title": "Supplier", "@sage/xtrem-purchasing/pages__purchase_return__close____title": "Close return", "@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_1": "You are about the change the status of this return to closed. You cannot undo this change.", "@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_2": "You are about to change the status of this return to closed and reverse all stock allocations. You cannot undo this change.", "@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_title": "Confirm status change", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__decimalDigits": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__id": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__rounding": "Rounding", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_return__companyCurrency____title": "Company currency", "@sage/xtrem-purchasing/pages__purchase_return__companyFxRate____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_return__confirm____title": "Confirm", "@sage/xtrem-purchasing/pages__purchase_return__confirm_dialog_content": "You are about to set this purchase return to \"Confirmed\"", "@sage/xtrem-purchasing/pages__purchase_return__confirm_dialog_title": "Confirm update", "@sage/xtrem-purchasing/pages__purchase_return__confirm_status_updated": "Status updated.", "@sage/xtrem-purchasing/pages__purchase_return__create_invoice_dialog_content": "You are about to create an invoice from this purchase order.", "@sage/xtrem-purchasing/pages__purchase_return__create_invoice_dialog_title": "Confirm invoice creation", "@sage/xtrem-purchasing/pages__purchase_return__createPurchaseInvoice____title": "Create invoice", "@sage/xtrem-purchasing/pages__purchase_return__currency____columns__title__id": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_return__currency____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-purchasing/pages__purchase_return__currency____placeholder": "Select currency", "@sage/xtrem-purchasing/pages__purchase_return__currency____title": "Transaction currency", "@sage/xtrem-purchasing/pages__purchase_return__customSave____title": "Save", "@sage/xtrem-purchasing/pages__purchase_return__defaultDimension____title": "Set dimensions", "@sage/xtrem-purchasing/pages__purchase_return__displayStatus____title": "Display status", "@sage/xtrem-purchasing/pages__purchase_return__externalNote____helperText": "Notes display on supplier documents.", "@sage/xtrem-purchasing/pages__purchase_return__externalNote____title": "Supplier notes", "@sage/xtrem-purchasing/pages__purchase_return__externalNoteLine____helperText": "Notes display on supplier documents.", "@sage/xtrem-purchasing/pages__purchase_return__externalNoteLine____title": "Supplier line notes", "@sage/xtrem-purchasing/pages__purchase_return__fxRateDate____title": "Exchange rate date", "@sage/xtrem-purchasing/pages__purchase_return__goToSysNotificationPage____title": "Retry", "@sage/xtrem-purchasing/pages__purchase_return__headerSection____title": "General", "@sage/xtrem-purchasing/pages__purchase_return__informationSection____title": "Information", "@sage/xtrem-purchasing/pages__purchase_return__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_return__internalNote____title": "Internal notes", "@sage/xtrem-purchasing/pages__purchase_return__internalNoteLine____helperText": "Notes display on internal documents.", "@sage/xtrem-purchasing/pages__purchase_return__internalNoteLine____title": "Internal line notes", "@sage/xtrem-purchasing/pages__purchase_return__invoice_created": "Invoice created: {{invoiceNumbers}}.", "@sage/xtrem-purchasing/pages__purchase_return__invoice_not_created": "Could not create invoice.", "@sage/xtrem-purchasing/pages__purchase_return__isExternalNote____title": "Add notes to the supplier document", "@sage/xtrem-purchasing/pages__purchase_return__isExternalNoteLine____title": "Add notes to the supplier document", "@sage/xtrem-purchasing/pages__purchase_return__isTransferHeaderNote____title": "Repeat the document notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_return__isTransferLineNote____title": "Repeat all the line notes on new documents.", "@sage/xtrem-purchasing/pages__purchase_return__itemsSection____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_return__line_delete_action_dialog_content": "You are about to delete this purchase return line. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_return__line_delete_action_dialog_title": "Confirm delete", "@sage/xtrem-purchasing/pages__purchase_return__lineCount____title": "Number of items", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title": "Name", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__2": "ISO 4217 code", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__3": "Decimal places", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__4": "Rounding", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__currency__id__title__5": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__item__name__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__item__name__title": "Category", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__item__name__title__2": "Unit", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__stockUnit__symbol__title": "Name", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__stockUnit__symbol__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__unit__symbol__title": "Name", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__columns__unit__symbol__title__2": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__lookupDialogTitle__reason": "Select reason", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__lookupDialogTitle__unit__symbol": "Select unit", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__columns__title__decimalDigits": "Scale", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__title": "Purchase receipt line", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__title__2": "Received quantity", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__nestedFields__purchaseReceiptLine__title__3": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__postfix__charge": "%", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__postfix__discount": "%", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__allocationStatus": "Allocation status", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__amountExcludingTaxInCompanyCurrency": "Total excluding tax company currency", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__approvalStatus": "Approval status", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__charge": "Charge", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__discount": "Discount", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__grossPrice": "Gross price", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__isExternalNote": "Add notes to the supplier document", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__item__id": "Item ID", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__itemDescription": "Item description", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__netPrice": "Net price", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__origin": "Origin", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__priceOrigin": "Price origin", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__purchaseReceiptLine__returnedQuantity": "Returned quantity", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__quantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__quantityAllocated": "Allocated quantity", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__reason": "Return reason", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__shippedStatus": "Shipping status", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__status": "Status", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__stockUnit__symbol": "Stock unit", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__unit__symbol": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_return__lines____columns__title__unitToStockUnitConversionFactor": "Stock unit conversion factor", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title": "Allocate stock", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title__2": "Issued stock", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title__3": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_return__lines____dropdownActions__title__4": "Delete", "@sage/xtrem-purchasing/pages__purchase_return__lines____inlineActions__title": "Open line panel", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__line2__title": "Description", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__line2Right__title": "Amount", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__title__title": "Product", "@sage/xtrem-purchasing/pages__purchase_return__lines____mobileCard__titleRight__title": "Status", "@sage/xtrem-purchasing/pages__purchase_return__lines____optionsMenu__title": "All statuses", "@sage/xtrem-purchasing/pages__purchase_return__lines____optionsMenu__title__2": "All open statuses", "@sage/xtrem-purchasing/pages__purchase_return__lines____optionsMenu__title__3": "Allocation required", "@sage/xtrem-purchasing/pages__purchase_return__lines____sidebar__headerDropdownActions__title": "Issued stock", "@sage/xtrem-purchasing/pages__purchase_return__lines____sidebar__headerDropdownActions__title__2": "Dimensions", "@sage/xtrem-purchasing/pages__purchase_return__lines____sidebar__headerDropdownActions__title__3": "Delete", "@sage/xtrem-purchasing/pages__purchase_return__lines____title": "Lines", "@sage/xtrem-purchasing/pages__purchase_return__noteBlock____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_return__notesSection____title": "Notes", "@sage/xtrem-purchasing/pages__purchase_return__number____title": "Number", "@sage/xtrem-purchasing/pages__purchase_return__post____title": "Post stock", "@sage/xtrem-purchasing/pages__purchase_return__post__allocation_status": "You need to allocate stock to all lines before you can post.", "@sage/xtrem-purchasing/pages__purchase_return__post_action_dialog_content": "You are about to set this purchase return to 'Returned'.", "@sage/xtrem-purchasing/pages__purchase_return__post_action_dialog_title": "Confirm posting", "@sage/xtrem-purchasing/pages__purchase_return__post_from_main_row": "Return posted", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__financeIntegrationAppRecordId": "Accounting integration reference", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__postingStatus": "Status", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__targetDocumentNumber": "Document number", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____columns__title__targetDocumentType": "Document type", "@sage/xtrem-purchasing/pages__purchase_return__postingDetails____title": "Posting", "@sage/xtrem-purchasing/pages__purchase_return__postingMessageBlock____title": "Error details", "@sage/xtrem-purchasing/pages__purchase_return__postingSection____title": "Posting", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__amountExcludingTax": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__document__number": "Purchase invoice", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__grossPrice": "Gross price", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__quantity": "Quantity", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____columns__title__unit__symbol": "Purchase unit", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____dropdownActions__title": "View invoice", "@sage/xtrem-purchasing/pages__purchase_return__purchaseInvoiceLines____title": "Purchase invoice lines", "@sage/xtrem-purchasing/pages__purchase_return__purchaseOrderPod____columns__title___id": "Purchase order number", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title___id": "Receipt number", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title___id__2": "Quantity remaining on receipt", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title__purchaseReceiptLine__status": "Receipt status", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____columns__title__returnedQuantity": "Quantity in purchase unit", "@sage/xtrem-purchasing/pages__purchase_return__purchaseReceiptLine____title": "Purchase receipt line to purchase return line", "@sage/xtrem-purchasing/pages__purchase_return__rateDescription____title": "Exchange rate", "@sage/xtrem-purchasing/pages__purchase_return__reject____title": "Reject", "@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_content_1": "You are about to reject this purchase return. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_content_2": "You are about to reject this purchase return and reverse all stock allocations. This action cannot be undone.", "@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_title": "Confirm rejection", "@sage/xtrem-purchasing/pages__purchase_return__repost____title": "Repost", "@sage/xtrem-purchasing/pages__purchase_return__repost_errors": "Errors occurred while reposting:", "@sage/xtrem-purchasing/pages__purchase_return__requestApproval____title": "Submit for approval", "@sage/xtrem-purchasing/pages__purchase_return__return_date_cannot_be_future": "The return request date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_return__return_items__uncheck_return_items_with_allocations": "Remove the allocations before disabling the item returns.", "@sage/xtrem-purchasing/pages__purchase_return__return_request_date_cannot_be_future": "The return request date cannot be later than today.", "@sage/xtrem-purchasing/pages__purchase_return__returnItems____title": "Return items to supplier", "@sage/xtrem-purchasing/pages__purchase_return__returnRequestDate____title": "Return request date", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__columns__legalCompany__name__title": "Tax engine", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__columns__legalCompany__name__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__title__id": "ID ", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____lookupDialogTitle": "Select return site", "@sage/xtrem-purchasing/pages__purchase_return__returnSite____title": "Return site", "@sage/xtrem-purchasing/pages__purchase_return__returnToAddress____columns__title__concatenatedAddress": "Return to address", "@sage/xtrem-purchasing/pages__purchase_return__returnToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_return__returnToAddress____title": "Return to address", "@sage/xtrem-purchasing/pages__purchase_return__selectFromPurchaseReceiptsLookup____title": "Add lines from receipts", "@sage/xtrem-purchasing/pages__purchase_return__shippingStatus____title": "Shipping status", "@sage/xtrem-purchasing/pages__purchase_return__status____title": "Status", "@sage/xtrem-purchasing/pages__purchase_return__status_updated": "Return status updated.", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_allocate": "Allocate", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_approve": "Approve", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_confirm": "Confirm", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_creation": "Create", "@sage/xtrem-purchasing/pages__purchase_return__step_sequence_post": "Post", "@sage/xtrem-purchasing/pages__purchase_return__stockSite____title": "Stock site", "@sage/xtrem-purchasing/pages__purchase_return__stockTransactionStatus____title": "Stock status", "@sage/xtrem-purchasing/pages__purchase_return__submit_for_approval_from_main_row": "Submitted for approval", "@sage/xtrem-purchasing/pages__purchase_return__supplierAddress____columns__title__concatenatedAddress": "Supplier address", "@sage/xtrem-purchasing/pages__purchase_return__supplierAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-purchasing/pages__purchase_return__supplierAddress____title": "Supplier address", "@sage/xtrem-purchasing/pages__purchase_return__supplierReturnReference____title": "Supplier reference", "@sage/xtrem-purchasing/pages__purchase_return__totalAmountExcludingTax____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_return__totalAmountExcludingTaxInCompanyCurrency____title": "Excluding tax", "@sage/xtrem-purchasing/pages__purchase_return__totalExcludingTaxValue____title": "Total excluding tax", "@sage/xtrem-purchasing/pages__purchase_return__totalIncludingTaxValue____title": "Total including tax", "@sage/xtrem-purchasing/pages__purchase_return__totalsSection____title": "Totals", "@sage/xtrem-purchasing/pages__purchase_return__totalsSectionCompanyCurrencyDetailsBlock____title": "Amounts in company currency", "@sage/xtrem-purchasing/pages__purchase_return__totalsSectionTaxTotalsBlock____title": "Calculated amounts", "@sage/xtrem-purchasing/pages__purchase_return_select_supplier": "You need to select a supplier before creating a receipt.", "@sage/xtrem-purchasing/pages__purchasing__on_recalculate_price_title": "Update price", "@sage/xtrem-purchasing/pages__purchasing__recalculate": "Recalculate", "@sage/xtrem-purchasing/pages__purchasing__recalculate_price": "You are about to recalculate prices, discounts and charges to reflect the latest price list.", "@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_invoice": "Purchase invoice", "@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_receipt_from_purchase_return": "Purchase receipt", "@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_return": "Purchase return", "@sage/xtrem-purchasing/pages__purchasing_order__on_price_determination_title": "Update price", "@sage/xtrem-purchasing/pages__purchasing_order__update_price_from_quantity": "The purchase quantity was updated. Do you want to recalculate prices, discounts, and charges?", "@sage/xtrem-purchasing/pages__receipt__to_invoice__confirm_greater_quantity": "You are about to create one or more invoice lines with a quantity greater than the receipt quantity.", "@sage/xtrem-purchasing/pages__receipt__to_invoice__create_purchase_dialog_title": "Confirm invoice quantity", "@sage/xtrem-purchasing/pages__receipt__to_invoice_confirm__lower_greater_quantity": "You are about to create one or more invoice lines with a quantity less or greater than the receipt quantity.", "@sage/xtrem-purchasing/pages__receipt__to_invoice_confirm_lower_quantity": "You are about to create one or more invoice lines with a quantity less than the receipt quantity.", "@sage/xtrem-purchasing/pages__receipt__to_return__confirm_greater_quantity": "The returns quantity is more than the received quantity.", "@sage/xtrem-purchasing/pages__receipt__to_return__create_purchase_dialog_title": "Confirm return quantity", "@sage/xtrem-purchasing/pages__receipt__to_return_confirm__lower_greater_quantity": "The returns quantity is more than the received quantity.", "@sage/xtrem-purchasing/pages__receipt__to_return_confirm_lower_quantity": "The returns quantity is less than than the received quantity.", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel____title": "New purchase order", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__cancelPurchaseOrder____title": "Cancel", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__createPurchaseOrder____title": "Create", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__date____title": "Order date", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__expectedReceiptDate____title": "Expected receipt date", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__grossPrice____title": "Gross price", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__item____columns__title__category__name": "Category", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__priceOrigin____title": "Price origin", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__projected_stock_button_text": "Projected stock", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__purchaseQuantity____title": "Purchase quantity", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__select_supplier_link_text": "Select supplier", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__site____title": "Purchase site", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockQuantity____title": "Stock quantity", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockSite____lookupDialogTitle": "Select stock site", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockSite____title": "Stock site", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockUnit____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockUnit____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__stockUnit____title": "Stock unit", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__supplier____title": "Supplier", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__id": "ID", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__purchaseLeadTime": "Lead time", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____columns__title__type": "Type", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__suppliers____title": "Supplier", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__unit____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__unit____columns__title__symbol": "Symbol", "@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__unit____title": "Purchase unit", "@sage/xtrem-purchasing/pages__return_from_receipt__error_zero_or_negative": "You need to enter a quantity.", "@sage/xtrem-purchasing/pages__return_from_receipt__greater_quantity_error": "The returns quantity needs to be the same as or less than the receipt.", "@sage/xtrem-purchasing/pages__site_extension_control_PO_approval": "You need to approve or reject pending documents before disabling the approval process.", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry____title": "Unbilled accounts payable", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__asOfDate____title": "As of date", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____columns__title__id": "ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____columns__title__name": "Name", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____lookupDialogTitle": "Select company", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____placeholder": "Select...", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__company____title": "Company", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__criteriaBlock____title": "Criteria", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__executionDate____title": "Last run date", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__country__name": "Tax ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____lookupDialogTitle": "Select from supplier", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__fromSupplier____title": "From supplier", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__account__id": "Unbilled accounts payable account", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__account__name": "Account name", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__accountItem__id": "Account item ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__accountItem__name": "Account item", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__billBySupplier": "Bill-by supplier", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__billBySupplier__businessEntity__id": "Bill-by supplier ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__company__id": "Company ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__company__name": "Company", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__companyCurrency__id": "Company currency ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__companyCurrency__name": "Company currency", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__creditedQuantity": "Credited quantity", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__currency__id": "Currency ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__documentDate": "Document date", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__financialSite": "Financial site", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__financialSite__id": "Financial site ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoicedQuantity": "Invoiced quantity", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableAmount": "Unbilled amount", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableAmountInCompanyCurrency": "Unbilled in company currency", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableAmountInCompanyCurrencyAtAsOfDate": "Unbilled in company currency per the As of date", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__invoiceReceivableQuantity": "Unbilled quantity", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__item__id": "Item ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__netPrice": "Net price", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__purchaseUnit__name": "Purchase unit", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__quantity": "Received quantity", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__receiptInternalId": "Receipt number", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__returnedQuantity": "Returned quantity", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__stockSite": "Stock site", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____columns__title__stockSite__id": "Stock site ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__lines____title": "Results", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__mainSection____title": "General", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__notification_inquiry_finished": "Unbilled accounts payable finished.", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__notification_request_sent": "Unbilled accounts payable request sent.", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__resultsSection____title": "Results", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__runUnbilledAccountPayableInquiry____title": "Run", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____columns__title__legalCompany__name": "Company", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____lookupDialogTitle": "Select site", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____placeholder": "Select site", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__sites____title": "Site", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__status____title": "Status", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____lookupDialogTitle": "Select to supplier", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__toSupplier____title": "To supplier", "@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__user____title": "User", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_closing_dialog_content": "When you close this purchase order line, the link to the sales order remains.", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_closing_dialog_title": "Close purchase order line", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_deletion_dialog_content": "When you delete this purchase order line, the link to the original order is also deleted.", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_deletion_dialog_title": "Delete purchase order line", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_quantity_changed_dialog_content": "When you decrease the purchase order line quantity, the quantity on the link to the original order is also decreased.", "@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_quantity_changed_dialog_title": "Decrease purchase order line quantity", "@sage/xtrem-purchasing/pages_confirm_button": "Confirm", "@sage/xtrem-purchasing/pages_purchase_order_receipt_date_cannot_be_less_than_order_date": "The expected receipt date must be later than the order date.", "@sage/xtrem-purchasing/pages_sidebar_block_title_order": "Order", "@sage/xtrem-purchasing/pages_sidebar_block_title_orders": "Orders", "@sage/xtrem-purchasing/pages_sidebar_block_title_price": "Price", "@sage/xtrem-purchasing/pages_sidebar_block_title_purchase": "Purchase", "@sage/xtrem-purchasing/pages_sidebar_block_title_quantity": "Quantity", "@sage/xtrem-purchasing/pages_sidebar_block_title_stock": "Stock", "@sage/xtrem-purchasing/pages_sidebar_block_title_totals": "Totals", "@sage/xtrem-purchasing/pages_sidebar_block_title_variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-purchasing/pages_sidebar_tab_title_address": "Address", "@sage/xtrem-purchasing/pages_sidebar_tab_title_information": "Information", "@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes": "Line notes", "@sage/xtrem-purchasing/pages_sidebar_tab_title_matching": "Matching", "@sage/xtrem-purchasing/pages_sidebar_tab_title_origin": "Origin", "@sage/xtrem-purchasing/pages_sidebar_tab_title_price": "Price", "@sage/xtrem-purchasing/pages_sidebar_tab_title_progress": "Progress", "@sage/xtrem-purchasing/pages_warning_button": "Confirm", "@sage/xtrem-purchasing/pages-confirm-approve": "Approve", "@sage/xtrem-purchasing/pages-confirm-cancel": "Cancel", "@sage/xtrem-purchasing/pages-confirm-continue": "Continue", "@sage/xtrem-purchasing/pages-confirm-create": "Create", "@sage/xtrem-purchasing/pages-confirm-post": "Post", "@sage/xtrem-purchasing/pages-confirm-reject": "Reject", "@sage/xtrem-purchasing/pages-confirm-send": "Send", "@sage/xtrem-purchasing/pages-confirm-submit": "Submit", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_content": "When you close this purchase order, the links to the sales orders remain.", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_title": "Close purchase order", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_content": "When you delete this purchase order, the links to the original orders are also deleted.", "@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_title": "Delete purchase order", "@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-cancel": "Cancel", "@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-continue": "Continue", "@sage/xtrem-purchasing/permission__accept_all_variances__name": "Accept all variances", "@sage/xtrem-purchasing/permission__approve__name": "Approve", "@sage/xtrem-purchasing/permission__close__name": "Close", "@sage/xtrem-purchasing/permission__confirm__name": "Confirm", "@sage/xtrem-purchasing/permission__manage__name": "Manage", "@sage/xtrem-purchasing/permission__open__name": "Open", "@sage/xtrem-purchasing/permission__post__name": "Post", "@sage/xtrem-purchasing/permission__read__name": "Read", "@sage/xtrem-purchasing/purchase_order__email_subject": "{{siteName}}: Order {{purchaseOrderNumber}}", "@sage/xtrem-purchasing/purchase_order__lib__purchase_receipt_creation_failed": "The creation of purchase receipt failed. {{diagnose}}.", "@sage/xtrem-purchasing/purchase_order_approval_managed": "The approval workflow needs to be disabled to update the status to confirmed.", "@sage/xtrem-purchasing/purchase_order_lib__resync__end": "Purchase order status sync finished.", "@sage/xtrem-purchasing/purchase_order_lib__resync__start": "Purchase order status sync start.", "@sage/xtrem-purchasing/purchase_receipt__lib__purchase_invoice_creation_failed": "The purchase invoice number could not be created. {{newPurchaseInvoiceDiagnoses}}.", "@sage/xtrem-purchasing/purchase_receipt__lib__purchase_return_creation_failed": "The record was not created. {{newPurchaseReturnDiagnoses}}.", "@sage/xtrem-purchasing/purchase_receipt__lib__purchase_return_creation_succeeded": "Purchase return created.", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order__price_not_set": "Some prices could not be determined from the supplier price list. Check the purchase orders", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_creation_failed": "The purchase order was not created: {{newPurchaseOrderDiagnoses}}.", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_creation_succeeded": "Purchase order number created.", "@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_required_date_not_met": "The expected receipt date is later than the requirement date.", "@sage/xtrem-purchasing/purchase_return__lib__credit_memo_creation_failed": "Reason code is missing.", "@sage/xtrem-purchasing/purchase_return__lib__no_financial_site": "No financial site found: {{site}}.", "@sage/xtrem-purchasing/purchase_return__purchase_return_already_closed": "The purchase return is already closed.", "@sage/xtrem-purchasing/purchase_return__purchase_return_line_without_item": "All lines need to have an item.", "@sage/xtrem-purchasing/purchase_return__purchase_return_not_found": "Purchase return not found", "@sage/xtrem-purchasing/purchase_return__uncheck_return_items_approved_or_rejected": "You cannot enable or disable item returns. The approval process has been completed.", "@sage/xtrem-purchasing/purchase_return__uncheck_return_items_with_allocations": "Remove the allocations before disabling the item returns.", "@sage/xtrem-purchasing/receipt_from_order_dialog_title": "Select order lines", "@sage/xtrem-purchasing/receipt_from_order_select": "Select", "@sage/xtrem-purchasing/requisition_to_order__confirm__lower_greater_quantity": "You are about to create one or more order lines with a quantity different to the ordered quantity.", "@sage/xtrem-purchasing/requisition_to_order__confirm_greater_quantity": "You are about to create one or more order lines with a quantity greater than the ordered quantity.", "@sage/xtrem-purchasing/requisition_to_order__confirm_lower_quantity": "You are about to create one or more order lines with a quantity less than the ordered quantity.", "@sage/xtrem-purchasing/requisition_to_order__create_purchase_dialog_title": "Confirm order quantity", "@sage/xtrem-purchasing/requisition_to_order_zero_quantity_error": "You need to enter a quantity greater than zero.", "@sage/xtrem-purchasing/return_from_receipt_dialog_title": "Select receipt lines", "@sage/xtrem-purchasing/return_from_receipt_select": "Select", "@sage/xtrem-purchasing/return_to_credit_memo__confirm__lower_greater_quantity": "You are about to create one or more order lines with a quantity less or greater than the returned quantity.", "@sage/xtrem-purchasing/return_to_credit_memo__confirm_greater_quantity": "You are about to create a credit memo with a quantity greater than the returned quantity.", "@sage/xtrem-purchasing/return_to_credit_memo__confirm_lower_quantity": "You are about to create a credit memo with a quantity less than the returned quantity.", "@sage/xtrem-purchasing/return_to_credit_memo__create_purchase_dialog_title": "Confirm credit memo quantity", "@sage/xtrem-purchasing/return_to_credit_memo__error_zero_or_negative_quantity": "You need to enter a quantity greater than zero.", "@sage/xtrem-purchasing/return_to_credit_memo__error_zero_or_negative_unit_price": "You need to enter a unit price greater than zero.", "@sage/xtrem-purchasing/search": "Search", "@sage/xtrem-purchasing/status_and_is_purchase_requisition_approval_managed": "Document status needs to be \"Draft\" and approval workflow disabled.", "@sage/xtrem-purchasing/status_and_is_purchase_return_approval_managed": "The document needs to be at \"Draft\" status and the approval workflow needs to be disabled.", "@sage/xtrem-purchasing/widgets__orders_not_received____callToActions__SeeAll__title": "See all", "@sage/xtrem-purchasing/widgets__orders_not_received____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-purchasing/widgets__orders_not_received____dataDropdownMenu__orderBy__orderDate__title": "Sort by order date", "@sage/xtrem-purchasing/widgets__orders_not_received____dataDropdownMenu__orderBy__status__title": "Sort by status", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line2__title": "Site", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line2Right__title": "Status", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line3__title": "Order date", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__line3Right__title": "Earliest expected date", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__title__title": "Number", "@sage/xtrem-purchasing/widgets__orders_not_received____rowDefinition__titleRight__title": "Supplier", "@sage/xtrem-purchasing/widgets__orders_not_received____title": "Purchase orders not received", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____callToActions__SeeAll__title": "See all", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____dataDropdownMenu__orderBy__date__title": "Sort by receipt date", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____dataDropdownMenu__orderBy__displayStatus__title": "Sort by status", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__line2__title": "Site", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__line2Right__title": "Status", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__line3__title": "Receipt date", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__title__title": "Number", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____rowDefinition__titleRight__title": "Supplier", "@sage/xtrem-purchasing/widgets__receipts_not_invoiced____title": "Purchase receipts not invoiced", "@sage/xtrem-purchasing/widgets__unposted_receipts____callToActions__SeeAll__title": "See all", "@sage/xtrem-purchasing/widgets__unposted_receipts____dataDropdownMenu__orderBy__date__title": "Sort by receipt date", "@sage/xtrem-purchasing/widgets__unposted_receipts____dataDropdownMenu__orderBy__number__title": "Sort by number", "@sage/xtrem-purchasing/widgets__unposted_receipts____dataDropdownMenu__orderBy__status__title": "Sort by status", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__line2__title": "Site", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__line2Right__title": "Status", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__line3__title": "Receipt date", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__title__title": "Number", "@sage/xtrem-purchasing/widgets__unposted_receipts____rowDefinition__titleRight__title": "Supplier", "@sage/xtrem-purchasing/widgets__unposted_receipts____title": "Unposted purchase receipts"}