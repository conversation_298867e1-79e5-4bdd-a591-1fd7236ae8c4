import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, NodeCreateData, Reference, UpdateAction, date, integer } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremSupplyChain from '..';

@decorators.subNode<StockTransferOrder>({
    extends: () => xtremDistribution.nodes.BaseOutboundOrderDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    isCustomizable: true,
    hasAttachments: true,
    async controlBegin(cx) {
        await xtremSupplyChain.events.control.stockTransferLib.shippingSiteIsNotEqualToReceivingSite(cx, this);
        await xtremSupplyChain.events.control.stockTransferLib.financialSiteIsEqualForShippingSiteAndReceivingSite(
            cx,
            this,
        );
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);

        if (this.$.status === NodeStatus.modified) {
            await xtremSupplyChain.events.control.stockTransferOrder.cannotUpdateStockTransferOrderReceivingSite(
                cx,
                this,
            );
            await xtremSupplyChain.events.control.stockTransferOrder.cannotUpdateStockTransferOrderStockSite(cx, this);
        }
    },
    async saveBegin() {
        if (this.$.status === NodeStatus.modified) {
            const old = await this.$.old;
            if ((await old.status) === 'draft' && (await this.status) === 'pending') {
                await this.lines.forEach(async line => {
                    await line.$.set({ status: 'pending' });
                });
            }
        }

        const statuses = await xtremSupplyChain.functions.calculateOrderStatuses(this.lines, await this.approvalStatus);
        if (statuses?.receivingStatus && statuses?.shippingStatus && statuses?.status && statuses?.displayStatus) {
            await this.$.set({
                receivingStatus: statuses.receivingStatus,
                shippingStatus: statuses.shippingStatus,
                status: statuses.status,
                displayStatus: statuses.displayStatus,
            });
        }

        // On control events this throws an error because the stockTransferShipment node is readonly
        if (['pending', 'inProgress'].includes(await this.status)) {
            if (this.$.status === NodeStatus.added) {
                await this.$.context.flushDeferredActions();
            }
            // financeIntegrationCheck
        }
    },
    /**
     * Stock transfer order create actions :
     */
})
export class StockTransferOrder
    extends xtremDistribution.nodes.BaseOutboundOrderDocument
    implements xtremMasterData.interfaces.Document, xtremStockData.interfaces.DocumentHeaderWithStockAllocation
{
    override getStockSite() {
        return this.stockSite;
    }

    override getEffectiveDate() {
        return this.shippingDate;
    }

    @decorators.enumPropertyOverride<StockTransferOrder, 'status'>({})
    override readonly status: Promise<xtremSupplyChain.enums.StockTransferOrderStatus>;

    @decorators.enumProperty<StockTransferOrder, 'receivingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremDistribution.enums.receivingStatusDataType,
        defaultValue: 'notReceived',
        lookupAccess: true,
    })
    readonly receivingStatus: Promise<xtremDistribution.enums.ReceivingStatus>;

    @decorators.enumPropertyOverride<StockTransferOrder, 'displayStatus'>({
        dependsOn: ['status', 'shippingStatus', 'receivingStatus', 'approvalStatus'],
        defaultValue: 'draft',
        async updatedValue() {
            return xtremSupplyChain.functions.calculateOrderDisplayStatus(
                await this.status,
                await this.shippingStatus,
                await this.receivingStatus,
                await this.approvalStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremSupplyChain.enums.StockTransferOrderDisplayStatus>;

    @decorators.referencePropertyOverride<StockTransferOrder, 'site'>({
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isInventory: true,
                businessEntity: { isSupplier: true, supplier: { isActive: true } },
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<StockTransferOrder, 'businessRelation'>({
        dependsOn: ['receivingSite'],
        async defaultValue() {
            return (await (await this.receivingSite)?.businessEntity)?.customer;
        },
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<StockTransferOrder, 'supplier'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Supplier,
        dataType: () => xtremMasterData.dataTypes.supplier,
        dependsOn: ['site'],
        lookupAccess: true,
        filters: {
            control: {
                async businessEntity() {
                    return (await (await this.site).businessEntity)._id;
                },
            },
        },
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        async defaultValue() {
            return (await (await this.site)?.businessEntity)?.supplier;
        },
        updatedValue: useDefaultValue,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<StockTransferOrder, 'financialSite'>({
        dependsOn: ['site'],
        node: () => xtremSystem.nodes.Site,
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        async defaultValue() {
            return xtremFinanceData.functions.getFinancialSite(await this.site);
        },
        updatedValue: useDefaultValue,
    })
    override readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<StockTransferOrder, 'receivingSite'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['site', 'financialSite'],
        dataType: () => xtremMasterData.dataTypes.masterDataSite,
        node: () => xtremSystem.nodes.Site,
        filters: {
            control: {
                isInventory: true,
                businessEntity: {
                    isCustomer: true,
                    customer: { isActive: true },
                },
                async legalCompany() {
                    return (await this.financialSite)?.legalCompany;
                },
                async _id() {
                    return { _ne: (await this.site)?._id };
                },
            },
        },
    })
    readonly receivingSite: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<StockTransferOrder, 'stockTransferOrderDocumentType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSupplyChain.enums.stockTransferOrderDocumentTypeDataType,
        defaultValue: 'interSite',
        duplicatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockTransferOrderDocumentType: Promise<xtremSupplyChain.enums.StockTransferOrderDocumentType>;

    @decorators.enumProperty<StockTransferOrder, 'stockTransferOrderFlowType'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSupplyChain.enums.stockTransferOrderFlowTypeDataType,
        defaultValue: 'twoSteps',
        duplicatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockTransferOrderFlowType: Promise<xtremSupplyChain.enums.StockTransferOrderFlowType>;

    @decorators.referencePropertyOverride<StockTransferOrder, 'currency'>({
        dependsOn: ['shipToCustomer'],
        node: () => xtremMasterData.nodes.Currency,
        async defaultValue() {
            return (await (await this.shipToCustomer)?.businessEntity)?.currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.datePropertyOverride<StockTransferOrder, 'fxRateDate'>({
        dependsOn: ['date'],
        defaultValue() {
            return this.date;
        },
        duplicatedValue: useDefaultValue,
    })
    override readonly fxRateDate: Promise<date>;

    @decorators.referenceProperty<StockTransferOrder, 'requester'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            return this.$.context.read(xtremSystem.nodes.User, { email: (await this.$.context.user)?.email });
        },
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
    })
    readonly requester: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<StockTransferOrder, 'shipToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.shipToCustomer)?.primaryAddress;
        },
    })
    readonly shipToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referencePropertyOverride<StockTransferOrder, 'shipToCustomerAddress'>({
        filters: {
            control: {
                async businessEntity() {
                    return (await this.shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    override readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referencePropertyOverride<StockTransferOrder, 'incoterm'>({
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Incoterm,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.incoterm || null;
        },
    })
    override readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.enumProperty<StockTransferOrder, 'allocationStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['allocationStatus'] }],
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        computeValue() {
            return xtremStockData.functions.allocationLib.computeHeaderAllocationStatus(
                this,
                xtremSupplyChain.functions.isStockTransferOrderLineConsideredForAllocationStatus,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    readonly allocableLinesCollectionName = 'lines';

    @decorators.enumProperty<StockTransferOrder, 'allocationRequestStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.allocationRequestStatusDataType,
        dependsOn: [{ lines: ['allocationRequestStatus'] }],
        computeValue() {
            return xtremStockData.functions.automaticAllocationLib.computeHeaderAllocationRequestStatus(this);
        },
    })
    readonly allocationRequestStatus: Promise<xtremStockData.enums.AllocationRequestStatus>;

    @decorators.booleanProperty<StockTransferOrder, 'isCloseHidden'>({
        isPublished: true,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (
                (await this.status) === 'inProgress' ||
                (await this.status) === 'closed' ||
                (await this.allocationRequestStatus) === 'inProgress'
            );
        },
    })
    readonly isCloseHidden: Promise<boolean>;

    @decorators.datePropertyOverride<StockTransferOrder, 'shippingDate'>({
        dependsOn: [
            'requestedDeliveryDate',
            'date',
            'doNotShipBeforeDate',
            'doNotShipAfterDate',
            'deliveryLeadTime',
            'workDays',
        ],
        async defaultValue() {
            const doNotShipBeforeDate = await this.doNotShipBeforeDate;
            const doNotShipAfterDate = await this.doNotShipAfterDate;
            return xtremDistribution.functions.subWorkDays(this.$.context, {
                requestedDeliveryDate: await this.requestedDeliveryDate,
                orderDate: await this.date,
                doNotShipBeforeDate: doNotShipBeforeDate || null,
                doNotShipAfterDate: doNotShipAfterDate || null,
                deliveryLeadTime: await this.deliveryLeadTime,
                workDaysMask: await this.workDays,
            });
        },
        // duplicatedValue: useDefaultValue,
    })
    override readonly shippingDate: Promise<date>;

    @decorators.collectionPropertyOverride<StockTransferOrder, 'lines'>({
        dependsOn: [
            'shipToCustomerAddress',
            'shipToAddress',
            'deliveryMode',
            'requestedDeliveryDate',
            'deliveryLeadTime',
            'doNotShipBeforeDate',
            'doNotShipAfterDate',
            'date',
            'workDays',
            'currency',
        ],
        node: () => xtremSupplyChain.nodes.StockTransferOrderLine,
    })
    override readonly lines: Collection<xtremSupplyChain.nodes.StockTransferOrderLine>;

    // The usage of a computeValue instead of a query allows to use directly "this.lines"
    // instead of having to read the specific document first => This property can be copy/paste as is.
    @decorators.jsonProperty<StockTransferOrder, 'jsonAggregateLandedCostTypes'>({
        isPublished: true,
        dependsOn: [{ lines: ['landedCostLines'] }],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        computeValue() {
            return xtremSupplyChain.functions.getStockTransferOrderLandedCostsPerType(this.$.context, this);
        },
        lookupAccess: true,
    })
    readonly jsonAggregateLandedCostTypes: Promise<xtremLandedCost.interfaces.JsonAggregateLandedCostTypes>;

    /**
     * Method that closes a stock transfer order line of a stock transfer order
     * @param context
     * @param stockTransferOrderLine contains a unique stockTransferOrderLine reference
     * @returns enum StockTransferOrderLineCloseStatusMethodReturn
     */
    @decorators.mutation<typeof StockTransferOrder, 'closeLine'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrderLine',
                type: 'reference',
                node: () => xtremSupplyChain.nodes.StockTransferOrderLine,
                isMandatory: true,
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremDistribution.enums.documentCloseStatusMethodReturnDataType,
        },
    })
    static async closeLine(
        context: Context,
        stockTransferOrderLine: xtremSupplyChain.nodes.StockTransferOrderLine,
    ): Promise<xtremDistribution.enums.DocumentCloseStatusMethodReturn> {
        if (
            (await (await stockTransferOrderLine.item).isStockManaged) &&
            (await stockTransferOrderLine.allocationStatus) !== 'notAllocated'
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_line_quantity_allocated',
                    'Remove the stock allocation before closing the line.',
                ),
            );
        }
        if ((await stockTransferOrderLine.allocationRequestStatus) === 'inProgress') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_line_allocated_request_in_progress',
                    'You cannot close the stock transfer order line. An allocation request is in progress.',
                ),
            );
        }
        if ((await stockTransferOrderLine.shippingStatus) === 'shipped') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_line___line_is_shipped',
                    'You cannot close the stock transfer order line. It is already shipped.',
                ),
            );
        }

        const stockTransferOrder = await context.read(
            xtremSupplyChain.nodes.StockTransferOrder,
            {
                number: await (await stockTransferOrderLine.document).number,
            },
            { forUpdate: true },
        );
        return xtremSupplyChain.functions.closeLine(stockTransferOrder, stockTransferOrderLine);
    }

    /**
     * Method that opens a stock transfer order line of a stock transfer order
     * @param context
     * @param stockTransferOrderLine contains a unique stockTransferOrderLine reference
     * of the line if it is greater than the actual quantity of the line and the quantity already delivered
     * @returns enum
     */
    @decorators.mutation<typeof StockTransferOrder, 'openLine'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrderLine',
                type: 'reference',
                node: () => xtremSupplyChain.nodes.StockTransferOrderLine,
                isMandatory: true,
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremDistribution.enums.documentOpenStatusMethodReturnDataType,
        },
    })
    static async openLine(
        context: Context,
        stockTransferOrderLine: xtremSupplyChain.nodes.StockTransferOrderLine,
    ): Promise<xtremDistribution.enums.DocumentOpenStatusMethodReturn> {
        const stockTransferOrder = await context.read(
            xtremSupplyChain.nodes.StockTransferOrder,
            {
                number: await (await stockTransferOrderLine.document).number,
            },
            { forUpdate: true },
        );
        return xtremSupplyChain.functions.openLine(stockTransferOrder, stockTransferOrderLine);
    }

    /**
     * Method that closes a stock transfer order
     * @param context
     * @param stockTransferOrderNumber the number of the stock transfer order
     * @returns enum stockTransferOrderCloseStatusMethod
     */
    @decorators.mutation<typeof StockTransferOrder, 'close'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockTransferOrder,
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremDistribution.enums.documentCloseStatusMethodReturnDataType,
        },
    })
    static async close(
        context: Context,
        stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    ): Promise<xtremDistribution.enums.DocumentCloseStatusMethodReturn> {
        if (
            await stockTransferOrder.lines.some(
                async line =>
                    (await (await line.item).isStockManaged) && (await line.allocationStatus) !== 'notAllocated',
            )
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_order_quantity_allocated',
                    'Remove the stock allocation before closing the order.',
                ),
            );
        }
        if ((await stockTransferOrder.allocationRequestStatus) === 'inProgress') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_close_allocated_request_in_progress',
                    'You can only close the stock transfer order after the allocation request for the lines is complete.',
                ),
            );
        }
        return xtremSupplyChain.functions.closeStockTransferOrder(stockTransferOrder);
    }

    /**
     * Method that opens a stock transfer order
     * @param context
     * @param stockTransferOrderNumber the number of the stock transfer order
     * @returns enum stockTransferOrderOpenStatusMethodReturn
     */
    @decorators.mutation<typeof StockTransferOrder, 'open'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockTransferOrder,
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremDistribution.enums.documentOpenStatusMethodReturnDataType,
        },
    })
    static open(
        context: Context,
        stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    ): Promise<xtremDistribution.enums.DocumentOpenStatusMethodReturn> {
        return xtremSupplyChain.functions.openStockTransferOrder(stockTransferOrder);
    }

    /**
     * Method that change the header status from draft to pending and propagate it to the lines
     * @param context
     * @param stockTransferOrderNumber the number of the stock transfer order
     * @returns enum stockTransferOrderConfirmMethodReturn
     */
    @decorators.mutation<typeof StockTransferOrder, 'confirm'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockTransferOrder,
            },
        ],
        return: {
            type: 'enum',
            dataType: () => xtremDistribution.enums.documentConfirmStatusMethodReturnDataType,
        },
    })
    static async confirm(
        context: Context,
        stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    ): Promise<xtremDistribution.enums.DocumentConfirmStatusMethodReturn> {
        const financeIntegrationResult = await this.financeIntegrationCheck(context, stockTransferOrder);
        if (financeIntegrationResult.length > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_confirm_stock_transfer_order_mandatory_dimensions',
                    ' {{errorMessage}}',
                    {
                        errorMessage: financeIntegrationResult.map(result => result).join('\n\n'),
                    },
                ),
            );
        }
        return xtremSupplyChain.functions.confirm(stockTransferOrder);
    }

    /**
     * Method that calculate subWorkDays -> because not work properly on front-end
     * @param context
     * @param requestedDeliveryDate
     * @param orderDate
     * @param doNotShipBeforeDate
     * @param doNotShipAfterDate
     * @param deliveryLeadTime
     * @param workDaysMask
     * @returns date subWorkDaysMethodReturn
     */
    @decorators.mutation<typeof StockTransferOrder, 'subWorkDays'>({
        isPublished: true,
        parameters: [
            { name: 'requestedDeliveryDate', type: 'date', isMandatory: true },
            { name: 'orderDate', type: 'date', isMandatory: true },
            { name: 'doNotShipBeforeDate', type: 'date', isNullable: true },
            { name: 'doNotShipAfterDate', type: 'date', isNullable: true },
            { name: 'deliveryLeadTime', type: 'integer', isMandatory: true },
            { name: 'workDaysMask', type: 'integer', isMandatory: true },
        ],
        return: { type: 'date', isMandatory: true },
    })
    static subWorkDays(
        context: Context,
        requestedDeliveryDate: date,
        orderDate: date,
        doNotShipBeforeDate: date | null,
        doNotShipAfterDate: date | null,
        deliveryLeadTime: integer,
        workDaysMask: integer,
    ): date {
        return xtremDistribution.functions.subWorkDays(context, {
            requestedDeliveryDate,
            orderDate,
            doNotShipBeforeDate,
            doNotShipAfterDate,
            deliveryLeadTime,
            workDaysMask,
        });
    }

    /**
     * Method that calculate addWorkDays -> because not work properly on front-end
     * @param context
     * @param shippingDate
     * @param deliveryLeadTime
     * @param workDays
     * @returns date addWorkDaysMethodReturn
     */
    @decorators.mutation<typeof StockTransferOrder, 'addWorkDays'>({
        isPublished: true,
        parameters: [
            { name: 'shippingDate', type: 'date', isMandatory: true },
            { name: 'deliveryLeadTime', type: 'integer', isMandatory: true },
            { name: 'workDays', type: 'integer', isMandatory: true },
        ],
        return: { type: 'date', isMandatory: true },
    })
    static addWorkDays(_context: Context, shippingDate: date, deliveryLeadTime: integer, workDays: integer): date {
        return xtremDistribution.functions.addWorkDays(shippingDate, deliveryLeadTime, workDays);
    }

    /**
     * Toggle the isPrinted property to true
     * @param _context
     * @param order
     * @returns boolean
     */
    @decorators.mutation<typeof StockTransferOrder, 'setIsPrintedTrue'>({
        isPublished: true,
        parameters: [
            { name: 'order', type: 'reference', isMandatory: true, isWritable: true, node: () => StockTransferOrder },
            { name: 'isPrinted', type: 'boolean', isMandatory: false },
        ],
        return: { type: 'boolean' },
    })
    static async setIsPrintedTrue(_context: Context, order: StockTransferOrder, isPrinted = true): Promise<boolean> {
        if (['draft', 'pending', 'inProgress'].includes(await order.status)) {
            order.canUpdateIsPrinted = true;
            if (!isPrinted) {
                await order.$.set({ isSent: false });
            }
            await order.$.set({ isPrinted });
            await order.$.save();
        }
        return true;
    }

    // // not for now
    // @decorators.bulkMutation<typeof StockTransferOrder, 'printBulk'>({
    //     isPublished: true,
    //     async onComplete(context, reports) {
    //         const reportName = context.localize(
    //             '@sage/xtrem-supply-chain/node__stock_transfer_order_bulk_print_report_name',
    //             'Stock transfer order',
    //         );
    //         await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
    //             documentType: 'stockTransferOrder',
    //             documents: reports,
    //             reportName,
    //         });
    //     },
    // })
    // static printBulk(context: Context, document: StockTransferOrder) {
    //     const reportName = 'stockTransferOrder';
    //     return xtremReporting.nodes.Report.generateUploadedFile(context, reportName, '', {
    //         variables: JSON.stringify({ order: document._id }),
    //     });
    // }

    /* ********************************
Notification for the allocation engine
******************************** */
    @decorators.mutation<typeof StockTransferOrder, 'requestAutoAllocation'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: {
                    document: {
                        type: 'reference',
                        isWritable: true,
                        node: () => xtremSupplyChain.nodes.StockTransferOrder,
                    },
                    requestType: 'string',
                },
            },
        ],
        return: { type: 'string' },
    })
    static async requestAutoAllocation(
        context: Context,
        data: {
            document: Reference<xtremSupplyChain.nodes.StockTransferOrder>;
            requestType: xtremStockData.enums.AllocationRequestType;
        },
    ): Promise<string> {
        return xtremStockData.functions.notificationLib.requestAutoStockAllocation(
            context,
            {
                documentType: 'stockTransferOrder',
                processType: 'document',
                requestType: data.requestType,
                document: await data.document,
            },
            xtremSupplyChain.functions.isStockTransferOrderLineAllocable,
        );
    }

    @decorators.notificationListener<typeof StockTransferOrder>({
        startsReadOnly: true,
        topic: 'StockTransferOrder/allocation/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<xtremStockData.interfaces.AllocationReplyPayload>,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandlerForAutomaticAllocation(
                context,
                envelope,
                error,
                StockTransferOrder,
            );
        },
    })
    static async onAllocationReply(
        context: Context,
        payload: xtremStockData.interfaces.AllocationReplyPayload,
    ): Promise<void> {
        await xtremStockData.functions.notificationLib.reactToAutomaticAllocationReply(
            context,
            payload,
            StockTransferOrder,
            xtremSupplyChain.functions.isStockTransferOrderLineAllocable,
        );
    }

    @decorators.mutation<typeof StockTransferOrder, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrder',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => StockTransferOrder,
                isNullable: false,
            },
        ],
        return: {
            type: 'array',
            item: 'string',
        },
    })
    static async financeIntegrationCheck(
        context: Context,
        stockTransferOrder: xtremSupplyChain.nodes.StockTransferOrder,
    ): Promise<string[]> {
        const returnMessage: string[] = [];
        const stockSite = await stockTransferOrder.stockSite;
        if (stockSite) {
            const companyId = (await stockSite.legalCompany)._id;
            const documentNumber = await stockTransferOrder.number;

            const mandatoryAttributes = context.query(xtremFinanceData.nodes.CompanyAttributeType, {
                filter: { company: companyId, isRequired: true },
            });
            const mandatoryDimensions = context.query(xtremFinanceData.nodes.CompanyDimensionType, {
                filter: { company: companyId, isRequired: true },
            });

            await stockTransferOrder.lines.forEach(async line => {
                const undefinedAttributes = await xtremFinanceData.functions.mandatoryAttributeControl(
                    (await line.storedAttributes) ?? { employee: '', project: '', task: '' },
                    await mandatoryAttributes.toArray(),
                );
                const undefinedDimensions = await xtremFinanceData.functions.mandatoryDimensionControl(
                    (await line.storedDimensions) ?? '{}',
                    await mandatoryDimensions.toArray(),
                );
                const undefinedAttributeDimension = [...undefinedAttributes, ...undefinedDimensions];

                if (undefinedAttributeDimension.length > 0) {
                    const arrayAsString = undefinedAttributeDimension.map(String).join(', ');

                    const message = xtremFinanceData.classes.LocalizedMessages.mandatoryDimensionTypeNotFound(
                        context,
                        arrayAsString,
                        (await (await line.item)?.name) || null,
                        'company',
                        documentNumber,
                        false,
                    );

                    returnMessage.push(message);
                }
            });
        }
        return returnMessage;
    }

    @decorators.mutation<typeof StockTransferOrder, 'approve'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrder',
                type: 'reference',
                isMandatory: true,
                node: () => StockTransferOrder,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async approve(context: Context, stockTransferOrder: StockTransferOrder): Promise<string> {
        const document = await StockTransferOrder.getWritableNode(context, stockTransferOrder);

        if ((await document.approvalStatus) !== 'pendingApproval' || (await document.status) === 'closed') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/cant_approve_stock_transfer_order_when_not_pending_approval_or_closed',
                    'You can only approve a stock transfer order if the approval status is {{pendingApproval}}.',
                    {
                        pendingApproval: context.localizeEnumMember(
                            '@sage/xtrem-distribution/DocumentApprovalStatus',
                            'pendingApproval',
                        ),
                    },
                ),
            );
        }

        const financeIntegrationResult = await this.financeIntegrationCheck(context, document);
        if (financeIntegrationResult.length > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/nodes__stock_transfer_order__cannot_approve_stock_transfer_order_mandatory_dimensions',
                    ' {{errorMessage}}',
                    {
                        errorMessage: financeIntegrationResult.map(result => result).join('\n\n'),
                    },
                ),
            );
        }

        await document.lines
            .filter(async line => (await line.status) === 'draft')
            .forEach(line => line.$.set({ status: 'pending' }));
        await document.$.set({ approvalStatus: 'approved' });
        await document.$.save();

        return context.localize(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order__approved',
            'The stock transfer order is approved.',
        );
    }

    @decorators.mutation<typeof StockTransferOrder, 'reject'>({
        isPublished: true,
        parameters: [
            {
                name: 'stockTransferOrder',
                type: 'reference',
                isMandatory: true,
                node: () => StockTransferOrder,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async reject(context: Context, stockTransferOrder: StockTransferOrder): Promise<string> {
        const document = await StockTransferOrder.getWritableNode(context, stockTransferOrder);

        if ((await document.approvalStatus) !== 'pendingApproval' || (await document.status) === 'closed') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-supply-chain/cant_reject_stock_transfer_order_when_not_pending_approval_or_closed',
                    'You can only reject a stock transfer order if the approval status is {{pendingApproval}}.',
                    {
                        pendingApproval: context.localizeEnumMember(
                            '@sage/xtrem-supply-chain/StockTransferOrderApprovalStatus',
                            'pendingApproval',
                        ),
                    },
                ),
            );
        }

        await document.$.set({ approvalStatus: 'rejected' });
        await StockTransferOrder.close(context, document);
        await document.$.save();

        return context.localize(
            '@sage/xtrem-supply-chain/nodes__stock_transfer_order__rejected',
            'The stock transfer order has been rejected.',
        );
    }

    /** Page url of the document TODO get this from the datatype // tunnelPAge of the document??  */
    @decorators.stringPropertyOverride<StockTransferOrder, 'page'>({
        getValue: () => '@sage/xtrem-supply-chain/StockTransferOrder',
    })
    override readonly page: Promise<string>;

    override async beforeSendApprovalRequestMail(
        context: Context,
        user?: xtremSystem.nodes.User,
    ): Promise<xtremMasterData.interfaces.ApprovalRequestMail> {
        await xtremSupplyChain.functions.controlBeforeSendMailApproval(context, this);

        const subject = context.localize(
            '@sage/xtrem-supply-chain/functions__stock_transfer_order__approval_email_subject',
            '[Stock transfer order {{stockTransferOrderNumber}}] approval request',
            { stockTransferOrderNumber: await this.number },
        );
        const mailerUser = user ?? (await xtremSupplyChain.functions.getApprovalUser(context, this));

        return {
            subject,
            template: 'stockTransferOrderApprovalMail',
            mailerUser,
            data: await xtremSupplyChain.functions.getTransferOrderDataForApproval(context, this, mailerUser),
        };
    }

    private static getWritableNode(
        context: Context,
        stockTransferOrder: StockTransferOrder,
    ): Promise<StockTransferOrder> {
        return context.read(StockTransferOrder, { _id: stockTransferOrder._id }, { forUpdate: true });
    }

    @decorators.mutation<typeof StockTransferOrder, 'createStockTransferShipment'>({
        isPublished: true,
        parameters: [
            {
                isMandatory: true,
                isWritable: true,
                name: 'stockTransferOrder',
                type: 'reference',
                node: () => StockTransferOrder,
            },
        ],
        return: {
            type: 'reference',
            node: () => xtremSupplyChain.nodes.StockTransferShipment,
        },
    })
    static async createStockTransferShipment(
        context: Context,
        stockTransferOrder: StockTransferOrder,
    ): Promise<xtremSupplyChain.nodes.StockTransferShipment> {
        // run controls
        await xtremSupplyChain.functions.controlStockTransferOrderForStockTransferShipmentCreation(
            context,
            await stockTransferOrder.lines.toArray(),
        );
        let shipmentHeader: NodeCreateData<xtremSupplyChain.nodes.StockTransferShipment> = {};
        const shipmentLines: NodeCreateData<xtremSupplyChain.nodes.StockTransferShipmentLine>[] =
            await stockTransferOrder.lines
                .filter(async line => (await line.status) !== 'closed')
                .map(async stockTransferOrderLine => {
                    if (Object.keys(shipmentHeader).length === 0) {
                        shipmentHeader = {
                            site: (await stockTransferOrderLine.site)?._id,
                            receivingSite: (await stockTransferOrder.receivingSite)._id,
                            incoterm: (await stockTransferOrder.incoterm)?._id,
                            shipToCustomer: (await stockTransferOrder.shipToCustomer)?._id,
                            shipToCustomerAddress: (await stockTransferOrder.shipToCustomerAddress)?._id,
                            shipToAddress: await stockTransferOrder.shipToAddress,
                            shipToContact: await stockTransferOrder.shipToContact,
                            stockSite: (await stockTransferOrderLine.stockSite)?._id,
                            currency: (await stockTransferOrder.currency)?._id,
                            deliveryMode: (await stockTransferOrderLine.deliveryMode)?._id,
                            deliveryLeadTime: await stockTransferOrder.deliveryLeadTime,
                        };
                    }
                    return {
                        item: (await stockTransferOrderLine.item)?._id,
                        itemDescription: await stockTransferOrderLine.itemDescription,
                        quantity: await stockTransferOrderLine.quantity,
                        unit: (await stockTransferOrderLine.unit)?._id,
                        unitToStockUnitConversionFactor: await stockTransferOrderLine.unitToStockUnitConversionFactor,
                        stockUnit: (await stockTransferOrderLine.stockUnit)?._id,
                        storedAttributes: await stockTransferOrderLine.storedAttributes,
                        storedDimensions: await stockTransferOrderLine.storedDimensions,
                        internalNote: await stockTransferOrderLine.internalNote,
                        linkToStockTransferOrderLines: [
                            {
                                _action: 'create' as UpdateAction,
                                to: stockTransferOrderLine._id,
                                quantity: await stockTransferOrderLine.quantity,
                                quantityInStockUnit: await stockTransferOrderLine.quantityInStockUnit,
                                amount: 0,
                                unit: (await stockTransferOrderLine.unit)?._id,
                                stockUnit: (await stockTransferOrderLine.stockUnit)?._id,
                                currency: (await stockTransferOrder.currency)?._id,
                            },
                        ],
                    };
                })
                .toArray();
        const shipmentPayload = {
            ...shipmentHeader,
            lines: shipmentLines,
        };
        const shipment = await context.create(xtremSupplyChain.nodes.StockTransferShipment, shipmentPayload);
        await shipment.$.save();

        return context.read(xtremSupplyChain.nodes.StockTransferShipment, {
            _id: shipment._id,
        });
    }
}
