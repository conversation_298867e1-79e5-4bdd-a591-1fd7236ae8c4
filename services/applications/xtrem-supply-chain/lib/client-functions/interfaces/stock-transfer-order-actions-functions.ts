import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Customer } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-supply-chain-api';
import type { Site, User } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface UpdateStockTransferOrderPrintStatusParameters {
    stockTransferOrderPage: ui.Page<GraphApi>;
    _id: string;
    status: string;
    isPrinted: boolean;
    number: string;
}

export interface PrintStockTransferOrderParameters {
    isCalledFromRecordPage: boolean;
    stockTransferOrderPage: ui.Page<GraphApi>;
    _id: string;
    status: string;
    number: string;
}

export interface GetStockTransferOrderLinesParameter {
    stockTransferOrderPage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SetDimensionActionParameter {
    stockTransferOrderPage: ui.Page<GraphApi>;
    recordNumber: string;
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
}
export interface CreateStockTransferShipmentActionParameters {
    stockTransferOrderPage: ui.Page<GraphApi>;
    _id: string;
    isCalledFromRecordPage: boolean;
}

export interface CreateStockTransferShipmentMutationParameters {
    stockTransferOrderPage: ui.Page<GraphApi>;
    _id: string;
}

export interface ProformaInvoiceDialogParameters {
    transferOrder: string;
    version?: number;
    customerComment?: string;
    expirationDate?: string;
}

export interface LoadApproversParameters {
    stockTransferOrderPage: ui.Page<GraphApi>;
    siteId: string;
}

export interface UserApprover extends ui.PartialNodeWithId<User> {
    type: string;
    sortOrder: number;
}

export interface SiteApprover {
    stockTransferOrderDefaultApprover: FilteredUsers;
    stockTransferOrderSubstituteApprover: FilteredUsers;
}

export interface FilteredUsers {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
}
