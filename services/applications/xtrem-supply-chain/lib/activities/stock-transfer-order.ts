import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSupplyChain from '..';
import { commonStockTransferActivities } from '../functions/common';

export const stockTransferOrder = new Activity({
    description: 'Stock transfer order',
    node: () => xtremSupplyChain.nodes.StockTransferOrder,
    __filename,
    permissions: ['read', 'manage', 'approve', 'confirm'],
    operationGrants: {
        read: [
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationLookUp,
            {
                operations: ['proposeAllocationsToTransfer'],
                on: [() => xtremStockData.nodes.Stock],
            },
            {
                operations: ['lookup'],
                on: [() => xtremStockData.nodes.AllocationResult],
            },
            {
                operations: ['searchStock', 'lookup'],
                on: [() => xtremStockData.nodes.Stock],
            },
            {
                operations: ['haveStockDetail'],
                on: [() => xtremStockData.nodes.StockMovementDetail],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremMasterData.nodes.BaseLineToLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLine],
            },
            {
                operations: ['getValuationCost', 'getProjectedStock'],
                on: [() => xtremMasterData.nodes.ItemSite],
            },
            {
                operations: ['getAttributesAndDimensionsFromItem', 'getDefaultAttributesAndDimensions'],
                on: [() => xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault],
            },
            {
                operations: ['financeIntegrationCheck'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
        ],
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'closeLine',
                    'openLine',
                    'close',
                    'open',
                    'subWorkDays',
                    'addWorkDays',
                    'requestAutoAllocation',
                    'financeIntegrationCheck',
                    'sendApprovalRequestMail',
                ],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationOperation,
            {
                operations: ['setDimension'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrderLine],
            },
            { operations: ['getValuationCost', 'getProjectedStock'], on: [() => xtremMasterData.nodes.ItemSite] },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            {
                operations: ['proposeAllocationsToTransfer'],
                on: [() => xtremStockData.nodes.Stock],
            },
            {
                operations: ['lookup'],
                on: [() => xtremStockData.nodes.AllocationResult],
            },
            {
                operations: ['searchStock', 'lookup'],
                on: [() => xtremStockData.nodes.Stock],
            },
            {
                operations: ['haveStockDetail'],
                on: [() => xtremStockData.nodes.StockMovementDetail],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremMasterData.nodes.BaseLineToLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipment],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLine],
            },
            {
                operations: ['getValuationCost'],
                on: [() => xtremMasterData.nodes.ItemSite],
            },
            {
                operations: ['getAttributesAndDimensionsFromItem', 'getDefaultAttributesAndDimensions'],
                on: [() => xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault],
            },
            {
                operations: ['financeIntegrationCheck'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
        ],
        approve: [
            {
                operations: ['read', 'approve', 'reject'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationLookUp,
        ],
        confirm: [
            {
                operations: ['read', 'setIsPrintedTrue', 'confirm'],
                on: [() => xtremSupplyChain.nodes.StockTransferOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferShipmentLineToStockTransferOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremSupplyChain.nodes.StockTransferReceiptLineToStockTransferShipmentLine],
            },
            ...commonStockTransferActivities,
            ...xtremStockData.functions.allocationLib.allocationLookUp,
        ],
    },
});
