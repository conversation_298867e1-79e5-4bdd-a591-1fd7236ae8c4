{"name": "@sage/xtrem-ap-automation", "description": "Sage AI AP automation adapter", "buildStamp": "2025-08-28T13:01:24.681Z", "version": "59.0.8", "author": "sage", "license": "UNLICENSED", "xtrem": {"scope": "purchasing", "isService": true, "queue": "purchasing"}, "keywords": ["xtrem-application-package"], "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-finance": "workspace:*", "@sage/xtrem-finance-data": "workspace:*", "@sage/xtrem-infrastructure-adapter": "workspace:*", "@sage/xtrem-master-data": "workspace:*", "@sage/xtrem-purchasing": "workspace:*", "@sage/xtrem-sales": "workspace:*", "@sage/xtrem-scheduler": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-structure": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-tax": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-ui-plugin-pdf": "workspace:*", "@sage/xtrem-upload": "workspace:*", "axios": "^1.11.0", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-ap-automation-api": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-communication-api": "workspace:*", "@sage/xtrem-master-data-api": "workspace:*", "@sage/xtrem-purchasing-api": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-sales-api": "workspace:*", "@sage/xtrem-scheduler-api": "workspace:*", "@sage/xtrem-structure-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-upload-api": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/jsonwebtoken": "^9.0.0", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/qs": "^6.9.16", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "sinon": "^21.0.0", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "extract:demo:data": "xtrem layers --extract demo", "extract:qa:data": "xtrem layers --extract qa", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "load:demo:data": "xtrem layers --load setup,demo", "load:perf:data": "xtrem layers --load setup,master-data,perf", "load:qa:data": "xtrem layers --load setup,qa", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "manage": "xtrem manage", "qa:cucumber": "xtrem test test/cucumber/* --integration --noTimeout --layers=qa", "qa:cucumber:browser": "xtrem test test/cucumber/* --integration --browser --noTimeout --layers=qa", "schema:create": "xtrem schema --create", "schema:reset": "xtrem schema --reset", "schema:restore": "xtrem schema --restore-from-s3", "schema:upgrade": "xtrem upgrade --run --dev", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --noTimeout --unit --graphql --layers=test", "test:ci": "xtrem test --noTimeout --unit --ci --layers=test", "test:graphql": "xtrem test --noTimeout --graphql --layers=test", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "test:unit": "xtrem test --noTimeout --unit --layers=test", "view-report": "node ../../../scripts/allure/view-report.js", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}