{"name": "@sage/xtrem~services", "description": "Umbrella project for Xtrem development", "version": "59.0.8", "license": "UNLICENSED", "scripts": {"build": "pnpm -sw build:services", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:modified": "pnpm -sw build:modified", "extract:demo:data": "cd main/xtrem-services-main && pnpm extract:demo:data", "extract:layer:data": "cd main/xtrem-services-main && pnpm extract:layer:data", "extract:qa:data": "cd main/xtrem-services-main && pnpm extract:qa:data", "lint": "pnpm -sw lint:services", "load:demo:data": "cd main/xtrem-services-main && pnpm load:demo:data", "load:layer:data": "cd main/xtrem-services-main && pnpm load:layer:data", "load:qa:data": "cd main/xtrem-services-main && pnpm load:qa:data", "load:setup:data": "cd main/xtrem-services-main && pnpm load:setup:data", "load:test:data": "cd main/xtrem-services-main && pnpm load:test:data", "postgres:clean": "pnpm -sw postgres:clean", "postgres:reset": "pnpm -sw postgres:reset", "postgres:setup": "pnpm -sw postgres:setup", "postgres:stop": "pnpm -sw postgres:stop", "schema:reset": "cd main/xtrem-services-main && pnpm schema:reset", "schema:upgrade:test": "cd main/xtrem-services-main && pnpm schema:upgrade:test", "sqs:clean": "pnpm -sw sqs:clean", "sqs:reset": "pnpm -sw sqs:reset", "sqs:setup": "pnpm -sw sqs:setup", "sqs:stop": "pnpm -sw sqs:stop", "sqs:sync": "pnpm -sw sqs:sync", "start": "cd main/xtrem-services-main && npm start", "test": "pnpm -sw test:services", "test:functional": "xdev run lerna-cache/test-functional.sh", "test:functional:ci": "xdev run lerna-cache/test-functional.sh :ci", "test:integration": "xtrem test --integration --browser", "test:smoke:ci": "pnpm -sw test:smoke:ci", "test:smokecd": "xdev run lerna-cache/test-integration-smoke-cd.sh"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0"}}