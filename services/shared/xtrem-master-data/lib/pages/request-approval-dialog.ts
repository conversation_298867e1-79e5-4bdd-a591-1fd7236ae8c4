import type { BaseDocument$Operations, BaseDocument as BaseDocumentNode, GraphApi } from '@sage/xtrem-master-data-api';
import type { User } from '@sage/xtrem-system-api';
import { validEmail } from '@sage/xtrem-system/build/lib/shared-functions/email-validation';
import * as ui from '@sage/xtrem-ui';
import type { FilteredUsers, UserApprover } from '../client-functions/interfaces';
import { confirmDialogWithAcceptButtonText } from '../client-functions/page-functions';
import * as pillColorCommon from '../client-functions/pill-color';
import * as MasterDataUtils from '../client-functions/utils';

@ui.decorators.page<RequestApprovalDialog, BaseDocumentNode>({
    title: 'Approval request',
    module: 'master-data',
    businessActions() {
        return [this.selectApprover, this.sendApprovalRequestButton];
    },
    onLoad() {
        const params = this.$.queryParameters;

        const users = JSON.parse(params.users as string);
        const defaultApprover: FilteredUsers = JSON.parse(params.defaultApprover as string);
        const substituteApprover: FilteredUsers = JSON.parse(params.substituteApprover as string);
        this.nodeName = JSON.parse(params.nodeName as string);
        this.isApproval = JSON.parse(params.isApproval as string);

        if (params.dialogTitle) {
            this.$.page.title = JSON.parse(params.dialogTitle as string);
        }
        if (params.emailHelperText) {
            this.emailAddressApproval.helperText = JSON.parse(params.emailHelperText as string);
        }
        if (params.confirmTitle) {
            this.confirmTitle = JSON.parse(params.confirmTitle as string);
        }
        if (params.confirmMessage) {
            this.confirmMessage = JSON.parse(params.confirmMessage as string);
        }

        this.resultMessage = params.resultMessage
            ? JSON.parse(params.resultMessage as string)
            : ui.localize(
                  '@sage/xtrem-master-data/pages__request_approval_dialog__email_sent_to_approval',
                  'Email sent for approval to: ',
              );

        this.errorMessage = params.errorMessage
            ? JSON.parse(params.errorMessage as string)
            : ui.localize(
                  '@sage/xtrem-master-data/pages__request_approval_dialog__email_exception_request',
                  'Could not send request email.',
              );
        if (params.emailNotSent) {
            this.emailNotSent = JSON.parse(params.emailNotSent as string);
        }
        if (params.selectButtonTitle) {
            this.selectApprover.title = JSON.parse(params.selectButtonTitle as string);
        }

        this.loadApprovers(defaultApprover, substituteApprover, users);
    },
})
export class RequestApprovalDialog extends ui.Page<GraphApi, BaseDocumentNode> {
    private nodeName: string;

    private isApproval: boolean;

    private confirmTitle: string;

    private confirmMessage: string;

    private resultMessage: string;

    private errorMessage: string;

    private emailNotSent: string;

    @ui.decorators.pageAction<RequestApprovalDialog>({
        isTransient: true,
        buttonType: 'secondary',
        async onClick() {
            await this.$.dialog
                .custom('info', this.approverSelectionBlock)
                .then(() => {
                    this.emailAddressApproval.value = this.selectedUser.value?.email
                        ? this.selectedUser.value?.email
                        : this.emailAddressApproval.value;
                })
                .finally(() => {
                    this.selectedUser.value = null;
                });
        },
        title: 'Select approver',
        isTitleHidden: true,
    })
    selectApprover: ui.PageAction;

    @ui.decorators.pageAction<RequestApprovalDialog>({
        title: 'Send',
        buttonType: 'secondary',
        onError(error: string | (Error & { errors: Array<any> })) {
            return `${this.errorMessage} (${MasterDataUtils.formatError(this, error)})`;
        },
        async onClick() {
            const email = this.emailAddressApproval.value ?? '';

            if (email.length === 0 || !validEmail(email)) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-master-data/pages__request_approval_dialog__invalid-email',
                        'Invalid email address: {{value}}',
                        {
                            value: this.emailAddressApproval.value,
                        },
                    ),
                    { type: 'error' },
                );
            } else if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    this.confirmTitle
                        ? this.confirmTitle
                        : ui.localize(
                              '@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_confirm_title',
                              'Stock transfer order approval request',
                          ),
                    this.confirmMessage
                        ? this.confirmMessage
                        : ui.localize(
                              '@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_content',
                              'You are about to send the approval email.',
                          ),
                    ui.localize('@sage/xtrem-master-data/pages-confirm-send', 'Send'),
                )
            ) {
                this.$.loader.isHidden = false;
                if (this.$.recordId && this.emailAddressApproval.value) {
                    const mailerUser = this.users.value.find(user => user.email === this.emailAddressApproval.value);

                    const isSent = this.isApproval
                        ? await (
                              this.$.graph.node(this.nodeName as keyof GraphApi) as BaseDocument$Operations
                          ).mutations
                              .sendApprovalRequestMail(true, {
                                  document: this.$.recordId,
                                  user: {
                                      email: this.emailAddressApproval.value,
                                      lastName: mailerUser?.lastName ?? '',
                                      firstName: mailerUser?.firstName ?? '',
                                  },
                              })
                              .execute()
                        : await (
                              this.$.graph.node(this.nodeName as keyof GraphApi) as {
                                  mutations: { sendNotificationToBuyerMail: Function };
                              }
                          ).mutations
                              .sendNotificationToBuyerMail(true, {
                                  document: this.$.recordId,
                                  user: {
                                      email: this.emailAddressApproval.value,
                                      lastName: mailerUser?.lastName ?? '',
                                      firstName: mailerUser?.firstName ?? '',
                                  },
                              })
                              .execute();

                    if (!isSent) {
                        throw new Error(
                            this.emailNotSent
                                ? this.emailNotSent
                                : ui.localize(
                                      '@sage/xtrem-master-data/pages__request_approval_dialog__email_not_sent',
                                      'The request for approval cannot be sent by email.',
                                  ),
                        );
                    }

                    this.$.showToast(`${this.resultMessage + this.emailAddressApproval.value}.`, {
                        type: 'success',
                        timeout: 20000,
                    });
                }
                this.requestApprovalSection.isHidden = true;
                this.$.loader.isHidden = true;
                this.$.finish({ isSent: true });
            }
        },
    })
    sendApprovalRequestButton: ui.PageAction;

    @ui.decorators.textField<RequestApprovalDialog>({
        isHidden: true,
    })
    number: ui.fields.Text;

    @ui.decorators.section<RequestApprovalDialog>({ isTitleHidden: true, title: 'Request for approval' })
    requestApprovalSection: ui.containers.Section;

    @ui.decorators.block<RequestApprovalDialog>({
        parent() {
            return this.requestApprovalSection;
        },
    })
    requestApprovalBlock: ui.containers.Block;

    @ui.decorators.textField<RequestApprovalDialog>({
        parent() {
            return this.requestApprovalBlock;
        },
        title: 'To',
        helperText: 'A request for approval email will be sent to this address',
        isFullWidth: true,
        isTransient: true,
        validation(val) {
            if (val.length > 0 && !validEmail(val)) {
                return ui.localize(
                    '@sage/xtrem-master-data/pages__request_approval_dialog__invalid-email',
                    'Invalid email address: {{value}}',
                    {
                        value: val,
                    },
                );
            }
            return undefined;
        },
    })
    emailAddressApproval: ui.fields.Text;

    @ui.decorators.block<RequestApprovalDialog>({
        title: 'Select approver',
    })
    approverSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<RequestApprovalDialog, User & { sortOrder: number; type: string }>({
        parent() {
            return this.approverSelectionBlock;
        },
        canSelect: false,
        title: 'Users',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        isReadOnly: true,
        orderBy: { sortOrder: +1 },
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.label({
                title: 'Approver',
                bind: 'type',
                backgroundColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize(
                            '@sage/xtrem-master-data/pages__request_approval_dialog__default_approver',
                            'Default',
                        );
                    return pillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'backgroundColor');
                },
                borderColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize(
                            '@sage/xtrem-master-data/pages__request_approval_dialog__default_approver',
                            'Default',
                        );
                    return pillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'borderColor');
                },
                color(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize(
                            '@sage/xtrem-master-data/pages__request_approval_dialog__default_approver',
                            'Default',
                        );
                    return pillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'textColor');
                },
            }),
            ui.nestedFields.technical({ bind: 'sortOrder' }),
        ],
        onRowClick(_rowId, rowData) {
            this.selectedUser.value = rowData as unknown as ui.PartialNode<User>;
        },
    })
    users: ui.fields.Table<User & { sortOrder: number; type: string }>;

    @ui.decorators.referenceField<RequestApprovalDialog, User>({
        parent() {
            return this.approverSelectionBlock;
        },
        title: 'Selected user',
        lookupDialogTitle: 'Select selected user',
        isTitleHidden: true,
        isReadOnly: true,
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'email',
        helperTextField: 'displayName',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
    })
    selectedUser: ui.fields.Reference<User>;

    loadApprovers(defaultApprover: FilteredUsers, substituteApprover: FilteredUsers, users: UserApprover[]) {
        const usersList: UserApprover[] = [];
        let sortOrder = 0;

        if (defaultApprover?._id) {
            usersList.push({
                ...defaultApprover,
                type: ui.localize(
                    '@sage/xtrem-master-data/pages__request_approval_dialog__default_approver',
                    'Default',
                ),
                sortOrder,
            });
            sortOrder += 1;
            this.emailAddressApproval.value = defaultApprover.email;
        }

        if (substituteApprover && substituteApprover?._id !== defaultApprover?._id) {
            usersList.push({
                ...substituteApprover,
                type: ui.localize(
                    '@sage/xtrem-master-data/pages__request_approval_dialog__substitute_approver',
                    'Substitute',
                ),
                sortOrder,
            });
            sortOrder += 1;
            if (!defaultApprover) {
                this.emailAddressApproval.value = substituteApprover.email;
            }
        }

        usersList.push(
            ...users
                .filter(userFilter => ![defaultApprover?._id, substituteApprover?._id].includes(userFilter._id))
                .map((user, index: number) => ({
                    ...user,
                    type: '',
                    sortOrder: sortOrder + index,
                })),
        );
        this.users.value = usersList;
    }
}
