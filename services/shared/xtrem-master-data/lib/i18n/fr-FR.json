{"@sage/xtrem-master-data/activity__allergen__name": "Allerg<PERSON>", "@sage/xtrem-master-data/activity__bom_revision_sequence__name": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/activity__business_entity__name": "Entité commerciale", "@sage/xtrem-master-data/activity__capability_level__name": "Niveau d'aptitude", "@sage/xtrem-master-data/activity__container__name": "Contenant", "@sage/xtrem-master-data/activity__cost_category__name": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/activity__currency__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/activity__customer__name": "Client", "@sage/xtrem-master-data/activity__customer_price_reason__name": "Motif prix client", "@sage/xtrem-master-data/activity__customer_supplier_category__name": "<PERSON><PERSON><PERSON><PERSON> client", "@sage/xtrem-master-data/activity__daily_shift__name": "Horaire journalier", "@sage/xtrem-master-data/activity__delivery_mode__name": "Mode de livraison", "@sage/xtrem-master-data/activity__employee__name": "Collaborateur", "@sage/xtrem-master-data/activity__ghs_classification__name": "Classification SGH", "@sage/xtrem-master-data/activity__group_resource__name": "Groupe de ressources", "@sage/xtrem-master-data/activity__incoterm__name": "Règle d'Incoterms", "@sage/xtrem-master-data/activity__indirect_cost_origin__name": "Origine coût indirect", "@sage/xtrem-master-data/activity__indirect_cost_section__name": "Section coût indirect", "@sage/xtrem-master-data/activity__item__name": "Article", "@sage/xtrem-master-data/activity__item_category__name": "Catégorie d'article", "@sage/xtrem-master-data/activity__item_site__name": "Article-site", "@sage/xtrem-master-data/activity__item_site_cost__name": "Coût article-site", "@sage/xtrem-master-data/activity__item_site_supplier__name": "Article-site-fournisseur", "@sage/xtrem-master-data/activity__labour_resource__name": "Ressource main d'oeuvre", "@sage/xtrem-master-data/activity__license_plate_number__name": "Numéro de contenant interne", "@sage/xtrem-master-data/activity__location__name": "Emplacement", "@sage/xtrem-master-data/activity__location_sequence__name": "Compteur emplacement", "@sage/xtrem-master-data/activity__location_type__name": "Type d'emplacement", "@sage/xtrem-master-data/activity__location_zone__name": "Zone d'emplacement", "@sage/xtrem-master-data/activity__machine_resource__name": "Ressource machine", "@sage/xtrem-master-data/activity__payment_term__name": "Condition de paiement", "@sage/xtrem-master-data/activity__reason_code__name": "Code motif", "@sage/xtrem-master-data/activity__sequence_number__name": "Compteur", "@sage/xtrem-master-data/activity__sequence_number_assignment__name": "Attribution de compteurs", "@sage/xtrem-master-data/activity__shift_detail__name": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/activity__standard__name": "Standard", "@sage/xtrem-master-data/activity__supplier__name": "Fournisseur", "@sage/xtrem-master-data/activity__supplier_certificate__name": "Certificat fournisseur", "@sage/xtrem-master-data/activity__tool_resource__name": "Ressource outil", "@sage/xtrem-master-data/activity__unit_of_measure__name": "Unité de mesure", "@sage/xtrem-master-data/activity__weekly_shift__name": "<PERSON><PERSON><PERSON> he<PERSON>", "@sage/xtrem-master-data/business_entity_address_node_only_one_primary_contact": "", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological_control_must_be_active_fr_legislation": "Numéro de compteur {{sequenceNumber}} : le contrôle chronologique doit être actif pour la législation FR.", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological-control-must-be-active": "Numéro de compteur {{sequenceNumber}} : le contrôle chronologique doit être actif pour la législation FR.", "@sage/xtrem-master-data/classes__sequence-number-generator__document_date_higher_than_next_date": "La date de document {{currentDate}} est postérieure à la date du document suivant {{nextDocumentDate}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document_date_lower_than_previous_date": "La date de document {{currentDate}} est antérieure à la date du document précédent {{previousDocumentDate}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-cannot-be-later-than-today": "La date du document ne peut pas être postérieure à la date du jour.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-earlier-than-previous-document-date": "La date de document {{current}} est antérieure à la date du document précédent {{previousDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-later-than-next-document-date": "La date de document {{current}} est postérieure à la date du document suivant {{nextDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__future_date_not_allowed": "La date du document ne peut pas être postérieure à la date du jour.", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-component-type-value": "Type de composant incorrect : {{type}}", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-enum-value": "Valeur incorrecte : {{enumValue}}", "@sage/xtrem-master-data/classes__sequence-number-generator__monthly-sequence-numbers-not-allowed": "Numéro de compteur {{sequenceNumber}} : les compteurs mensuels ne sont pas autorisés.", "@sage/xtrem-master-data/classes__sequence-number-generator__no-sequence-number-assigned": "Aucun compteur n'a été attribué pour ce type de document.", "@sage/xtrem-master-data/classes__sequence-number-generator__node-instance-is-required": "L'instance du node est requise.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-at-application-level-definition-and-no-input-value-for-site": "Le compteur {{id}} est défini au niveau {{definitionLevel}} et le site n'est pas renseigné.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-defined-at-application-level": "Le numéro de compteur {{id}} n'est pas défini au niveau de l'application. V<PERSON> devez renseigner un site ou une société dans les composants.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-instance-not-found": "L'instance de compteur est introuvable.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-not-defined": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_continue": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/client_functions__master_data__resync_status_message": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_title": "", "@sage/xtrem-master-data/client_functions__master_data_resync_submitted": "", "@sage/xtrem-master-data/company_node_only_one_primary_address": "Une seule adresse principale est autorisée.", "@sage/xtrem-master-data/company_node_only_one_primary_contact": "Vous ne pouvez définir qu'un seul contact principal.", "@sage/xtrem-master-data/control__item__landedCost_service_option_inactive": "Vous pouvez uniquement créer un article de type frais d'approche si l'option de service Frais d'approche est activée.", "@sage/xtrem-master-data/control__value_should_be_positive": "La valeur de champ doit correspondre à un numéro positif : {{propertyName}}.", "@sage/xtrem-master-data/control-begin__sequence-number__component-length-different-than-0": "Lorsque la fréquence de réinitialisation est fixée à {{rtzLevel}}, vous devez renseigner une longueur différente de 0 dans le type de composant {{type}}.", "@sage/xtrem-master-data/control-begin__sequence-number__definition_level_is_not_present_in_components": "Le compteur est défini au niveau {{definitionLevel}}. <PERSON><PERSON> renseigner une valeur {{definitionLevel}} dans le tableau des composants.", "@sage/xtrem-master-data/control-begin__sequence-number__no_sequence_number_component": "Renseignez un composant de compteur.", "@sage/xtrem-master-data/control-begin__sequence-number__rtz_level_is_not_present_in_components": "Lorsque la fréquence de réinitialisation est fixée à {{rtzLevel}}, vous devez renseigner le {{type}} dan le tableau des Composants.", "@sage/xtrem-master-data/control-begin__sequence-number__sequence-numeric-wrong-component": "Le type du compteur est numérique. Vous pouvez uniquement renseigner des composants numériques.", "@sage/xtrem-master-data/create": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/create-confirmation": "Enregistrement créé", "@sage/xtrem-master-data/customer-delivery-address-used": "Vous ne pouvez pas supprimer l'adresse d'expédition {{deliveryAddressName}}. Elle est déjà utilisée.", "@sage/xtrem-master-data/customer-item-used": "Vous ne pouvez pas supprimer l'article client {{customerItemName}}. Il est déjà utilisé.", "@sage/xtrem-master-data/data_types__address_entity_type_enum__name": "Enum type entité adresse", "@sage/xtrem-master-data/data_types__address_line_data_type__name": "Type de données ligne adresse", "@sage/xtrem-master-data/data_types__amount_data_type__name": "Type de données montant", "@sage/xtrem-master-data/data_types__amount_in_company_currency__name": "Montant en devise société", "@sage/xtrem-master-data/data_types__amount_in_financial_site_currency__name": "Montant en devise de site financier", "@sage/xtrem-master-data/data_types__amount_in_transaction_currency__name": "Montant en devise transaction", "@sage/xtrem-master-data/data_types__approval_status_enum__name": "Enum statut d'approbation", "@sage/xtrem-master-data/data_types__base_certificate_property_data_type__name": "Type de données propriété certificat de base", "@sage/xtrem-master-data/data_types__base_decimal__name": "Décimale de base", "@sage/xtrem-master-data/data_types__base_display_status_enum__name": "Enum statut d'affichage de base", "@sage/xtrem-master-data/data_types__base_origin_enum__name": "Enum origine base", "@sage/xtrem-master-data/data_types__base_price__name": "Prix de base", "@sage/xtrem-master-data/data_types__base_sequence_number_component_type_enum__name": "Enum de type composant de compteur de base", "@sage/xtrem-master-data/data_types__base_status_enum__name": "Enum statut de base", "@sage/xtrem-master-data/data_types__bom_revision_sequence__name": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/data_types__business_entity__name": "Entité commerciale", "@sage/xtrem-master-data/data_types__business_entity_id__name": "Code entité commerciale", "@sage/xtrem-master-data/data_types__business_entity_type_enum__name": "Enum type entité commerciale", "@sage/xtrem-master-data/data_types__business_relation_type_enum__name": "Enum type relation commerciale", "@sage/xtrem-master-data/data_types__capacity_percentage__name": "Pourcentage de capacité", "@sage/xtrem-master-data/data_types__city_data_type__name": "Type de données ville", "@sage/xtrem-master-data/data_types__coefficient_data_type__name": "Type de données coefficient", "@sage/xtrem-master-data/data_types__company_price_data_type__name": "Type de données prix société", "@sage/xtrem-master-data/data_types__constant_sequence_data_type__name": "Type de donnée séquence constante", "@sage/xtrem-master-data/data_types__consumption_mode_enum__name": "Enum mode de consommation", "@sage/xtrem-master-data/data_types__contact_position_data_type__name": "Type de données position contact", "@sage/xtrem-master-data/data_types__contact_property_data_type__name": "Type de données propriété contact", "@sage/xtrem-master-data/data_types__contact_role_enum__name": "Enum rôle contact", "@sage/xtrem-master-data/data_types__container_type_enum__name": "Enum type contnenant", "@sage/xtrem-master-data/data_types__cost_calculation_method_enum__name": "Enum méthode de calcul coût", "@sage/xtrem-master-data/data_types__cost_category_type_enum__name": "Enum type catégorie coût", "@sage/xtrem-master-data/data_types__cost_data_type__name": "Type de données coût", "@sage/xtrem-master-data/data_types__cost_valuation_method_enum__name": "Enum méthode de valorisation coût", "@sage/xtrem-master-data/data_types__cost_value_data_type__name": "Type de données valeur coût", "@sage/xtrem-master-data/data_types__currency__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__customer__name": "Client", "@sage/xtrem-master-data/data_types__customer_display_status_enum__name": "Enum statut d'affichage client", "@sage/xtrem-master-data/data_types__customer_on_hold_type_enum__name": "Enum type de client bloqué", "@sage/xtrem-master-data/data_types__customer_supplier_category__name": "<PERSON><PERSON><PERSON><PERSON> client", "@sage/xtrem-master-data/data_types__delivery_mode__name": "Mode de livraison", "@sage/xtrem-master-data/data_types__discount_charge_calculation_basis_enum__name": "Enum base calcul remise et frais", "@sage/xtrem-master-data/data_types__discount_charge_calculation_rule_enum__name": "Enum base calcul remise et frais", "@sage/xtrem-master-data/data_types__discount_charge_sign_enum__name": "Enum signe remise et frais", "@sage/xtrem-master-data/data_types__discount_charge_value_type_enum__name": "Enum type valeur remise et frais", "@sage/xtrem-master-data/data_types__discount_or_penalty_type_enum__name": "Enum type escompte ou pénalité", "@sage/xtrem-master-data/data_types__document_number__name": "Numéro de document", "@sage/xtrem-master-data/data_types__due_date_type_enum__name": "Enum type date échéance", "@sage/xtrem-master-data/data_types__duration_data_type__name": "Type de données durée", "@sage/xtrem-master-data/data_types__ean_number_data_type__name": "Type de données EAN", "@sage/xtrem-master-data/data_types__efficiency_percentage__name": "Pourcentage efficience", "@sage/xtrem-master-data/data_types__email_action_type_enum__name": "Enum type d'action e-mail", "@sage/xtrem-master-data/data_types__exchange_rate__name": "Cours de change", "@sage/xtrem-master-data/data_types__extra_large_string__name": "Cha<PERSON>ne très volumineuse", "@sage/xtrem-master-data/data_types__fake_site_reference_datatype__name": "Type de données de référence de site fictif", "@sage/xtrem-master-data/data_types__incoterm__name": "Incoterm", "@sage/xtrem-master-data/data_types__incoterm_data_type__name": "Type de données Incoterm", "@sage/xtrem-master-data/data_types__indirect_cost_percentage__name": "Pourcentage coûts indirects", "@sage/xtrem-master-data/data_types__input_sequence_data_type__name": "Type de donnée séquence saisie", "@sage/xtrem-master-data/data_types__item__name": "Article", "@sage/xtrem-master-data/data_types__item_category__name": "Catégorie d'article", "@sage/xtrem-master-data/data_types__item_category_type_enum__name": "Enum type catégorie article", "@sage/xtrem-master-data/data_types__item_flow_type_enum__name": "Enum type de flux article", "@sage/xtrem-master-data/data_types__item_price_type_enum__name": "Enum type de prix article", "@sage/xtrem-master-data/data_types__item_status_enum__name": "Enum statut article", "@sage/xtrem-master-data/data_types__item_type_enum__name": "Enum type article", "@sage/xtrem-master-data/data_types__label_format_data_type__name": "Type de données format libellé", "@sage/xtrem-master-data/data_types__large_string__name": "<PERSON><PERSON><PERSON> de grande taille", "@sage/xtrem-master-data/data_types__legal_entity_enum__name": "Enum entité légale", "@sage/xtrem-master-data/data_types__localized_sic_description_data_type__name": "Type de données description NAF localisée", "@sage/xtrem-master-data/data_types__location__name": "Emplacement", "@sage/xtrem-master-data/data_types__location_category_enum__name": "Enum catégorie d'emplacement", "@sage/xtrem-master-data/data_types__location_sequence__name": "Compteur emplacement", "@sage/xtrem-master-data/data_types__location_sequence_type_enum__name": "Enum de type de compteur d'emplacement", "@sage/xtrem-master-data/data_types__lot_management_enum__name": "Enum gestion des lots", "@sage/xtrem-master-data/data_types__master_data_company__name": "Société de donnée de référence", "@sage/xtrem-master-data/data_types__master_data_site__name": "Site de donnée de référence", "@sage/xtrem-master-data/data_types__medium_string__name": "<PERSON><PERSON><PERSON> moyenne", "@sage/xtrem-master-data/data_types__model_data_type__name": "Type de données modèle", "@sage/xtrem-master-data/data_types__note__name": "Note", "@sage/xtrem-master-data/data_types__order_cost_data_type__name": "Type de données coût commande", "@sage/xtrem-master-data/data_types__order_type_enum__name": "Enum type commande", "@sage/xtrem-master-data/data_types__payment_method_enum__name": "Enum mode de règlement", "@sage/xtrem-master-data/data_types__payment_term__name": "Condition de paiement", "@sage/xtrem-master-data/data_types__payment_term_data_type__name": "Type de données condition de paiement", "@sage/xtrem-master-data/data_types__payment_term_discount_or_penalty_type_enum__name": "Enum type escompte ou pénalité condition de paiement", "@sage/xtrem-master-data/data_types__percentage__name": "Pourcentage", "@sage/xtrem-master-data/data_types__percentage_work_order_data_type__name": "Type de données ordre de fabrication pourcentage", "@sage/xtrem-master-data/data_types__period_type_enum__name": "Enum type période", "@sage/xtrem-master-data/data_types__postcode_data_type__name": "Type de données code postal", "@sage/xtrem-master-data/data_types__potency_percentage__name": "Pourcentage titre", "@sage/xtrem-master-data/data_types__preferred_process_enum__name": "Enum processus préféré", "@sage/xtrem-master-data/data_types__price__name": "Prix", "@sage/xtrem-master-data/data_types__price_data_type__name": "Type de données prix", "@sage/xtrem-master-data/data_types__price_in_sales_price__name": "Prix en prix de vente", "@sage/xtrem-master-data/data_types__price_percentage__name": "Pourcentage prix", "@sage/xtrem-master-data/data_types__quantity__name": "Quantité", "@sage/xtrem-master-data/data_types__quantity_in_purchase_unit__name": "Qté unité achat", "@sage/xtrem-master-data/data_types__quantity_in_sales_unit__name": "Quantité en unité de vente", "@sage/xtrem-master-data/data_types__quantity_in_stock_unit__name": "Quantité en unité de stock", "@sage/xtrem-master-data/data_types__quantity_in_unit__name": "Quantité en unité", "@sage/xtrem-master-data/data_types__quantity_in_volume_unit__name": "Quantité en unité de volume", "@sage/xtrem-master-data/data_types__quantity_in_weight_unit__name": "Quantité en unité de poids", "@sage/xtrem-master-data/data_types__reason_code__name": "Code motif", "@sage/xtrem-master-data/data_types__region_data_type__name": "Type de données région", "@sage/xtrem-master-data/data_types__replenishment_method_enum__name": "Enum méthode de réapprovisionnement", "@sage/xtrem-master-data/data_types__resource_cost__name": "Coût ressource", "@sage/xtrem-master-data/data_types__resource_group_type_enum__name": "Enum yype groupe ressource", "@sage/xtrem-master-data/data_types__run_time_data_type__name": "Type de données run time", "@sage/xtrem-master-data/data_types__scrap_factor_percentage__name": "Pourcentage rebut", "@sage/xtrem-master-data/data_types__sequence_counter_definition_level_enum__name": "Enum niveau définition compteurr", "@sage/xtrem-master-data/data_types__sequence_number__name": "Compteur", "@sage/xtrem-master-data/data_types__sequence_number_field_kind_enum__name": "Enum type champ compteur", "@sage/xtrem-master-data/data_types__sequence_number_reset_frequency_enum__name": "Enum fréquence réinitialisation compteur", "@sage/xtrem-master-data/data_types__sequence_number_type_enum__name": "Enum type compteur", "@sage/xtrem-master-data/data_types__serial_number_management_enum__name": "Enum gestion n° série", "@sage/xtrem-master-data/data_types__serial_number_usage_enum__name": "Enum utilisation numéro série", "@sage/xtrem-master-data/data_types__setup_time_data_type__name": "Type de données heure paramétrage", "@sage/xtrem-master-data/data_types__shift_data_type__name": "Type de données équipe", "@sage/xtrem-master-data/data_types__standard_property_data_type__name": "Type de données propriété standard", "@sage/xtrem-master-data/data_types__stock_management_mode_enum__name": "Enum mode de gestion de stock", "@sage/xtrem-master-data/data_types__stock_quantity__name": "Quantité de stock", "@sage/xtrem-master-data/data_types__stock_quantity_variance_percentage__name": "Pourcentage d'écart de quantité de stock", "@sage/xtrem-master-data/data_types__stock_variation_value__name": "Valeur de variation de stock", "@sage/xtrem-master-data/data_types__supplier__name": "Fournisseur", "@sage/xtrem-master-data/data_types__supplier_item_property_data_type__name": "Type de données propriété article fournisseur", "@sage/xtrem-master-data/data_types__supplier_type_enum__name": "Enum type fournisseur", "@sage/xtrem-master-data/data_types__symbol_data_type__name": "Type de données symbole", "@sage/xtrem-master-data/data_types__tax_calculation_status_enum__name": "Enum statut du calcul de taxe", "@sage/xtrem-master-data/data_types__telephone_number_data_type__name": "Type de données numéro téléphone", "@sage/xtrem-master-data/data_types__time_data_type__name": "Type de données heure", "@sage/xtrem-master-data/data_types__time_zone__name": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-master-data/data_types__title_enum__name": "Enum intitulés", "@sage/xtrem-master-data/data_types__unit_conversion_coefficient__name": "Coefficient de conversion d'unité", "@sage/xtrem-master-data/data_types__unit_conversion_type_enum__name": "Enum type conversion unité", "@sage/xtrem-master-data/data_types__unit_of_measure__name": "Unité de mesure", "@sage/xtrem-master-data/data_types__unit_type_enum__name": "Enum type unité", "@sage/xtrem-master-data/data_types__version_data_type__name": "Type de données version", "@sage/xtrem-master-data/data_types__volume_percentage__name": "Pourcentage de volume", "@sage/xtrem-master-data/data_types__week_days_enum__name": "Enum jours ouvrés", "@sage/xtrem-master-data/data_types__weight_percentage__name": "Pourcentage de poids", "@sage/xtrem-master-data/data_types__work_in_progress_document_type_enum__name": "Enum type document en-cours", "@sage/xtrem-master-data/data_types__zone_type_enum__name": "Enum type de zone", "@sage/xtrem-master-data/data-types/percentage__value_greater_than_a_maximum": "La valeur en pourcentage ({{value}}) ne doit pas dépasser {{maxValue}}.", "@sage/xtrem-master-data/data-types/percentage__value_less_than_a_minimum": "La valeur du pourcentage ({{value}}) doit être supérieure à {{minValue}}.", "@sage/xtrem-master-data/data-types/percentage__value_not_in_allowed_range": "La valeur en pourcentage ({{value}}) doit être comprise entre {{minValue}} et {{maxValue}}.", "@sage/xtrem-master-data/days": "jour(s)", "@sage/xtrem-master-data/delete-confirmation": "Enregistrement supprimé", "@sage/xtrem-master-data/delete-dialog-content": "Vous êtes sur le point de supprimer cet enregistrement. Confirmer ?", "@sage/xtrem-master-data/delete-dialog-title": "Confirmer la <PERSON>", "@sage/xtrem-master-data/edit-create-customer-price": "Ajouter nouveau prix de vente", "@sage/xtrem-master-data/edit-create-line": "Ajouter nouveau client", "@sage/xtrem-master-data/edit-create-supplier": "Ajouter nouveau fournisseur", "@sage/xtrem-master-data/edit-create-supplier-price": "Ajouter nouveau prix fournisseur", "@sage/xtrem-master-data/email-validation-error": "Adresse e-mail invalide", "@sage/xtrem-master-data/enums__address_entity_type__businessEntity": "Entité commerciale", "@sage/xtrem-master-data/enums__address_entity_type__company": "Société", "@sage/xtrem-master-data/enums__address_entity_type__customer": "Client", "@sage/xtrem-master-data/enums__address_entity_type__site": "Site", "@sage/xtrem-master-data/enums__address_entity_type__supplier": "Fournisseur", "@sage/xtrem-master-data/enums__approval_status__approved": "Approu<PERSON><PERSON>", "@sage/xtrem-master-data/enums__approval_status__changeRequested": "Changement demandé", "@sage/xtrem-master-data/enums__approval_status__confirmed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__approval_status__draft": "Brouillon", "@sage/xtrem-master-data/enums__approval_status__pendingApproval": "En attente d'approbation", "@sage/xtrem-master-data/enums__approval_status__rejected": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__approved": "Approu<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__changeRequested": "Changement demandé", "@sage/xtrem-master-data/enums__base_display_status__closed": "Soldé", "@sage/xtrem-master-data/enums__base_display_status__confirmed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__credited": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__draft": "Brouillon", "@sage/xtrem-master-data/enums__base_display_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__invoiced": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__noVariance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__ordered": "Commandé", "@sage/xtrem-master-data/enums__base_display_status__paid": "Totalement réglé", "@sage/xtrem-master-data/enums__base_display_status__partiallyCredited": "Partiellement cré<PERSON>", "@sage/xtrem-master-data/enums__base_display_status__partiallyInvoiced": "Partiellement facturé", "@sage/xtrem-master-data/enums__base_display_status__partiallyOrdered": "Partiellement commandé", "@sage/xtrem-master-data/enums__base_display_status__partiallyPaid": "Partiellement réglé", "@sage/xtrem-master-data/enums__base_display_status__partiallyReceived": "Partiellement ré<PERSON>", "@sage/xtrem-master-data/enums__base_display_status__partiallyReturned": "Partiellement retourné", "@sage/xtrem-master-data/enums__base_display_status__partiallyShipped": "Partiellement expédié", "@sage/xtrem-master-data/enums__base_display_status__pending": "En attente", "@sage/xtrem-master-data/enums__base_display_status__pendingApproval": "En attente d'approbation", "@sage/xtrem-master-data/enums__base_display_status__posted": "Comptabilisé", "@sage/xtrem-master-data/enums__base_display_status__postingError": "Erreur de comptabilisation", "@sage/xtrem-master-data/enums__base_display_status__postingInProgress": "Comptabilisation en cours", "@sage/xtrem-master-data/enums__base_display_status__quote": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__readyToProcess": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__readyToShip": "<PERSON>r<PERSON><PERSON> à expédier", "@sage/xtrem-master-data/enums__base_display_status__received": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__rejected": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__returned": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__shipped": "Expédié", "@sage/xtrem-master-data/enums__base_display_status__stockError": "Erreur de stock", "@sage/xtrem-master-data/enums__base_display_status__taxCalculationFailed": "Échec du calcul de taxe", "@sage/xtrem-master-data/enums__base_display_status__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__varianceApproved": "<PERSON><PERSON><PERSON> approuv<PERSON>", "@sage/xtrem-master-data/enums__base_origin__direct": "Direct", "@sage/xtrem-master-data/enums__base_origin__invoice": "Facture", "@sage/xtrem-master-data/enums__base_origin__order": "Commande", "@sage/xtrem-master-data/enums__base_origin__purchaseCreditMemo": "Avoir d'achat", "@sage/xtrem-master-data/enums__base_origin__purchaseInvoice": "Facture d'achat", "@sage/xtrem-master-data/enums__base_origin__purchaseOrder": "Commande d'achat", "@sage/xtrem-master-data/enums__base_origin__purchaseReceipt": "<PERSON><PERSON><PERSON> d'<PERSON>", "@sage/xtrem-master-data/enums__base_origin__purchaseRequisition": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_origin__purchaseReturn": "Retour d'achat", "@sage/xtrem-master-data/enums__base_origin__purchaseSuggestion": "Suggestion d'achat", "@sage/xtrem-master-data/enums__base_origin__return": "Retour", "@sage/xtrem-master-data/enums__base_origin__shipment": "Expédition", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__company": "Société", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__day": "Jour", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceAlpha": "Séquence alpha", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceNumber": "Compteur", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__site": "Site", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__year": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__approved": "Approu<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__changeRequested": "Changement demandé", "@sage/xtrem-master-data/enums__base_status__closed": "Soldé", "@sage/xtrem-master-data/enums__base_status__confirmed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__credited": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__draft": "Brouillon", "@sage/xtrem-master-data/enums__base_status__error": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__inProgress": "En cours", "@sage/xtrem-master-data/enums__base_status__invoiced": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__noVariance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__partiallyCredited": "Partiellement cré<PERSON>", "@sage/xtrem-master-data/enums__base_status__partiallyInvoiced": "Partiellement facturé", "@sage/xtrem-master-data/enums__base_status__partiallyReceived": "Partiellement ré<PERSON>", "@sage/xtrem-master-data/enums__base_status__partiallyReturned": "Partiellement retourné", "@sage/xtrem-master-data/enums__base_status__partiallyShipped": "Partiellement expédié", "@sage/xtrem-master-data/enums__base_status__pending": "En attente", "@sage/xtrem-master-data/enums__base_status__pendingApproval": "En attente d'approbation", "@sage/xtrem-master-data/enums__base_status__posted": "Comptabilisé", "@sage/xtrem-master-data/enums__base_status__postingError": "Erreur de comptabilisation", "@sage/xtrem-master-data/enums__base_status__postingInProgress": "Comptabilisation en cours", "@sage/xtrem-master-data/enums__base_status__quote": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__readyToProcess": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__readyToShip": "<PERSON>r<PERSON><PERSON> à expédier", "@sage/xtrem-master-data/enums__base_status__received": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__rejected": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__returned": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__shipped": "Expédié", "@sage/xtrem-master-data/enums__base_status__stockError": "Erreur de stock", "@sage/xtrem-master-data/enums__base_status__taxCalculationFailed": "Échec du calcul de taxe", "@sage/xtrem-master-data/enums__base_status__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__varianceApproved": "<PERSON><PERSON><PERSON> approuv<PERSON>", "@sage/xtrem-master-data/enums__business_entity_type__all": "<PERSON>ut", "@sage/xtrem-master-data/enums__business_entity_type__customer": "Client", "@sage/xtrem-master-data/enums__business_entity_type__supplier": "Fournisseur", "@sage/xtrem-master-data/enums__business_relation_type__customer": "Client", "@sage/xtrem-master-data/enums__business_relation_type__supplier": "Fournisseur", "@sage/xtrem-master-data/enums__consumption_mode__none": "Aucun", "@sage/xtrem-master-data/enums__consumption_mode__quantity": "Quantité", "@sage/xtrem-master-data/enums__consumption_mode__time": "Temps", "@sage/xtrem-master-data/enums__contact_role__commercialContact": "Contact commercial", "@sage/xtrem-master-data/enums__contact_role__financialContact": "Contact financier", "@sage/xtrem-master-data/enums__contact_role__mainContact": "Contact principal", "@sage/xtrem-master-data/enums__container_type__barrel": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__container_type__bigBag": "Grand sac", "@sage/xtrem-master-data/enums__container_type__box": "Boite", "@sage/xtrem-master-data/enums__container_type__container": "Contenant", "@sage/xtrem-master-data/enums__container_type__other": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__container_type__pack": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__container_type__pallet": "Palette", "@sage/xtrem-master-data/enums__cost_calculation_method__compound": "Cascade", "@sage/xtrem-master-data/enums__cost_calculation_method__cumulate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__budgeted": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__other": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__simulated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__standard": "Standard", "@sage/xtrem-master-data/enums__cost_valuation_method__averageCost": "Prix moyen pondé<PERSON>", "@sage/xtrem-master-data/enums__cost_valuation_method__fifoCost": "Prix FIFO", "@sage/xtrem-master-data/enums__cost_valuation_method__standardCost": "Coût standard", "@sage/xtrem-master-data/enums__customer_display_status__active": "Actif", "@sage/xtrem-master-data/enums__customer_display_status__inactive": "Inactif", "@sage/xtrem-master-data/enums__customer_display_status__onHold": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__customer_on_hold_type__blocking": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__customer_on_hold_type__none": "Aucun", "@sage/xtrem-master-data/enums__customer_on_hold_type__warning": "Avertissement", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPrice": "Prix brut", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPriceAndCompound": "Prix brut et cascade", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byLine": "Par ligne", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byUnit": "Par unité", "@sage/xtrem-master-data/enums__discount_charge_sign__decrease": "Diminution", "@sage/xtrem-master-data/enums__discount_charge_sign__increase": "Augmentation", "@sage/xtrem-master-data/enums__discount_charge_value_type__amount": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__discount_charge_value_type__percentage": "Pourcentage", "@sage/xtrem-master-data/enums__discount_or_penalty_type__amount": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__discount_or_penalty_type__percentage": "Pourcentage", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDate": "Après la date de facture", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDateAndExtendedToEndOfMonth": "Après date de facture et prolongation jusqu'à la fin du mois", "@sage/xtrem-master-data/enums__due_date_type__afterTheEndOfTheMonthOfInvoiceDate": "Après la fin du mois de la date de facture", "@sage/xtrem-master-data/enums__email_action_type__approved": "Approu<PERSON><PERSON>", "@sage/xtrem-master-data/enums__email_action_type__rejected": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_category_type__allergen": "Allerg<PERSON>", "@sage/xtrem-master-data/enums__item_category_type__ghsClassification": "Classification SGH", "@sage/xtrem-master-data/enums__item_category_type__none": "Aucun", "@sage/xtrem-master-data/enums__item_flow_type__manufactured": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_flow_type__purchased": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_flow_type__sold": "Vendu", "@sage/xtrem-master-data/enums__item_flow_type__subcontracted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_price_type__discount": "Remise", "@sage/xtrem-master-data/enums__item_price_type__normal": "Normal", "@sage/xtrem-master-data/enums__item_price_type__specialOffer": "Offre spéciale", "@sage/xtrem-master-data/enums__item_status__active": "Actif", "@sage/xtrem-master-data/enums__item_status__inDevelopment": "Élaboration", "@sage/xtrem-master-data/enums__item_status__notRenewed": "Non renouvelé", "@sage/xtrem-master-data/enums__item_status__notUsable": "Non utilisable", "@sage/xtrem-master-data/enums__item_status__obsolete": "Obsolète", "@sage/xtrem-master-data/enums__item_type__good": "Produit", "@sage/xtrem-master-data/enums__item_type__landedCost": "Frais d'approche", "@sage/xtrem-master-data/enums__item_type__service": "Service", "@sage/xtrem-master-data/enums__legal_entity__corporation": "Personne morale", "@sage/xtrem-master-data/enums__legal_entity__physicalPerson": "<PERSON>ne physique", "@sage/xtrem-master-data/enums__location_category__customer": "Client", "@sage/xtrem-master-data/enums__location_category__dock": "Quai", "@sage/xtrem-master-data/enums__location_category__internal": "Interne", "@sage/xtrem-master-data/enums__location_category__subcontract": "Sous-traitance", "@sage/xtrem-master-data/enums__location_category__virtual": "Virtuel", "@sage/xtrem-master-data/enums__location_sequence_type__alphabetical": "Alphabétique", "@sage/xtrem-master-data/enums__location_sequence_type__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__location_sequence_type__numerical": "Numérique", "@sage/xtrem-master-data/enums__lot_management__lotManagement": "Lot", "@sage/xtrem-master-data/enums__lot_management__lotSublotManagement": "Lots et sous-lots", "@sage/xtrem-master-data/enums__lot_management__notManaged": "Aucune", "@sage/xtrem-master-data/enums__order_type__closed": "Soldé", "@sage/xtrem-master-data/enums__order_type__firm": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__order_type__planned": "Planifié", "@sage/xtrem-master-data/enums__order_type__suggested": "Suggestion", "@sage/xtrem-master-data/enums__payment_method__ACH": "ACH", "@sage/xtrem-master-data/enums__payment_method__cash": "Espèces", "@sage/xtrem-master-data/enums__payment_method__creditCard": "Carte de <PERSON>", "@sage/xtrem-master-data/enums__payment_method__EFT": "EFT", "@sage/xtrem-master-data/enums__payment_method__none": "Aucun", "@sage/xtrem-master-data/enums__payment_method__printedCheck": "Chèque imprimé", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__amount": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__percentage": "Pourcentage", "@sage/xtrem-master-data/enums__payment_term_from__invoiceDate": "Date de facture", "@sage/xtrem-master-data/enums__period_type__day": "Jour", "@sage/xtrem-master-data/enums__period_type__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__period_type__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__period_type__year": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__preferred_process__production": "Production", "@sage/xtrem-master-data/enums__preferred_process__purchasing": "Achats", "@sage/xtrem-master-data/enums__replenishment_method__byMRP": "Par CBN", "@sage/xtrem-master-data/enums__replenishment_method__byReorderPoint": "Par point de commande", "@sage/xtrem-master-data/enums__replenishment_method__notManaged": "Non géré", "@sage/xtrem-master-data/enums__resource_group_type__labor": "Main d'oeuvre", "@sage/xtrem-master-data/enums__resource_group_type__machine": "Machine", "@sage/xtrem-master-data/enums__resource_group_type__subcontract": "Sous-traitance", "@sage/xtrem-master-data/enums__resource_group_type__tool": "Outil", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__application": "Application", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__company": "Société", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__site": "Site", "@sage/xtrem-master-data/enums__sequence_number_field_kind__company": "Société", "@sage/xtrem-master-data/enums__sequence_number_field_kind__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_field_kind__day": "Jour", "@sage/xtrem-master-data/enums__sequence_number_field_kind__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_field_kind__sequenceNumber": "Compteur", "@sage/xtrem-master-data/enums__sequence_number_field_kind__site": "Site", "@sage/xtrem-master-data/enums__sequence_number_field_kind__week": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_field_kind__year": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__monthly": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__noReset": "Pas de <PERSON>Z", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__yearly": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_type__alphanumeric": "Alphanumérique", "@sage/xtrem-master-data/enums__sequence_number_type__numeric": "Numérique", "@sage/xtrem-master-data/enums__serial_number_management__managed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__serial_number_management__notManaged": "Aucune", "@sage/xtrem-master-data/enums__serial_number_usage__issueAndReceipt": "Sortie et réception", "@sage/xtrem-master-data/enums__serial_number_usage__issueOnly": "Sortie uniquement", "@sage/xtrem-master-data/enums__stock_management_mode__byOrder": "Par ordre", "@sage/xtrem-master-data/enums__stock_management_mode__byProject": "Par affaire", "@sage/xtrem-master-data/enums__stock_management_mode__onStock": "Sur stock", "@sage/xtrem-master-data/enums__supplier_type__chemical": "Industrie chimique", "@sage/xtrem-master-data/enums__supplier_type__foodAndBeverage": "Produits alimentaires et boissons", "@sage/xtrem-master-data/enums__supplier_type__other": "Autres", "@sage/xtrem-master-data/enums__tax_calculation_status__done": "Effectuée", "@sage/xtrem-master-data/enums__tax_calculation_status__failed": "Échec", "@sage/xtrem-master-data/enums__tax_calculation_status__inProgress": "En cours", "@sage/xtrem-master-data/enums__tax_calculation_status__notDone": "Non effectuée", "@sage/xtrem-master-data/enums__title__dr": "Dr", "@sage/xtrem-master-data/enums__title__family": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__title__master": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__title__miss": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__title__mr": "<PERSON>.", "@sage/xtrem-master-data/enums__title__mrs": "Mme", "@sage/xtrem-master-data/enums__title__ms": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__title__prof": "Pr", "@sage/xtrem-master-data/enums__unit_conversion_type__other": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_conversion_type__purchase": "Achats", "@sage/xtrem-master-data/enums__unit_conversion_type__sales": "Vente", "@sage/xtrem-master-data/enums__unit_type__area": "Surface", "@sage/xtrem-master-data/enums__unit_type__each": "Numération", "@sage/xtrem-master-data/enums__unit_type__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_type__temperature": "Température", "@sage/xtrem-master-data/enums__unit_type__time": "Temps", "@sage/xtrem-master-data/enums__unit_type__volume": "Volume", "@sage/xtrem-master-data/enums__unit_type__weight": "Poids", "@sage/xtrem-master-data/enums__week_days__friday": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__monday": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__saturday": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__sunday": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__thursday": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__tuesday": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__wednesday": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__work_in_progress_document_type__materialNeed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseOrder": "<PERSON><PERSON> d'a<PERSON>t", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReceipt": "<PERSON><PERSON><PERSON> d'<PERSON>", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReturn": "Retour d'achat", "@sage/xtrem-master-data/enums__work_in_progress_document_type__salesOrder": "Commande de vente", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferOrder": "Ordre de transfert de stock", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferReceipt": "Réception de transfert de stock", "@sage/xtrem-master-data/enums__work_in_progress_document_type__workOrder": "Ordre de fabrication", "@sage/xtrem-master-data/enums__zone_type__chemical": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__frozen": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__hazard": "Danger", "@sage/xtrem-master-data/enums__zone_type__magnetic": "Magnétique", "@sage/xtrem-master-data/enums__zone_type__restricted": "Réglementé", "@sage/xtrem-master-data/enums__zone_type__secured": "Sécurisé", "@sage/xtrem-master-data/enums__zone_type__sensitive": "Sensible", "@sage/xtrem-master-data/enums__zone_type__virtual": "Virtuel", "@sage/xtrem-master-data/events__control__document_external_note_must_be_empty": "La note externe doit être vide si la propriété 'isExternalNote' est False.", "@sage/xtrem-master-data/events/control__address-control__postalcode-validation-error": "Code postal invalide", "@sage/xtrem-master-data/events/control__address-control__postcode-validation-error": "Code postal invalide", "@sage/xtrem-master-data/events/control__address-control__telephone-validation-error": "Numéro de téléphone invalide", "@sage/xtrem-master-data/events/control__address-control__zipcode-validation-error": "Code postal invalide", "@sage/xtrem-master-data/events/control__base_sequence_number_control_length": "La longueur du compteur du composant doit être identique à l'une de ces valeurs : {{lengths}}.", "@sage/xtrem-master-data/events/control__base_sequence_number_control_type": "Le type de composant n'est pas autorisé : {{type}}.", "@sage/xtrem-master-data/events/control__business-entity__customer_already_exists_with_same_name": "Il existe déjà un client avec le même nom.", "@sage/xtrem-master-data/events/control__business-entity__site_already_exists_with_same_name": "Un site existe déjà avec le même nom.", "@sage/xtrem-master-data/events/control__business-entity__supplier_already_exists_with_same_name": "Un fournisseur existe déjà avec le même nom.", "@sage/xtrem-master-data/events/control__cost-category__the_standard_cost_category_must_be_mandatory": "Renseignez la catégorie de coût standard.", "@sage/xtrem-master-data/events/control__item__allergens-not-allowed": "Les allergènes sont uniquement autorisés pour les articles alimentaires.", "@sage/xtrem-master-data/events/control__item__cannot-be-bom-revision-managed": "Un article géré par révision de nomenclature doit avoir été fabriqué.", "@sage/xtrem-master-data/events/control__item__cannot-be-phantom": "Un article fantôme peut être fabriqué ou géré en stock mais il ne peut être ni acheté ni vendu.", "@sage/xtrem-master-data/events/control__item__code-must-be-a-number": "Le code {{gtinCode}} doit contenir 13 chiffres.", "@sage/xtrem-master-data/events/control__item__empty-preferredProcess": "Un article géré en stock doit être fabriqué, acheté ou les deux.", "@sage/xtrem-master-data/events/control__item__ghs-classification-not-allowed": "La classification SGH est uniquement autorisée pour les articles chimiques.", "@sage/xtrem-master-data/events/control__item__incorrect-value-for-economic-quantity": "Renseigner un lot économique étant un multiple de la quantité batch.", "@sage/xtrem-master-data/events/control__item__must-be-service": "L'article doit être de type 'Service'.", "@sage/xtrem-master-data/events/control__item__must-be-service-or-landed-cost": "L'article doit être de type service ou frais d'approche.", "@sage/xtrem-master-data/events/control__item__must-not-be-service": "L'article ne peut pas être de type 'Service'.", "@sage/xtrem-master-data/events/control__item__must-not-be-service-or-landed-cost": "L'article ne peut pas être de type service ou frais d'approche.", "@sage/xtrem-master-data/events/control__item__property-incorrect-for-not-stock-managed-items": "La propriété {{property}} est incorrecte pour des articles non gérés en stock.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-and-landed-cost-items": "Le champ {{property}} n'est pas disponible pour les article de type service ou frais d'approche.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-items": "La propriété {{property}} n'est pas gérée pour les articles de service.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect": "Une valeur de processus préféré doit être sélectionnée pour un article acheté ou fabriqué.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-manufacturing-items": "Le processus préféré 'Production' est incorrect pour les articles gérés en stock qui ne sont pas fabriqués.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-purchasing-items": "Le processus préféré 'Achat' est incorrect pour les articles gérés en stock qui ne sont pas fabriqués.", "@sage/xtrem-master-data/events/control__location_sequence_control__range_item_length": "La longueur du compteur du composant doit être identique à la la longueur du composant.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_capital_letters": "Un composant de compteur alphabétique peut uniquement contenir des lettres majuscules.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_range": "La valeur de départ ne peut pas être supérieure à la valeur de fin.", "@sage/xtrem-master-data/events/control__location_sequence_control_length": "Le compteur et le composant doivent avoir la même longueur.", "@sage/xtrem-master-data/events/control__location_sequence_control_number_value": "Un composant de compteur numérique peut uniquement contenir un numéro.", "@sage/xtrem-master-data/events/control__location_sequence_control_numeric_range": "La valeur de départ ne peut pas être supérieure à la valeur de fin.", "@sage/xtrem-master-data/events/control__sequence-number__force-reset-with-tenant": "Ce compteur est réinitialisé lorsque vous réinitialisez le tenant.", "@sage/xtrem-master-data/events/control__time-control__end-date-cannot-be-empty": "<PERSON><PERSON> <PERSON><PERSON> renseigner la date de fin.", "@sage/xtrem-master-data/events/control__time-control__end-datetime-cannot-be-empty": "<PERSON><PERSON> de<PERSON> renseigner l'heure de fin.", "@sage/xtrem-master-data/events/control__time-control__invalid-date-range": "La borne de date de {{start}} à {{end}} est incorrecte.", "@sage/xtrem-master-data/events/control__time-control__invalid-datetime-range": "La borne de date et d'heure de {{start}} à {{end}} est incorrecte.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-empty": "<PERSON><PERSON> <PERSON><PERSON> renseigner la date de début.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-greater-than-end-date": "La date de début doit être antérieure à la date de fin.", "@sage/xtrem-master-data/events/control__time-control__start-datetime-cannot-be-empty": "<PERSON><PERSON> de<PERSON> renseigner l'heure de début.", "@sage/xtrem-master-data/events/control__time-control__time-cannot-be-empty": "<PERSON><PERSON> de<PERSON> renseigner l'heure.", "@sage/xtrem-master-data/events/control__time-control__time-format-HH-MM": "L'heure {{timeToValidate}} doit respecter le format HH:MM.", "@sage/xtrem-master-data/found-matching-business-entities": "Entités commerciales correspondantes trouvées", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description": "Documents imprimés : {{numberOfDocuments}}.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description_error": "Un ou plusieurs documents n'ont pas été imprimés. Veuillez consulter l'historique des tâches batch pour plus de détails.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_fail": "Échec de l'impression de {{reportName}}", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_success": "Impression de {{reportName}} effectuée", "@sage/xtrem-master-data/function__customer_price_reason_priority_already_exists": "Il existe déjà un motif de prix client pour cette priorité.", "@sage/xtrem-master-data/functions__business_entity__incorrect_format_siret": "Le format est incorrect. Utilisez le format du numéro SIRET : {{format}}.", "@sage/xtrem-master-data/functions__business_entity__not-a-valid-tax-id": "Le format est incorrect. Utilisez le format du n° de TVA : {{format}}.", "@sage/xtrem-master-data/functions__common__download_file": "Télécharger fichier", "@sage/xtrem-master-data/functions__common__history": "Historique", "@sage/xtrem-master-data/functions__common__invalid_characters": "La valeur de compteur contient des caractères invalides.", "@sage/xtrem-master-data/functions__common__sequence_number_id_is_in_use": "Cette valeur de compteur est déjà utilisée. Utilisez une autre valeur.", "@sage/xtrem-master-data/functions__common__sequence_number_id_must_have_two_characters": "La valeur de compteur doit contenir deux caractères.", "@sage/xtrem-master-data/functions__exchange-rate__no-rate-found-for-this-currency-pair": "Aucun cours trouvé pour cette combinaison de devises.", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-enum-value": "Valeur incorrecte : {{enumValue}}", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-length": "Longueur incorrecte : {{sequenceNumberId}}", "@sage/xtrem-master-data/functions__sequence-number-lib__no_company_sequence_number_value_defined": "Renseignez le compteur pour la société {{companyId}}.", "@sage/xtrem-master-data/functions__sequence-number-lib__no_site_sequence_number_value_defined": "Renseignez le compteur pour le site {{siteId}}.", "@sage/xtrem-master-data/functions__sequence-number-lib__no-component-of-type": "{{sequenceNumberId}} : aucun composant du type {{componentType}}", "@sage/xtrem-master-data/functions__sequence-number-lib__sequence-number-exceeded": "Compteur {{sequenceNumberId}} dépassé", "@sage/xtrem-master-data/functions__unit-of-measure-lib__different-unit-type": "Les unités {{fromUnit}} et {{toUnit}} ne sont pas du même type.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__no-factors-for-the-units": "Aucun coefficient de conversion n'a été défini entre les unités {{fromUnit}} et {{toUnit}}.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__unit-missing": "<PERSON><PERSON> devez renseigner ou sélectionner une unité de stock avant de renseigner ou sélectionner une unité d'achat.", "@sage/xtrem-master-data/generate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/info": "Infos", "@sage/xtrem-master-data/invalid-period": "<PERSON><PERSON><PERSON><PERSON> incorrecte : {{dates}}", "@sage/xtrem-master-data/invalid-quantity-range": "Borne de quantités incorrecte : {{qtyRange}}", "@sage/xtrem-master-data/item__price-cannot-be-negative": "Le prix ne peut pas être négatif.", "@sage/xtrem-master-data/item-not-sold": "Affecter le statut 'Vendu' à l'article {{itemName}}.", "@sage/xtrem-master-data/item-site-supplier-record-created": "Fournisseur article-site créé", "@sage/xtrem-master-data/item-site-supplier-updated.": "Fournisseur article-site mis à jour", "@sage/xtrem-master-data/item-site-updated.": "Article-site mis à jour", "@sage/xtrem-master-data/location_sequence_component_mandatory_constant": "Renseignez une constante.", "@sage/xtrem-master-data/mailer_no_mailer_redirect_url_provided": "L'URL de redirection du mailer n'est pas renseignée dans le fichier de configuration.", "@sage/xtrem-master-data/menu_item__declarations": "Déclarations", "@sage/xtrem-master-data/menu_item__dev-tools": "Outils de développement", "@sage/xtrem-master-data/menu_item__employee": "Collaborateur", "@sage/xtrem-master-data/menu_item__features": "Paramétrages fonctionnels", "@sage/xtrem-master-data/menu_item__features-inventory": "Stock", "@sage/xtrem-master-data/menu_item__features-items": "Articles", "@sage/xtrem-master-data/menu_item__features-manufacturing": "", "@sage/xtrem-master-data/menu_item__features-purchasing": "Achats", "@sage/xtrem-master-data/menu_item__features-resources": "Ressources", "@sage/xtrem-master-data/menu_item__features-sales": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__features-stock": "Stock", "@sage/xtrem-master-data/menu_item__finance": "Finance", "@sage/xtrem-master-data/menu_item__inventory": "Stock", "@sage/xtrem-master-data/menu_item__inventory-data": "Données de stock", "@sage/xtrem-master-data/menu_item__item-data": "Données articles", "@sage/xtrem-master-data/menu_item__items": "Articles", "@sage/xtrem-master-data/menu_item__licence-plate-data": "Données contenants", "@sage/xtrem-master-data/menu_item__location-data": "Données emplacements", "@sage/xtrem-master-data/menu_item__manufacturing": "Production", "@sage/xtrem-master-data/menu_item__purchasing": "Achats", "@sage/xtrem-master-data/menu_item__resources": "Ressources", "@sage/xtrem-master-data/menu_item__resources-data": "<PERSON><PERSON><PERSON> ressources", "@sage/xtrem-master-data/menu_item__sales": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__stock": "Stock", "@sage/xtrem-master-data/menu_item__stock-data": "Données de stock", "@sage/xtrem-master-data/multiple-existing-business-entities": "Plusieurs entités commerciales correspondent. Sélectionnez-en une dans le champ 'Entité commerciale'.", "@sage/xtrem-master-data/node__base_document__no_validation_email_allowed": "Aucune validation par e-mail n'est autorisée {{document}}", "@sage/xtrem-master-data/node_base_resource_location_site_mismatch": "L'emplacement et votre ressource doivent partager le même site.", "@sage/xtrem-master-data/node-extensions__company_extension__property__addresses": "Adresses", "@sage/xtrem-master-data/node-extensions__company_extension__property__contacts": "Contacts", "@sage/xtrem-master-data/node-extensions__company_extension__property__country": "Pays", "@sage/xtrem-master-data/node-extensions__company_extension__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__company_extension__property__customerOnHoldCheck": "Contrôle de client bloqué", "@sage/xtrem-master-data/node-extensions__company_extension__property__isSequenceNumberIdUsed": "Code compteur utilisé", "@sage/xtrem-master-data/node-extensions__company_extension__property__priceScale": "Échelle de prix", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryAddress": "Adresse principale", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryContact": "Contact principal", "@sage/xtrem-master-data/node-extensions__company_extension__property__sequenceNumberId": "Code compteur", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser": "Accessible pour l'utilisateur actuel", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__failed": "Échec d'accessibilité pour l'utilisateur actuel.", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__nodeName": "Nom du node", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__options": "Options", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__propertyOrOperation": "Propriété ou opération", "@sage/xtrem-master-data/node-extensions__country_extension__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__businessEntity": "Entité commerciale", "@sage/xtrem-master-data/node-extensions__site_extension__property__country": "Pays", "@sage/xtrem-master-data/node-extensions__site_extension__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__defaultLocation": "Emplacement par défaut", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialCurrency": "<PERSON><PERSON> financière", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialSite": "Site financier", "@sage/xtrem-master-data/node-extensions__site_extension__property__isFinance": "Finance", "@sage/xtrem-master-data/node-extensions__site_extension__property__isInventory": "Stock", "@sage/xtrem-master-data/node-extensions__site_extension__property__isLocationManaged": "G<PERSON>ré par emplacement", "@sage/xtrem-master-data/node-extensions__site_extension__property__isManufacturing": "Production", "@sage/xtrem-master-data/node-extensions__site_extension__property__isProjectManagement": "Gestion à l'affaire", "@sage/xtrem-master-data/node-extensions__site_extension__property__isPurchase": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSales": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSequenceNumberIdUsed": "Code compteur utilisé", "@sage/xtrem-master-data/node-extensions__site_extension__property__itemSites": "Articles-site", "@sage/xtrem-master-data/node-extensions__site_extension__property__primaryAddress": "Adresse principale", "@sage/xtrem-master-data/node-extensions__site_extension__property__sequenceNumberId": "Code compteur", "@sage/xtrem-master-data/node-extensions__site_extension__property__siret": "SIRET", "@sage/xtrem-master-data/node-extensions__site_extension__property__stockSite": "Site de stock", "@sage/xtrem-master-data/node-extensions__site_extension__property__taxIdNumber": "N° de TVA", "@sage/xtrem-master-data/node-extensions__site_extension__property__timeZone": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__query__timezones": "Fuseaux horaires", "@sage/xtrem-master-data/node-extensions__site_extension__query__timezones__failed": "Échec des fuseaux horaires.", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__address__node_name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address__property__addressLine1": "Adresse ligne 1", "@sage/xtrem-master-data/nodes__address__property__addressLine2": "Adresse ligne 2", "@sage/xtrem-master-data/nodes__address__property__city": "Ville", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddress": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddressWithoutName": "Adresse concaténée sans nom", "@sage/xtrem-master-data/nodes__address__property__country": "Pays", "@sage/xtrem-master-data/nodes__address__property__locationPhoneNumber": "N° tél. emplacement", "@sage/xtrem-master-data/nodes__address__property__name": "Nom", "@sage/xtrem-master-data/nodes__address__property__postcode": "Code postal", "@sage/xtrem-master-data/nodes__address__property__region": "Région", "@sage/xtrem-master-data/nodes__address_base__node_name": "Base d'adresses", "@sage/xtrem-master-data/nodes__address_base__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address_base__property__addressLine1": "Adresse ligne 1", "@sage/xtrem-master-data/nodes__address_base__property__addressLine2": "Adresse ligne 2", "@sage/xtrem-master-data/nodes__address_base__property__city": "Ville", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddress": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddressWithoutName": "Adresse concaténée sans nom", "@sage/xtrem-master-data/nodes__address_base__property__country": "Pays", "@sage/xtrem-master-data/nodes__address_base__property__isActive": "Active", "@sage/xtrem-master-data/nodes__address_base__property__locationPhoneNumber": "N° tél. emplacement", "@sage/xtrem-master-data/nodes__address_base__property__name": "Nom", "@sage/xtrem-master-data/nodes__address_base__property__postcode": "Code postal", "@sage/xtrem-master-data/nodes__address_base__property__region": "Région", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__allergen__node_name": "Allerg<PERSON>", "@sage/xtrem-master-data/nodes__allergen__property__id": "Code", "@sage/xtrem-master-data/nodes__allergen__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__allergen__property__name": "Nom", "@sage/xtrem-master-data/nodes__allergen__property__pictogram": "Pictogram<PERSON>", "@sage/xtrem-master-data/nodes__base_business_relation__node_name": "Relation commerciale de base", "@sage/xtrem-master-data/nodes__base_business_relation__property__businessEntity": "Entité commerciale", "@sage/xtrem-master-data/nodes__base_business_relation__property__category": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_business_relation__property__country": "Pays", "@sage/xtrem-master-data/nodes__base_business_relation__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_business_relation__property__id": "Code", "@sage/xtrem-master-data/nodes__base_business_relation__property__image": "Image", "@sage/xtrem-master-data/nodes__base_business_relation__property__internalNote": "Note interne", "@sage/xtrem-master-data/nodes__base_business_relation__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__base_business_relation__property__legalEntity": "Entité juridique", "@sage/xtrem-master-data/nodes__base_business_relation__property__minimumOrderAmount": "Montant minimum commande", "@sage/xtrem-master-data/nodes__base_business_relation__property__name": "Nom", "@sage/xtrem-master-data/nodes__base_business_relation__property__paymentTerm": "Condition de paiement", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryAddress": "Adresse principale", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryContact": "Contact principal", "@sage/xtrem-master-data/nodes__base_business_relation__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__base_business_relation__property__taxIdNumber": "N° de TVA", "@sage/xtrem-master-data/nodes__base_capability__node_name": "Aptitude de base", "@sage/xtrem-master-data/nodes__base_capability__property__capabilityLevel": "Niveau d'aptitude", "@sage/xtrem-master-data/nodes__base_capability__property__dateEndValid": "Date de fin de validité", "@sage/xtrem-master-data/nodes__base_capability__property__dateRangeValidity": "Validité de borne de date", "@sage/xtrem-master-data/nodes__base_capability__property__dateStartValid": "Date de début de validité", "@sage/xtrem-master-data/nodes__base_capability__property__id": "Code", "@sage/xtrem-master-data/nodes__base_capability__property__name": "Nom", "@sage/xtrem-master-data/nodes__base_certificate__node_name": "Certificat de base", "@sage/xtrem-master-data/nodes__base_certificate__property__certificationBody": "Organisme de certification", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfCertification": "Date de certification", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfOriginalCertification": "Date du certificat d'origine", "@sage/xtrem-master-data/nodes__base_certificate__property__id": "Code", "@sage/xtrem-master-data/nodes__base_certificate__property__standard": "Standard", "@sage/xtrem-master-data/nodes__base_certificate__property__validUntil": "<PERSON><PERSON> jusqu'à", "@sage/xtrem-master-data/nodes__base_distribution_document__fx_rate_not_found": "Aucun cours de change trouvé.", "@sage/xtrem-master-data/nodes__base_distribution_document__node_name": "Document de distribution de base", "@sage/xtrem-master-data/nodes__base_distribution_document__property__businessRelation": "Relation commerciale", "@sage/xtrem-master-data/nodes__base_distribution_document__property__companyCurrency": "<PERSON><PERSON> socié<PERSON>", "@sage/xtrem-master-data/nodes__base_distribution_document__property__companyFxRate": "Cours de change de la société", "@sage/xtrem-master-data/nodes__base_distribution_document__property__fxRateDate": "Date du cours de change", "@sage/xtrem-master-data/nodes__base_distribution_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_distribution_document_line__node_name": "Ligne de document de distribution de base", "@sage/xtrem-master-data/nodes__base_distribution_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo": "Mettre la ligne à jour en", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__failed": "Échec de mise à jour de la ligne en", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentIds": "Codes documents", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentLineIds": "Codes de lignes de documents", "@sage/xtrem-master-data/nodes__base_document__bulkMutation__bulkResync": "Resync en masse", "@sage/xtrem-master-data/nodes__base_document__bulkMutation__bulkResync__failed": "Échec de la resynchronisation en masse.", "@sage/xtrem-master-data/nodes__base_document__financial_site_is_not_financial": "Le site doit être un site financier.", "@sage/xtrem-master-data/nodes__base_document__header_currency_not_updatable": "La devise de cet enregistrement ne peut pas être modifiée.", "@sage/xtrem-master-data/nodes__base_document__id_already_exists": "Le code existe déjà. Aucun compteur ne sera alloué au document courant.", "@sage/xtrem-master-data/nodes__base_document__lines_mandatory": "Le document doit contenir au moins une ligne.", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail": "Envoyer e-mail de demande d'approbation", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__failed": "Échec d'envoi d'e-mail de demande d'approbation.", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__document": "Document", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__user": "Utilisa<PERSON>ur", "@sage/xtrem-master-data/nodes__base_document__no_financial_site": "Site financier introuvable pour le site courant", "@sage/xtrem-master-data/nodes__base_document__node_name": "Document de base", "@sage/xtrem-master-data/nodes__base_document__order_date_not_updatable": "La date de commande ne peut pas être modifiée lorsque le statut de l'enregistrement est En cours.", "@sage/xtrem-master-data/nodes__base_document__property__approvalPage": "Page d'approbation", "@sage/xtrem-master-data/nodes__base_document__property__approvalStatus": "Statut approbation", "@sage/xtrem-master-data/nodes__base_document__property__approvalUrl": "URL d'approbation", "@sage/xtrem-master-data/nodes__base_document__property__businessEntityAddress": "Adresse de l'entité commerciale", "@sage/xtrem-master-data/nodes__base_document__property__canPrint": "Imprimable", "@sage/xtrem-master-data/nodes__base_document__property__canUpdateClosedDocument": "Possible de mettre à jour un document soldé", "@sage/xtrem-master-data/nodes__base_document__property__companyCurrency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__date": "Date", "@sage/xtrem-master-data/nodes__base_document__property__displayStatus": "Statut d'affichage", "@sage/xtrem-master-data/nodes__base_document__property__documentDate": "Date de document", "@sage/xtrem-master-data/nodes__base_document__property__documentUrl": "URL du document", "@sage/xtrem-master-data/nodes__base_document__property__externalNote": "Note externe", "@sage/xtrem-master-data/nodes__base_document__property__financialSite": "Site financier", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForResync": "Forcer la mise à jour pour la resync", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-master-data/nodes__base_document__property__internalNote": "Note interne", "@sage/xtrem-master-data/nodes__base_document__property__isExternalNote": "Note externe", "@sage/xtrem-master-data/nodes__base_document__property__isOverwriteNote": "Ecrasement note", "@sage/xtrem-master-data/nodes__base_document__property__isPrinted": "Impression", "@sage/xtrem-master-data/nodes__base_document__property__isSent": "Envoi", "@sage/xtrem-master-data/nodes__base_document__property__isTransferHeaderNote": "Transférer note d'en-tête", "@sage/xtrem-master-data/nodes__base_document__property__isTransferLineNote": "Transférer note de ligne", "@sage/xtrem-master-data/nodes__base_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__page": "Page", "@sage/xtrem-master-data/nodes__base_document__property__postingDate": "Date de comptabilisation", "@sage/xtrem-master-data/nodes__base_document__property__site": "Site", "@sage/xtrem-master-data/nodes__base_document__property__siteAddress": "Adresse du site", "@sage/xtrem-master-data/nodes__base_document__property__status": "Statut", "@sage/xtrem-master-data/nodes__base_document__property__stockSite": "Site de stock", "@sage/xtrem-master-data/nodes__base_document__property__text": "Texte", "@sage/xtrem-master-data/nodes__base_document__property__transactionCurrency": "Devise de transaction", "@sage/xtrem-master-data/nodes__base_document__site_is_not_inventory": "Le site doit être un site de stock.", "@sage/xtrem-master-data/nodes__base_document__stock_site_is_not_inventory": "Le site doit être un site de stock.", "@sage/xtrem-master-data/nodes__base_document__stock_site_legal_company_mismatch": "Le site doit appartenir à la même société légale.", "@sage/xtrem-master-data/nodes__base_document__update_not_allowed_status_closed": "L'enregistrement du document ne peut pas être mis à jour lorsque son statut est Clos : {{number}}.", "@sage/xtrem-master-data/nodes__base_document_item_line__node_name": "Ligne d'article de document de base", "@sage/xtrem-master-data/nodes__base_document_item_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentId": "Code de document", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentNumber": "Numéro de document", "@sage/xtrem-master-data/nodes__base_document_item_line__property__externalNote": "Note externe", "@sage/xtrem-master-data/nodes__base_document_item_line__property__forceUpdateForStock": "Forcer la mise à jour pour le stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__internalNote": "Note interne", "@sage/xtrem-master-data/nodes__base_document_item_line__property__isExternalNote": "Note externe", "@sage/xtrem-master-data/nodes__base_document_item_line__property__item": "Article", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemDescription": "Description de l'article", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemSite": "Article-site", "@sage/xtrem-master-data/nodes__base_document_item_line__property__origin": "Origine", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantity": "Quantité", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__site": "Site", "@sage/xtrem-master-data/nodes__base_document_item_line__property__siteLinkedAddress": "Adresse liée du site", "@sage/xtrem-master-data/nodes__base_document_item_line__property__status": "Statut", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSite": "Site de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSiteLinkedAddress": "Adresse liée site de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockUnit": "Unité de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unit": "Unité", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unitToStockUnitConversionFactor": "Coefficient de conversion unité en unité de stock", "@sage/xtrem-master-data/nodes__base_document_line__node_name": "Ligne de documents de base", "@sage/xtrem-master-data/nodes__base_document_line__property__documentId": "Code document", "@sage/xtrem-master-data/nodes__base_document_line__property__documentNumber": "Numéro de document", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__node_name": "Consultation de liigne de document de base", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__commodityCode": "Code marchandise", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__company": "Société", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__date": "Date", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__fromItem": "Article début", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__itemCategory": "Catégorie d'article", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__site": "Site", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__toItem": "Article fin", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__user": "Utilisa<PERSON>ur", "@sage/xtrem-master-data/nodes__base_inbound_document__node_name": "Document de base entrant", "@sage/xtrem-master-data/nodes__base_inbound_document__property__businessRelation": "Relation commerciale", "@sage/xtrem-master-data/nodes__base_inbound_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_inbound_document_line__node_name": "Ligne de document de base entrant", "@sage/xtrem-master-data/nodes__base_inbound_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document__node_name": "Document de réception de base entrant", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document_line__node_name": "Ligne de document de réception de base entrant", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_line_discount_charge__improper_calculation_rule": "Règle de calcul incorrecte. Pour les pourcentages, utilisez la règle de calcul 'Par unité'.", "@sage/xtrem-master-data/nodes__base_line_discount_charge__node_name": "Remise ou frais de la ligne de base", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__amount": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basis": "Base", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basisDeterminated": "Base déterminée", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationBasis": "Base du calcul", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationRule": "<PERSON><PERSON><PERSON> de calcul", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__sign": "<PERSON>s", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__value": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueDeterminated": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueType": "Type de valeur", "@sage/xtrem-master-data/nodes__base_line_to_line__node_name": "Ligne de base vers ligne", "@sage/xtrem-master-data/nodes__base_line_to_line__property__amount": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_to_line__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_to_line__property__from": "De", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantity": "Quantité", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantityInStockUnit": "Quantité en unité de stock", "@sage/xtrem-master-data/nodes__base_line_to_line__property__stockUnit": "Unité de stock", "@sage/xtrem-master-data/nodes__base_line_to_line__property__to": "À", "@sage/xtrem-master-data/nodes__base_line_to_line__property__unit": "Unité", "@sage/xtrem-master-data/nodes__base_outbound_document__node_name": "Document de base sortant", "@sage/xtrem-master-data/nodes__base_outbound_document__property__businessRelation": "Relation commerciale", "@sage/xtrem-master-data/nodes__base_outbound_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_outbound_document_line__node_name": "Ligne de document de base sortant", "@sage/xtrem-master-data/nodes__base_outbound_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_outbound_order_document__node_name": "Document de commande de base entrant", "@sage/xtrem-master-data/nodes__base_outbound_order_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_outbound_order_document_line__node_name": "Ligne de document de commande de base entrant", "@sage/xtrem-master-data/nodes__base_outbound_order_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document__node_name": "Document d'expédition de base entrant", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document_line__node_name": "Ligne de document d'expédition de base entrant", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_resource__node_name": "Ressource de base", "@sage/xtrem-master-data/nodes__base_resource__property__activeFrom": "Début activité", "@sage/xtrem-master-data/nodes__base_resource__property__activeRange": "Borne active", "@sage/xtrem-master-data/nodes__base_resource__property__activeTo": "Fin activité", "@sage/xtrem-master-data/nodes__base_resource__property__description": "Description", "@sage/xtrem-master-data/nodes__base_resource__property__efficiency": "Efficience", "@sage/xtrem-master-data/nodes__base_resource__property__id": "Code", "@sage/xtrem-master-data/nodes__base_resource__property__isActive": "Active", "@sage/xtrem-master-data/nodes__base_resource__property__location": "Emplacement", "@sage/xtrem-master-data/nodes__base_resource__property__name": "Nom", "@sage/xtrem-master-data/nodes__base_resource__property__resourceCostCategories": "Catégories de coûts ressources", "@sage/xtrem-master-data/nodes__base_resource__property__resourceImage": "Image de la ressource", "@sage/xtrem-master-data/nodes__base_resource__property__site": "Site", "@sage/xtrem-master-data/nodes__base_resource__property__weeklyShift": "Horaires he<PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number__node_name": "Compteur de base", "@sage/xtrem-master-data/nodes__base_sequence_number__property__componentLength": "<PERSON><PERSON><PERSON> composant", "@sage/xtrem-master-data/nodes__base_sequence_number__property__components": "Composants", "@sage/xtrem-master-data/nodes__base_sequence_number__property__definitionLevel": "Niveau de définition", "@sage/xtrem-master-data/nodes__base_sequence_number__property__id": "Code", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isChronological": "Est chronologique", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isClearedByReset": "Effacé par réinitialisation", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isUsed": "En utilisation", "@sage/xtrem-master-data/nodes__base_sequence_number__property__legislation": "Législation", "@sage/xtrem-master-data/nodes__base_sequence_number__property__minimumLength": "Longueur minimum", "@sage/xtrem-master-data/nodes__base_sequence_number__property__name": "Nom", "@sage/xtrem-master-data/nodes__base_sequence_number__property__rtzLevel": "Niveau RAZ", "@sage/xtrem-master-data/nodes__base_sequence_number__property__sequenceNumberAssignments": "Attributions de compteurs", "@sage/xtrem-master-data/nodes__base_sequence_number__property__type": "Type", "@sage/xtrem-master-data/nodes__base_sequence_number__query__getDocumentNodeNames": "Obtenir noms node document", "@sage/xtrem-master-data/nodes__base_sequence_number__query__getDocumentNodeNames__failed": "Échec d'obtention des noms de node de document.", "@sage/xtrem-master-data/nodes__base_sequence_number_component__node_name": "Composant de compteur de base", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__type": "Type", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__bom_revision_sequence__node_name": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__components": "Composants", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isDefault": "<PERSON><PERSON> <PERSON><PERSON>", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isSequenceGenerated": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__node_name": "Composant de compteur de révision de nomenclature", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__business_entity__address_mandatory": "L'entité commerciale doit comporter au moins une adresse.", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__business_entity__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__business_entity__incorrect_format_siret": "Le format est incorrect. Utilisez le format du numéro SIRET : {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__node_name": "Entité commerciale", "@sage/xtrem-master-data/nodes__business_entity__not-a-valid-tax-id": "Le format est incorrect. Utilisez le format du n° de TVA : {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__primary_address": "L'entité commerciale ne doit avoir qu'une seule adresse principale.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_active": "<PERSON><PERSON> de<PERSON> activer l'adresse principale de l'entité commerciale.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_mandatory": "Attribuez au moins une adresse principale à cette entité commerciale.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact": "L'adresse devrait avoir un contact principal.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact_active": "<PERSON><PERSON> devez activer le contact de l'adresse principale.", "@sage/xtrem-master-data/nodes__business_entity__property__addresses": "Adresses", "@sage/xtrem-master-data/nodes__business_entity__property__contacts": "Contacts", "@sage/xtrem-master-data/nodes__business_entity__property__country": "Pays", "@sage/xtrem-master-data/nodes__business_entity__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__customer": "Client", "@sage/xtrem-master-data/nodes__business_entity__property__id": "Code", "@sage/xtrem-master-data/nodes__business_entity__property__image": "Image", "@sage/xtrem-master-data/nodes__business_entity__property__isActive": "Active", "@sage/xtrem-master-data/nodes__business_entity__property__isCustomer": "Client", "@sage/xtrem-master-data/nodes__business_entity__property__isSite": "Site", "@sage/xtrem-master-data/nodes__business_entity__property__isSupplier": "Fournisseur", "@sage/xtrem-master-data/nodes__business_entity__property__legalEntity": "Entité juridique", "@sage/xtrem-master-data/nodes__business_entity__property__name": "Nom", "@sage/xtrem-master-data/nodes__business_entity__property__parent": "Parent", "@sage/xtrem-master-data/nodes__business_entity__property__primaryAddress": "Adresse principale", "@sage/xtrem-master-data/nodes__business_entity__property__primaryContact": "Contact principal", "@sage/xtrem-master-data/nodes__business_entity__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__business_entity__property__site": "Site", "@sage/xtrem-master-data/nodes__business_entity__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__business_entity__property__taxIdNumber": "N° de TVA", "@sage/xtrem-master-data/nodes__business_entity__property__website": "Site web", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__business_entity_address__node_name": "Adresse de l'entité commerciale", "@sage/xtrem-master-data/nodes__business_entity_address__property__businessEntity": "Entité commerciale", "@sage/xtrem-master-data/nodes__business_entity_address__property__concatenatedAddress": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity_address__property__contacts": "Contacts", "@sage/xtrem-master-data/nodes__business_entity_address__property__deliveryDetail": "<PERSON><PERSON><PERSON> liv<PERSON>son", "@sage/xtrem-master-data/nodes__business_entity_address__property__isPrimary": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity_address__property__primaryContact": "Contact principal", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__business_entity_contact__node_name": "Contact entité commerciale", "@sage/xtrem-master-data/nodes__business_entity_contact__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity_contact__property__businessEntity": "Entité commerciale", "@sage/xtrem-master-data/nodes__business_entity_contact__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__business-entity-type-control-customer": "Cette condition de paiement est déjà attribuée à des clients.", "@sage/xtrem-master-data/nodes__business-entity-type-control-supplier": "Cette condition de paiement est déjà attribuée à des fournisseurs.", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__capability_level__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__capability_level__node_name": "Niveau d'aptitude", "@sage/xtrem-master-data/nodes__capability_level__property__description": "Description", "@sage/xtrem-master-data/nodes__capability_level__property__id": "Code", "@sage/xtrem-master-data/nodes__capability_level__property__level": "Niveau", "@sage/xtrem-master-data/nodes__capability_level__property__name": "Nom", "@sage/xtrem-master-data/nodes__company__address_mandatory": "Attribuez au moins une adresse à la société.", "@sage/xtrem-master-data/nodes__company__primary_address": "La société ne doit avoir qu'une seule adresse principale.", "@sage/xtrem-master-data/nodes__company__primary_address_mandatory": "Sélectionnez au moins une adresse principale pour la société.", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__company_address__node_name": "<PERSON><PERSON><PERSON> <PERSON> la société", "@sage/xtrem-master-data/nodes__company_address__property__company": "Société", "@sage/xtrem-master-data/nodes__company_address__property__contacts": "Contacts", "@sage/xtrem-master-data/nodes__company_address__property__isPrimary": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__company_contact__node_name": "Contact société", "@sage/xtrem-master-data/nodes__company_contact__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__company_contact__property__company": "Société", "@sage/xtrem-master-data/nodes__company_contact__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__contact__node_name": "Contact", "@sage/xtrem-master-data/nodes__contact__property__email": "E-mail", "@sage/xtrem-master-data/nodes__contact__property__firstName": "Prénom", "@sage/xtrem-master-data/nodes__contact__property__image": "Image", "@sage/xtrem-master-data/nodes__contact__property__lastName": "Nom de famille", "@sage/xtrem-master-data/nodes__contact__property__locationPhoneNumber": "N° tél. emplacement", "@sage/xtrem-master-data/nodes__contact__property__position": "Profession", "@sage/xtrem-master-data/nodes__contact__property__preferredName": "Nom préféré", "@sage/xtrem-master-data/nodes__contact__property__role": "Fonction", "@sage/xtrem-master-data/nodes__contact__property__title": "Civilité", "@sage/xtrem-master-data/nodes__contact_base__node_name": "Base de contact", "@sage/xtrem-master-data/nodes__contact_base__not-a-valid-email": "E-mail incorrect : {{email}}", "@sage/xtrem-master-data/nodes__contact_base__property__contact": "Contact", "@sage/xtrem-master-data/nodes__contact_base__property__displayName": "Nom d'affichage", "@sage/xtrem-master-data/nodes__contact_base__property__email": "E-mail", "@sage/xtrem-master-data/nodes__contact_base__property__firstName": "Prénom", "@sage/xtrem-master-data/nodes__contact_base__property__image": "Image", "@sage/xtrem-master-data/nodes__contact_base__property__isActive": "Active", "@sage/xtrem-master-data/nodes__contact_base__property__lastName": "Nom de famille", "@sage/xtrem-master-data/nodes__contact_base__property__locationPhoneNumber": "N° tél. emplacement", "@sage/xtrem-master-data/nodes__contact_base__property__position": "Profession", "@sage/xtrem-master-data/nodes__contact_base__property__preferredName": "Prénom usuel", "@sage/xtrem-master-data/nodes__contact_base__property__role": "Fonction", "@sage/xtrem-master-data/nodes__contact_base__property__title": "Civilité", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__container__node_name": "Contenant", "@sage/xtrem-master-data/nodes__container__property__consumedLocationCapacity": "Capacité d'emplacement consommée", "@sage/xtrem-master-data/nodes__container__property__id": "Code", "@sage/xtrem-master-data/nodes__container__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__container__property__isInternal": "Interne", "@sage/xtrem-master-data/nodes__container__property__isSingleItem": "Article unique", "@sage/xtrem-master-data/nodes__container__property__isSingleLot": "Lot unique", "@sage/xtrem-master-data/nodes__container__property__labelFormat": "Format d'étiquette", "@sage/xtrem-master-data/nodes__container__property__name": "Nom", "@sage/xtrem-master-data/nodes__container__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__container__property__storageCapacity": "Capacité de stockage", "@sage/xtrem-master-data/nodes__container__property__type": "Type", "@sage/xtrem-master-data/nodes__container__sequence-number-not-required": "Aucun compteur est demandé pour le contenant externe.", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__cost_category__node_name": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/nodes__cost_category__property__costCategoryType": "Type de catégorie coût", "@sage/xtrem-master-data/nodes__cost_category__property__id": "Code", "@sage/xtrem-master-data/nodes__cost_category__property__isMandatory": "Obligatoire", "@sage/xtrem-master-data/nodes__cost_category__property__name": "Nom", "@sage/xtrem-master-data/nodes__cost-category__can-have-only-one-cost-type-of": "Vous pouvez avoir uniquement un type de coût {{costCategoryType}}.", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__currency__deleting-record": "Suppression de l'enregistrement {{exchangeRateId}} (longueur de {{exchangeRateLength}}) des cours de change.", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate": "Enregistrer le cours de change", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__failed": "Échec d'enregistrement du cours de change.", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__base": "Base", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__dateRate": "Taux date", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__destination": "Destination", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__rate": "Cours", "@sage/xtrem-master-data/nodes__currency__node_name": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__property__currentExchangeRates": "Cours de change actuels", "@sage/xtrem-master-data/nodes__currency__property__decimalDigits": "Décimales", "@sage/xtrem-master-data/nodes__currency__property__exchangeRates": "Cours de change", "@sage/xtrem-master-data/nodes__currency__property__exchangeRatesDestinationInverse": "Renversement de destination des cours de change", "@sage/xtrem-master-data/nodes__currency__property__icon": "Icône", "@sage/xtrem-master-data/nodes__currency__property__id": "Code", "@sage/xtrem-master-data/nodes__currency__property__isActive": "Active", "@sage/xtrem-master-data/nodes__currency__property__lastUpdate": "Dernière mise à jour", "@sage/xtrem-master-data/nodes__currency__property__name": "Nom", "@sage/xtrem-master-data/nodes__currency__property__rounding": "Arrondi", "@sage/xtrem-master-data/nodes__currency__property__symbol": "Symbole", "@sage/xtrem-master-data/nodes__currency_id": "Le code doit contenir 3 caractères.", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__customer__at_least_one_active_delivery_address_mandatory": "Attribuez au moins une adresse de livraison active à ce client.", "@sage/xtrem-master-data/nodes__customer__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__customer__enter_a_shipping_address_for_customer": "Renseigner une adresse d'expédition pour le client.", "@sage/xtrem-master-data/nodes__customer__incorrect_format_siret": "{{siret}} n'est pas un numéro SIRET correct. Le format attendu est: {{format}}", "@sage/xtrem-master-data/nodes__customer__node_name": "Client", "@sage/xtrem-master-data/nodes__customer__not-a-valid-tax-id": "{{taxIdNumber}} n'est pas un numéro de TVA correct. Le format attendu est : {{format}}", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address": "Le client peut avoir uniquement une adresse d'expédition principale.", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address_mandatory": "Attribuez une adresse d'expédition principale active au client.", "@sage/xtrem-master-data/nodes__customer__primary_ship_to_address_mandatory": "Le client doit avoir au moins une adresse d'expédition principale active.", "@sage/xtrem-master-data/nodes__customer__property__billToAddress": "Adresse de facturation", "@sage/xtrem-master-data/nodes__customer__property__billToCustomer": "Client facturé", "@sage/xtrem-master-data/nodes__customer__property__category": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer__property__creditLimit": "<PERSON>ite <PERSON>", "@sage/xtrem-master-data/nodes__customer__property__deliveryAddresses": "Adresse d'expédition", "@sage/xtrem-master-data/nodes__customer__property__displayStatus": "Statut d'affichage", "@sage/xtrem-master-data/nodes__customer__property__isOnHold": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer__property__itemPrices": "Prix articles", "@sage/xtrem-master-data/nodes__customer__property__items": "Articles", "@sage/xtrem-master-data/nodes__customer__property__payByAddress": "<PERSON><PERSON><PERSON> du payeur", "@sage/xtrem-master-data/nodes__customer__property__payByCustomer": "Client payeur", "@sage/xtrem-master-data/nodes__customer__property__paymentTerm": "Condition de paiement", "@sage/xtrem-master-data/nodes__customer__property__primaryShipToAddress": "<PERSON><PERSON><PERSON> de livraison principale", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__customer_price_reason__node_name": "Motif prix client", "@sage/xtrem-master-data/nodes__customer_price_reason__property__description": "Description", "@sage/xtrem-master-data/nodes__customer_price_reason__property__id": "Code", "@sage/xtrem-master-data/nodes__customer_price_reason__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__customer_price_reason__property__name": "Nom", "@sage/xtrem-master-data/nodes__customer_price_reason__property__priority": "Priorité", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__customer_supplier_category__change_not_possible_category_is_used_on_customer": "Vous pouvez uniquement modifier un type de catégorie client s'il n'est pas attribué à un client.", "@sage/xtrem-master-data/nodes__customer_supplier_category__node_name": "Catégorie fournisseur et client", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__id": "Code", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isCustomer": "Client", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSequenceNumberManagement": "Gestion des compteurs", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSupplier": "Fournisseur", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__name": "Nom", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__customer-minimum__order_amount-cannot-be-negative": "Le montant minimum de commande ne peut pas être négatif.", "@sage/xtrem-master-data/nodes__customer-supplier-category__customer_or_supplier": "<PERSON><PERSON> de<PERSON> sélectionner un client ou un fournisseur.", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-cannot-be-set": "Le compteur ne peut pas être défini.", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-is-mandatory": "Le compteur est obligatoire.", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__daily_shift__no_details_on_full_day_shift": "Une journée complète ne peut pas contenir des détails horaires.", "@sage/xtrem-master-data/nodes__daily_shift__no_overlap_on_shift_details": "Les détails horaires ne peuvent pas se chevaucher.", "@sage/xtrem-master-data/nodes__daily_shift__node_name": "Horaires journaliers", "@sage/xtrem-master-data/nodes__daily_shift__property__capacity": "Capacité", "@sage/xtrem-master-data/nodes__daily_shift__property__formattedCapacity": "Capacité formatée", "@sage/xtrem-master-data/nodes__daily_shift__property__id": "Code", "@sage/xtrem-master-data/nodes__daily_shift__property__isFullDay": "<PERSON><PERSON><PERSON> compl<PERSON>", "@sage/xtrem-master-data/nodes__daily_shift__property__name": "Nom", "@sage/xtrem-master-data/nodes__daily_shift__property__setupId": "Code paramétrage", "@sage/xtrem-master-data/nodes__daily_shift__property__shiftDetails": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__daily_shift_detail__node_name": "Détails horaires journaliers", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__dailyShift": "Horaires journaliers", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__shiftDetail": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__delivery_detail__node_name": "<PERSON><PERSON><PERSON> liv<PERSON>son", "@sage/xtrem-master-data/nodes__delivery_detail__property__address": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__delivery_detail__property__incoterm": "Règle d'Incoterms", "@sage/xtrem-master-data/nodes__delivery_detail__property__isActive": "Active", "@sage/xtrem-master-data/nodes__delivery_detail__property__isFridayWorkDay": "Jour ouv<PERSON> vendred", "@sage/xtrem-master-data/nodes__delivery_detail__property__isMondayWorkDay": "Jour ouvré lundi", "@sage/xtrem-master-data/nodes__delivery_detail__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSaturdayWorkDay": "Jour ouv<PERSON>", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSundayWorkDay": "Jour ouvré dimanche", "@sage/xtrem-master-data/nodes__delivery_detail__property__isThursdayWorkDay": "Jour ouvré jeudi", "@sage/xtrem-master-data/nodes__delivery_detail__property__isTuesdayWorkDay": "Jour ouvré mardi", "@sage/xtrem-master-data/nodes__delivery_detail__property__isWednesdayWorkDay": "Jour ouvré mercredi", "@sage/xtrem-master-data/nodes__delivery_detail__property__leadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__delivery_detail__property__mode": "Mode", "@sage/xtrem-master-data/nodes__delivery_detail__property__shipmentSite": "Site expédition", "@sage/xtrem-master-data/nodes__delivery_detail__property__workDaysSelection": "Sélection des jours ouvrés", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__delivery_mode__node_name": "Mode de livraison", "@sage/xtrem-master-data/nodes__delivery_mode__property__description": "Description", "@sage/xtrem-master-data/nodes__delivery_mode__property__id": "Code", "@sage/xtrem-master-data/nodes__delivery_mode__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__delivery_mode__property__name": "Nom", "@sage/xtrem-master-data/nodes__detailed_resource__node_name": "Resso<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__detailed_resource__property__efficiency": "Efficience", "@sage/xtrem-master-data/nodes__detailed_resource__property__location": "Emplacement", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceCostCategories": "Catégories de coûts ressources", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceGroup": "Groupe de ressources", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__dev_tools__node_name": "Outils de développement", "@sage/xtrem-master-data/nodes__dev_tools__property__id": "Code", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__employee__node_name": "Collaborateur", "@sage/xtrem-master-data/nodes__employee__property__firstName": "Prénom", "@sage/xtrem-master-data/nodes__employee__property__id": "Code", "@sage/xtrem-master-data/nodes__employee__property__image": "Image", "@sage/xtrem-master-data/nodes__employee__property__isActive": "Active", "@sage/xtrem-master-data/nodes__employee__property__lastName": "Nom de famille", "@sage/xtrem-master-data/nodes__employee__property__name": "Nom", "@sage/xtrem-master-data/nodes__employee__property__resource": "Ressource", "@sage/xtrem-master-data/nodes__employee__property__site": "Site", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__exchange_rate__node_name": "Cours de change", "@sage/xtrem-master-data/nodes__exchange_rate__property__base": "Base", "@sage/xtrem-master-data/nodes__exchange_rate__property__dateRate": "Taux date", "@sage/xtrem-master-data/nodes__exchange_rate__property__destination": "Destination", "@sage/xtrem-master-data/nodes__exchange_rate__property__divisor": "Diviseur", "@sage/xtrem-master-data/nodes__exchange_rate__property__rate": "Cours", "@sage/xtrem-master-data/nodes__exchange_rate__property__shortDescription": "Description courte", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate": "Taux de conversion", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__failed": "Échec de taux de conversion.", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__amount": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__base": "Base", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__destination": "Destination", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__rateDate": "Date taux", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_naf": "Utiliser le format de numéro NAF : {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_rcs": "Utiliser le format de numéro RCS : {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_siren": "Utiliser le format de numéro SIREN : {{format}}", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__ghs_classification__node_name": "Classification SGH", "@sage/xtrem-master-data/nodes__ghs_classification__property__hazard": "Danger", "@sage/xtrem-master-data/nodes__ghs_classification__property__id": "Code", "@sage/xtrem-master-data/nodes__ghs_classification__property__name": "Nom", "@sage/xtrem-master-data/nodes__ghs_classification__property__pictogram": "Pictogram<PERSON>", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__group_resource__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__group_resource__node_name": "Groupe de ressources", "@sage/xtrem-master-data/nodes__group_resource__property__efficiency": "Efficience", "@sage/xtrem-master-data/nodes__group_resource__property__minCapabilityLevel": "Niveau minimum d'aptitude", "@sage/xtrem-master-data/nodes__group_resource__property__replacements": "Remplacements", "@sage/xtrem-master-data/nodes__group_resource__property__resources": "Ressources", "@sage/xtrem-master-data/nodes__group_resource__property__type": "Type", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__incoterm__node_name": "Règle d'Incoterms", "@sage/xtrem-master-data/nodes__incoterm__property__description": "Description", "@sage/xtrem-master-data/nodes__incoterm__property__id": "Code", "@sage/xtrem-master-data/nodes__incoterm__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__incoterm__property__name": "Nom", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__indirect_cost_origin__node_name": "Origine coût indirect", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__id": "Code", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__name": "Nom", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__indirect_cost_section__node_name": "Section coût indirect", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__calculationMethod": "Mode de calcul", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__id": "Code", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__lines": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__name": "Nom", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__node_name": "Section coût indirect", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostOrigin": "Origine coût indirect", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostSection": "Section coût indirect", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__percentage": "Pourcentage", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item__bom_revision_sequence_number_not_managed": "Vous pouvez uniquement ajouter un numéro de révision à une nomenclature si l'article est géré par révision de nomenclature.", "@sage/xtrem-master-data/nodes__item__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__item__cannot_change_purchased_property_supplier_exists": "Vous n'êtes pas autorisé à changer la propriété d'achat. Certains enregistrements existent dans la page Fournisseurs.", "@sage/xtrem-master-data/nodes__item__cannot_change_sold_property_customers_exists": "Vous n'êtes pas autorisé à changer la propriété de vente. Certains enregistrements existent dans la page Clients.", "@sage/xtrem-master-data/nodes__item__commodity_code_EU_format": "Utiliser le format de code marchandise UE : {{format}}", "@sage/xtrem-master-data/nodes__item__commodity_code_format": "Utiliser le format de code marchandise : {{format}}", "@sage/xtrem-master-data/nodes__item__community_code_EU_format": "{{communityCodeEU}} n'est pas un code marchandise UE correct. Le format attendu est : {{format}}", "@sage/xtrem-master-data/nodes__item__density-cannot-be-negative": "La densité ne peut pas être négative.", "@sage/xtrem-master-data/nodes__item__expiration_mangement_cannot_be_enabled_without_lot_management": "La gestion de la péremption ne peut pas être activée avec des articles qui ne sont pas gérés par lot.", "@sage/xtrem-master-data/nodes__item__invalid-quantity": "La quantité maximum doit être supérieure à la quantité minimum.", "@sage/xtrem-master-data/nodes__item__mandatory_property": "Renseigner une valeur pour les articles gérés par numéro de série", "@sage/xtrem-master-data/nodes__item__min-price-cannot-be-set-if-no-currency-is-defined": "V<PERSON> de<PERSON> sélectionner une devise avant de pouvoir renseigner un prix minimum.", "@sage/xtrem-master-data/nodes__item__node_name": "Article", "@sage/xtrem-master-data/nodes__item__not-a-volume-measure": "L'unité {{unitOfMeasure}} n'est pas liée au volume.", "@sage/xtrem-master-data/nodes__item__not-a-weight-measure": "L'unité {{unitOfMeasure}} n'est pas liée au poids.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-negative": "Le prix ne peut pas être négatif.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-set-if-no-currency-is-defined": "V<PERSON> de<PERSON> sélectionner une devise avant de pouvoir renseigner un prix de base.", "@sage/xtrem-master-data/nodes__item__property__allergens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__basePrice": "Prix de base", "@sage/xtrem-master-data/nodes__item__property__bomRevisionSequenceNumber": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/nodes__item__property__capacity": "Capacité", "@sage/xtrem-master-data/nodes__item__property__category": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__classifications": "Classifications", "@sage/xtrem-master-data/nodes__item__property__commodityCode": "Code marchandise", "@sage/xtrem-master-data/nodes__item__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__customerPrices": "Prix clients", "@sage/xtrem-master-data/nodes__item__property__customers": "Clients", "@sage/xtrem-master-data/nodes__item__property__density": "Densité", "@sage/xtrem-master-data/nodes__item__property__description": "Description", "@sage/xtrem-master-data/nodes__item__property__eanNumber": "EAN", "@sage/xtrem-master-data/nodes__item__property__id": "Code", "@sage/xtrem-master-data/nodes__item__property__image": "Image", "@sage/xtrem-master-data/nodes__item__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__item__property__isBomRevisionManaged": "Géré par révision de nomenclature", "@sage/xtrem-master-data/nodes__item__property__isBought": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__isExpiryManaged": "Gestion péremption", "@sage/xtrem-master-data/nodes__item__property__isManufactured": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__isPhantom": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__isPotencyManagement": "Gestion sur titre", "@sage/xtrem-master-data/nodes__item__property__isSold": "Vendu", "@sage/xtrem-master-data/nodes__item__property__isStockManaged": "Gestion des stocks", "@sage/xtrem-master-data/nodes__item__property__isTraceabilityManagement": "Gestion de la traçabilité", "@sage/xtrem-master-data/nodes__item__property__itemSites": "Articles-site", "@sage/xtrem-master-data/nodes__item__property__lotManagement": "Gestion des lots", "@sage/xtrem-master-data/nodes__item__property__lotSequenceNumber": "Compteur lot", "@sage/xtrem-master-data/nodes__item__property__maximumSalesQuantity": "Quantité de vente maximum", "@sage/xtrem-master-data/nodes__item__property__minimumPrice": "Prix minimum", "@sage/xtrem-master-data/nodes__item__property__minimumSalesQuantity": "Quantité de vente minimum", "@sage/xtrem-master-data/nodes__item__property__name": "Nom", "@sage/xtrem-master-data/nodes__item__property__prices": "Prix", "@sage/xtrem-master-data/nodes__item__property__purchaseUnit": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversion": "Coefficient de conversion achat stock", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversionDedicated": "Coefficient de conversion achat stock dédiée", "@sage/xtrem-master-data/nodes__item__property__salesUnit": "<PERSON>é de vente", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversion": "Conversion unité de vente en unité de stock", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversionDedicated": "Conversion unité de vente en unité de stock dédiée", "@sage/xtrem-master-data/nodes__item__property__serialNumberManagement": "Gestion n° série", "@sage/xtrem-master-data/nodes__item__property__serialNumberSequenceNumber": "Compteur de numéro de série", "@sage/xtrem-master-data/nodes__item__property__serialNumberUsage": "Utilisation des numéros de série", "@sage/xtrem-master-data/nodes__item__property__status": "Statut", "@sage/xtrem-master-data/nodes__item__property__stockUnit": "Unité de stock", "@sage/xtrem-master-data/nodes__item__property__supplierPrices": "Prix fournisseurs", "@sage/xtrem-master-data/nodes__item__property__suppliers": "Fournisseurs", "@sage/xtrem-master-data/nodes__item__property__type": "Type", "@sage/xtrem-master-data/nodes__item__property__useSupplierSerialNumbers": "Utiliser les numéros de série fournisseurs", "@sage/xtrem-master-data/nodes__item__property__volume": "Volume", "@sage/xtrem-master-data/nodes__item__property__volumeUnit": "Unité de volume", "@sage/xtrem-master-data/nodes__item__property__weight": "Poids", "@sage/xtrem-master-data/nodes__item__property__weightUnit": "Unité de poids", "@sage/xtrem-master-data/nodes__item__purchase_unit_not_0_decimal_places": "L'unité d'achat ({{unitOfMeasure}}) ne peut pas contenir de décimales pour les articles avec numéros de série.", "@sage/xtrem-master-data/nodes__item__sales_unit_not_0_decimal_places": "L'unité de vente ({{unitOfMeasure}}) ne peut pas contenir de décimales pour les articles avec numéros de série.", "@sage/xtrem-master-data/nodes__item__standard_unit_of_measure_not_changeable": "La conversion de {{from}} à {{to}} est standard. Vous ne pouvez pas la modifier.", "@sage/xtrem-master-data/nodes__item__stock_unit_not_0_decimal_places": "L'unité de stock ({{unitOfMeasure}}) ne peut pas contenir de décimales pour les articles avec numéros de série.", "@sage/xtrem-master-data/nodes__item__the_lot_and_serial_number_cannot_use_the_same_sequence_number": "Le lot et le numéro de série ne peuvent pas utiliser le même compteur.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_lot": "Cette propriété ne peut pas être utilisée avec des articles non gérés par lots.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_serial_number": "L'utilisation de compteurs est incompatible avec les articles non gérés par numéros de série.", "@sage/xtrem-master-data/nodes__item__volume-cannot-be-negative": "Le volume ne peut pas être négatif.", "@sage/xtrem-master-data/nodes__item__weight-cannot-be-negative": "Le poids ne peut pas être négatif.", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_allergen__node_name": "Allergène article", "@sage/xtrem-master-data/nodes__item_allergen__property__allergen": "Allerg<PERSON>", "@sage/xtrem-master-data/nodes__item_allergen__property__item": "Article", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used": "Vous ne pouvez pas modifier le type d'une catégorie d'article en utilisation.", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used_on_supplier": "Vous pouvez uniquement modifier un type de catégorie fournisseur s'il n'est pas attribué à un fournisseur.", "@sage/xtrem-master-data/nodes__item_category__node_name": "Catégorie d'article", "@sage/xtrem-master-data/nodes__item_category__property__id": "Code", "@sage/xtrem-master-data/nodes__item_category__property__isSequenceNumberManagement": "Gestion des compteurs", "@sage/xtrem-master-data/nodes__item_category__property__name": "Nom", "@sage/xtrem-master-data/nodes__item_category__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__item_category__property__type": "Type", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_classifications__node_name": "Classification articles", "@sage/xtrem-master-data/nodes__item_classifications__property__classification": "Classification", "@sage/xtrem-master-data/nodes__item_classifications__property__item": "Article", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_customer__node_name": "Article-client", "@sage/xtrem-master-data/nodes__item_customer__property__customer": "Client", "@sage/xtrem-master-data/nodes__item_customer__property__id": "Code", "@sage/xtrem-master-data/nodes__item_customer__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__item_customer__property__item": "Article", "@sage/xtrem-master-data/nodes__item_customer__property__maximumSalesQuantity": "Quantité de vente maximum", "@sage/xtrem-master-data/nodes__item_customer__property__minimumSalesQuantity": "Quantité de vente minimum", "@sage/xtrem-master-data/nodes__item_customer__property__name": "Nom", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnit": "<PERSON>é de vente", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnitToStockUnitConversion": "Conversion unité de vente en unité de stock", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_customer_price__node_name": "Prix article-client", "@sage/xtrem-master-data/nodes__item_customer_price__property__charge": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__customer": "Client", "@sage/xtrem-master-data/nodes__item_customer_price__property__discount": "Remise", "@sage/xtrem-master-data/nodes__item_customer_price__property__endDate": "Date fin", "@sage/xtrem-master-data/nodes__item_customer_price__property__fromQuantity": "Quantité début", "@sage/xtrem-master-data/nodes__item_customer_price__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__item_customer_price__property__item": "Article", "@sage/xtrem-master-data/nodes__item_customer_price__property__price": "Prix", "@sage/xtrem-master-data/nodes__item_customer_price__property__priceReason": "Motif prix", "@sage/xtrem-master-data/nodes__item_customer_price__property__salesSite": "Site de vente", "@sage/xtrem-master-data/nodes__item_customer_price__property__startDate": "Date de début", "@sage/xtrem-master-data/nodes__item_customer_price__property__stockSite": "Site de stock", "@sage/xtrem-master-data/nodes__item_customer_price__property__toQuantity": "Quantité fin", "@sage/xtrem-master-data/nodes__item_customer_price__property__unit": "Unité", "@sage/xtrem-master-data/nodes__item_customer_price__property__validUnits": "Unités valides", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice": "Obtenir prix de vente", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice__failed": "Échec d'obtention du prix de vente.", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice__parameter__priceParameters": "Paramétres de prix", "@sage/xtrem-master-data/nodes__item_not_active": "<PERSON><PERSON> <PERSON> supprimer les articles inactifs avant de changer le statut du document.", "@sage/xtrem-master-data/nodes__item_price__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_price__node_name": "Prix article", "@sage/xtrem-master-data/nodes__item_price__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__dateValid": "Date de validité", "@sage/xtrem-master-data/nodes__item_price__property__dateValidFrom": "Date de validité début", "@sage/xtrem-master-data/nodes__item_price__property__dateValidTo": "Date de validité fin", "@sage/xtrem-master-data/nodes__item_price__property__fromQuantity": "Quantité début", "@sage/xtrem-master-data/nodes__item_price__property__item": "Article", "@sage/xtrem-master-data/nodes__item_price__property__price": "Prix", "@sage/xtrem-master-data/nodes__item_price__property__priority": "Priorité", "@sage/xtrem-master-data/nodes__item_price__property__site": "Site", "@sage/xtrem-master-data/nodes__item_price__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__item_price__property__toQuantity": "Quantité fin", "@sage/xtrem-master-data/nodes__item_price__property__type": "Type", "@sage/xtrem-master-data/nodes__item_price__property__unit": "Unité", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice": "Obt<PERSON><PERSON> le prix d'achat", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice__parameter__priceParameters": "Paramétres de prix", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_site__deletion_forbidden": "Il existe un enregistrement d'en-cours avec cet article-site. Vous ne pouvez pas le supprimer.", "@sage/xtrem-master-data/nodes__item_site__node_name": "Article-site", "@sage/xtrem-master-data/nodes__item_site__property__batchQuantity": "Quantité de batch", "@sage/xtrem-master-data/nodes__item_site__property__completedProductDefaultLocation": "Emplacement par défaut de l'article lancé", "@sage/xtrem-master-data/nodes__item_site__property__costs": "Coûts", "@sage/xtrem-master-data/nodes__item_site__property__defaultSupplier": "Fournisseur par défaut", "@sage/xtrem-master-data/nodes__item_site__property__economicOrderQuantity": "Lot économique", "@sage/xtrem-master-data/nodes__item_site__property__expectedQuantity": "Quantité attendue", "@sage/xtrem-master-data/nodes__item_site__property__id": "Code", "@sage/xtrem-master-data/nodes__item_site__property__inboundDefaultLocation": "Emplacement par défaut en entrée", "@sage/xtrem-master-data/nodes__item_site__property__indirectCostSection": "Section coût indirect", "@sage/xtrem-master-data/nodes__item_site__property__isOrderToOrder": "Ordre à la demande", "@sage/xtrem-master-data/nodes__item_site__property__item": "Article", "@sage/xtrem-master-data/nodes__item_site__property__itemSiteCost": "Coût article-site", "@sage/xtrem-master-data/nodes__item_site__property__outboundDefaultLocation": "Emplacement par défaut en sortie", "@sage/xtrem-master-data/nodes__item_site__property__preferredProcess": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__prodLeadTime": "Délai de production", "@sage/xtrem-master-data/nodes__item_site__property__purchaseLeadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__reorderPoint": "Point de commande", "@sage/xtrem-master-data/nodes__item_site__property__replenishmentMethod": "Méthode de réapprovisionnement", "@sage/xtrem-master-data/nodes__item_site__property__requiredQuantity": "Quantité demandée", "@sage/xtrem-master-data/nodes__item_site__property__safetyStock": "Stock de sécurité", "@sage/xtrem-master-data/nodes__item_site__property__site": "Site", "@sage/xtrem-master-data/nodes__item_site__property__stdCostValue": "Valeur de coût standard", "@sage/xtrem-master-data/nodes__item_site__property__stockUnit": "Unité de stock", "@sage/xtrem-master-data/nodes__item_site__property__suppliers": "Fournisseurs", "@sage/xtrem-master-data/nodes__item_site__property__valuationMethod": "Méthode de valorisation", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite": "Obtenir article-site valorisé", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite__failed": "Obtenir article-site valorisé.", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite__parameter__searchCriteria": "Critères de recherche", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_site_cost__node_name": "Coût article-site", "@sage/xtrem-master-data/nodes__item_site_cost__property__costCategory": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/nodes__item_site_cost__property__forQuantity": "Quantité", "@sage/xtrem-master-data/nodes__item_site_cost__property__fromDate": "Date début", "@sage/xtrem-master-data/nodes__item_site_cost__property__indirectCost": "Coût indirect", "@sage/xtrem-master-data/nodes__item_site_cost__property__isCalculated": "Calculé", "@sage/xtrem-master-data/nodes__item_site_cost__property__isUpdatingPreviousCost": "Mise à jour coût précédent", "@sage/xtrem-master-data/nodes__item_site_cost__property__itemSite": "Article-site", "@sage/xtrem-master-data/nodes__item_site_cost__property__laborCost": "Coût de main d'oeuvre", "@sage/xtrem-master-data/nodes__item_site_cost__property__machineCost": "Coût machine", "@sage/xtrem-master-data/nodes__item_site_cost__property__materialCost": "Co<PERSON>t matière", "@sage/xtrem-master-data/nodes__item_site_cost__property__stockUnit": "Unité de stock", "@sage/xtrem-master-data/nodes__item_site_cost__property__toDate": "Taxe début", "@sage/xtrem-master-data/nodes__item_site_cost__property__toolCost": "Coût outil", "@sage/xtrem-master-data/nodes__item_site_cost__property__totalCost": "Coût total", "@sage/xtrem-master-data/nodes__item_site_cost__property__unitCost": "Coût unitaire", "@sage/xtrem-master-data/nodes__item_site_cost__property__version": "Version", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost": "O<PERSON>enir coût article-site", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__failed": "O<PERSON><PERSON>r coût article-site.", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__effectiveDate": "Date effective", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__item": "Article", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__site": "Site", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_site_supplier__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__item_site_supplier__node_name": "Article-site-fournisseur", "@sage/xtrem-master-data/nodes__item_site_supplier__property__isDefaultItemSupplier": "Article-fournisseur par défaut", "@sage/xtrem-master-data/nodes__item_site_supplier__property__item": "Article", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSite": "Article-site", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSupplier": "Article-fournisseur", "@sage/xtrem-master-data/nodes__item_site_supplier__property__minimumPurchaseOrderQuantity": "Quantité minimum commande d'achat", "@sage/xtrem-master-data/nodes__item_site_supplier__property__priority": "Priorité", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseLeadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseUnit": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__property__site": "Site", "@sage/xtrem-master-data/nodes__item_site_supplier__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__item_site_supplier__property__uStoredPriority": "Priorité stockée", "@sage/xtrem-master-data/nodes__item_site_supplier__suppliers-dont-match": "Le fournisseur doit être le même que celui qui est référencé sur l'article-fournisseur.", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_supplier__node_name": "Article-fournisseur", "@sage/xtrem-master-data/nodes__item_supplier__property__id": "Code", "@sage/xtrem-master-data/nodes__item_supplier__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__item_supplier__property__isDefaultItemSupplier": "Article-fournisseur par défaut", "@sage/xtrem-master-data/nodes__item_supplier__property__item": "Article", "@sage/xtrem-master-data/nodes__item_supplier__property__minimumPurchaseQuantity": "Quantité d'achat minimum", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseLeadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseUnitOfMeasure": "Unité de mesure d'achat", "@sage/xtrem-master-data/nodes__item_supplier__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemCode": "Code article fournisseur", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemName": "Nom article fournisseur", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierPriority": "Priorité du fournisseur", "@sage/xtrem-master-data/nodes__item_supplier__purchase_unit_forbidden": "Définissez un coefficient de conversion de l'unité de stock {{stockUnit}} en unité d'achat {{purchaseUnit}} pour l'article {{item}}.", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__item_supplier_price__node_name": "Prix article-fournisseur", "@sage/xtrem-master-data/nodes__item_supplier_price__property__currency": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValid": "Date validité", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidFrom": "Date validité début", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidTo": "Date validité fin", "@sage/xtrem-master-data/nodes__item_supplier_price__property__fromQuantity": "Quantité début", "@sage/xtrem-master-data/nodes__item_supplier_price__property__item": "Article", "@sage/xtrem-master-data/nodes__item_supplier_price__property__itemSupplier": "Article-fournisseur", "@sage/xtrem-master-data/nodes__item_supplier_price__property__price": "Prix", "@sage/xtrem-master-data/nodes__item_supplier_price__property__priority": "Priorité", "@sage/xtrem-master-data/nodes__item_supplier_price__property__site": "Site", "@sage/xtrem-master-data/nodes__item_supplier_price__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__item_supplier_price__property__toQuantity": "Quantité fin", "@sage/xtrem-master-data/nodes__item_supplier_price__property__type": "Type", "@sage/xtrem-master-data/nodes__item_supplier_price__property__unit": "Unité", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice": "Obt<PERSON><PERSON> le prix d'achat", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice__failed": "Échec d'obtention du délai d'achat.", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice__parameter__priceParameters": "Paramétres de prix", "@sage/xtrem-master-data/nodes__item-category__sequence-number-cannot-be-set": "Le compteur ne peut pas être défini.", "@sage/xtrem-master-data/nodes__item-category__sequence-number-is-mandatory": "Le compteur est obligatoire.", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_infinite_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_specific_to_infinite": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_improper_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate-greater-than-toDate": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity_improper_range": "La borne de quantité ne peut pas chevaucher une autre borne de quantité.", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity-greater-than-toQuantity": "Renseignez une 'Quantité début' inférieure à la 'Quantité fin'.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_infinite_range": "La nouvelle borne de dates ne peut pas couvrir une période infinie s'il existe une ligne avec une borne de temps spécifique.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_specific_to_infinite": "La nouvelle borne de date ne peut pas couvrir une période spécifique s'il existe une ligne avec une borne de temps non définie.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_improper_range": "La borne de date ne peut pas chevaucher une autre borne de date.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate-greater-than-endDate": "Renseignez une date de début inférieure à la date de fin.", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-range": "Borne de quantités incorrecte : {{qtyRange}}", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-unit-of-measure": "L'unité {{uom}} n'est pas liée à la quantité.", "@sage/xtrem-master-data/nodes__item-price__price-cannot-be-negative": "Le prix ne peut pas être négatif.", "@sage/xtrem-master-data/nodes__item-price__price-overlap": "Chevauchement des prix : {{err<PERSON><PERSON>}}", "@sage/xtrem-master-data/nodes__item-price__priority-cannot-be-negative": "La priorité ne peut pas être négative.", "@sage/xtrem-master-data/nodes__item-price__quantity-cannot-be-negative": "La quantité ne peut pas être négative.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory": "Sélectionner un site de stock.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-purchase-site": "Sélectionner un site de stock ou un site d'achat.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-or-purchase-site": "Sélectionner un site de stock, de vente ou d'achat.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-site": "Sélectionner un site de stock de vente.", "@sage/xtrem-master-data/nodes__item-site__valuation-method-must-be-standard-cost": "Les articles qui ne sont pas gérés en stock doivent utiliser la méthode de valorisation de coût standard.", "@sage/xtrem-master-data/nodes__item-site-cost__calculated-cost-only-for-manufactured-item": "Un coût calculé est uniquement compatible avec un article fabriqué.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_deletion_impossible_if_before_today": "Suppression non autorisée. La date de début du coût de l'article-site est antérieure à la date actuelle.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_update_impossible": "La date de début du coût de l'article-site est antérieure ou égale à la date courante.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-from-date-already-set": "Un coût existe déjà avec la même date de début.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-updating-previous-cost": "E<PERSON>ur de mise à jour du coût précédent : {{errors}}", "@sage/xtrem-master-data/nodes__item-site-cost__incorrect-key-changes": "L'article-site et la catégorie d'un coût article-site ne peuvent pas être modifiés.", "@sage/xtrem-master-data/nodes__item-site-cost__labor-cost-only-for-manufactured-item": "Un coût de main d'oeuvre est uniquement compatible avec un article fabriqué.", "@sage/xtrem-master-data/nodes__item-site-cost__material-cost-only-for-manufactured-item": "Un coût machine est uniquement compatible avec un article fabriqué.", "@sage/xtrem-master-data/nodes__item-site-cost__tool-cost-only-for-manufactured-item": "Un coût outil est uniquement compatible avec un article fabriqué.", "@sage/xtrem-master-data/nodes__item-supplier-price__fromQuantity_improper_range": "La borne de quantité ne peut pas chevaucher une autre borne de quantité.", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__labor_capability__node_name": "Aptitude main d'oeuvre", "@sage/xtrem-master-data/nodes__labor_capability__property__labor": "Main d'oeuvre", "@sage/xtrem-master-data/nodes__labor_capability__property__machine": "Machine", "@sage/xtrem-master-data/nodes__labor_capability__property__service": "Service", "@sage/xtrem-master-data/nodes__labor_capability__property__tool": "Outil", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__labor_resource__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__labor_resource__node_name": "Ressource main d'oeuvre", "@sage/xtrem-master-data/nodes__labor_resource__property__capabilities": "Aptitudes", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers": "<PERSON><PERSON><PERSON> les numéros de contenants internes en masse", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__failed": "<PERSON><PERSON><PERSON> numéros de contenants internes en masse.", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__containerId": "Code contenant", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleItem": "Article unique", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleLot": "Lot unique", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__locationId": "Code emplacement", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__numberToCreate": "Nombre à créer", "@sage/xtrem-master-data/nodes__license_plate_number__node_name": "Numéro de contenant interne", "@sage/xtrem-master-data/nodes__license_plate_number__property__consumedCapacity": "Capacité consommée", "@sage/xtrem-master-data/nodes__license_plate_number__property__container": "Contenant", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleItem": "Article unique", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleLot": "Lot unique", "@sage/xtrem-master-data/nodes__license_plate_number__property__location": "Emplacement", "@sage/xtrem-master-data/nodes__license_plate_number__property__number": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__property__owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license-plate-number__location-required-for-automatic-number-generation": "Le numéro contenant interne ne peut pas être généré pour le contenant {{containerId}} sans un emplacement.", "@sage/xtrem-master-data/nodes__license-plate-number__no_sequence_counter": "Aucun compteur avec l'ID {{sequenceId}}", "@sage/xtrem-master-data/nodes__license-plate-number__no-default-sequence": "Le numéro contenant interne ne peut pas être généré. Renseignez un compteur pour le contenant {{containerId}}.", "@sage/xtrem-master-data/nodes__license-plate-number__owner-not-required": "Aucun propriétaire est demandé pour le numéro de contenant interne.", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__location__location_category_virtual_not_allowed": "La catégorie d'emplacement virtuel n'est pas valide. V<PERSON> sélectionner une catégorie d'emplacement différente.", "@sage/xtrem-master-data/nodes__location__location_zone_virtual_not_allowed": "La zone d'emplacement virtuel n'est pas valide. <PERSON><PERSON> sélectionner une zone d'emplacement différente.", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations": "<PERSON><PERSON><PERSON> les emplacements de masse", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__failed": "Échec de création des emplacements en masse.", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locations": "Emplacements", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locationSequence": "Compteur emplacement", "@sage/xtrem-master-data/nodes__location__mutation__getLocations": "Obtenir localisations", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__failed": "Échec d'obtention des emplacements.", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__locationSequence": "Compteur emplacement", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__requiredCombinations": "Combinaisons demandées", "@sage/xtrem-master-data/nodes__location__node_name": "Emplacement", "@sage/xtrem-master-data/nodes__location__property__dangerousGoodAllowed": "Matières dangereuses autorisées", "@sage/xtrem-master-data/nodes__location__property__id": "Code", "@sage/xtrem-master-data/nodes__location__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__location__property__isVirtualAllowed": "Emplacement virtuel autorisé", "@sage/xtrem-master-data/nodes__location__property__locationType": "Type d'emplacement", "@sage/xtrem-master-data/nodes__location__property__locationZone": "Zone d'emplacement", "@sage/xtrem-master-data/nodes__location__property__name": "Nom", "@sage/xtrem-master-data/nodes__location__property__site": "Site", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__location_sequence__node_name": "Compteur emplacement", "@sage/xtrem-master-data/nodes__location_sequence__property__components": "Composants", "@sage/xtrem-master-data/nodes__location_sequence__property__counterLength": "<PERSON><PERSON>ur compteur", "@sage/xtrem-master-data/nodes__location_sequence__property__id": "Code", "@sage/xtrem-master-data/nodes__location_sequence__property__lastSequenceUsed": "<PERSON><PERSON> compteur utilisé", "@sage/xtrem-master-data/nodes__location_sequence__property__name": "Nom", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocations": "Nombre d'emplacements", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsRemaining": "Nombre d'emplacements restants", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsUsed": "Nombre d'emplacements utilisés", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__location_sequence_component__node_name": "Composant de compteur d'emplacement", "@sage/xtrem-master-data/nodes__location_sequence_component__property__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__location_sequence_component__property__endValue": "Valeur fin", "@sage/xtrem-master-data/nodes__location_sequence_component__property__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__location_sequence_component__property__locationSequence": "Compteur emplacement", "@sage/xtrem-master-data/nodes__location_sequence_component__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__location_sequence_component__property__startValue": "<PERSON><PERSON> début", "@sage/xtrem-master-data/nodes__location_sequence_component__property__type": "Type", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__location_type__node_name": "Type d'emplacement", "@sage/xtrem-master-data/nodes__location_type__property__description": "Description", "@sage/xtrem-master-data/nodes__location_type__property__id": "Code", "@sage/xtrem-master-data/nodes__location_type__property__isVirtualAllowed": "Emplacement virtuel autorisé", "@sage/xtrem-master-data/nodes__location_type__property__locationCategory": "Catégorie d'emplacement", "@sage/xtrem-master-data/nodes__location_type__property__name": "Nom", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__location_zone__node_name": "Zone d'emplacement", "@sage/xtrem-master-data/nodes__location_zone__property__id": "Code", "@sage/xtrem-master-data/nodes__location_zone__property__isVirtualAllowed": "Emplacement virtuel autorisé", "@sage/xtrem-master-data/nodes__location_zone__property__locations": "Emplacements", "@sage/xtrem-master-data/nodes__location_zone__property__name": "Nom", "@sage/xtrem-master-data/nodes__location_zone__property__site": "Site", "@sage/xtrem-master-data/nodes__location_zone__property__zoneType": "Type de zone", "@sage/xtrem-master-data/nodes__location-type__location_category_virtual_not_allowed": "La catégorie emplacement virtuel n'est pas autorisée.", "@sage/xtrem-master-data/nodes__location-zone__site_modify": "Impossible d'actualiser le site. Des emplacements sont liés à cette zone de stockage.", "@sage/xtrem-master-data/nodes__location-zone__zone_type_virtual_not_allowed": "Le type de zone emplacement virtuel n'est pas autorisé.", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__machine_resource__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__machine_resource__node_name": "Ressource machine", "@sage/xtrem-master-data/nodes__machine_resource__property__contractId": "ID contrat", "@sage/xtrem-master-data/nodes__machine_resource__property__contractName": "Contrat", "@sage/xtrem-master-data/nodes__machine_resource__property__minCapabilityLevel": "Niveau minimum d'aptitude", "@sage/xtrem-master-data/nodes__machine_resource__property__model": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__machine_resource__property__serialNumber": "Numéro de série", "@sage/xtrem-master-data/nodes__machine_resource__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__payment_term__node_name": "Condition de paiement", "@sage/xtrem-master-data/nodes__payment_term__property__businessEntityType": "Type d'entité commerciale", "@sage/xtrem-master-data/nodes__payment_term__property__days": "Jours", "@sage/xtrem-master-data/nodes__payment_term__property__description": "Description", "@sage/xtrem-master-data/nodes__payment_term__property__discountAmount": "Montant d'escompte", "@sage/xtrem-master-data/nodes__payment_term__property__discountDate": "Date d'escompte", "@sage/xtrem-master-data/nodes__payment_term__property__discountFrom": "Calcul date escompte", "@sage/xtrem-master-data/nodes__payment_term__property__discountType": "Type d'escompte", "@sage/xtrem-master-data/nodes__payment_term__property__dueDateType": "Type date d'échéance", "@sage/xtrem-master-data/nodes__payment_term__property__id": "Code", "@sage/xtrem-master-data/nodes__payment_term__property__isActive": "Active", "@sage/xtrem-master-data/nodes__payment_term__property__name": "Nom", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyAmount": "Montant de pénalité", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyType": "Type de pénalité", "@sage/xtrem-master-data/nodes__payment_term_discount_amount_percentage_error": "Le montant d'escompte doit être inférieur à 100%.", "@sage/xtrem-master-data/nodes__payment_term_discount_date_should_be_before_due_date": "La date d'escompte doit être égale ou antérieure à la date d'échéance.", "@sage/xtrem-master-data/nodes__payment_term_discount_from_needs_to_match_due_date_type": "Le type de la date d'échéance et le type d'escompte doivent être identiques.", "@sage/xtrem-master-data/nodes__payment_term_discount_mandatory": "Lors<PERSON> vous renseignez une date, le type et le montant d'escompte sont obligatoires.", "@sage/xtrem-master-data/nodes__payment_term_penalty_amount_percentage_error": "Le montant de la pénalité doit être inférieur à 100%.", "@sage/xtrem-master-data/nodes__range_sequence_component__node_name": "Composant de compteur de borne", "@sage/xtrem-master-data/nodes__range_sequence_component__property__endValue": "Valeur fin", "@sage/xtrem-master-data/nodes__range_sequence_component__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__range_sequence_component__property__startValue": "<PERSON><PERSON> début", "@sage/xtrem-master-data/nodes__range_sequence_number__last_sequence_used_not_found": "Le format du numéro de révision de nomenclature est incorrect. Renseignez un nouveau numéro de révision de nomenclature.", "@sage/xtrem-master-data/nodes__range_sequence_number__no_combinations_found": "La séquence a atteint la valeur finale. Il est impossible de générer de nouveaux compteurs.", "@sage/xtrem-master-data/nodes__range_sequence_number__node_name": "Compteur borne", "@sage/xtrem-master-data/nodes__range_sequence_number__property__components": "Composants", "@sage/xtrem-master-data/nodes__range_sequence_number__property__definitionLevel": "Niveau de définition", "@sage/xtrem-master-data/nodes__range_sequence_number__property__isChronological": "Est chronologique", "@sage/xtrem-master-data/nodes__range_sequence_number__property__legislation": "Législation", "@sage/xtrem-master-data/nodes__range_sequence_number__property__numberOfCombinations": "Nombre de combinaison", "@sage/xtrem-master-data/nodes__range_sequence_number__property__rtzLevel": "Niveau RAZ", "@sage/xtrem-master-data/nodes__range_sequence_number__property__type": "Type", "@sage/xtrem-master-data/nodes__range_sequence_number__required_combinations_must_be_greater_than_zero": "<PERSON><PERSON> devez renseigner un chiffre supérieur à zéro.", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__reason_code__node_name": "Code motif", "@sage/xtrem-master-data/nodes__reason_code__property__id": "Code", "@sage/xtrem-master-data/nodes__reason_code__property__isActive": "Actif", "@sage/xtrem-master-data/nodes__reason_code__property__name": "Nom", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__resource_cost_category__node_name": "Catégories coûts ressources", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costCategory": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costUnit": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__resource_cost_category__property__indirectCostSection": "Section coût indirect", "@sage/xtrem-master-data/nodes__resource_cost_category__property__resource": "Ressource", "@sage/xtrem-master-data/nodes__resource_cost_category__property__runCost": "Coût d'exploitation", "@sage/xtrem-master-data/nodes__resource_cost_category__property__setupCost": "Coût de réglage", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__resource_group_replacement__node_name": "Remplacement groupe de ressources", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__replacement": "Remplacement", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__resourceGroup": "Groupe de ressources", "@sage/xtrem-master-data/nodes__resource-cost-category__the-cost-category-is-mandatory": "La catégorie de coût {{costCategory}} est obligatoire.", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number__node_name": "Compteur", "@sage/xtrem-master-data/nodes__sequence_number__property__componentLength": "<PERSON><PERSON><PERSON> composant", "@sage/xtrem-master-data/nodes__sequence_number__property__components": "Composants", "@sage/xtrem-master-data/nodes__sequence_number__property__definitionLevel": "Niveau de définition", "@sage/xtrem-master-data/nodes__sequence_number__property__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number__property__isChronological": "Chronologique", "@sage/xtrem-master-data/nodes__sequence_number__property__isClearedByReset": "Effacé par réinitialisation", "@sage/xtrem-master-data/nodes__sequence_number__property__isUsed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number__property__legislation": "Législation", "@sage/xtrem-master-data/nodes__sequence_number__property__minimumLength": "Longueur minimum", "@sage/xtrem-master-data/nodes__sequence_number__property__name": "Nom", "@sage/xtrem-master-data/nodes__sequence_number__property__rtzLevel": "Niveau de remise à zéro", "@sage/xtrem-master-data/nodes__sequence_number__property__sequenceNumberAssignments": "Attributions de compteurs", "@sage/xtrem-master-data/nodes__sequence_number__property__type": "Type", "@sage/xtrem-master-data/nodes__sequence_number__property__values": "Valeurs", "@sage/xtrem-master-data/nodes__sequence_number__query__getDocumentNodeNames": "Obtenir noms node document", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_company_mandatory": "Renseignez le compteur pour la société {{companyId}}.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_site_mandatory": "Renseignez le compteur pour le site {{siteId}}.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__node_name": "Attribution de compteurs", "@sage/xtrem-master-data/nodes__sequence_number_assignment__only_allowed_for_sales_invoice_credit_memo_fr_legislation": "Les numéros de compteur de comptabilisation peuvent uniquement être attribués aux factures et avoirs de vente pour la législation française.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__company": "Société", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__currentLegislationId": "Code législation actuelle", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isActive": "Active", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isAssignOnPosting": "Attribuer à la comptabilisation", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isDefaultAssignment": "Attribution par défaut", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isUsed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__legislation": "Législation", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumberAssignmentDocumentType": "Type de document d'attribution de numéros de série", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__setupId": "Code paramétrage", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__site": "Site", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__node_name": "Type de document d'attribution de numéros de série", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__displayOrder": "Ordre d'affichage", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeFactory": "Node livré", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeValues": "Valeurs de node", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignmentModule": "Module d'attribution de compteurs", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignments": "Attributions de compteurs", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__setupId": "Code paramétrage", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__node_name": "Module d'attribution de compteurs", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__name": "Nom", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__nodes": "Nodes", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__node_name": "Paramétrage d'attribution de compteurs", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__property__modules": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number_component__node_name": "Composant de compteur", "@sage/xtrem-master-data/nodes__sequence_number_component__property__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_component__property__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_component__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__sequence_number_component__property__type": "Type", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__sequence_number_value__mutation__updateSequenceNumberValues": "Actualiser les valeurs de compteur", "@sage/xtrem-master-data/nodes__sequence_number_value__mutation__updateSequenceNumberValues__parameter__sequenceNumberValues": "Valeurs de compteurs", "@sage/xtrem-master-data/nodes__sequence_number_value__node_name": "<PERSON><PERSON> de compteur", "@sage/xtrem-master-data/nodes__sequence_number_value__property__additionalInfo": "Infos supplémentaires", "@sage/xtrem-master-data/nodes__sequence_number_value__property__company": "Société", "@sage/xtrem-master-data/nodes__sequence_number_value__property__period": "Période", "@sage/xtrem-master-data/nodes__sequence_number_value__property__periodDate": "Date période", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceNumber": "Compteur", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceValue": "<PERSON><PERSON> de compteur", "@sage/xtrem-master-data/nodes__sequence_number_value__property__site": "Site", "@sage/xtrem-master-data/nodes__sequence-number__length_exceed": "Longueur de compteur dépassé. V<PERSON> devez renseigner un compteur plus court.", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__shift_detail__node_name": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/nodes__shift_detail__property__duration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__shift_detail__property__formattedDuration": "Durée formatée", "@sage/xtrem-master-data/nodes__shift_detail__property__id": "Code", "@sage/xtrem-master-data/nodes__shift_detail__property__name": "Nom", "@sage/xtrem-master-data/nodes__shift_detail__property__setupId": "Code paramétrage", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftEnd": "Horaires fin", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftStart": "Ho<PERSON><PERSON> d<PERSON>", "@sage/xtrem-master-data/nodes__site__incorrect_format_siret": "{{siret}} n'est pas un numéro SIRET correct. Le format attendu est: {{format}}", "@sage/xtrem-master-data/nodes__site__not-a-valid-tax-id": "{{taxIdNumber}} n'est pas un numéro de TVA correct. Le format attendu est : {{format}}", "@sage/xtrem-master-data/nodes__site_extension__corporation_and_site": "Vous ne pouvez pas définir l'entité légale comme {{legalEntity}} car l'entité commerciale est définie comme un site.", "@sage/xtrem-master-data/nodes__site_extension__current_site_is_financial_site": "Le site courant est déjà un site financier.", "@sage/xtrem-master-data/nodes__site_extension__financial_site_mandatory": "Ce site n'est pas un site financier. Renseignez un site financier.", "@sage/xtrem-master-data/nodes__site_extension__invalid_timezone": "La valeur du fuseau horaire est incorrecte : {{value}}.", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__standard__node_name": "Standard", "@sage/xtrem-master-data/nodes__standard__property__code": "Code", "@sage/xtrem-master-data/nodes__standard__property__id": "Code", "@sage/xtrem-master-data/nodes__standard__property__industrySector": "Secteur d'industrie", "@sage/xtrem-master-data/nodes__standard__property__name": "Nom", "@sage/xtrem-master-data/nodes__standard__property__sdo": "Standard", "@sage/xtrem-master-data/nodes__standard__property__version": "Version", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__standard_industrial_classification__node_name": "Classification industrielle standard", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__legislation": "Législation", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicCode": "Code SIC", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicDescription": "Description SIC", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__supplier__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__supplier__incorrect_format_siret": "{{siret}} n'est pas un numéro SIRET correct. Le format attendu est: {{format}}", "@sage/xtrem-master-data/nodes__supplier__node_name": "Fournisseur", "@sage/xtrem-master-data/nodes__supplier__property__billByAddress": "Adresse du fournisseur facturant", "@sage/xtrem-master-data/nodes__supplier__property__billBySupplier": "Fournisseur du fournisseur facturant", "@sage/xtrem-master-data/nodes__supplier__property__category": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__certificates": "Certificats", "@sage/xtrem-master-data/nodes__supplier__property__deliveryMode": "Mode de livraison", "@sage/xtrem-master-data/nodes__supplier__property__incoterm": "Règle d'Incoterms", "@sage/xtrem-master-data/nodes__supplier__property__itemPrices": "Prix articles", "@sage/xtrem-master-data/nodes__supplier__property__items": "Articles", "@sage/xtrem-master-data/nodes__supplier__property__parent": "Parent", "@sage/xtrem-master-data/nodes__supplier__property__paymentMethod": "Mode de paiement", "@sage/xtrem-master-data/nodes__supplier__property__paymentTerm": "Condition de paiement", "@sage/xtrem-master-data/nodes__supplier__property__payToAddress": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__payToSupplier": "Fournisseur payé", "@sage/xtrem-master-data/nodes__supplier__property__prices": "Prix", "@sage/xtrem-master-data/nodes__supplier__property__returnToAddress": "<PERSON><PERSON><PERSON> <PERSON> retour", "@sage/xtrem-master-data/nodes__supplier__property__returnToSupplier": "Fournisseur de retour", "@sage/xtrem-master-data/nodes__supplier__property__standardIndustrialClassification": "Classification industrielle standard", "@sage/xtrem-master-data/nodes__supplier__property__supplierType": "Type de fournisseur", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier": "Obt<PERSON>r fournisseur par défaut", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__failed": "Échec de l'application du fournisseur par défaut.", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__item": "Article", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__site": "Site", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate": "Renouvellement du certificat", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__failed": "Échec de renouvellement du certificat.", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__certificate": "Certificat", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__newCertificateDate": "Nouvelle date du certificat", "@sage/xtrem-master-data/nodes__supplier_certificate__node_name": "Certificat fournisseur", "@sage/xtrem-master-data/nodes__supplier_certificate__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__team__node_name": "Équipe", "@sage/xtrem-master-data/nodes__team__property__description": "Description", "@sage/xtrem-master-data/nodes__team__property__id": "Code", "@sage/xtrem-master-data/nodes__team__property__name": "Nom", "@sage/xtrem-master-data/nodes__team__property__site": "Site", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__tool_resource__bulkMutation__bulkDelete": "Suppression de masse", "@sage/xtrem-master-data/nodes__tool_resource__node_name": "Ressource outil", "@sage/xtrem-master-data/nodes__tool_resource__property__consumptionMode": "Mode de consommation", "@sage/xtrem-master-data/nodes__tool_resource__property__hoursTracked": "Suivi des heures", "@sage/xtrem-master-data/nodes__tool_resource__property__item": "Article", "@sage/xtrem-master-data/nodes__tool_resource__property__quantity": "Quantité", "@sage/xtrem-master-data/nodes__tool_resource__property__unitProduced": "Unité produite", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__item": "Le client doit avoir un article pour la conversion d'unité.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__type": "Renseignez un type de flux 'Ventes' avant de pouvoir renseigner un client.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__flow_without_item": "Vous ne pouvez pas avoir un coefficient de conversion unitaire pour un flux sans article.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_delete": "Vous ne pouvez pas supprimer le coefficient de conversion des unités. La propriété 'isStandard' est vraie.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_property_update": "Vous ne pouvez pas modifier les unités de conversion standard livrées par défaut.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_update": "Vous ne pouvez pas mettre à jour, supprimer ou ajouter un coefficient de conversion des unités de conversion standard fournies par défaut.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__item__type": "Renseignez un article avant de pouvoir renseigner un type de flux.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__purchase__customer": "Il est impossible de combiner le type de flux d'achat et le client.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__sales__supplier": "Il est impossible de combiner le type de flux d'achat et le fournisseur.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__customer": "Le fournisseur ou le client doit être défini, mais pas les deux.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__item": "Le fournisseur doit avoir un article pour la conversion d'unité.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__type": "<PERSON><PERSON> devez renseigner un type de flux 'Achats' avant de pouvoir renseigner un fournisseur.", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__unit_conversion_factor__node_name": "Coefficient de conversion d'unité", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__coefficient": "Coefficient", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__customer": "Client", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__fromUnit": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__isStandard": "Standard", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__item": "Article", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__toUnit": "Unité fin", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__type": "Type", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__unit_of_measure__decrease_not_possible_unit_of_measure_is_used": "Vous ne pouvez pas réduire les décimales d'une unité de mesure en cours d\"utilisation.", "@sage/xtrem-master-data/nodes__unit_of_measure__node_name": "Unité de mesure", "@sage/xtrem-master-data/nodes__unit_of_measure__property__conversionFactor": "Coefficient de conversion", "@sage/xtrem-master-data/nodes__unit_of_measure__property__decimalDigits": "Décimales", "@sage/xtrem-master-data/nodes__unit_of_measure__property__description": "Description", "@sage/xtrem-master-data/nodes__unit_of_measure__property__id": "Code", "@sage/xtrem-master-data/nodes__unit_of_measure__property__isActive": "Active", "@sage/xtrem-master-data/nodes__unit_of_measure__property__name": "Nom", "@sage/xtrem-master-data/nodes__unit_of_measure__property__setupId": "Code paramétrage", "@sage/xtrem-master-data/nodes__unit_of_measure__property__symbol": "Symbole", "@sage/xtrem-master-data/nodes__unit_of_measure__property__type": "Type", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo": "Convertir de en", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__failed": "Échec de convertir de en.", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__conversionFactor": "Coefficient de conversion", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__customer": "Client", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__formatToUnitDecimalDigits": "Formater en décimales d'unité", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__fromUnit": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__item": "Article", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__quantity": "Quantité", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__toUnit": "Unité fin", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__type": "Type", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit": "Obtenir l'unité achat", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__failed": "Échec d'obtention de l'unité d'achat.", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__item": "Article", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor": "Obtenir coefficient de conversion d'unité", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__failed": "Obtenir le coefficient de conversion d'unité.", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__customer": "Client", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__fromUnit": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__item": "Article", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__supplier": "Fournisseur", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__toUnit": "Unité fin", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__type": "Type", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__version_information__node_name": "Informations de version", "@sage/xtrem-master-data/nodes__version_information__property__text": "Texte", "@sage/xtrem-master-data/nodes__version_information__property__version": "Version", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport": "Exporter", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__filter": "Filtre", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__id": "Code", "@sage/xtrem-master-data/nodes__weekly_shift__no_daily_shift_on_full_week_shift": "Une semaine complète ne peut pas contenir des horaires journaliers.", "@sage/xtrem-master-data/nodes__weekly_shift__node_name": "Horaires he<PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__capacity": "Capacité", "@sage/xtrem-master-data/nodes__weekly_shift__property__formattedCapacity": "Capacité formatée", "@sage/xtrem-master-data/nodes__weekly_shift__property__fridayShift": "Ho<PERSON><PERSON> du vendredi", "@sage/xtrem-master-data/nodes__weekly_shift__property__id": "Code", "@sage/xtrem-master-data/nodes__weekly_shift__property__isFullWeek": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__mondayShift": "Horaires du lundi", "@sage/xtrem-master-data/nodes__weekly_shift__property__name": "Nom", "@sage/xtrem-master-data/nodes__weekly_shift__property__saturdayShift": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__setupId": "Code paramétrage", "@sage/xtrem-master-data/nodes__weekly_shift__property__sundayShift": "Horaires du dimanche", "@sage/xtrem-master-data/nodes__weekly_shift__property__thursdayShift": "Horaires du jeudi", "@sage/xtrem-master-data/nodes__weekly_shift__property__tuesdayShift": "Horaires du mardi", "@sage/xtrem-master-data/nodes__weekly_shift__property__wednesdayShift": "Horaires du mercredi", "@sage/xtrem-master-data/nodes__work_in_progress__node_name": "En-cours", "@sage/xtrem-master-data/nodes__work_in_progress__property__actualQuantity": "Quantité réelle", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentId": "Code du document", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentLine": "Ligne document", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentNumber": "Numéro de document", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentType": "Type de document", "@sage/xtrem-master-data/nodes__work_in_progress__property__endDate": "Date fin", "@sage/xtrem-master-data/nodes__work_in_progress__property__expectedQuantity": "Quantité attendue", "@sage/xtrem-master-data/nodes__work_in_progress__property__item": "Article", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentLine": "Ligne de document d'origine", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentType": "Type de document d'origine", "@sage/xtrem-master-data/nodes__work_in_progress__property__outstandingQuantity": "Quantité en attente", "@sage/xtrem-master-data/nodes__work_in_progress__property__remainingQuantityToAllocate": "Quantité restante à allouer", "@sage/xtrem-master-data/nodes__work_in_progress__property__site": "Site", "@sage/xtrem-master-data/nodes__work_in_progress__property__startDate": "Date de début", "@sage/xtrem-master-data/nodes__work_in_progress__property__status": "Statut", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite": "Obtenir quantité d'en-cours par article-site", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__failed": "Obtenir quantité d'en-cours par article-site.", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentDate": "Date actuelle", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentItem": "Article actuel", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentSite": "Site actuel", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentStatus": "Statut actuel", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__originDocumentType": "Type de document d'origine", "@sage/xtrem-master-data/nodes_sequence_number_assignment_company_leg": "La législation {{legislation}} doit être la même que la législation de votre société {{companyLegislation}}.", "@sage/xtrem-master-data/nodes_sequence_number_assignment_site_error": "Votre site doit appartenir à {{company}}.", "@sage/xtrem-master-data/nodes-resource-group-cannot-be-replaced-by-itself": "Le groupe de ressources ne peut pas être remplacé par lui-même.", "@sage/xtrem-master-data/nodes-resource-group-replacement-resource-group-should-be-replaced-by-another-one-with-same-site": "Le groupe de ressources doit être remplacé par un autre groupe du même site.", "@sage/xtrem-master-data/or": "Ou", "@sage/xtrem-master-data/package__name": "Données de base", "@sage/xtrem-master-data/page__item_customer_price_panel__no_price_list_available": "Aucun tarif disponible.", "@sage/xtrem-master-data/page__item_customer_price_panel__price_is_zero": "Le prix est égal à zéro.", "@sage/xtrem-master-data/page__item_supplier_price_panel__no_price_list_available": "Aucun tarif disponible.", "@sage/xtrem-master-data/page-extensions__user_extension____navigationPanel__listItem__line11__title": "Tableau de bord sélectionné", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__title": "Civilité", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____lookupDialogTitle": "Sélectionner le tableau de bord", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____title": "Tableau de bord sélectionné", "@sage/xtrem-master-data/pages__address__country____columns__title__continent": "Continent", "@sage/xtrem-master-data/pages__address__deleteAddress____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__address_functions__businessentity_primary_active_address_mandatory": "Attribuer une adresse principale active à l'entité commerciale.", "@sage/xtrem-master-data/pages__address_functions__company_primary_active_address_mandatory": "Attribuer une adresse principale active à la société.", "@sage/xtrem-master-data/pages__address_functions__customer_primary_active_address_mandatory": "Attribuer une adresse principale active au client.", "@sage/xtrem-master-data/pages__address_functions__primary_active_address_mandatory": "Attribuer une adresse principale active à {{addressEntityTypeLocalized}}.", "@sage/xtrem-master-data/pages__address_functions__site_primary_active_address_mandatory": "Attribuer une adresse principale active au site.", "@sage/xtrem-master-data/pages__address_functions__supplier_primary_active_address_mandatory": "Attribuer une adresse principale active au fournisseur.", "@sage/xtrem-master-data/pages__address_functions_add_new____title": "Nouvelle adresse {{addressEntityTypeLocalized}}", "@sage/xtrem-master-data/pages__address_functions_address_contacts____title": "Contacts pour l'adresse {{addressEntityTypeLocalized}} {{addressName}}.", "@sage/xtrem-master-data/pages__address_functions_businessentity_add_new____title": "Nouvelle adresse de l'entité commerciale", "@sage/xtrem-master-data/pages__address_functions_businessentity_address_contacts____title": "Contacts pour l'adresse de l'entité commerciale : {{addressName}}", "@sage/xtrem-master-data/pages__address_functions_businessentity_edit____title": "Modifier l'adresse de l'entité commerciale", "@sage/xtrem-master-data/pages__address_functions_edit____title": "Modifier l'adresse {{addressEntityTypeLocalized}}", "@sage/xtrem-master-data/pages__address_panel__edit____title": "Modifier l'adresse", "@sage/xtrem-master-data/pages__address_panel__isPrimary_must_be_active": "Une adresse principale doit être active.", "@sage/xtrem-master-data/pages__address_panel__isPrimaryForAnotherEntity____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__address_panel__new____title": "Nouvelle adresse", "@sage/xtrem-master-data/pages__address-contacts_panel_add_new____title": "Nouveau contact", "@sage/xtrem-master-data/pages__address-contacts_panel_edit____title": "Modifier le contact", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_active": "Actif", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_inactive": "Inactive", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_primary_address": "Principal", "@sage/xtrem-master-data/pages__allergen____navigationPanel__listItem__pictogram__title": "Pictogram<PERSON>", "@sage/xtrem-master-data/pages__allergen____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__allergen____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__allergen____objectTypeSingular": "Allerg<PERSON>", "@sage/xtrem-master-data/pages__allergen____title": "Allerg<PERSON>", "@sage/xtrem-master-data/pages__allergen___id____title": "Code", "@sage/xtrem-master-data/pages__allergen__allergenImageBlock____title": "Pictogram<PERSON>", "@sage/xtrem-master-data/pages__allergen__allergenInformationBlock____title": "Informations allergènes", "@sage/xtrem-master-data/pages__allergen__generalSection____title": "Général", "@sage/xtrem-master-data/pages__allergen__id____title": "Code", "@sage/xtrem-master-data/pages__allergen__idBlock____title": "Code", "@sage/xtrem-master-data/pages__allergen__isActive____title": "Actif", "@sage/xtrem-master-data/pages__allergen__name____title": "Nom", "@sage/xtrem-master-data/pages__already_used_message": "Le compteur précédent a déjà été utilisé pour générer un numéro de document.", "@sage/xtrem-master-data/pages__already_used_title": "Compteur utilisé", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__componentLength__title": "<PERSON><PERSON>ur compteur", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__isDefault__title": "Défaut", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypePlural": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypeSingular": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/pages__bom_revision_sequence____title": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/pages__bom_revision_sequence__addComponent____title": "Ajouter", "@sage/xtrem-master-data/pages__bom_revision_sequence__capital_letters_only": "Un composant de compteur alphabétique peut uniquement contenir des lettres majuscules.", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentLength____title": "<PERSON><PERSON>ur compteur", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__endValue": "Valeur fin", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__startValue": "<PERSON><PERSON> début", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__type": "Type", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____title": "Composants", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentsBlock____title": "Composants", "@sage/xtrem-master-data/pages__bom_revision_sequence__default": "Défaut", "@sage/xtrem-master-data/pages__bom_revision_sequence__digits_only": "Un composant de compteur numérique peut uniquement contenir des numéros.", "@sage/xtrem-master-data/pages__bom_revision_sequence__id____title": "Code", "@sage/xtrem-master-data/pages__bom_revision_sequence__invalid_range": "La valeur de départ ne peut pas être supérieure à la valeur de fin.", "@sage/xtrem-master-data/pages__bom_revision_sequence__isDefault____title": "Défaut", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainBlock____title": "Détails", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainSection____title": "Général", "@sage/xtrem-master-data/pages__bom_revision_sequence__name____title": "Nom", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__listItem__line6__title": "N° TVA", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__business_entity____objectTypePlural": "Entités commerciales", "@sage/xtrem-master-data/pages__business_entity____objectTypeSingular": "Entité commerciale", "@sage/xtrem-master-data/pages__business_entity____title": "Entité commerciale", "@sage/xtrem-master-data/pages__business_entity___id____title": "ID", "@sage/xtrem-master-data/pages__business_entity__address_active": "Active", "@sage/xtrem-master-data/pages__business_entity__address_inactive": "Inactive", "@sage/xtrem-master-data/pages__business_entity__addresses____addButtonText": "A<PERSON><PERSON> adresse", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__concatenatedAddressWithoutName": "Adresse sans nom", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__contacts": "Contacts", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__isPrimary": "Adresse principale", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title": "Modifier l'adresse", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__2": "Définir comme adresse principale", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__3": "A<PERSON>ter le contact", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__4": "Supprimer les adresses et contacts", "@sage/xtrem-master-data/pages__business_entity__addresses____title": "Adresses", "@sage/xtrem-master-data/pages__business_entity__addressSection____title": "Adresses", "@sage/xtrem-master-data/pages__business_entity__contact_active": "Actif", "@sage/xtrem-master-data/pages__business_entity__contact_inactive": "Inactif", "@sage/xtrem-master-data/pages__business_entity__contacts____addButtonText": "Ajouter contact", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__isPrimary": "Contact principal", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__2": "Définir comme contact principal", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__contacts____headerLabel__title": "Actif", "@sage/xtrem-master-data/pages__business_entity__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__regionLabel": "Intitulé région", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__zipLabel": "Libellé ZIP", "@sage/xtrem-master-data/pages__business_entity__country____lookupDialogTitle": "Sélectionner le pays", "@sage/xtrem-master-data/pages__business_entity__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__business_entity__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__business_entity__currency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__display_primary_address": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__displayAddresses____columns__title": "Contacts", "@sage/xtrem-master-data/pages__business_entity__id____title": "Code", "@sage/xtrem-master-data/pages__business_entity__idBlock____title": "ID", "@sage/xtrem-master-data/pages__business_entity__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__business_entity__isCustomer____title": "Client", "@sage/xtrem-master-data/pages__business_entity__isNaturalPerson____title": "<PERSON>ne physique", "@sage/xtrem-master-data/pages__business_entity__isSite____title": "Site", "@sage/xtrem-master-data/pages__business_entity__isSupplier____title": "Fournisseur", "@sage/xtrem-master-data/pages__business_entity__legalEntity____title": "Entité juridique", "@sage/xtrem-master-data/pages__business_entity__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__business_entity__mainSection____title": "Général", "@sage/xtrem-master-data/pages__business_entity__name____title": "Nom", "@sage/xtrem-master-data/pages__business_entity__parent____lookupDialogTitle": "Sélectionner l'entité commerciale parente", "@sage/xtrem-master-data/pages__business_entity__parent____title": "Entité commerciale parente", "@sage/xtrem-master-data/pages__business_entity__roleBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__business_entity__siret____title": "SIRET", "@sage/xtrem-master-data/pages__business_entity__taxIdNumber____title": "N° de TVA", "@sage/xtrem-master-data/pages__business_entity__website____title": "Site web", "@sage/xtrem-master-data/pages__business_entity_address_panel____title": "Volet de l'adresse de l'entité commerciale", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine1____title": "Adresse ligne 1", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine2____title": "Adresse ligne 2", "@sage/xtrem-master-data/pages__business_entity_address_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__business_entity_address_panel__city____title": "Ville", "@sage/xtrem-master-data/pages__business_entity_address_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____lookupDialogTitle": "Sélectionner le pays", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryDetail____title": "<PERSON><PERSON><PERSON> liv<PERSON>son", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____postfix": "jour(s)", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____lookupDialogTitle": "Sélectionner le mode de livraison", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____title": "Mode de livraison", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____lookupDialogTitle": "Sélectionner la règle d'Incoterms", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____title": "Règle d'Incoterms®", "@sage/xtrem-master-data/pages__business_entity_address_panel__informationBlock____title": "Informations d'expédition", "@sage/xtrem-master-data/pages__business_entity_address_panel__isActiveShippingAddress____title": "Active", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimary____title": "Adresse principale", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimaryShippingAddress____title": "<PERSON><PERSON><PERSON> de livraison principale", "@sage/xtrem-master-data/pages__business_entity_address_panel__isShippingAddress____title": "Adresse d'expédition", "@sage/xtrem-master-data/pages__business_entity_address_panel__locationPhoneNumber____title": "N° téléphone", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainBlock____title": "Informations d'adresse", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_address_panel__name____title": "Nom", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____title": "Site d'expédition", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingBlock____title": "Informations d'expédition", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingSection____title": "Informations", "@sage/xtrem-master-data/pages__business_entity_address_panel__workDaysSelection____title": "Jours ouvrés", "@sage/xtrem-master-data/pages__business_entity_contact_panel____title": "Volet du contact de l'adresse de l'entité commerciale", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____lookupDialogTitle": "Sélectionner l'adresse", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_contact_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__business_entity_contact_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__business_entity_contact_panel__email____title": "E-mail", "@sage/xtrem-master-data/pages__business_entity_contact_panel__firstName____title": "Prénom", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isActive____title": "Active", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isPrimary____title": "Contact principal", "@sage/xtrem-master-data/pages__business_entity_contact_panel__lastName____title": "Nom de famille", "@sage/xtrem-master-data/pages__business_entity_contact_panel__locationPhoneNumber____title": "N° téléphone", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainBlock____title": "Informations de contact", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainSection____title": "Général", "@sage/xtrem-master-data/pages__business_entity_contact_panel__position____title": "Profession", "@sage/xtrem-master-data/pages__business_entity_contact_panel__preferredName____title": "Nom préféré", "@sage/xtrem-master-data/pages__business_entity_contact_panel__role____title": "Fonction", "@sage/xtrem-master-data/pages__business_entity_contact_panel__title____title": "Civilité", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____subtitle": "Sélectionner une entité commerciale répondant à vos besoins.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____title": "<PERSON><PERSON>er un client à partir d'une entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressBlock____title": "Définition de l'adresse d'expédition.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____addButtonText": "A<PERSON><PERSON> adresse", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title": "Jours ouvrés", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__concatenatedAddressWithoutName": "Adresse sans nom", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail": "Adresse expédition", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__incoterm__name": "Règle d'Incoterms®", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isActive": "Statut adresse d'expédition", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isPrimary": "<PERSON>resse livraison principale", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__leadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__mode__name": "Mode livraison", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Site expédition", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__isPrimary": "Adresse principale", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title": "Modifier l'adresse", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__2": "Définir comme adresse principale", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__3": "Définir comme adresse principale d'expédition", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__4": "Supprimer les adresses et contacts", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____title": "Adresses", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____lookupDialogTitle": "Sélectionner l'entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____title": "<PERSON>réer depuis entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity_already_a_customer": "Cette entité commerciale est déjà définie comme client.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialBlock____title": "Financières.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialSection____title": "Financières", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalBlock____title": "Sélectionner l'entité commerciale à utiliser pour créer le client.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalSection____title": "Général", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__paymentTerm____lookupDialogTitle": "Sélectionner la condition de paiement", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__primary_ship_to_address_mandatory": "Le client devrait avoir au moins une adresse d'expédition principale active.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____subtitle": "Sélectionner une entité commerciale répondant à vos besoins.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____title": "Créer un site à partir d'une entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____lookupDialogTitle": "Sélectionner l'entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____title": "<PERSON>réer depuis entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity_already_a_site": "Cette entité commerciale est déjà définie comme site.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____lookupDialogTitle": "Sélectionner le site financier", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalBlock____title": "Sélectionner l'entité commerciale pour créer le site.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalSection____title": "Général", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__isFinance____title": "Finance", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementBlock____title": "Gestion", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementSection____title": "Gestion", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____subtitle": "Sélectionner une entité commerciale répondant à vos besoins.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____title": "<PERSON><PERSON>er un fournisseur à partir d'une entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____lookupDialogTitle": "Sélectionner l'entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____title": "<PERSON>réer depuis entité commerciale", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity_already_a_supplier": "Cette entité commerciale est déjà définie comme fournisseur.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialBlock____title": "Financières.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialSection____title": "Financières", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalBlock____title": "Sélectionner l'entité commerciale à utiliser pour créer le fournisseur.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalSection____title": "Général", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__paymentTerm____lookupDialogTitle": "Sélectionner la condition de paiement", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__listItem__level__title": "Niveau", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__capability_level____objectTypePlural": "Niveaux d'aptitude", "@sage/xtrem-master-data/pages__capability_level____objectTypeSingular": "Niveau d'aptitude", "@sage/xtrem-master-data/pages__capability_level____title": "Niveau d'aptitude", "@sage/xtrem-master-data/pages__capability_level__description____title": "Description", "@sage/xtrem-master-data/pages__capability_level__id____title": "Code", "@sage/xtrem-master-data/pages__capability_level__level____title": "Niveau", "@sage/xtrem-master-data/pages__capability_level__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__capability_level__mainSection____title": "Général", "@sage/xtrem-master-data/pages__capability_level__name____title": "Nom", "@sage/xtrem-master-data/pages__capability_panel____title": "Aptitude", "@sage/xtrem-master-data/pages__capability_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__capability_panel__cancelAction____title": "Annuler", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__description": "Description", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__id": "Code", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__level": "Niveau", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le niveau d'aptitude", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____title": "Niveau d'aptitude", "@sage/xtrem-master-data/pages__capability_panel__create____title": "OK", "@sage/xtrem-master-data/pages__capability_panel__createAction____title": "OK", "@sage/xtrem-master-data/pages__capability_panel__dateEndValid____title": "Fin d'activité", "@sage/xtrem-master-data/pages__capability_panel__dateStartValid____title": "Début d'activité", "@sage/xtrem-master-data/pages__capability_panel__id____title": "Code", "@sage/xtrem-master-data/pages__capability_panel__labor____columns__title___id": "Code", "@sage/xtrem-master-data/pages__capability_panel__labor____lookupDialogTitle": "Sélectionner la main-d'oeuvre", "@sage/xtrem-master-data/pages__capability_panel__labor____title": "Main d'oeuvre", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__description": "Description", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__id": "Code", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__minCapabilityLevel__level": "Niveau aptitude mini", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__model": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__serialNumber": "Numéro série", "@sage/xtrem-master-data/pages__capability_panel__machine____lookupDialogTitle": "Sélectionner la machine", "@sage/xtrem-master-data/pages__capability_panel__machine____title": "Machine", "@sage/xtrem-master-data/pages__capability_panel__name____title": "Nom", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__description": "Description", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__id": "Code", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__capability_panel__service____lookupDialogTitle": "Sélectionner l'entrée de menu", "@sage/xtrem-master-data/pages__capability_panel__service____title": "Article de service", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__description": "Description", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__id": "Code", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__quantity": "Quantité", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__resourceGroup__name": "Groupe de ressources", "@sage/xtrem-master-data/pages__capability_panel__tool____lookupDialogTitle": "Sélectionner l'outil", "@sage/xtrem-master-data/pages__capability_panel__tool____title": "Outil", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_business_entity_address_title": "Modifier l'adresse de l'entité commerciale", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_company_address_title": "Modifier l'adresse de la société", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_customer_address_title": "Modifier l'adresse client", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_site_address_title": "Modifier adresse site", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_supplier_address_title": "Modifier l'adresse fournisseur", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_business_entity_address_title": "Nouvelle adresse de l'entité commerciale", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_company_address_title": "Nouvelle adresse de la société", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_customer_address_title": "Nouvelle adresse client", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_site_address_title": "Nouvelle adresse site", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_supplier_address_title": "Nouvelle adresse fournis<PERSON>ur", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__edit_contact_title": "Modifier le contact", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__new_contact_title": "Nouveau contact", "@sage/xtrem-master-data/pages__company____add_site": "Ajouter site", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line10__title": "Forme juridique", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line11__title": "<PERSON><PERSON> de compteur", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line8__title": "NAF (APE)", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__company____objectTypePlural": "Sociétés", "@sage/xtrem-master-data/pages__company____objectTypeSingular": "Société", "@sage/xtrem-master-data/pages__company____title": "Société", "@sage/xtrem-master-data/pages__company__addresses____addButtonText": "A<PERSON><PERSON> adresse", "@sage/xtrem-master-data/pages__company__addresses____columns__title__concatenatedAddressWithoutName": "Adresse sans nom", "@sage/xtrem-master-data/pages__company__addresses____columns__title__contacts": "Contacts", "@sage/xtrem-master-data/pages__company__addresses____columns__title__isPrimary": "Adresse principale", "@sage/xtrem-master-data/pages__company__addresses____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title": "Modifier l'adresse", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__2": "Définir comme adresse principale", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__3": "Ajouter contact", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__4": "Supprimer adresses et contacts", "@sage/xtrem-master-data/pages__company__addresses____title": "Adresses", "@sage/xtrem-master-data/pages__company__addressSection____title": "Adresses", "@sage/xtrem-master-data/pages__company__contacts____addButtonText": "Ajouter contact", "@sage/xtrem-master-data/pages__company__contacts____columns__title__isPrimary": "Contact principal", "@sage/xtrem-master-data/pages__company__contacts____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__company__contacts____columns__title__role": "Fonction", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__2": "Définir comme contact principal", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__contacts____headerLabel__title": "Actif", "@sage/xtrem-master-data/pages__company__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__company__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__company__country____lookupDialogTitle": "Sélectionner le pays", "@sage/xtrem-master-data/pages__company__creditLimitBlock____title": "<PERSON>ite <PERSON>", "@sage/xtrem-master-data/pages__company__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__company__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__company__currency____placeholder": "Sélectionner...", "@sage/xtrem-master-data/pages__company__currency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__customerOnHoldCheck____title": "Contrôle de client bloqué", "@sage/xtrem-master-data/pages__company__description____title": "Description", "@sage/xtrem-master-data/pages__company__display_primary_address": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__id____title": "Code", "@sage/xtrem-master-data/pages__company__isSequenceNumberIdUsed____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__legalForm____title": "Forme juridique", "@sage/xtrem-master-data/pages__company__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-master-data/pages__company__legislation____placeholder": "Sélectionner...", "@sage/xtrem-master-data/pages__company__mainBlock____title": "Informations société", "@sage/xtrem-master-data/pages__company__mainSection____title": "Général", "@sage/xtrem-master-data/pages__company__managementSection____title": "Gestion", "@sage/xtrem-master-data/pages__company__naf____title": "NAF (APE)", "@sage/xtrem-master-data/pages__company__name____title": "Nom", "@sage/xtrem-master-data/pages__company__paymentTrackingBlock____title": "Suivi des règlements", "@sage/xtrem-master-data/pages__company__rcs____title": "RCS", "@sage/xtrem-master-data/pages__company__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__company__sequenceNumberId____title": "<PERSON><PERSON> de compteur", "@sage/xtrem-master-data/pages__company__siren____title": "SIREN", "@sage/xtrem-master-data/pages__company__siteBlock____title": "Organisation", "@sage/xtrem-master-data/pages__compare__number_remaining_to__required": "Les combinaisons demandées ne peuvent pas être supérieures aux combinaisons restantes.", "@sage/xtrem-master-data/pages__contact_panel__isPrimary_must_be_active": "Un contact principal doit être actif.", "@sage/xtrem-master-data/pages__contact_selection_panel____title": "Volet de sélection de contact", "@sage/xtrem-master-data/pages__contact_selection_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__email": "E-mail", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__firstName": "Prénom", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__lastName": "Nom", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__title": "Civilité", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____title": "Contacts", "@sage/xtrem-master-data/pages__contact_selection_panel__contactSelectionBlock____title": "Sélectionner les contacts", "@sage/xtrem-master-data/pages__contact_selection_panel__ok____title": "OK", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__firstName": "Prénom", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__lastName": "Nom", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__title": "Civilité", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____title": "Contact sélectionné", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__consumedLocationCapacity__title": "Capacité de stockage demandée", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isInternal__title": "Interne", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleItem__title": "Article unique", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleLot__title": "Lot unique", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__labelFormat__title": "Format d'étiquette", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__locationCategory__title": "Type", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__sequenceNumber__title": "Compteur", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__storageCapacity__title": "Capacité de stockage", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__container____objectTypePlural": "Contenants", "@sage/xtrem-master-data/pages__container____objectTypeSingular": "Contenant", "@sage/xtrem-master-data/pages__container____title": "Contenant", "@sage/xtrem-master-data/pages__container__consumedLocationCapacity____title": "Capacité de stockage demandée", "@sage/xtrem-master-data/pages__container__id____title": "Code", "@sage/xtrem-master-data/pages__container__isActive____title": "Actif", "@sage/xtrem-master-data/pages__container__isInternal____title": "Interne", "@sage/xtrem-master-data/pages__container__isSingleItem____title": "Article unique", "@sage/xtrem-master-data/pages__container__isSingleLot____title": "Lot unique", "@sage/xtrem-master-data/pages__container__labelFormat____title": "Format étiquette", "@sage/xtrem-master-data/pages__container__mainSection____title": "Général", "@sage/xtrem-master-data/pages__container__name____title": "Nom", "@sage/xtrem-master-data/pages__container__sequenceNumber____columns__title__id": "Code", "@sage/xtrem-master-data/pages__container__sequenceNumber____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__container__sequenceNumber____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le compteur", "@sage/xtrem-master-data/pages__container__sequenceNumber____title": "Compteur", "@sage/xtrem-master-data/pages__container__storageCapacity____title": "Capacité stockage", "@sage/xtrem-master-data/pages__container__type____title": "Type contenant", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line2__title": "Code", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line3__title": "Type", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__cost_category____objectTypePlural": "Catégories de coût", "@sage/xtrem-master-data/pages__cost_category____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__cost_category____title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__cost_category__costCategoryType____title": "Type", "@sage/xtrem-master-data/pages__cost_category__id____title": "Code", "@sage/xtrem-master-data/pages__cost_category__isMandatory____title": "Obligatoire", "@sage/xtrem-master-data/pages__cost_category__mainSection____title": "Général", "@sage/xtrem-master-data/pages__cost_category__name____title": "Nom", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line_5__title": "ISO 3166-1 alpha-3", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line2__title": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line3__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line4__title": "Continent", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line5__title": "ISO 3166-1 alpha-3", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line6__title": "Etat membre de l'UE", "@sage/xtrem-master-data/pages__country____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__country____objectTypePlural": "Pays", "@sage/xtrem-master-data/pages__country____objectTypeSingular": "Pays", "@sage/xtrem-master-data/pages__country____title": "Pays", "@sage/xtrem-master-data/pages__country__continent____title": "Continent", "@sage/xtrem-master-data/pages__country__countryFlagBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__country__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__country__currency____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__country__currency____columns__title__symbol": "Symbole", "@sage/xtrem-master-data/pages__country__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__country__currency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__country__generalSection____title": "Général", "@sage/xtrem-master-data/pages__country__id____title": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__country__isEuMember____title": "Etat membre UE", "@sage/xtrem-master-data/pages__country__iso31661Alpha3____title": "ISO 3166-1 alpha-3", "@sage/xtrem-master-data/pages__country__legislation____placeholder": "Sélectionner la législation", "@sage/xtrem-master-data/pages__country__legislation____title": "Législation", "@sage/xtrem-master-data/pages__country__mainBlock____title": "Informations pays", "@sage/xtrem-master-data/pages__country__name____title": "Nom", "@sage/xtrem-master-data/pages__country__regionLabel____title": "Libellé de région", "@sage/xtrem-master-data/pages__country__zipLabel____title": "Libellé de code postal", "@sage/xtrem-master-data/pages__country_invalid_id": "Le code doit contenir 2 caractères.", "@sage/xtrem-master-data/pages__country_invalid_iso_code": "Le code doit contenir 3 caractères.", "@sage/xtrem-master-data/pages__create_test_data____title": "<PERSON><PERSON><PERSON> donn<PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__create_test_data__instructions____content": "", "@sage/xtrem-master-data/pages__create_test_data__linkField____title": "Page Confluence", "@sage/xtrem-master-data/pages__create_test_data__mainBlock____title": "Informations", "@sage/xtrem-master-data/pages__create_test_data__mainSection____title": "Général", "@sage/xtrem-master-data/pages__create-test-data__instuctions": "Cette page donne accès à des outils de développement pour créer de grands ensembles de données à des fins de test.\n            \nConsultez la page Confluence pour obtenir des instructions détaillées relatives aux valeurs de chaque propriété.", "@sage/xtrem-master-data/pages__create-test-data__link_instuctions": "Instructions détaillées sur la façon de créer des données de test.", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line_4__title": "Décimales", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line3__title": "ISO 4217", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line4__title": "Décimales", "@sage/xtrem-master-data/pages__currency____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__currency____objectTypePlural": "Devi<PERSON>", "@sage/xtrem-master-data/pages__currency____objectTypeSingular": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency___id____title": "Code", "@sage/xtrem-master-data/pages__currency__addExchangeRate____title": "Ajouter", "@sage/xtrem-master-data/pages__currency__addRateSection____title": "A<PERSON>ter taux", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__icon": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____title": "De", "@sage/xtrem-master-data/pages__currency__converterResult____placeholder": "Résultat", "@sage/xtrem-master-data/pages__currency__converterResult____title": "Résultat", "@sage/xtrem-master-data/pages__currency__converterSection____title": "Convertisseur", "@sage/xtrem-master-data/pages__currency__converterToAmount____placeholder": "Montant à convertir", "@sage/xtrem-master-data/pages__currency__converterToAmount____title": "Montant à convertir", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__icon": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__id": "Code", "@sage/xtrem-master-data/pages__currency__converterToCurrency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__currency__converterToCurrency____placeholder": "Sélectionner la devise", "@sage/xtrem-master-data/pages__currency__converterToCurrency____title": "À", "@sage/xtrem-master-data/pages__currency__currencyRate____placeholder": "Cours", "@sage/xtrem-master-data/pages__currency__currencyRate____title": "Cours", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__dateRate": "Taux date", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__id": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__symbol": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__rate": "Cours", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__shortDescription": "Description courte", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__image__title": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line2Right__title": "Description courte", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line3Right__title": "Taux date", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__subtitleRight__title": "Description courte", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__title__title": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRatesSection____title": "Détails", "@sage/xtrem-master-data/pages__currency__decimalDigits____title": "Décimales", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__icon": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__destinationCurrency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__currency__destinationCurrency____placeholder": "Sélectionner la devise", "@sage/xtrem-master-data/pages__currency__destinationCurrency____title": "Devise de destination", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderBlock____title": "Cours actuels", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderSection____title": "Cours actuels", "@sage/xtrem-master-data/pages__currency__divisor____title": "<PERSON><PERSON> d'origine", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__dateRate": "Date", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__name": "Nom", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__rate": "Cours", "@sage/xtrem-master-data/pages__currency__exchangeRates____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__exchangeRates____title": "Cours de change", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____chart__xAxis__title": "Date", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____title": "Cours de change", "@sage/xtrem-master-data/pages__currency__exchangeRatesSection____title": "Cours de change", "@sage/xtrem-master-data/pages__currency__icon____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__id____title": "ISO 4217", "@sage/xtrem-master-data/pages__currency__invalid_id": "Le code doit contenir 3 caractères.", "@sage/xtrem-master-data/pages__currency__mainSection____title": "Général", "@sage/xtrem-master-data/pages__currency__name____title": "Nom", "@sage/xtrem-master-data/pages__currency__rateDate____placeholder": "Sélectionner...", "@sage/xtrem-master-data/pages__currency__rateDate____title": "Date taux", "@sage/xtrem-master-data/pages__currency__rounding____title": "Arrondi", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__icon": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_message": "Les devises et la date sont déjà associées à un cours de change. Voulez-vous conserver le cours existant ou appliquer votre nouveau cours ?", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_title": "Ce cours existe déjà.", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_title": "Ajouter cours de change", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_reverse_rate_confirmation_message": "Le taux inversé existe déjà pour la date mentionnée. Voulez-vous conserver le taux inversé ou appliquer un nouveau taux ?", "@sage/xtrem-master-data/pages__currency__side_panel_add_inverse_currency_rate_confirmation_title": "Le cours inversé existe déjà", "@sage/xtrem-master-data/pages__currency__symbol____title": "Symbole", "@sage/xtrem-master-data/pages__customer____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__2": "Débloquer", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_4__title": "Pays principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_5__title": "Ville", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line10__title": "E-mail", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line12__title": "N° TVA", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line13__title": "Statut", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line18__title": "Montant minimum commande", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line19__title": "Condition de paiement", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line20__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line21__title": "Catégorie client", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line22__title": "<PERSON>ite <PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line6__title": "Code postal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line7__title": "N° tél principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line8__title": "Contact principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line9__title": "N° de tél du contact principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__columns__title__id": "Code", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__columns__title__name": "Nom", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__title": "Nom", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title": "Tous les statuts ouverts", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__2": "Tous", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__3": "Actif", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__4": "Inactif", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____objectTypePlural": "Clients", "@sage/xtrem-master-data/pages__customer____objectTypeSingular": "Client", "@sage/xtrem-master-data/pages__customer____title": "Client", "@sage/xtrem-master-data/pages__customer__addItem____title": "Ajouter", "@sage/xtrem-master-data/pages__customer__addItemPriceLine____title": "Ajouter", "@sage/xtrem-master-data/pages__customer__address_assigned_be_primary": "Vous ne pouvez pas supprimer une adresse principale pour l'entité commerciale.", "@sage/xtrem-master-data/pages__customer__address_assigned_primary": "Vous ne pouvez pas supprimer une adresse principale.", "@sage/xtrem-master-data/pages__customer__address_assigned_to_billToAddress": "Cette adresse est attribuée à l'adresse de facturation principale.", "@sage/xtrem-master-data/pages__customer__address_assigned_to_deliveryAddress": "<PERSON>tte adresse sert d'adresse d'expédition.", "@sage/xtrem-master-data/pages__customer__addresses____addButtonText": "A<PERSON><PERSON> adresse", "@sage/xtrem-master-data/pages__customer__addresses____columns__title": "Jours ouvrés", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__concatenatedAddressWithoutName": "Adresse sans nom", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail": "Adresse expédition", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__incoterm__name": "Règle Incoterms®", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isActive": "Statut adresse expédition", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isPrimary": "<PERSON>resse livraison principale", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__leadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__mode__name": "Mode livraison", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Site expédition", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__isPrimary": "Adresse principale", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title": "Modifier l'adresse", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__2": "Définir comme adresse principale", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__3": "Définir comme adresse principale d'expédition", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__4": "A<PERSON>ter le contact", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__5": "Supprimer les adresses et contacts", "@sage/xtrem-master-data/pages__customer__addresses____title": "Adresses", "@sage/xtrem-master-data/pages__customer__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_id": "Ce code est déjà attribué à un client.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_name": "Ce nom est déjà attribué à un client.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_taxIdNumber": "Ce n° de TVA est déjà attribué à un client.", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine1": "Ligne 1", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine2": "Ligne 2", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__postcode": "Code ZIP", "@sage/xtrem-master-data/pages__customer__billToAddress____title": "Adresse principale client facturé", "@sage/xtrem-master-data/pages__customer__billToCustomer____title": "Client facturé", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____columns__title__concatenatedAddress": "Adresse principale client facturé", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____dropdownActions__title": "<PERSON><PERSON>lace<PERSON>", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____title": "Adresse principale client facturé", "@sage/xtrem-master-data/pages__customer__businessEntity____columns__title__isNaturalPerson": "<PERSON>ne physique", "@sage/xtrem-master-data/pages__customer__category____columns__title__sequenceNumber__name": "Compteur", "@sage/xtrem-master-data/pages__customer__category____lookupDialogTitle": "Sélectionner la catégorie", "@sage/xtrem-master-data/pages__customer__commercialBlock____title": "Commerciales", "@sage/xtrem-master-data/pages__customer__commercialSection____title": "Commerciales", "@sage/xtrem-master-data/pages__customer__contact_assigned_primary": "Vous ne pouvez pas supprimer un contact principal.", "@sage/xtrem-master-data/pages__customer__contacts____addButtonText": "Ajouter contact", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__isPrimary": "Contact principal", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__2": "Définir comme contact principal", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__contacts____headerLabel__title": "Actif", "@sage/xtrem-master-data/pages__customer__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__customer__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__customer__country____lookupDialogTitle": "Sélectionner le pays", "@sage/xtrem-master-data/pages__customer__createCustomer____title": "Nouveau", "@sage/xtrem-master-data/pages__customer__createFromBusinessEntity____title": "<PERSON>réer depuis entité commerciale", "@sage/xtrem-master-data/pages__customer__creditLimit____title": "<PERSON>ite <PERSON>", "@sage/xtrem-master-data/pages__customer__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____addButtonText": "Ajouter adresse d'expédition", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title___id": "Code", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__name": "Nom", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__regionLabel": "Intitulé région", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__zipLabel": "Libellé ZIP", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__3": "Ligne 2", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__4": "Ville", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__5": "Pays", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__6": "N° tél.", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__7": "Code", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__title__concatenatedAddressWithoutName": "Adresse sans nom", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__title__workDaysSelection": "Jours ouvrés", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title": "Modifier l'adresse d'expédition", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__2": "Définir comme adresse principale.", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__3": "Définir comme adresse principale", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__4": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____headerLabel__title": "Active", "@sage/xtrem-master-data/pages__customer__display_address_active": "Active", "@sage/xtrem-master-data/pages__customer__display_primary_address": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__displayAddresses____columns__title__2": "Adresse principale", "@sage/xtrem-master-data/pages__customer__displayAddresses____columns__title__3": "Adresse expédition", "@sage/xtrem-master-data/pages__customer__displayStatus____title": "Statut d'affichage", "@sage/xtrem-master-data/pages__customer__financialBlock____title": "Financières", "@sage/xtrem-master-data/pages__customer__financialSection____title": "Financières", "@sage/xtrem-master-data/pages__customer__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__customer__internalNote____helperText": "Les notes s'affichent sur les documents internes.", "@sage/xtrem-master-data/pages__customer__internalNote____title": "Notes internes", "@sage/xtrem-master-data/pages__customer__isNaturalPerson____title": "<PERSON>ne physique", "@sage/xtrem-master-data/pages__customer__isOnHold____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__customer__businessEntity__name": "Client", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__endDate": "Date fin", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__fromQuantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__isActive": "Actif", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__description": "Descr. article", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__id": "Code article", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__name": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__price": "Prix", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__priceReason__name": "Motif prix", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__salesSite__id": "Site vente", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__startDate": "Date début", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__stockSite__id": "Site stock", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__toQuantity": "Qté fin", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__unit__id": "Unité", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title__2": "Modifier", "@sage/xtrem-master-data/pages__customer__itemPricesSection____title": "Prix articles", "@sage/xtrem-master-data/pages__customer__items____columns__columns__item__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__items____columns__title__id": "Code article-client", "@sage/xtrem-master-data/pages__customer__items____columns__title__isActive": "Actif", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__description": "Descr. article", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__id": "Code article", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__name": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__maximumSalesQuantity": "Quantité vente max", "@sage/xtrem-master-data/pages__customer__items____columns__title__minimumSalesQuantity": "Quantité vente mini", "@sage/xtrem-master-data/pages__customer__items____columns__title__name": "Nom article-client", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnit__name": "<PERSON><PERSON> vente", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnitToStockUnitConversion": "Coeff. conversion US", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__items____title": "Articles", "@sage/xtrem-master-data/pages__customer__itemSection____title": "Articles", "@sage/xtrem-master-data/pages__customer__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__customer__mainSection____title": "Général", "@sage/xtrem-master-data/pages__customer__minimumOrderAmount____title": "Montant minimum commande", "@sage/xtrem-master-data/pages__customer__noteBlock____title": "Notes", "@sage/xtrem-master-data/pages__customer__notesSection____title": "Notes", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine1": "Ligne 1", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine2": "Ligne 2", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__postcode": "Code ZIP", "@sage/xtrem-master-data/pages__customer__payByAddress____title": "Adresse principal du client payeur", "@sage/xtrem-master-data/pages__customer__payByCustomer____title": "Client facturé", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____columns__title__concatenatedAddress": "Adresse principal du client payeur", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____dropdownActions__title": "<PERSON><PERSON>lace<PERSON>", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____title": "Adresse principal du client payeur", "@sage/xtrem-master-data/pages__customer__paymentTerm____lookupDialogTitle": "Sélectionner la condition de paiement", "@sage/xtrem-master-data/pages__customer__primary_active_address_contact_mandatory": "Attribuez un contact principal actif à l'adresse.", "@sage/xtrem-master-data/pages__customer__primary_active_address_mandatory": "Attribuer une adresse principale active au client.", "@sage/xtrem-master-data/pages__customer__primary_ship_to_address_mandatory": "Le client devrait avoir au moins une adresse d'expédition principale active.", "@sage/xtrem-master-data/pages__customer__put_on_hold": "<PERSON><PERSON> bloq<PERSON>", "@sage/xtrem-master-data/pages__customer__putOnHold____title": "Bloquer", "@sage/xtrem-master-data/pages__customer__remove_on_hold": "Blocage client retiré", "@sage/xtrem-master-data/pages__customer__removeOnHold____title": "Débloquer", "@sage/xtrem-master-data/pages__customer__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__customer__ShippingSection____title": "Expédition", "@sage/xtrem-master-data/pages__customer__website____title": "Site web", "@sage/xtrem-master-data/pages__customer_address_add_new____title": "Nouvelle adresse client", "@sage/xtrem-master-data/pages__customer_address_contacts____title": "Contacts pour l'adresse client : {{value}}", "@sage/xtrem-master-data/pages__customer_address_edit____title": "Modifier l'adresse client", "@sage/xtrem-master-data/pages__customer_address_panel_new__isPrimaryShippingAddress_must_be_active": "Activez l'adresse d'expédition principale.", "@sage/xtrem-master-data/pages__customer_contact_list__primary_contact": "Contact principal", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypePlural": "Motifs prix client", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypeSingular": "Motif prix client", "@sage/xtrem-master-data/pages__customer_price_reason____title": "Motif prix client", "@sage/xtrem-master-data/pages__customer_price_reason__description____title": "Description", "@sage/xtrem-master-data/pages__customer_price_reason__mainSection____title": "Général", "@sage/xtrem-master-data/pages__customer_price_reason__name____title": "Nom", "@sage/xtrem-master-data/pages__customer_price_reason__priority____title": "Priorité", "@sage/xtrem-master-data/pages__customer_price_reason__priority_already_exists": "La priorité {{priority}} existe déjà.", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Gestion de compteurs", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line_4__title": "Fournisseur", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line2__title": "Code", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line3__title": "Client", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line4__title": "Fournisseur", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__sequenceNumber__title": "Compteur", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__title__title": "Nom", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypePlural": "Catégories fournisseur et client", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypeSingular": "Catégorie fournisseur et client", "@sage/xtrem-master-data/pages__customer_supplier_category____title": "Catégorie fournisseur et client", "@sage/xtrem-master-data/pages__customer_supplier_category__id____title": "Code", "@sage/xtrem-master-data/pages__customer_supplier_category__isCustomer____title": "Client", "@sage/xtrem-master-data/pages__customer_supplier_category__isSequenceNumberManagement____title": "Gestion de compteurs", "@sage/xtrem-master-data/pages__customer_supplier_category__isSupplier____title": "Fournisseur", "@sage/xtrem-master-data/pages__customer_supplier_category__mainSection____title": "Général", "@sage/xtrem-master-data/pages__customer_supplier_category__name____title": "Nom", "@sage/xtrem-master-data/pages__customer_supplier_category__sequenceNumber____title": "Compteur", "@sage/xtrem-master-data/pages__customer-supplier__category_dialog_content": "La catégorie que vous avez sélectionnée est associée à un compteur. Voulez-vous générer un nouveau code ou garder le code actuel?", "@sage/xtrem-master-data/pages__customer-supplier__generate_ID": "Générer le code", "@sage/xtrem-master-data/pages__customer-supplier__keep_current_id-": "Garder le code actuel", "@sage/xtrem-master-data/pages__customer-supplier__select_id_number_title": "Sélectionner le numéro de code", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-customer": "Sélectionner le code du compteur client", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier": "Sélectionner le code du compteur fournisseur", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier-customer": "Sélectionner le code du compteur fournisseur et client", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__formattedCapacity__title": "Capacité", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__isFullDay__title": "<PERSON><PERSON><PERSON> compl<PERSON>", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__daily_shift____objectTypePlural": "Horaires journaliers", "@sage/xtrem-master-data/pages__daily_shift____objectTypeSingular": "Horaire journalier", "@sage/xtrem-master-data/pages__daily_shift____title": "Horaires journaliers", "@sage/xtrem-master-data/pages__daily_shift___id____title": "Code", "@sage/xtrem-master-data/pages__daily_shift__addShiftDetail____title": "Ajouter détails horaires", "@sage/xtrem-master-data/pages__daily_shift__detailsBlock____title": "Détails", "@sage/xtrem-master-data/pages__daily_shift__formattedCapacity____title": "Capacité", "@sage/xtrem-master-data/pages__daily_shift__isFullDay____title": "<PERSON><PERSON><PERSON> compl<PERSON>", "@sage/xtrem-master-data/pages__daily_shift__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__daily_shift__mainSection____title": "Général", "@sage/xtrem-master-data/pages__daily_shift__name____title": "Nom", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__formattedDuration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__id": "Code", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__name": "Nom", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftEnd": "Horaires fin", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftStart": "Ho<PERSON><PERSON> d<PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____title": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__formattedDuration": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftEnd": "Horaires fin", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftStart": "Ho<PERSON><PERSON> d<PERSON>", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____lookupDialogTitle": "Sélectionner les détails horaires", "@sage/xtrem-master-data/pages__delete_page_dialog_content": "Vous êtes sur le point de supprimer cet enregistrement.", "@sage/xtrem-master-data/pages__delete_page_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-master-data/pages__delete_page_Item_delete_supplier_price_dialog_content": "Vous êtes sur le point de supprimer cette ligne. Cette action est irréversible après enregistrement du document.", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title__duplicate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__delivery_mode____objectTypePlural": "Modes de livraison", "@sage/xtrem-master-data/pages__delivery_mode____objectTypeSingular": "Mode de livraison", "@sage/xtrem-master-data/pages__delivery_mode____title": "Mode de livraison", "@sage/xtrem-master-data/pages__delivery_mode__description____title": "Description", "@sage/xtrem-master-data/pages__delivery_mode__id____title": "Code", "@sage/xtrem-master-data/pages__delivery_mode__name____title": "Nom", "@sage/xtrem-master-data/pages__delivery_mode__section____title": "Général", "@sage/xtrem-master-data/pages__email_exception": "Impossible d'envoyer l'e-mail : ({{exception}}).", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2Right__title": "Ressource", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__employee____objectTypePlural": "Collaborateurs", "@sage/xtrem-master-data/pages__employee____objectTypeSingular": "Collaborateur", "@sage/xtrem-master-data/pages__employee____title": "Collaborateur", "@sage/xtrem-master-data/pages__employee__firstName____title": "Prénom", "@sage/xtrem-master-data/pages__employee__id____title": "Code", "@sage/xtrem-master-data/pages__employee__image____title": "Image", "@sage/xtrem-master-data/pages__employee__isActive____title": "Actif", "@sage/xtrem-master-data/pages__employee__lastName____title": "Nom de famille", "@sage/xtrem-master-data/pages__employee__mainSection____title": "Général", "@sage/xtrem-master-data/pages__employee__resource____columns__title__resourceGroup__type": "Type", "@sage/xtrem-master-data/pages__employee__resource____lookupDialogTitle": "Sélectionner la ressource", "@sage/xtrem-master-data/pages__employee__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__enter_email_address_and_last_name": "L'e-mail n'a pas été envoyé. V<PERSON> de<PERSON> renseigner une adresse e-mail et un nom de famille.", "@sage/xtrem-master-data/pages__ghs_classification____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__ghs_classification____objectTypePlural": "Classifications SGH", "@sage/xtrem-master-data/pages__ghs_classification____objectTypeSingular": "Classification SGH", "@sage/xtrem-master-data/pages__ghs_classification____title": "Classification SGH", "@sage/xtrem-master-data/pages__ghs_classification__generalSection____title": "Général", "@sage/xtrem-master-data/pages__ghs_classification__ghsClassificationInformationBlock____title": "Classification SGH", "@sage/xtrem-master-data/pages__ghs_classification__hazard____title": "Dangers", "@sage/xtrem-master-data/pages__ghs_classification__id____title": "Code", "@sage/xtrem-master-data/pages__ghs_classification__idBlock____title": "Code", "@sage/xtrem-master-data/pages__ghs_classification__name____title": "Nom", "@sage/xtrem-master-data/pages__ghs_classification__pictogram____title": "Pictogram<PERSON>", "@sage/xtrem-master-data/pages__ghs_classification__pictogramBlock____title": "Pictogram<PERSON>", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__title": "Efficience", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__location__title": "Emplacement", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__minCapabilityLevel__title": "Niveau d'aptitude", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__type__title": "Type de ressource", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__weeklyShift__title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__group_resource____objectTypePlural": "Groupes de ressources", "@sage/xtrem-master-data/pages__group_resource____objectTypeSingular": "Groupe de ressources", "@sage/xtrem-master-data/pages__group_resource____title": "Groupe de ressources", "@sage/xtrem-master-data/pages__group_resource__addCostCategory____title": "A<PERSON>ter caté<PERSON><PERSON> co<PERSON>t", "@sage/xtrem-master-data/pages__group_resource__addReplacementLine____title": "Ajouter", "@sage/xtrem-master-data/pages__group_resource__addResource____helperText": "Ajouter ressource", "@sage/xtrem-master-data/pages__group_resource__addResource____title": "Ajouter ressource", "@sage/xtrem-master-data/pages__group_resource__blockDetails____title": "Réglages", "@sage/xtrem-master-data/pages__group_resource__blockReplacements____title": "Groupes de remplacement", "@sage/xtrem-master-data/pages__group_resource__blockResources____title": "Ressources", "@sage/xtrem-master-data/pages__group_resource__blockWeekly____title": "Détails horaires hebdomadaires", "@sage/xtrem-master-data/pages__group_resource__costBlock____title": "Coût", "@sage/xtrem-master-data/pages__group_resource__costSection____title": "Coût", "@sage/xtrem-master-data/pages__group_resource__description____title": "Description", "@sage/xtrem-master-data/pages__group_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__group_resource__efficiency____title": "Efficience", "@sage/xtrem-master-data/pages__group_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__group_resource__id____title": "Code", "@sage/xtrem-master-data/pages__group_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__group_resource__location____lookupDialogTitle": "Sélectionner l'emplacement", "@sage/xtrem-master-data/pages__group_resource__location____title": "Emplacement", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le niveau d'aptitude", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____title": "Niveau d'aptitude", "@sage/xtrem-master-data/pages__group_resource__name____title": "Nom", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title___sortValue": "Priorité", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__id": "Code", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__name": "Nom", "@sage/xtrem-master-data/pages__group_resource__replacements____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__replacements____title": "Remplacements", "@sage/xtrem-master-data/pages__group_resource__resourceCapacity____title": "Capacité hebdomadaire", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Type catégorie coût", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoire", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costCategory__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costUnit__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coût indirect", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__runCost": "Opération", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__setupCost": "Réglage", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____title": "Catégories coûts ressources", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title": "Type", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__isActive": "Actif", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__weeklyShift__id": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__2": "Exclure", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resources____title": "Ressources", "@sage/xtrem-master-data/pages__group_resource__section____title": "Général", "@sage/xtrem-master-data/pages__group_resource__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__group_resource__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__group_resource__transferInResource____helperText": "Inclure", "@sage/xtrem-master-data/pages__group_resource__transferInResource____title": "Inclure", "@sage/xtrem-master-data/pages__group_resource__type____title": "Type de ressource", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____columns__title__dailyShift": "Horaires journaliers", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____title": "Détails he<PERSON>", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____columns__title__formattedCapacity": "Capacité", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____lookupDialogTitle": "Sélectionner les horaires hebdomadaires", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__incoterm____objectTypePlural": "Règles d'Incoterms®", "@sage/xtrem-master-data/pages__incoterm____objectTypeSingular": "Règle d'Incoterms®", "@sage/xtrem-master-data/pages__incoterm____title": "Règle d'Incoterms®", "@sage/xtrem-master-data/pages__incoterm__description____title": "Description", "@sage/xtrem-master-data/pages__incoterm__id____title": "Code", "@sage/xtrem-master-data/pages__incoterm__name____title": "Nom", "@sage/xtrem-master-data/pages__incoterm__section____title": "Général", "@sage/xtrem-master-data/pages__indirect_cost_origin____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypePlural": "Origines coûts indirects", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypeSingular": "Origine coût indirect", "@sage/xtrem-master-data/pages__indirect_cost_origin____title": "Origine coût indirect", "@sage/xtrem-master-data/pages__indirect_cost_origin__id____title": "Code", "@sage/xtrem-master-data/pages__indirect_cost_origin__mainSection____title": "Général", "@sage/xtrem-master-data/pages__indirect_cost_origin__name____title": "Nom", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line_4__title": "Mode de calcul", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line4__title": "Mode de calcul", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypePlural": "Sections coûts indirects", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypeSingular": "Section coût indirect", "@sage/xtrem-master-data/pages__indirect_cost_section____title": "Section coût indirect", "@sage/xtrem-master-data/pages__indirect_cost_section__addOrigin____title": "Ajouter", "@sage/xtrem-master-data/pages__indirect_cost_section__calculationMethod____title": "Mode de calcul", "@sage/xtrem-master-data/pages__indirect_cost_section__id____title": "Code", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__id": "Origine coût indirect", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__name": "Nom", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__percentage": "Pourcentage", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____title": "Détails", "@sage/xtrem-master-data/pages__indirect_cost_section__mainSection____title": "Général", "@sage/xtrem-master-data/pages__indirect_cost_section__name____title": "Nom", "@sage/xtrem-master-data/pages__indirect_cost_section__originsBlock____title": "Détails", "@sage/xtrem-master-data/pages__indirect_cost_section__totalPercentage____title": "Pourcentage total", "@sage/xtrem-master-data/pages__invalid-email": "Adresse e-mail incorrecte : {{email}}.", "@sage/xtrem-master-data/pages__item____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__emptyStateClickableText": "<PERSON><PERSON>er un article.", "@sage/xtrem-master-data/pages__item____navigationPanel__inlineActions__title__duplicate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__basePrice__title": "Prix de base", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__commodityCode__title": "Code marchandise", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__title": "<PERSON><PERSON> de <PERSON>e", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__expirationDate__title": "Gestion dates péremption", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBomRevisionManaged__title": "Révision de nomenclature", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBought__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isManufactured__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isPhantom__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isSold__title": "Vendu", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isStockManaged__title": "Gestion stocks", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line2Right__title": "GTIN-13", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line3Right__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotManagement__title": "Gestion des lots", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotSequenceNumber__title": "Compteur lot", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__maximumSalesQuantity__title": "Quantité de vente maximum", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumPrice__title": "Prix minimum", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumSalesQuantity__title": "Quantité de vente minimum", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__purchaseUnit__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__salesUnit__title": "<PERSON><PERSON> vente", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberManagement__title": "Gestion n° série", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberSequenceNumber__title": "Compteur série", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__stockUnit__title": "Unité de stock", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__volumeUnit__title": "Unité volume", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__weightUnit__title": "Unité poids", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__3": "Élaboration", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__4": "Non renouvelable", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__5": "Obsolète", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__6": "Non utilisable", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__7": "Service", "@sage/xtrem-master-data/pages__item____objectTypePlural": "Articles", "@sage/xtrem-master-data/pages__item____objectTypeSingular": "Article", "@sage/xtrem-master-data/pages__item____title": "Article", "@sage/xtrem-master-data/pages__item___id____title": "Code", "@sage/xtrem-master-data/pages__item__addItemSite____title": "Ajouter", "@sage/xtrem-master-data/pages__item__addNewAllergen____title": "Sélectionner l'allergène", "@sage/xtrem-master-data/pages__item__addNewAllergenAction____title": "Ajouter un allergène", "@sage/xtrem-master-data/pages__item__addNewClassification____title": "Sélectionner la classification SGH", "@sage/xtrem-master-data/pages__item__addNewClassificationAction____title": "Ajouter une classification", "@sage/xtrem-master-data/pages__item__addSupplier____title": "Ajouter", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__id": "Code", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__name": "Nom", "@sage/xtrem-master-data/pages__item__allergens____dropdownActions__title__remove": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__allergens____mobileCard__subtitle__title": "Code", "@sage/xtrem-master-data/pages__item__allergens____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__allergenSection____title": "Allerg<PERSON>", "@sage/xtrem-master-data/pages__item__basePrice____title": "Prix de base", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____helperText": "Sélectionnez un compteur ou laissez le champ vide pour renseigner des numéros de révision de nomenclature manuels.", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____lookupDialogTitle": "Sélectionner le compteur de révision de nomenclature", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____title": "Compteur de révision de nomenclature", "@sage/xtrem-master-data/pages__item__capacity____title": "Capacité", "@sage/xtrem-master-data/pages__item__category____columns__title__sequenceNumber__name": "Compteur", "@sage/xtrem-master-data/pages__item__category____lookupDialogTitle": "Sélectionner la catégorie d'article", "@sage/xtrem-master-data/pages__item__category____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__classifications____mobileCard__subtitle__title": "Code", "@sage/xtrem-master-data/pages__item__commodityCode____title": "Code marchandise", "@sage/xtrem-master-data/pages__item__commodityCodeEU____title": "Code marchandise UE", "@sage/xtrem-master-data/pages__item__communityCodeEU____title": "Code marchandise UE", "@sage/xtrem-master-data/pages__item__customers____columns__columns__salesUnit__name__title__3": "Symbole", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__businessEntity__id": "Code client", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__name": "Client", "@sage/xtrem-master-data/pages__item__customers____columns__title__id": "Code article-client", "@sage/xtrem-master-data/pages__item__customers____columns__title__isActive": "Actif", "@sage/xtrem-master-data/pages__item__customers____columns__title__maximumSalesQuantity": "Quantité vente max.", "@sage/xtrem-master-data/pages__item__customers____columns__title__minimumSalesQuantity": "Quantité vente mini", "@sage/xtrem-master-data/pages__item__customers____columns__title__name": "Article-client", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnit__name": "<PERSON><PERSON> vente", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnitToStockUnitConversion": "Coeff. conversion US", "@sage/xtrem-master-data/pages__item__customers____dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__customers____inlineActions__title__openLinePanel": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-master-data/pages__item__customers____mobileCard__title__title": "Client", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title": "Tous", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title__2": "Actifs", "@sage/xtrem-master-data/pages__item__customers____sidebar__headerDropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__customers____title": "Clients", "@sage/xtrem-master-data/pages__item__customerSection____title": "Clients", "@sage/xtrem-master-data/pages__item__customsUnitBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__density____title": "Densité", "@sage/xtrem-master-data/pages__item__description____title": "Description", "@sage/xtrem-master-data/pages__item__eanNumber____title": "GTIN-13", "@sage/xtrem-master-data/pages__item__financialBlock____title": "Financières", "@sage/xtrem-master-data/pages__item__financialSection____title": "Financières", "@sage/xtrem-master-data/pages__item__generateId": "Générer le code", "@sage/xtrem-master-data/pages__item__generateNewId": "La catégorie que vous avez sélectionnée est déjà associée à un compteur. Voulez-vous garder le code ou en générer un nouveau ?", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__id": "Code", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__name": "Nom", "@sage/xtrem-master-data/pages__item__ghsClassifications____dropdownActions__title__remove": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__ghsClassifications____title": "Classifications SGH", "@sage/xtrem-master-data/pages__item__ghsClassificationSection____title": "Classification SGH", "@sage/xtrem-master-data/pages__item__good_stock_block_title": "Stock", "@sage/xtrem-master-data/pages__item__good_stock_unit_title": "Unité de stock", "@sage/xtrem-master-data/pages__item__headerSection____title": "Section d'en-tête", "@sage/xtrem-master-data/pages__item__image____title": "Image", "@sage/xtrem-master-data/pages__item__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__item__inventoryBlock____title": "Stock", "@sage/xtrem-master-data/pages__item__isBomRevisionManaged____title": "Révision de nomenclature", "@sage/xtrem-master-data/pages__item__isBought____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__isExpiryManaged____title": "Gestion dates péremption", "@sage/xtrem-master-data/pages__item__isManufactured____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__isPhantom____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__isPotencyManagement____title": "Titre", "@sage/xtrem-master-data/pages__item__isSold____title": "Vendu", "@sage/xtrem-master-data/pages__item__isStockManaged____title": "Gestion stocks", "@sage/xtrem-master-data/pages__item__isTraceabilityManagement____title": "Traçabilité", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__indirectCostSection__name__title__3": "Mode de calcul", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__item__name__title": "Nom", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__item__name__title__2": "Code", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title___id": "Code", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title__symbol": "Symbole", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__title__4": "<PERSON><PERSON> financière", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__title": "Société", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__prodLeadTime": "jour(s)", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__purchaseLeadTime": "jour(s)", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__batchQuantity": "Quantité batch", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__economicOrderQuantity": "Lot économique", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__expectedQuantity": "Quantité attendue", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__item__name": "Article", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__preferredProcess": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__prodLeadTime": "Délai production", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__replenishmentMethod": "Méthode réappro.", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__requiredQuantity": "Quantité demandée", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__safetyStock": "Stock sécurité", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__stdCostValue": "Coût unitaire", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__valuationMethod": "Nature dépense", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__edit": "Modifier", "@sage/xtrem-master-data/pages__item__itemSites____title": "Articles-site", "@sage/xtrem-master-data/pages__item__keepCurrentId": "Garder le code actuel", "@sage/xtrem-master-data/pages__item__lotManagement____title": "Gestion lots", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____lookupDialogTitle": "<PERSON>é<PERSON><PERSON><PERSON> le compteur du lot", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____title": "Compteur lot", "@sage/xtrem-master-data/pages__item__mainBlock____title": "Gestion", "@sage/xtrem-master-data/pages__item__mainSection____title": "Informations", "@sage/xtrem-master-data/pages__item__managementSection____title": "Gestion", "@sage/xtrem-master-data/pages__item__manufacturingBlock____title": "Production", "@sage/xtrem-master-data/pages__item__maximumSalesQuantity____title": "Quantité maximum", "@sage/xtrem-master-data/pages__item__minimumPrice____title": "Prix minimum", "@sage/xtrem-master-data/pages__item__minimumSalesQuantity____title": "Quantité minimum", "@sage/xtrem-master-data/pages__item__name____title": "Nom", "@sage/xtrem-master-data/pages__item__positionField1____title": "Position champ 1", "@sage/xtrem-master-data/pages__item__positionField2____title": "Position champ 2", "@sage/xtrem-master-data/pages__item__positionField3____title": "Position champ 3", "@sage/xtrem-master-data/pages__item__priceSection____title": "Prix fournisseurs", "@sage/xtrem-master-data/pages__item__purchase_unit_not_0_decimal_places": "L'unité d'achat ({{unitOfMeasure}}) ne peut pas contenir de décimales pour les articles avec numéros de série.", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__type": "Type", "@sage/xtrem-master-data/pages__item__purchaseUnit____lookupDialogTitle": "Sélectionner l'unité d'achat", "@sage/xtrem-master-data/pages__item__purchaseUnitBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversion____title": "Coefficient conversion US", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversionDedicated____title": "Coefficient dé<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__sales_unit_not_0_decimal_places": "L'unité de vente ({{unitOfMeasure}}) ne peut pas contenir de décimales pour les articles avec numéros de série.", "@sage/xtrem-master-data/pages__item__salesBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__symbol": "Symbole", "@sage/xtrem-master-data/pages__item__salesCurrency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__item__salesCurrency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__currency__name__title": "ISO 4217", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title": "Nom", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title__2": "Code", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title": "Nom", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__2": "Code", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__3": "Société", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__currency__name": "Sélectionner la devise", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__customer": "Sélectionner le client", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__priceReason__name": "Sélectionner le motif de prix", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__salesSite": "Sélectionner le site", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__stockSite": "Sélectionner le site", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__unit__name": "Sélectionner l'unité", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__charge": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__businessEntity__id": "Code client", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__id": "Client", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__name": "Client", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__discount": "Remise", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__endDate": "Validité fin", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__fromQuantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__isActive": "Actif", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__item__id": "Article", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__price": "Prix", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__priceReason__name": "Motif prix", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite": "Site vente", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite__name": "Site vente", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__startDate": "Validité début", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite": "Site stock", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite__id": "Site stock", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__toQuantity": "Qté fin", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__id": "Unité", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__name": "Unité", "@sage/xtrem-master-data/pages__item__salesPrices____dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____inlineActions__title__openLinePanel": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__title__title": "Client", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__titleRight__title": "Motif prix", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title": "Tous", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title__2": "Actifs", "@sage/xtrem-master-data/pages__item__salesPrices____sidebar__headerDropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____title": "Prix clients", "@sage/xtrem-master-data/pages__item__salesPricesSection____title": "Prix de vente", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__type": "Type", "@sage/xtrem-master-data/pages__item__salesUnit____lookupDialogTitle": "Sélectionner l'unité de vente", "@sage/xtrem-master-data/pages__item__salesUnitBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversion____title": "Coefficient conversion US", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversionDedicated____title": "Coefficient dé<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__saveItem____title": "Enregistrer", "@sage/xtrem-master-data/pages__item__selectId": "Sélectionner le numéro de code", "@sage/xtrem-master-data/pages__item__serialNumberManagement____title": "Gestion n° série", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____lookupDialogTitle": "Sé<PERSON><PERSON>ner le compteur du numéro de série", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____title": "Compteur série", "@sage/xtrem-master-data/pages__item__serialNumberUsage____title": "Utilisation des numéros de série", "@sage/xtrem-master-data/pages__item__service_stock_block_title": "Unité", "@sage/xtrem-master-data/pages__item__service_stock_unit_title": "Unité de base", "@sage/xtrem-master-data/pages__item__siteSection____title": "Sites", "@sage/xtrem-master-data/pages__item__status____title": "Statut", "@sage/xtrem-master-data/pages__item__stock_unit_not_0_decimal_places": "L'unité de stock ({{unitOfMeasure}}) ne peut pas contenir de décimales pour les articles avec numéros de série.", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__type": "Type", "@sage/xtrem-master-data/pages__item__stockUnit____lookupDialogTitle": "Sélectionner l'unité de stock", "@sage/xtrem-master-data/pages__item__stockUnit____title": "Unité de stock", "@sage/xtrem-master-data/pages__item__storageBlock____title": "Caractéristiques", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__currency__name__title": "ISO 4217", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title": "Nom", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__2": "Code", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__3": "Société", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title": "Nom", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__2": "Code", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__3": "N° TVA", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__4": "Pays", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__currency__name": "Sélectionner la devise", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__site": "Sélectionner le site", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__supplier": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__unit__name": "Sélectionner l'unité", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValid": "Validité", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidFrom": "Validité début", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidTo": "Validité fin", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__fromQuantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__price": "Prix", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__priority": "Priorité", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__site__businessEntity__id": "Code site", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__businessEntity__id": "Code fournisseur", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__id": "Fournisseur", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__name": "Nom", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__toQuantity": "Qté fin", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__type": "Type", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__id": "Unité", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__name": "Unité mesure", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__symbol": "Symbole", "@sage/xtrem-master-data/pages__item__supplierPrices____dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____inlineActions__title__openLinePanel": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__title__title": "Fournisseur", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__titleRight__title": "Type", "@sage/xtrem-master-data/pages__item__supplierPrices____sidebar__headerDropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____title": "Prix fournisseurs", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title": "Nom", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__2": "Code", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__3": "N° TVA", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__4": "Pays", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__purchaseUnitOfMeasure": "Sélectionner l'unité", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__supplier": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isActive": "Actif", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isDefaultItemSupplier": "Fournisseur défaut", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__minimumPurchaseQuantity": "Quantité achat mini", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseUnitOfMeasure": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier___id": "Code", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__businessEntity__id": "Code fournisseur", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__id": "Fournisseur", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__name": "Nom", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemCode": "Code article-fournisseur", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemName": "Article-fournisseur", "@sage/xtrem-master-data/pages__item__suppliers____dropdownActions__title__delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____inlineActions__title__openLinePanel": "Ouv<PERSON>r volet des lignes", "@sage/xtrem-master-data/pages__item__suppliers____mobileCard__title__title": "Fournisseur", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title": "Tous", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title__2": "Actifs", "@sage/xtrem-master-data/pages__item__suppliers____sidebar__headerDropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____title": "Fournisseurs", "@sage/xtrem-master-data/pages__item__supplierSection____title": "Fournisseurs", "@sage/xtrem-master-data/pages__item__type____title": "Type", "@sage/xtrem-master-data/pages__item__typeBlock____title": "Informations article", "@sage/xtrem-master-data/pages__item__unitBlock____title": "Stock", "@sage/xtrem-master-data/pages__item__unitSection____title": "Unités", "@sage/xtrem-master-data/pages__item__volume____title": "Volume", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__type": "Type", "@sage/xtrem-master-data/pages__item__volumeUnit____lookupDialogTitle": "Sélectionner l'unité de volume", "@sage/xtrem-master-data/pages__item__weight____title": "Poids", "@sage/xtrem-master-data/pages__item__weightUnit____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item__weightUnit____lookupDialogTitle": "Sélectionner l'unité de poids", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Gestion de compteurs", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__sequenceNumber__title": "Compteur du code article", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-master-data/pages__item_category____navigationPanel__optionsMenu__title": "Toutes", "@sage/xtrem-master-data/pages__item_category____objectTypePlural": "Catégories d'articles", "@sage/xtrem-master-data/pages__item_category____objectTypeSingular": "Catégorie d'article", "@sage/xtrem-master-data/pages__item_category____title": "Catégorie d'article", "@sage/xtrem-master-data/pages__item_category__declarationsBlock____title": "Déclarations", "@sage/xtrem-master-data/pages__item_category__generalBlock____title": "Général", "@sage/xtrem-master-data/pages__item_category__generalSection____title": "Général", "@sage/xtrem-master-data/pages__item_category__id____title": "Code", "@sage/xtrem-master-data/pages__item_category__isSequenceNumberManagement____title": "Gestion de compteurs", "@sage/xtrem-master-data/pages__item_category__name____title": "Nom", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____lookupDialogTitle": "Sélectionner le compteur du code article", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____title": "Compteur du code article", "@sage/xtrem-master-data/pages__item_category__type____title": "Déclarations", "@sage/xtrem-master-data/pages__item_customer__edit____title": "Modifier l'article-client", "@sage/xtrem-master-data/pages__item_customer_panel____title": "Ajouter l'article-client", "@sage/xtrem-master-data/pages__item_customer_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_customer_panel__id____title": "Code article-client", "@sage/xtrem-master-data/pages__item_customer_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_panel__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-master-data/pages__item_customer_panel__item____title": "Article", "@sage/xtrem-master-data/pages__item_customer_panel__item_is_inactive": "L'article est inactif. Le statut est {{status}}.", "@sage/xtrem-master-data/pages__item_customer_panel__mainSection____title": "Général", "@sage/xtrem-master-data/pages__item_customer_panel__maximumSalesQuantity____title": "Quantité de vente maximum", "@sage/xtrem-master-data/pages__item_customer_panel__minimumSalesQuantity____title": "Quantité de vente minimum", "@sage/xtrem-master-data/pages__item_customer_panel__name____title": "Nom article-client", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____lookupDialogTitle": "Sélectionner l'unité de vente", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____title": "<PERSON>é de vente", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnitToStockUnitConversion____title": "Coefficient conversion US", "@sage/xtrem-master-data/pages__item_customer_panel__save____title": "OK", "@sage/xtrem-master-data/pages__item_customer_price_panel____title": "Prix client", "@sage/xtrem-master-data/pages__item_customer_price_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__confirm____title": "Enregistrer", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__id": "Code", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__name": "Nom", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____lookupDialogTitle": "Sélectionner le client", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____title": "Client", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____title": "Remise", "@sage/xtrem-master-data/pages__item_customer_price_panel__edit____title": "Modifier le prix de vente", "@sage/xtrem-master-data/pages__item_customer_price_panel__endDate____title": "Date de fin", "@sage/xtrem-master-data/pages__item_customer_price_panel__fromQuantity____title": "Quantité début", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____title": "Article", "@sage/xtrem-master-data/pages__item_customer_price_panel__new____title": "New sales price", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_greater_than_100": "Renseignez un pourcentage inférieur à 100.", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_is_negative": "Renseigner un pourcentage positif.", "@sage/xtrem-master-data/pages__item_customer_price_panel__price____title": "Prix", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____lookupDialogTitle": "Sélectionner le motif de prix", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____title": "Motif prix", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____lookupDialogTitle": "Sélectionner le site de vente", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____title": "Site de vente", "@sage/xtrem-master-data/pages__item_customer_price_panel__startDate____title": "Date de début", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____lookupDialogTitle": "Sélectionner le site de stock", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____title": "Site stock", "@sage/xtrem-master-data/pages__item_customer_price_panel__toQuantity____title": "Qté fin", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____lookupDialogTitle": "Sélectionner l'unité", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____title": "Unité de mesure", "@sage/xtrem-master-data/pages__item_customer_price_panel__validUnits____title": "Unités valides", "@sage/xtrem-master-data/pages__item_customer_price_view_panel____title": "Consultation tarif", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__idBlock____title": "Code", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__columns__priceReason__name__title": "Priorité", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title___id": "Code", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__customer__id": "Client", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__endDate": "Date fin", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__fromQuantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__id": "Article", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__name": "Article", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__price": "Prix", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__priceReason__name": "Motif prix", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__id": "Site vente", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__name": "Site vente", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__startDate": "Date début", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__id": "Site stock", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__name": "Site stock", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__toQuantity": "Qté fin", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__unit__id": "Unité", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__mainSection____title": "Articles", "@sage/xtrem-master-data/pages__item_price_panel____title": "Prix fournisseur", "@sage/xtrem-master-data/pages__item_price_panel___id____title": "Code", "@sage/xtrem-master-data/pages__item_price_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_price_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__symbol": "Symbole", "@sage/xtrem-master-data/pages__item_price_panel__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__item_price_panel__currency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__dateValid____title": "<PERSON><PERSON> jusqu'au", "@sage/xtrem-master-data/pages__item_price_panel__edit____title": "Modifier le prix fournisseur", "@sage/xtrem-master-data/pages__item_price_panel__fromDate____title": "Date de début", "@sage/xtrem-master-data/pages__item_price_panel__fromQuantity____title": "Quantité début", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title": "Description", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__2": "Décimales", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__3": "Symbole", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__stockUnit__description": "Description", "@sage/xtrem-master-data/pages__item_price_panel__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-master-data/pages__item_price_panel__item____title": "Article", "@sage/xtrem-master-data/pages__item_price_panel__new____title": "Nouveau prix fournisseur", "@sage/xtrem-master-data/pages__item_price_panel__price____title": "Prix", "@sage/xtrem-master-data/pages__item_price_panel__priority____title": "Priorité", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__2": "Code", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__3": "Code", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__item_price_panel__site____title": "Site", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__2": "Code", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__3": "Code", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__2": "Code", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__3": "Symbole", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__4": "Décimales", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__country__name": "Pays", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__currency__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__taxIdNumber": "N° TVA", "@sage/xtrem-master-data/pages__item_price_panel__supplier____lookupDialogTitle": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__item_price_panel__supplier____title": "Fournisseur", "@sage/xtrem-master-data/pages__item_price_panel__toDate____title": "Date de fin", "@sage/xtrem-master-data/pages__item_price_panel__toQuantity____title": "Quantité fin", "@sage/xtrem-master-data/pages__item_price_panel__type____title": "Type", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__symbol": "Symbole", "@sage/xtrem-master-data/pages__item_price_panel__unit____lookupDialogTitle": "Sélectionner l'unité", "@sage/xtrem-master-data/pages__item_price_panel__unit____title": "Unité de mesure", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__averageDailyConsumption__title": "Consommation journalière moyenne", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__batchQuantity__title": "Quantité batch", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__economicOrderQuantity__title": "Lot économique", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__expectedQuantity__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemCategory__title": "Catégorie article", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemDescription__title": "Description article", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemId__title": "Code article", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__preferredProcess__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__postfix": "jours", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__reorderPoint__title": "Point de commande", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__replenishmentMethod__title": "Méthode de réapprovisionnement", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__requiredQuantity__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__safetyStock__title": "Stock de sécurité", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__stdCostValue__title": "Coût standard", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__title__title": "Article", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__valuationMethod__title": "Méthode de valorisation", "@sage/xtrem-master-data/pages__item_site____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__item_site____objectTypePlural": "Articles-site", "@sage/xtrem-master-data/pages__item_site____objectTypeSingular": "Article-site", "@sage/xtrem-master-data/pages__item_site____title": "Article-site", "@sage/xtrem-master-data/pages__item_site___id____title": "Code", "@sage/xtrem-master-data/pages__item_site___valuationMethod____title": "Méthode de valorisation", "@sage/xtrem-master-data/pages__item_site__addItemSiteCost____title": "Ajouter", "@sage/xtrem-master-data/pages__item_site__addSupplier____title": "Ajouter", "@sage/xtrem-master-data/pages__item_site__averageDailyConsumption____title": "Conso journalière moyenne", "@sage/xtrem-master-data/pages__item_site__batchQuantity____title": "Quantité batch", "@sage/xtrem-master-data/pages__item_site__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____lookupDialogTitle": "Sélectionner l'emplacement par défaut", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____title": "Article lancé", "@sage/xtrem-master-data/pages__item_site__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_site__costBlock____title": "Coût", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__costCategoryType": "Type", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__item_site__costCategory____title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title": "Type", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title__2": "Obligatoire", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__costCategory__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__forQuantity": "Quantité", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__fromDate": "Date début", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__isCalculated": "Calculé", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__totalCost": "Coût total", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__unitCost": "Coût unitaire", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__3": "<PERSON><PERSON><PERSON> p<PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____title": "Coûts", "@sage/xtrem-master-data/pages__item_site__costsBlock____title": "Coûts", "@sage/xtrem-master-data/pages__item_site__costSection____title": "Coûts", "@sage/xtrem-master-data/pages__item_site__counting-in-progress": "Comptage en cours", "@sage/xtrem-master-data/pages__item_site__countingInProgress____title": "Comptage en cours", "@sage/xtrem-master-data/pages__item_site__countingInProgressMention____title": "Mention comptage en cours", "@sage/xtrem-master-data/pages__item_site__economicOrderQuantity____title": "Lot économique", "@sage/xtrem-master-data/pages__item_site__edit____title": "Modifier l'article-site", "@sage/xtrem-master-data/pages__item_site__expectedQuantity____title": "Quantité attendue", "@sage/xtrem-master-data/pages__item_site__forQuantity____title": "Quantité", "@sage/xtrem-master-data/pages__item_site__fromDate____title": "Date début", "@sage/xtrem-master-data/pages__item_site__id____title": "Article", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____lookupDialogTitle": "Sélectionner l'emplacement par défaut", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____title": "Entrée", "@sage/xtrem-master-data/pages__item_site__indirectCost____title": "Indirect", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____lookupDialogTitle": "Sélectionner la section de coût indirect", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____title": "Section coût indirect", "@sage/xtrem-master-data/pages__item_site__isCalculated____title": "Calculé", "@sage/xtrem-master-data/pages__item_site__isOrderToOrder____title": "Ordre à la demande", "@sage/xtrem-master-data/pages__item_site__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-master-data/pages__item_site__item____title": "Article", "@sage/xtrem-master-data/pages__item_site__laborCost____title": "Main d'oeuvre", "@sage/xtrem-master-data/pages__item_site__locationBlock____title": "Emplacement par défaut", "@sage/xtrem-master-data/pages__item_site__machineCost____title": "Machine", "@sage/xtrem-master-data/pages__item_site__mainSection____title": "Général", "@sage/xtrem-master-data/pages__item_site__materialCost____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__new____title": "Nouvel article-site", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____lookupDialogTitle": "Sélectionner l'emplacement par défaut", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__preferredProcess____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____postfix": "jour(s)", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____title": "Délai de production", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____postfix": "jour(s)", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__qualityControlBlock____title": "Statut de contrôle qualité par défaut", "@sage/xtrem-master-data/pages__item_site__reorderPoint____title": "Point de commande", "@sage/xtrem-master-data/pages__item_site__replenishmentBlock____title": "Réapprovisionnement", "@sage/xtrem-master-data/pages__item_site__replenishmentMethod____title": "Méthode de réapprovisionnement", "@sage/xtrem-master-data/pages__item_site__replenishmentSection____title": "Réapprovisionnement", "@sage/xtrem-master-data/pages__item_site__requiredQuantity____title": "Quantité demandée", "@sage/xtrem-master-data/pages__item_site__safetyStock____title": "Stock de sécurité", "@sage/xtrem-master-data/pages__item_site__saveItemSite____title": "Enregistrer", "@sage/xtrem-master-data/pages__item_site__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__item_site__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__item_site__stdCostBlock____title": "Coût standard", "@sage/xtrem-master-data/pages__item_site__stdCostValue____title": "Coût standard", "@sage/xtrem-master-data/pages__item_site__stockBlock____title": "Stock", "@sage/xtrem-master-data/pages__item_site__stockRulesSection____title": "Règles de stock", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title___id": "Code", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__isDefaultItemSupplier": "<PERSON><PERSON> <PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__minimumPurchaseOrderQuantity": "Quantité achat mini", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseUnit__name": "Unité", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__id": "Code fournisseur", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__name": "Nom fournisseur", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliers____title": "Fournisseurs", "@sage/xtrem-master-data/pages__item_site__suppliersBlock____title": "Fournisseurs", "@sage/xtrem-master-data/pages__item_site__suppliersSection____title": "Fournisseurs", "@sage/xtrem-master-data/pages__item_site__toDate____title": "Date fin", "@sage/xtrem-master-data/pages__item_site__toolCost____title": "Outil", "@sage/xtrem-master-data/pages__item_site__totalCost____title": "Coût total", "@sage/xtrem-master-data/pages__item_site__unitCost____title": "Coût unitaire", "@sage/xtrem-master-data/pages__item_site__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__listItem__line3__title": "Date de début", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__item_site_cost____objectTypePlural": "Coûts article-site", "@sage/xtrem-master-data/pages__item_site_cost____objectTypeSingular": "Coût article-site", "@sage/xtrem-master-data/pages__item_site_cost____title": "Coût article-site", "@sage/xtrem-master-data/pages__item_site_cost___id____title": "Code", "@sage/xtrem-master-data/pages__item_site_cost__calculate____title": "Calculer", "@sage/xtrem-master-data/pages__item_site_cost__calculateAction____title": "Calculer", "@sage/xtrem-master-data/pages__item_site_cost__chartBlock____title": "Graphique des coûts", "@sage/xtrem-master-data/pages__item_site_cost__costBlock____title": "Détails des coûts", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__costCategoryType": "Type", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__isMandatory": "Obligatoire", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____lookupDialogTitle": "Sélectionner la catégorie de coût", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriod____title": "Nouvelle période", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriodAction____title": "Nouvelle période", "@sage/xtrem-master-data/pages__item_site_cost__forQuantity____title": "Quantité", "@sage/xtrem-master-data/pages__item_site_cost__fromDate____title": "Date début", "@sage/xtrem-master-data/pages__item_site_cost__isCalculated____title": "Calculé", "@sage/xtrem-master-data/pages__item_site_cost__item____title": "Article", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__id": "Code article", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__name": "Nom article", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__status": "Statut article", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__type": "Type article", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site": "Nom site", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site__id": "Code site", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____lookupDialogTitle": "Sélectionner l'article-site", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____title": "Article-site", "@sage/xtrem-master-data/pages__item_site_cost__laborCost____title": "Main d'oeuvre", "@sage/xtrem-master-data/pages__item_site_cost__machineCost____title": "Machine", "@sage/xtrem-master-data/pages__item_site_cost__mainSection____title": "Général", "@sage/xtrem-master-data/pages__item_site_cost__materialCost____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__site____title": "Site", "@sage/xtrem-master-data/pages__item_site_cost__toDate____title": "Date fin", "@sage/xtrem-master-data/pages__item_site_cost__toolCost____title": "Outil", "@sage/xtrem-master-data/pages__item_site_cost__totalCost____title": "Coût total", "@sage/xtrem-master-data/pages__item_site_cost__unitCost____title": "Coût unitaire", "@sage/xtrem-master-data/pages__item_site_cost__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_cost_panel____subtitle": "Volet coût article-site", "@sage/xtrem-master-data/pages__item_site_cost_panel____title": "Volet coût article-site", "@sage/xtrem-master-data/pages__item_site_cost_panel___id____title": "Code", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancelAction____title": "Annuler", "@sage/xtrem-master-data/pages__item_site_cost_panel__chartBlock____title": "Graphique des coûts", "@sage/xtrem-master-data/pages__item_site_cost_panel__costBlock____title": "Détails des coûts", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__costCategoryType": "Type", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__isMandatory": "Obligatoire", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____lookupDialogTitle": "Sélectionner la catégorie de coût", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__costChart____title": "Graphique des coûts", "@sage/xtrem-master-data/pages__item_site_cost_panel__deleteAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__forQuantity____title": "Quantité", "@sage/xtrem-master-data/pages__item_site_cost_panel__fromDate____title": "Date début", "@sage/xtrem-master-data/pages__item_site_cost_panel__invalid-from-date": "La date de début ne peut pas être antérieure à la date du jour.", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__item__id__columns__title__id": "Code", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__item__id__columns__title__symbol": "Symbole", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__site__id__title": "Symbole", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__batchQuantity": "Quantité batch", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__id": "Code article", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__name": "Nom article", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__id": "Code site", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__name": "Nom site", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____lookupDialogTitle": "Sélectionner l'article-site", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____title": "Article-site", "@sage/xtrem-master-data/pages__item_site_cost_panel__laborCost____title": "Main d'oeuvre", "@sage/xtrem-master-data/pages__item_site_cost_panel__machineCost____title": "Machine", "@sage/xtrem-master-data/pages__item_site_cost_panel__materialCost____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__item_site_cost_panel__saveAction____title": "Enregistrer", "@sage/xtrem-master-data/pages__item_site_cost_panel__toDate____title": "Date fin", "@sage/xtrem-master-data/pages__item_site_cost_panel__toolCost____title": "Outil", "@sage/xtrem-master-data/pages__item_site_cost_panel__totalCost____title": "Coût total", "@sage/xtrem-master-data/pages__item_site_cost_panel__unitCost____title": "Coût unitaire", "@sage/xtrem-master-data/pages__item_site_cost_panel__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_supplier____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypePlural": "Article-site-fournisseurs", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypeSingular": "Article-site-fournisseur", "@sage/xtrem-master-data/pages__item_site_supplier____title": "Article-site-fournisseur", "@sage/xtrem-master-data/pages__item_site_supplier___id____title": "Code", "@sage/xtrem-master-data/pages__item_site_supplier__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_site_supplier__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_site_supplier__createItemSiteSupplier____title": "Nouveau", "@sage/xtrem-master-data/pages__item_site_supplier__deleteItemSiteSupplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_supplier__identificationBlock____title": "Identification", "@sage/xtrem-master-data/pages__item_site_supplier__isDefaultItemSupplier____title": "Article-site-fournisseur par défaut", "@sage/xtrem-master-data/pages__item_site_supplier__itemDetailsBlock____title": "Détails article", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__id": "Code article", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__name": "Nom article", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__status": "Statut article", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__type": "Type article", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site": "Nom site", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site__id": "Code site", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____lookupDialogTitle": "Sélectionner l'article-site", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____title": "Article-site", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__country__name": "Pays", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__id": "Code", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__name": "Nom", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__taxIdNumber": "N° TVA", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____lookupDialogTitle": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____title": "Fournisseur", "@sage/xtrem-master-data/pages__item_site_supplier__mainSection____title": "Général", "@sage/xtrem-master-data/pages__item_site_supplier__minimumPurchaseOrderQuantity____title": "Quantité d'achat minimum", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____postfix": "jours", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseUnit____lookupDialogTitle": "Sélectionner l'unité d'achat", "@sage/xtrem-master-data/pages__item_site_supplier__saveItemSiteSupplier____title": "Enregistrer", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__country__name": "Pays", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__id": "Code", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name": "Nom", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name__2": "Code", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__taxIdNumber": "N° TVA", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____lookupDialogTitle": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__item_supplier__item_cost_edit____title": "Modifier le coût de l'article-site", "@sage/xtrem-master-data/pages__item_supplier__item_cost_new____title": "Nouveau coût article-site", "@sage/xtrem-master-data/pages__item_supplier__item_edit____title": "Modifier l'article-site-fournisseur", "@sage/xtrem-master-data/pages__item_supplier__item_new____title": "Nouvel article-site-fournisseur", "@sage/xtrem-master-data/pages__item_supplier__purchaseUnitOfMeasure____columns__title__id": "Code", "@sage/xtrem-master-data/pages__item_supplier_price_panel____title": "Prix fournisseur", "@sage/xtrem-master-data/pages__item_supplier_price_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_supplier_price_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValid____title": "Date validité", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidFrom____title": "Date début", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidTo____title": "Taxe début", "@sage/xtrem-master-data/pages__item_supplier_price_panel__fromQuantity____title": "Quantité début", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____title": "Article", "@sage/xtrem-master-data/pages__item_supplier_price_panel__price____title": "Prix", "@sage/xtrem-master-data/pages__item_supplier_price_panel__priority____title": "Priorité", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__country__name": "Pays", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__id": "Code", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__name": "Nom", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__taxIdNumber": "N° TVA", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____lookupDialogTitle": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__item_supplier_price_panel__toQuantity____title": "Quantité fin", "@sage/xtrem-master-data/pages__item_supplier_price_panel__type____title": "Type", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____lookupDialogTitle": "Sélectionner l'unité", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____title": "Unité de mesure", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel____title": "Consultation tarif", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel___id____title": "Code", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__idBlock____title": "Code", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidFrom": "Date de début de validité", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidTo": "<PERSON><PERSON> jusqu'au", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__fromQuantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__site__id": "Site", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__toQuantity": "Qté fin", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__mainSection____title": "Articles", "@sage/xtrem-master-data/pages__item-customer_panel__conversion_negative_value": "La conversion vente en stock doit être supérieure à 0.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_less_than_minimum_value": "La quantité de vente maximum doit être supérieure à la quantité de vente minimum.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_negative_value": "La quantité de vente maximum ne doit pas être inférieure à 0.", "@sage/xtrem-master-data/pages__item-customer_panel__minimum_quantity_negative_value": "La quantité de vente minimum ne doit pas être inférieure à 0.", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_buy_to_order": "Achat à la demande", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_make_to_order": "Fabrication à la commande", "@sage/xtrem-master-data/pages__item-site__preferred_process_cannot_be": "Un article géré en stock doit être fabriqué, acheté ou les deux.", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeFrom__title": "Début d'activité", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeTo__title": "Fin d'activité", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__resourceGroup__title": "Groupe de ressources", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__weeklyShift__title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__labor_resource____objectTypePlural": "Ressources main d'oeuvre", "@sage/xtrem-master-data/pages__labor_resource____objectTypeSingular": "Ressource main d'oeuvre", "@sage/xtrem-master-data/pages__labor_resource____title": "Ressource main d'oeuvre", "@sage/xtrem-master-data/pages__labor_resource___id____title": "Code", "@sage/xtrem-master-data/pages__labor_resource__activeFrom____title": "Début d'activité", "@sage/xtrem-master-data/pages__labor_resource__activeTo____title": "Fin d'activité", "@sage/xtrem-master-data/pages__labor_resource__addCapability____title": "Ajouter", "@sage/xtrem-master-data/pages__labor_resource__addCostCategory____title": "A<PERSON>ter caté<PERSON>ie <PERSON>", "@sage/xtrem-master-data/pages__labor_resource__blockCapabilities____title": "Aptitudes", "@sage/xtrem-master-data/pages__labor_resource__blockDetails____title": "Réglages", "@sage/xtrem-master-data/pages__labor_resource__blockWeekly____title": "Détails horaires hebdomadaires", "@sage/xtrem-master-data/pages__labor_resource__cancelAction____title": "Annuler", "@sage/xtrem-master-data/pages__labor_resource__cancelSidePanel____title": "Annuler", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__capabilityLevel__name": "Niveau aptitude", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateEndValid": "Date fin", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateStartValid": "Date début", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__machine__name": "Machine", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__service__name": "Service", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__tool__name": "Outil", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title__2": "Modifier", "@sage/xtrem-master-data/pages__labor_resource__capabilities____title": "Aptitudes", "@sage/xtrem-master-data/pages__labor_resource__capabilitiesSection____title": "Aptitudes", "@sage/xtrem-master-data/pages__labor_resource__costBlock____title": "Coût", "@sage/xtrem-master-data/pages__labor_resource__costSection____title": "Coût", "@sage/xtrem-master-data/pages__labor_resource__description____title": "Description", "@sage/xtrem-master-data/pages__labor_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__labor_resource__efficiency____title": "Efficience", "@sage/xtrem-master-data/pages__labor_resource__fullWeek____title": "24 h / 24", "@sage/xtrem-master-data/pages__labor_resource__id____title": "Code", "@sage/xtrem-master-data/pages__labor_resource__isActive____title": "Active", "@sage/xtrem-master-data/pages__labor_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__labor_resource__location____lookupDialogTitle": "Sélectionner l'emplacement", "@sage/xtrem-master-data/pages__labor_resource__location____title": "Emplacement", "@sage/xtrem-master-data/pages__labor_resource__name____title": "Nom", "@sage/xtrem-master-data/pages__labor_resource__resourceCapacity____title": "Capacité hebdomadaire", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Type catégorie coût", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoire", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costUnit__name__title__3": "Symbole", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costCategory__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costUnit__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coût indirect", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__runCost": "Opération", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__setupCost": "Réglage", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____title": "Catégories coûts ressources", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____lookupDialogTitle": "Sélectionner le groupe de ressources", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____title": "Groupe de ressources", "@sage/xtrem-master-data/pages__labor_resource__resourceImage____title": "Image", "@sage/xtrem-master-data/pages__labor_resource__saveAction____title": "Enregistrer", "@sage/xtrem-master-data/pages__labor_resource__saveSidePanel____title": "Enregistrer", "@sage/xtrem-master-data/pages__labor_resource__section____title": "Général", "@sage/xtrem-master-data/pages__labor_resource__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__labor_resource__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__capacity": "Capacité", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__dailyShift": "Horaires journaliers", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__day": "Jour", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift1": "Équipe 1", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift2": "Équipe 2", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift3": "Équipe 3", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift4": "Équipe 4", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift5": "Équipe 5", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____title": "Détails he<PERSON>", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____columns__title__formattedCapacity": "Capacité", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____lookupDialogTitle": "Sélectionner les horaires hebdomadaires", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__postfix": "kg", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__title": "Capacité utilisée", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isInternalIcon__title": "Interne", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isSingleLot__title": "Lot unique", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__line2__title": "Contenant", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__location__title": "Emplacement", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__sSingleItem__title": "Article unique", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__titleRight__title": "Type", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__license_plate_number____objectTypePlural": "Numéros de contenants internes", "@sage/xtrem-master-data/pages__license_plate_number____objectTypeSingular": "Numéro de contenant interne", "@sage/xtrem-master-data/pages__license_plate_number____title": "N° contenant interne", "@sage/xtrem-master-data/pages__license_plate_number___id____title": "Code", "@sage/xtrem-master-data/pages__license_plate_number__consumedCapacity____title": "Capacité utilisée", "@sage/xtrem-master-data/pages__license_plate_number__container____columns__title__isInternal": "Interne", "@sage/xtrem-master-data/pages__license_plate_number__container____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le conteneur", "@sage/xtrem-master-data/pages__license_plate_number__container____title": "Contenant", "@sage/xtrem-master-data/pages__license_plate_number__containerType____title": "Type de contenant", "@sage/xtrem-master-data/pages__license_plate_number__isInternal____title": "Interne", "@sage/xtrem-master-data/pages__license_plate_number__isSingleItem____title": "Article unique", "@sage/xtrem-master-data/pages__license_plate_number__isSingleLot____title": "Lot unique", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__locationType__id__title": "Nom", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__locationType__id__title__2": "Code", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__site__name__title": "Nom", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__site__name__title__2": "Code", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__license_plate_number__location____lookupDialogTitle": "Sélectionner l'emplacement", "@sage/xtrem-master-data/pages__license_plate_number__location____title": "Emplacement", "@sage/xtrem-master-data/pages__license_plate_number__locationSite____title": "Site", "@sage/xtrem-master-data/pages__license_plate_number__locationType____title": "Type d'emplacement", "@sage/xtrem-master-data/pages__license_plate_number__mainSection____title": "Général", "@sage/xtrem-master-data/pages__license_plate_number__mass_update__success": "Numéro contenant interne mis à jour.", "@sage/xtrem-master-data/pages__license_plate_number__number____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__id": "Code", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__name": "Nom", "@sage/xtrem-master-data/pages__license_plate_number__owner____lookupDialogTitle": "Sélectionner le propriétaire", "@sage/xtrem-master-data/pages__license_plate_number__owner____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation____title": "Numéros de contenants internes en masse", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__cancelLicensePlateNumbers____title": "Annuler", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleItem": "Article unique", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleLot": "Lot unique", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__sequenceNumber__id": "Compteur", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le conteneur", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____title": "Contenant", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleItem____title": "Article unique", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleLot____title": "Lot unique", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__columns__title__id": "Code", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__columns__title__name": "Nom", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__title": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleItem": "Article unique", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleLot": "Lot unique", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__locationType__name": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__name": "Emplacement", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__site__name": "Site", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____title": "Numéros de contenants internes", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbersBlock____title": "Numéros de contenants internes", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____columns__title__locationType__name": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____lookupDialogTitle": "Sélectionner l'emplacement", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____title": "Emplacement", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationSite____title": "Site", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationType____title": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__mainSection____title": "Général", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__numberToCreate____title": "N° contenants à générer", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__saveLicensePlateNumbers____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__searchButton____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__dangerousGoodAllowed__title": "Matières dangereuses autorisées", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationType__title": "Type", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationZone__title": "Zone", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__location____objectTypePlural": "Emplacements", "@sage/xtrem-master-data/pages__location____objectTypeSingular": "Emplacement", "@sage/xtrem-master-data/pages__location____title": "Emplacement", "@sage/xtrem-master-data/pages__location__dangerousGoodAllowed____title": "Matières dangereuses autorisées", "@sage/xtrem-master-data/pages__location__id____title": "Code", "@sage/xtrem-master-data/pages__location__locationType____columns__title__locationCategory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location__locationType____lookupDialogTitle": "Sélectionner le type d'emplacement", "@sage/xtrem-master-data/pages__location__locationType____title": "Type", "@sage/xtrem-master-data/pages__location__locationZone____columns__title__zoneType": "Type", "@sage/xtrem-master-data/pages__location__locationZone____lookupDialogTitle": "Sélectionner la zone de stockage", "@sage/xtrem-master-data/pages__location__locationZone____title": "Zone", "@sage/xtrem-master-data/pages__location__name____title": "Nom", "@sage/xtrem-master-data/pages__location__section____title": "Général", "@sage/xtrem-master-data/pages__location__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__location__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__location_mass_creation____title": "Emplacements en masse", "@sage/xtrem-master-data/pages__location_mass_creation__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__location_mass_creation__cancelAction____title": "Annuler", "@sage/xtrem-master-data/pages__location_mass_creation__createLocations____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationType__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationZone__name__title": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationType__name": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationZone__name": "Zone", "@sage/xtrem-master-data/pages__location_mass_creation__locations____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locationsBlock____title": "Emplacements", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__lastSequenceUsed": "<PERSON><PERSON> compteur utilisé", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberLocations": "Allocations totales", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberLocationsRemaining": "Alloc. restantes", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberOfCombinations": "Allocations totales", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le compteur d'emplacement", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____title": "Compteur emplacement", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____columns__title__locationCategory": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____lookupDialogTitle": "Sélectionner le type d'emplacement", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____title": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____columns__title__zoneType": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____lookupDialogTitle": "Sélectionner la zone de stockage", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____title": "Zone", "@sage/xtrem-master-data/pages__location_mass_creation__mainSection____title": "Général", "@sage/xtrem-master-data/pages__location_mass_creation__requiredCombinations____title": "Combinaisons demandées", "@sage/xtrem-master-data/pages__location_mass_creation__searchButton____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__location_mass_creation__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__location_mass_creation__site____placeholder": "Sélectionner...", "@sage/xtrem-master-data/pages__location_mass_creation__site____title": "Site", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__componentLength__title": "<PERSON><PERSON>ur compteur", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__counterLength__title": "<PERSON><PERSON><PERSON> de compteur", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__numberLocations__title": "Nombre d'emplacements", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__numberOfCombinations__title": "Nombre d'emplacements", "@sage/xtrem-master-data/pages__location_sequence____objectTypePlural": "Compteur emplacement", "@sage/xtrem-master-data/pages__location_sequence____objectTypeSingular": "Compteur emplacement", "@sage/xtrem-master-data/pages__location_sequence____title": "Compteur emplacement", "@sage/xtrem-master-data/pages__location_sequence___id____title": "Code", "@sage/xtrem-master-data/pages__location_sequence__addComponent____title": "Ajouter", "@sage/xtrem-master-data/pages__location_sequence__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__location_sequence__cancelAction____title": "Annuler", "@sage/xtrem-master-data/pages__location_sequence__capital_letters_only": "Un composant de compteur alphabétique peut uniquement contenir des lettres majuscules.", "@sage/xtrem-master-data/pages__location_sequence__componentLength____title": "<PERSON><PERSON>ur compteur", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__endValue": "Valeur fin", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__startValue": "<PERSON><PERSON> début", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__type": "Type", "@sage/xtrem-master-data/pages__location_sequence__components____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__components____title": "Composants", "@sage/xtrem-master-data/pages__location_sequence__componentsBlock____title": "Composants", "@sage/xtrem-master-data/pages__location_sequence__constant_invalid_length": "Vérifiez la longueur de la valeur de la constante.", "@sage/xtrem-master-data/pages__location_sequence__counterLength____title": "<PERSON><PERSON><PERSON> de compteur", "@sage/xtrem-master-data/pages__location_sequence__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__deleteAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__digits_only": "Un composant de compteur numérique peut uniquement contenir des numéros.", "@sage/xtrem-master-data/pages__location_sequence__id____title": "Code", "@sage/xtrem-master-data/pages__location_sequence__invalid_range": "La valeur de départ ne peut pas être supérieure à la valeur de fin.", "@sage/xtrem-master-data/pages__location_sequence__mainBlock____title": "Détails", "@sage/xtrem-master-data/pages__location_sequence__mainSection____title": "Général", "@sage/xtrem-master-data/pages__location_sequence__name____title": "Nom", "@sage/xtrem-master-data/pages__location_sequence__numberLocations____title": "Nombre d'emplacements", "@sage/xtrem-master-data/pages__location_sequence__numberOfCombinations____title": "Nombre d'emplacements", "@sage/xtrem-master-data/pages__location_sequence__saveAction____title": "Enregistrer", "@sage/xtrem-master-data/pages__location_sequence__saveLocationSequence____title": "Enregistrer", "@sage/xtrem-master-data/pages__location_type____navigationPanel__listItem__locationCategory__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__2": "Interne", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__3": "Quai", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__4": "Client", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__5": "Sous-traitance", "@sage/xtrem-master-data/pages__location_type____objectTypePlural": "Types d'emplacement", "@sage/xtrem-master-data/pages__location_type____objectTypeSingular": "Type d'emplacement", "@sage/xtrem-master-data/pages__location_type____title": "Type d'emplacement", "@sage/xtrem-master-data/pages__location_type__description____title": "Description", "@sage/xtrem-master-data/pages__location_type__id____title": "Code", "@sage/xtrem-master-data/pages__location_type__locationCategory____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_type__mainBlock____title": "Détails", "@sage/xtrem-master-data/pages__location_type__mainSection____title": "Général", "@sage/xtrem-master-data/pages__location_type__name____title": "Nom", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__zoneType__title": "Type", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__2": "Frigorifié", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__3": "Sensible", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__4": "Sécurisé", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__5": "Restreint", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__6": "Danger", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__7": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__8": "Magnétique", "@sage/xtrem-master-data/pages__location_zone____objectTypePlural": "Zones de stockage", "@sage/xtrem-master-data/pages__location_zone____objectTypeSingular": "Zone de stockage", "@sage/xtrem-master-data/pages__location_zone____title": "Zone de stockage", "@sage/xtrem-master-data/pages__location_zone__id____title": "Code", "@sage/xtrem-master-data/pages__location_zone__name____title": "Nom", "@sage/xtrem-master-data/pages__location_zone__section____title": "Général", "@sage/xtrem-master-data/pages__location_zone__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__location_zone__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__location_zone__zoneType____title": "Type", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeFrom__title": "Début d'activité", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeTo__title": "Fin d'activité", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__title": "Efficience", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__location__title": "Emplacement", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__minCapabilityLevel__title": "Niveau d'aptitude", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__model__title": "Mod<PERSON><PERSON> de machine", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__resourceGroup__title": "Groupe de ressources", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__serialNumber__title": "N° série de machine", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__weeklyShift__title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__machine_resource____objectTypePlural": "Ressources machine", "@sage/xtrem-master-data/pages__machine_resource____objectTypeSingular": "Ressource machine", "@sage/xtrem-master-data/pages__machine_resource____title": "Ressource machine", "@sage/xtrem-master-data/pages__machine_resource___id____title": "Code", "@sage/xtrem-master-data/pages__machine_resource__activeFrom____title": "Début d'activité", "@sage/xtrem-master-data/pages__machine_resource__activeTo____title": "Fin d'activité", "@sage/xtrem-master-data/pages__machine_resource__addCostCategory____title": "A<PERSON>ter caté<PERSON>ie <PERSON>", "@sage/xtrem-master-data/pages__machine_resource__blockContract____title": "Contrat", "@sage/xtrem-master-data/pages__machine_resource__blockDetails____title": "Réglages", "@sage/xtrem-master-data/pages__machine_resource__blockWeekly____title": "Détails horaires hebdomadaires", "@sage/xtrem-master-data/pages__machine_resource__cancelAction____title": "Annuler", "@sage/xtrem-master-data/pages__machine_resource__cancelSidePanel____title": "Annuler", "@sage/xtrem-master-data/pages__machine_resource__contractId____title": "Code", "@sage/xtrem-master-data/pages__machine_resource__contractName____title": "Nom", "@sage/xtrem-master-data/pages__machine_resource__contractSection____title": "Contrat", "@sage/xtrem-master-data/pages__machine_resource__costBlock____title": "Coût", "@sage/xtrem-master-data/pages__machine_resource__costSection____title": "Coût", "@sage/xtrem-master-data/pages__machine_resource__description____title": "Description", "@sage/xtrem-master-data/pages__machine_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__machine_resource__efficiency____title": "Efficience", "@sage/xtrem-master-data/pages__machine_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__machine_resource__id____title": "Code", "@sage/xtrem-master-data/pages__machine_resource__isActive____title": "Active", "@sage/xtrem-master-data/pages__machine_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__machine_resource__location____lookupDialogTitle": "Sélectionner l'emplacement", "@sage/xtrem-master-data/pages__machine_resource__location____title": "Emplacement", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____columns__title__description": "Description", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____lookupDialogTitle": "Sé<PERSON><PERSON><PERSON> le niveau d'aptitude", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____title": "Niveau d'aptitude", "@sage/xtrem-master-data/pages__machine_resource__model____title": "Mod<PERSON><PERSON> de machine", "@sage/xtrem-master-data/pages__machine_resource__name____title": "Nom", "@sage/xtrem-master-data/pages__machine_resource__resourceCapacity____title": "Capacité hebdomadaire", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Type catégorie coût", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoire", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costCategory__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costUnit__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coût indirect", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__runCost": "Opération", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__setupCost": "Réglage", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____title": "Catégories coûts ressources", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____lookupDialogTitle": "Sélectionner le groupe de ressources", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____title": "Groupe de ressources", "@sage/xtrem-master-data/pages__machine_resource__resourceImage____title": "Image", "@sage/xtrem-master-data/pages__machine_resource__saveAction____title": "Enregistrer", "@sage/xtrem-master-data/pages__machine_resource__section____title": "Général", "@sage/xtrem-master-data/pages__machine_resource__serialNumber____title": "N° série de machine", "@sage/xtrem-master-data/pages__machine_resource__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__machine_resource__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__country": "Pays", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__id": "Code", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__name": "Nom", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__taxIdNumber": "N° TVA", "@sage/xtrem-master-data/pages__machine_resource__supplier____lookupDialogTitle": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__machine_resource__weeklyDetails____columns__title__dailyShift": "Horaires journaliers", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____columns__title__formattedCapacity": "Capacité", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____lookupDialogTitle": "Sélectionner les horaires hebdomadaires", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__master_data__cancel": "Annuler", "@sage/xtrem-master-data/pages__master_data__confirm": "Confirmer", "@sage/xtrem-master-data/pages__master_data__update": "Mettre à jour", "@sage/xtrem-master-data/pages__master_data__warning-dialog-content": "Vous êtes sur le point d'actualiser la valeur du compteur.", "@sage/xtrem-master-data/pages__multiple__location__creation__success_multi": "{{num}} emplacement(s) créé(s)", "@sage/xtrem-master-data/pages__multiple_location_creation__success": "{{num}} emplacement créé", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__listItem__line3__title": "Type d'entité commerciale", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__payment_term____objectTypePlural": "Conditions de paiement", "@sage/xtrem-master-data/pages__payment_term____objectTypeSingular": "Condition de paiement", "@sage/xtrem-master-data/pages__payment_term____title": "Condition de paiement", "@sage/xtrem-master-data/pages__payment_term___id____title": "Code", "@sage/xtrem-master-data/pages__payment_term__amount": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__payment_term__blockDiscount____title": "Escompt<PERSON>", "@sage/xtrem-master-data/pages__payment_term__blockDue____title": "Date d'échéance", "@sage/xtrem-master-data/pages__payment_term__blockPenalty____title": "Pénalité", "@sage/xtrem-master-data/pages__payment_term__businessEntityType____title": "Type entité commerciale", "@sage/xtrem-master-data/pages__payment_term__days____title": "Nombre de jours", "@sage/xtrem-master-data/pages__payment_term__description____title": "Description", "@sage/xtrem-master-data/pages__payment_term__discountDate____title": "Jour", "@sage/xtrem-master-data/pages__payment_term__discountFrom____title": "De", "@sage/xtrem-master-data/pages__payment_term__discountType____title": "Type", "@sage/xtrem-master-data/pages__payment_term__dueDateType____title": "Type", "@sage/xtrem-master-data/pages__payment_term__id____title": "Code", "@sage/xtrem-master-data/pages__payment_term__name____title": "Nom", "@sage/xtrem-master-data/pages__payment_term__penaltyType____title": "Type", "@sage/xtrem-master-data/pages__payment_term__percentage": "Pourcentage", "@sage/xtrem-master-data/pages__payment_term__section____title": "Général", "@sage/xtrem-master-data/pages__reason_code____objectTypePlural": "Codes motif", "@sage/xtrem-master-data/pages__reason_code____objectTypeSingular": "Code motif", "@sage/xtrem-master-data/pages__reason_code____title": "Code motif", "@sage/xtrem-master-data/pages__reason_code__id____title": "Code", "@sage/xtrem-master-data/pages__reason_code__isActive____title": "Actif", "@sage/xtrem-master-data/pages__reason_code__mainSection____title": "Général", "@sage/xtrem-master-data/pages__reason_code__name____title": "Nom", "@sage/xtrem-master-data/pages__request_approval_dialog____title": "Demande d'approbation", "@sage/xtrem-master-data/pages__request_approval_dialog__approverSelectionBlock____title": "Sélectionner l'approbateur", "@sage/xtrem-master-data/pages__request_approval_dialog__default_approver": "<PERSON><PERSON> <PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__email_exception_request": "", "@sage/xtrem-master-data/pages__request_approval_dialog__email_not_sent": "", "@sage/xtrem-master-data/pages__request_approval_dialog__email_sent_to_approval": "", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____helperText": "Un e-mail de demande d'approbation sera envoyé à cette adresse.", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____title": "À", "@sage/xtrem-master-data/pages__request_approval_dialog__invalid-email": "Adresse e-mail incorrecte :{{value}}", "@sage/xtrem-master-data/pages__request_approval_dialog__requestApprovalSection____title": "Demande d'approbation", "@sage/xtrem-master-data/pages__request_approval_dialog__selectApprover____title": "Sélectionner l'approbateur", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__email": "E-mail", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__firstName": "Nom", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__lastName": "Nom", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____lookupDialogTitle": "Utiliser l'utilisateur sélectionné", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____title": "Utilisateur sélectionné", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_confirm_title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_content": "", "@sage/xtrem-master-data/pages__request_approval_dialog__sendApprovalRequestButton____title": "Envoyer", "@sage/xtrem-master-data/pages__request_approval_dialog__substitute_approver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__email": "E-mail", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__firstName": "Nom", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__lastName": "Nom", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__type": "Approbateur", "@sage/xtrem-master-data/pages__request_approval_dialog__users____title": "Utilisateurs", "@sage/xtrem-master-data/pages__resource_functions__duration_in_hours_and_minutes": "{{hours}} heures {{minutes}} minutes", "@sage/xtrem-master-data/pages__resource_group_transfer____title": "Transfert groupe ressources", "@sage/xtrem-master-data/pages__resource_group_transfer__block____title": "Code", "@sage/xtrem-master-data/pages__resource_group_transfer__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__resource_group_transfer__confirm____title": "Enregistrer", "@sage/xtrem-master-data/pages__resource_group_transfer__mainSection____title": "Général", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____columns__title__weeklyShift__id": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____lookupDialogTitle": "Sélectionner la ressource", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____title": "Ressource", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____columns__title__weeklyShift__id": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____lookupDialogTitle": "Sélectionner le groupe de ressources", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____title": "Groupe de ressources", "@sage/xtrem-master-data/pages__resource_group_transfer__type____title": "Type groupe ressource", "@sage/xtrem-master-data/pages__select_sold_to_contact_button_text": "Sélectionner contact client commande", "@sage/xtrem-master-data/pages__send_button_text": "Envoyer", "@sage/xtrem-master-data/pages__send_email_panel____title": "Envoyer devis de vente", "@sage/xtrem-master-data/pages__send_email_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____helperText": "Un e-mail sera envoyé à cette adresse.", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____title": "E-mail", "@sage/xtrem-master-data/pages__send_email_panel__emailFirstName____title": "Prénom", "@sage/xtrem-master-data/pages__send_email_panel__emailLastName____title": "Nom", "@sage/xtrem-master-data/pages__send_email_panel__emailTitles____title": "Civilité", "@sage/xtrem-master-data/pages__send_email_panel__selectSoldToContact____title": "Sélectionner contact client commande", "@sage/xtrem-master-data/pages__send_email_panel__sendEmailBlock____title": "À", "@sage/xtrem-master-data/pages__send_email_panel__sendSalesOrderButton____title": "Envoyer commande", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_4__title": "Niveau de définition", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_5__title": "Fréquence de réinitialisation", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line4__title": "Niveau de définition", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line5__title": "Réinitialiser la fréquence", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line6__title": "Type", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line7__title": "Contrôle chronologique", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__sequence_number____objectTypePlural": "Compteurs", "@sage/xtrem-master-data/pages__sequence_number____objectTypeSingular": "Compteur", "@sage/xtrem-master-data/pages__sequence_number____title": "Compteur", "@sage/xtrem-master-data/pages__sequence_number__addComponent____title": "A<PERSON>ter composant", "@sage/xtrem-master-data/pages__sequence_number__chronologicalControl____title": "Contrôle chronologique", "@sage/xtrem-master-data/pages__sequence_number__componentLength____title": "<PERSON><PERSON><PERSON> de compteur", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__type": "Type", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title__2": "Composant", "@sage/xtrem-master-data/pages__sequence_number__components____title": "Composants", "@sage/xtrem-master-data/pages__sequence_number__componentsBlock____title": "Composants", "@sage/xtrem-master-data/pages__sequence_number__counterLength____title": "<PERSON><PERSON>ur compteur", "@sage/xtrem-master-data/pages__sequence_number__createAction____title": "Nouveau", "@sage/xtrem-master-data/pages__sequence_number__createValue____title": "<PERSON><PERSON><PERSON> valeur de d<PERSON>", "@sage/xtrem-master-data/pages__sequence_number__definitionLevel____title": "Niveau de définition", "@sage/xtrem-master-data/pages__sequence_number__delete____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__id____title": "Code", "@sage/xtrem-master-data/pages__sequence_number__isChronological____title": "Contrôle chronologique", "@sage/xtrem-master-data/pages__sequence_number__isClearedByReset____title": "Forcer la réinitialisation avec le tenant", "@sage/xtrem-master-data/pages__sequence_number__isUsed____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-master-data/pages__sequence_number__mainSection____title": "Général", "@sage/xtrem-master-data/pages__sequence_number__name____title": "Nom", "@sage/xtrem-master-data/pages__sequence_number__propertiesBlock____title": "<PERSON><PERSON><PERSON> du compteur", "@sage/xtrem-master-data/pages__sequence_number__resetBlock____title": "Réinitialiser détails", "@sage/xtrem-master-data/pages__sequence_number__resetToZero____title": "Réinitialiser à zéro", "@sage/xtrem-master-data/pages__sequence_number__rtzLevel____title": "Fréquence de réinitialisation", "@sage/xtrem-master-data/pages__sequence_number__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__sequence_number__saveAction____title": "Enregistrer", "@sage/xtrem-master-data/pages__sequence_number__sequence____title": "Type de compteur", "@sage/xtrem-master-data/pages__sequence_number__sequenceNumberType____title": "Type", "@sage/xtrem-master-data/pages__sequence_number__type____title": "Type", "@sage/xtrem-master-data/pages__sequence_number__updateValue____title": "Actualiser valeur", "@sage/xtrem-master-data/pages__sequence_number_assignment_filter": "Filtre", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup____title": "Attribution de compteurs", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__addEditAssignmentLine____title": "Ajouter", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____title": "Société", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__isDefaultAssignment____title": "Inclure les valeurs par défaut", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____title": "Législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__lineBlock____title": "Critères", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__resultsBlock____title": "Résultats", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__company__name": "Société", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isDefaultAssignment": "Valeur par défaut", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__legislation__name": "Législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__name": "Document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__title": "Document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__sequenceNumber__name": "Compteur", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__site__name": "Site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____title": "Résultats", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____placeholder": "Sélectionner le site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____title": "Site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel____title": "Volet d'attribution de compteurs", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title__2": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title__3": "Nom", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__title__legislation__id": "Législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____placeholder": "Sélectionner la société", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____title": "Société", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__edit____title": "Modifier l'attribution de compteurs", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isAssignOnPosting____title": "Attribuer à la comptabilisation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isUsed____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____lookupDialogTitle": "Sélectionner la législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____placeholder": "Sélectionner la législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____title": "Législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__new____title": "Nouvelle attribution de compteur", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title__2": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title__3": "Nom", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__id": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__legislation__id": "Législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> le compteur", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____title": "Compteur", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__columns__sequenceNumberAssignmentModule__id__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__node": "Node", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__nodeFactory__name": "Document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__sequenceNumberAssignmentModule__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__setupId": "Code paramétrage", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____lookupDialogTitle": "Sélectionner le type de document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le compteur", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____title": "Type de document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title___id": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title__id": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title__name": "Nom", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title": "Législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__2": "Code", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__3": "Nom", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__4": "Législation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__title__legalCompany__id": "Société", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____placeholder": "Sélectionner le site", "@sage/xtrem-master-data/pages__sequence_number_dialog____title": "Actualiser la valeur du compteur", "@sage/xtrem-master-data/pages__sequence_number_dialog__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__sequence_number_dialog__definitionLevel____title": "Niveau de définition", "@sage/xtrem-master-data/pages__sequence_number_dialog__length____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_dialog__rtzLevel____title": "Réinitialiser la fréquence", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumber____title": "Compteur", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title___updateStamp": "<PERSON><PERSON><PERSON> mise à jour le", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__company__id": "Code société", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__company__name": "Société", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__newNextValue": "Nouvelle prochaine valeur", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__period": "Période", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__sequenceValue": "Nouvelle valeur", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__site__id": "Code site", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__site__name": "Site", "@sage/xtrem-master-data/pages__sequence_number_dialog__update____title": "Mettre à jour", "@sage/xtrem-master-data/pages__sequence_number_dialog_records_updated": "Enregistrement mis à jour.", "@sage/xtrem-master-data/pages__sequence_number_value____title": "<PERSON><PERSON><PERSON> la valeur de compteur", "@sage/xtrem-master-data/pages__sequence_number_value__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__sequence_number_value__confirm_action_dialog_content": "Vous êtes sur le point de créer la valeur de compteur.", "@sage/xtrem-master-data/pages__sequence_number_value__confirm-continue": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_value__definitionLevel____title": "Niveau de définition", "@sage/xtrem-master-data/pages__sequence_number_value__id____title": "Compteur", "@sage/xtrem-master-data/pages__sequence_number_value__minimumLength____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_value__rtzLevel____title": "Fréquence de réinitialisation", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title___updateStamp": "<PERSON><PERSON><PERSON> mise à jour le", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__id": "Code société", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__name": "Société", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__newNextValue": "Nouvelle prochaine valeur", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__periodDate": "Date période", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__sequenceValue": "Prochaine valeur", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__id": "Code site", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__name": "Site", "@sage/xtrem-master-data/pages__sequence_number_value_confirm_action_dialog_title": "Confirmer", "@sage/xtrem-master-data/pages__sequence-number_value_add_new____title": "<PERSON><PERSON><PERSON> la valeur de compteur", "@sage/xtrem-master-data/pages__sequence-number_value_create____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence-number_value_edit____title": "Mettre à jour la valeur", "@sage/xtrem-master-data/pages__sequence-number_value_update____title": "Mettre à jour", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftEnd__title": "Heure de fin", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftStart__title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__shift_detail____objectTypePlural": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/pages__shift_detail____objectTypeSingular": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/pages__shift_detail____title": "<PERSON><PERSON><PERSON> horaires", "@sage/xtrem-master-data/pages__shift_detail__formattedDuration____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__shift_detail__id____title": "Code", "@sage/xtrem-master-data/pages__shift_detail__mainBlock____title": "Détails", "@sage/xtrem-master-data/pages__shift_detail__mainSection____title": "Général", "@sage/xtrem-master-data/pages__shift_detail__name____title": "Nom", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____title": "Heure de fin", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____title": "<PERSON><PERSON> d<PERSON>", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line_4__title": "Société", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line10__title": "Site financier", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__sequenceNumber__title": "<PERSON><PERSON> de compteur", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__taxId__title": "N° TVA", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__site____objectTypePlural": "Sites", "@sage/xtrem-master-data/pages__site____objectTypeSingular": "Site", "@sage/xtrem-master-data/pages__site____title": "Site", "@sage/xtrem-master-data/pages__site__addressAndContactBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addresses____addButtonText": "A<PERSON><PERSON> adresse", "@sage/xtrem-master-data/pages__site__addresses____columns__title__concatenatedAddressWithoutName": "Adresse sans nom", "@sage/xtrem-master-data/pages__site__addresses____columns__title__isPrimary": "Adresse principale", "@sage/xtrem-master-data/pages__site__addresses____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title": "Modifier l'adresse", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__2": "Définir comme adresse principale", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__3": "A<PERSON>ter le contact", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__4": "Supprimer adresses et contacts", "@sage/xtrem-master-data/pages__site__addresses____title": "Adresses", "@sage/xtrem-master-data/pages__site__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__already_exists_with_same_id": "Ce code est déjà attribué à un site.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_name": "Ce nom est déjà attribué à un site.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_taxIdNumber": "Ce n° de TVA est déjà attribué à un site.", "@sage/xtrem-master-data/pages__site__businessEntity____columns__title__isNaturalPerson": "<PERSON>ne physique", "@sage/xtrem-master-data/pages__site__contacts____addButtonText": "Ajouter contact", "@sage/xtrem-master-data/pages__site__contacts____columns__title__isPrimary": "Contact principal", "@sage/xtrem-master-data/pages__site__contacts____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__2": "Définir comme contact principal", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__contacts____headerLabel__title": "Actif", "@sage/xtrem-master-data/pages__site__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__site__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__site__country____columns__title__regionLabel": "Intitulé région", "@sage/xtrem-master-data/pages__site__country____columns__title__zipLabel": "Libellé ZIP", "@sage/xtrem-master-data/pages__site__country____lookupDialogTitle": "Sélectionner le pays", "@sage/xtrem-master-data/pages__site__createFromBusinessEntity____title": "<PERSON>réer depuis entité commerciale", "@sage/xtrem-master-data/pages__site__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__site__defaultLocation____lookupDialogTitle": "Sélectionner l'emplacement par défaut", "@sage/xtrem-master-data/pages__site__defaultLocation____title": "Emplacement par défaut", "@sage/xtrem-master-data/pages__site__description____title": "Description", "@sage/xtrem-master-data/pages__site__display_primary_address": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title": "Contacts", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title__2": "Adresse principale", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title__isPrimaryForAnotherEntity": "Primary for another entity", "@sage/xtrem-master-data/pages__site__financialSite____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__site__financialSite____lookupDialogTitle": "Sélectionner le site financier", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createdBy": "C<PERSON><PERSON> par", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createStamp": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updatedBy": "Mis à jour par", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updateStamp": "Mis à jour", "@sage/xtrem-master-data/pages__site__groupRoleSites____title": "Groupes d'autorisations", "@sage/xtrem-master-data/pages__site__hierarchyChartContent____title": "Organisation", "@sage/xtrem-master-data/pages__site__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__site__isFinance____title": "Finance", "@sage/xtrem-master-data/pages__site__isInventory____title": "Stock", "@sage/xtrem-master-data/pages__site__isLocationManaged____title": "Gestion emplacements", "@sage/xtrem-master-data/pages__site__isManufacturing____title": "Production", "@sage/xtrem-master-data/pages__site__isPurchase____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__isSales____title": "Vente", "@sage/xtrem-master-data/pages__site__isSequenceNumberIdUsed____title": "Compteur utilisé", "@sage/xtrem-master-data/pages__site__legalCompany____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__site__legalCompany____lookupDialogTitle": "Sélectionner la société", "@sage/xtrem-master-data/pages__site__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__site__mainSection____title": "Général", "@sage/xtrem-master-data/pages__site__managementSection____title": "Gestion", "@sage/xtrem-master-data/pages__site__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__site__sequenceNumberId____title": "<PERSON><PERSON> de compteur", "@sage/xtrem-master-data/pages__site__siteGroupBlock____title": "Groupes de sites", "@sage/xtrem-master-data/pages__site__siteGroups____columns__title__isLegalCompany": "Société", "@sage/xtrem-master-data/pages__site__siteGroups____title": "Groupes de sites", "@sage/xtrem-master-data/pages__site__siteGroupSection____title": "Groupes de sites", "@sage/xtrem-master-data/pages__site__timeZone____title": "<PERSON><PERSON> ho<PERSON>", "@sage/xtrem-master-data/pages__site__userGroupBlock____title": "Groupes d'autorisation", "@sage/xtrem-master-data/pages__site__userGroupSection____title": "Groupes d'utilisateurs", "@sage/xtrem-master-data/pages__site__website____title": "Site web", "@sage/xtrem-master-data/pages__site_page_taxIdNumber_required_if_no_business_entity": "Le n° de TVA est demandé lorsqu' aucune entité commerciale n'est sélectionnée.", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line3__title": "Norme", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line6__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__standard____objectTypePlural": "Normes", "@sage/xtrem-master-data/pages__standard____objectTypeSingular": "Norme", "@sage/xtrem-master-data/pages__standard____title": "Normes", "@sage/xtrem-master-data/pages__standard__code____title": "Code", "@sage/xtrem-master-data/pages__standard__createStandard____title": "Nouveau", "@sage/xtrem-master-data/pages__standard__deleteStandard____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard__id____title": "Identifiant norme", "@sage/xtrem-master-data/pages__standard__idBlock____title": "Identifiant norme", "@sage/xtrem-master-data/pages__standard__industrySector____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__standard__mainSection____title": "Général", "@sage/xtrem-master-data/pages__standard__name____title": "Nom", "@sage/xtrem-master-data/pages__standard__saveStandard____title": "Enregistrer", "@sage/xtrem-master-data/pages__standard__sdo____title": "Norme", "@sage/xtrem-master-data/pages__standard__version____title": "Version", "@sage/xtrem-master-data/pages__stock_journal_inquiry": "Consultation des journaux de stock", "@sage/xtrem-master-data/pages__stock_posting_error": "Erreur de comptabilisation de stock", "@sage/xtrem-master-data/pages__supplier____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__isActive__title": "Actif", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line10__title": "Catégorie client", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line7__title": "Montant minimum commande", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line8__title": "Condition de paiement", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__columns__title__id": "Code", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__columns__title__name": "Nom", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__title": "Nom", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title": "Tous", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__2": "Actif", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__3": "Inactif", "@sage/xtrem-master-data/pages__supplier____objectTypePlural": "Fournisseurs", "@sage/xtrem-master-data/pages__supplier____objectTypeSingular": "Fournisseur", "@sage/xtrem-master-data/pages__supplier____title": "Fournisseur", "@sage/xtrem-master-data/pages__supplier___id____title": "ID", "@sage/xtrem-master-data/pages__supplier__addCertificate____title": "Ajouter", "@sage/xtrem-master-data/pages__supplier__addItem____title": "Ajouter", "@sage/xtrem-master-data/pages__supplier__addPriceLine____title": "Ajouter", "@sage/xtrem-master-data/pages__supplier__addressAndContactBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____addButtonText": "A<PERSON><PERSON> adresse", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__concatenatedAddressWithoutName": "Adresse sans nom", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__isPrimary": "Adresse principale", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title": "Modifier l'adresse", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__2": "Définir comme adresse principale", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__3": "A<PERSON>ter le contact", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__4": "Supprimer les adresses et contacts", "@sage/xtrem-master-data/pages__supplier__addresses____title": "Adresses", "@sage/xtrem-master-data/pages__supplier__addressSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_id": "Ce code est déjà attribué à un fournisseur.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_name": "Ce nom est déjà attribué à un fournisseur.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_taxIdNumber": "Ce numéro de TVA est déjà attribué à un fournisseur.", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__concatenatedAddress": "Adresse principale facturant", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__billByAddress____dropdownActions__title": "<PERSON><PERSON>lace<PERSON>", "@sage/xtrem-master-data/pages__supplier__billByAddress____title": "Adresse principale facturant", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine1": "Ligne 1", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine2": "Ligne 2", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__country__name": "Pays", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__postcode": "Code ZIP", "@sage/xtrem-master-data/pages__supplier__billBySupplier____title": "Fournisseur facturant", "@sage/xtrem-master-data/pages__supplier__businessEntity____columns__title__isNaturalPerson": "<PERSON>ne physique", "@sage/xtrem-master-data/pages__supplier__category____columns__title__sequenceNumber__name": "Compteur", "@sage/xtrem-master-data/pages__supplier__category____lookupDialogTitle": "Sélectionner la catégorie", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__certificationBody": "Organisme certification", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfCertification": "Date certification", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfOriginalCertification": "Date certification initiale", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__id": "Référence certificat", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__standard__id": "Standard", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__validUntil": "<PERSON><PERSON> jusqu'à", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__certificates____title": "Certificats", "@sage/xtrem-master-data/pages__supplier__certificateSection____title": "Certificats", "@sage/xtrem-master-data/pages__supplier__commercialBlock____title": "Commerciales", "@sage/xtrem-master-data/pages__supplier__commercialSection____title": "Commerciales", "@sage/xtrem-master-data/pages__supplier__contacts____addButtonText": "Ajouter contact", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__isPrimary": "Contact principal", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__2": "Définir comme contact principal", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__contacts____headerLabel__title": "Actif", "@sage/xtrem-master-data/pages__supplier__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__supplier__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__supplier__country____lookupDialogTitle": "Sélectionner le pays", "@sage/xtrem-master-data/pages__supplier__createFromBusinessEntity____title": "<PERSON>réer depuis entité commerciale", "@sage/xtrem-master-data/pages__supplier__currency____lookupDialogTitle": "Sélectionner la devise", "@sage/xtrem-master-data/pages__supplier__deliveryMode____lookupDialogTitle": "Sélectionner le mode de livraison", "@sage/xtrem-master-data/pages__supplier__deliveryMode____title": "Mode de livraison", "@sage/xtrem-master-data/pages__supplier__display_primary_address": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title": "Contacts", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title__2": "Adresse principale", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title__isPrimaryForAnotherEntity": "Primary for another entity", "@sage/xtrem-master-data/pages__supplier__euVatNumber____title": "ID TVA intracommunautaire", "@sage/xtrem-master-data/pages__supplier__financialBlock____title": "Financières", "@sage/xtrem-master-data/pages__supplier__financialSection____title": "Financières", "@sage/xtrem-master-data/pages__supplier__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__supplier__incoterm____lookupDialogTitle": "Sélectionner la règle d'Incoterms", "@sage/xtrem-master-data/pages__supplier__incoterm____title": "Règle d'Incoterms®", "@sage/xtrem-master-data/pages__supplier__industrySpecificBlock____title": "Certificats", "@sage/xtrem-master-data/pages__supplier__internalNote____title": "Notes internes", "@sage/xtrem-master-data/pages__supplier__isNaturalPerson____title": "<PERSON>ne physique", "@sage/xtrem-master-data/pages__supplier__itemBlock____title": "Articles", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isActive": "Actif", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isDefaultItemSupplier": "Fournisseur défaut", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__description": "Descr. article", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__id": "Code article", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__name": "Article", "@sage/xtrem-master-data/pages__supplier__items____columns__title__minimumPurchaseQuantity": "Quantité achat mini", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__name": "Unité mesure achat", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemCode": "Code article-fournisseur", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemName": "Nom article-fournisseur", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title": "Modifier", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title__2": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____title": "Articles", "@sage/xtrem-master-data/pages__supplier__itemSection____title": "Articles", "@sage/xtrem-master-data/pages__supplier__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__supplier__mainSection____title": "Général", "@sage/xtrem-master-data/pages__supplier__minimumOrderAmount____title": "Montant minimum commande", "@sage/xtrem-master-data/pages__supplier__noteBlock____title": "Notes", "@sage/xtrem-master-data/pages__supplier__noteSection____title": "Notes", "@sage/xtrem-master-data/pages__supplier__parent____lookupDialogTitle": "Sélectionner le parent", "@sage/xtrem-master-data/pages__supplier__parent____title": "Parent", "@sage/xtrem-master-data/pages__supplier__paymentMethod____title": "Mode de paiement", "@sage/xtrem-master-data/pages__supplier__paymentTerm____lookupDialogTitle": "Sélectionner la condition de paiement", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__concatenatedAddress": "Adresse principale payé", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__payToAddress____dropdownActions__title": "<PERSON><PERSON>lace<PERSON>", "@sage/xtrem-master-data/pages__supplier__payToAddress____title": "Adresse principale payé", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine1": "Ligne 1", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine2": "Ligne 2", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__country__name": "Pays", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__postcode": "Code ZIP", "@sage/xtrem-master-data/pages__supplier__payToSupplier____title": "Fournisseur facturant", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title": "Nom", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__2": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__3": "ID", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__4": "Intitulé région", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__5": "Libellé ZIP", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__title__country__name": "Pays", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__concatenatedAddress": "Adresse principale retour", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__returnToAddress____dropdownActions__title": "<PERSON><PERSON>lace<PERSON>", "@sage/xtrem-master-data/pages__supplier__returnToAddress____title": "Adresse principale retour", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine1": "Ligne 1", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine2": "Ligne 2", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__businessEntity__name": "Entité commerciale", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__country__name": "Pays", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__locationPhoneNumber": "N° tél", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__postcode": "Code ZIP", "@sage/xtrem-master-data/pages__supplier__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__currency__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidFrom": "Validité début", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidTo": "Validité fin", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__fromQuantity": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__description": "Descr. article", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__id": "Code article", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__name": "Article", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__id": "Site", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__name": "Site", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__toQuantity": "Qté fin", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title__2": "Modifier", "@sage/xtrem-master-data/pages__supplier__supplierPrices____title": "Prix fournisseurs", "@sage/xtrem-master-data/pages__supplier__supplierPricesBlock____title": "Prix fournisseurs", "@sage/xtrem-master-data/pages__supplier__website____title": "Site web", "@sage/xtrem-master-data/pages__supplier_certificate_panel____title": "Certificat fournisseur", "@sage/xtrem-master-data/pages__supplier_certificate_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__supplier_certificate_panel__certificationBody____title": "Organisme certification", "@sage/xtrem-master-data/pages__supplier_certificate_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfCertification____title": "Date de certification", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfOriginalCertification____title": "Date du certificat d'origine", "@sage/xtrem-master-data/pages__supplier_certificate_panel__edit____title": "Modifier le certificat fournisseur", "@sage/xtrem-master-data/pages__supplier_certificate_panel__id____title": "Référence du certificat", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainBlock____title": "Certificat", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainSection____title": "Général", "@sage/xtrem-master-data/pages__supplier_certificate_panel__new____title": "Nouveau certificat fournisseur", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__code": "Code", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__industrySector": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__sdo": "Standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__version": "Version", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____lookupDialogTitle": "Sélectionner le standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____title": "Standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__validUntil____title": "<PERSON><PERSON> jusqu'au", "@sage/xtrem-master-data/pages__supplier_item_panel____title": "Modifier l'article-fournisseur", "@sage/xtrem-master-data/pages__supplier_item_panel___id____title": "Code", "@sage/xtrem-master-data/pages__supplier_item_panel__cancel____title": "Annuler", "@sage/xtrem-master-data/pages__supplier_item_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__supplier_item_panel__isDefaultItemSupplier____title": "Fournisseur par défaut", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-master-data/pages__supplier_item_panel__item____title": "Article", "@sage/xtrem-master-data/pages__supplier_item_panel__mainBlock____title": "Article-fournisseur", "@sage/xtrem-master-data/pages__supplier_item_panel__mainSection____title": "Général", "@sage/xtrem-master-data/pages__supplier_item_panel__minimumPurchaseQuantity____title": "Quantité d'achat minimum", "@sage/xtrem-master-data/pages__supplier_item_panel__new____title": "Nouvel article-fournisseur", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseLeadTime____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____lookupDialogTitle": "Sélectionner l'unité", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____title": "Unité", "@sage/xtrem-master-data/pages__supplier_item_panel__supplier____lookupDialogTitle": "Sélectionner le fournisseur", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemCode____title": "Code article-fournisseur", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemName____title": "Nom article-fournisseur", "@sage/xtrem-master-data/pages__team____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__team____objectTypePlural": "Équipes", "@sage/xtrem-master-data/pages__team____objectTypeSingular": "Équipe", "@sage/xtrem-master-data/pages__team____title": "Équipe", "@sage/xtrem-master-data/pages__team__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__team__deleteAction____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__team__description____title": "Description", "@sage/xtrem-master-data/pages__team__id____title": "Code", "@sage/xtrem-master-data/pages__team__mainBlock____title": "Détails", "@sage/xtrem-master-data/pages__team__mainSection____title": "Général", "@sage/xtrem-master-data/pages__team__name____title": "Nom", "@sage/xtrem-master-data/pages__team__saveAction____title": "Enregistrer", "@sage/xtrem-master-data/pages__team__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__team__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__bulkActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeFrom__title": "Début d'activité", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeTo__title": "Fin d'activité", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__title": "Efficience", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__location__title": "Emplacement", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__resourceGroup__title": "Groupe de ressources", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__weeklyShift__title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__tool_resource____objectTypePlural": "Ressources outil", "@sage/xtrem-master-data/pages__tool_resource____objectTypeSingular": "Ressource outil", "@sage/xtrem-master-data/pages__tool_resource____title": "Ressource outil", "@sage/xtrem-master-data/pages__tool_resource___id____title": "Code", "@sage/xtrem-master-data/pages__tool_resource__activeFrom____title": "Début d'activité", "@sage/xtrem-master-data/pages__tool_resource__activeTo____title": "Fin d'activité", "@sage/xtrem-master-data/pages__tool_resource__addCostCategory____title": "A<PERSON>ter caté<PERSON>ie <PERSON>", "@sage/xtrem-master-data/pages__tool_resource__blockDetails____title": "Réglages", "@sage/xtrem-master-data/pages__tool_resource__blockWeekly____title": "Détails horaires hebdomadaires", "@sage/xtrem-master-data/pages__tool_resource__cancelAction____title": "Annuler", "@sage/xtrem-master-data/pages__tool_resource__cancelSidePanel____title": "Annuler", "@sage/xtrem-master-data/pages__tool_resource__consumptionMode____title": "Mode de consommation", "@sage/xtrem-master-data/pages__tool_resource__costBlock____title": "Coût", "@sage/xtrem-master-data/pages__tool_resource__costSection____title": "Coût", "@sage/xtrem-master-data/pages__tool_resource__description____title": "Description", "@sage/xtrem-master-data/pages__tool_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__tool_resource__efficiency____title": "Efficience", "@sage/xtrem-master-data/pages__tool_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__tool_resource__hoursTracked____title": "Durée de vie", "@sage/xtrem-master-data/pages__tool_resource__id____title": "Code", "@sage/xtrem-master-data/pages__tool_resource__isActive____title": "Active", "@sage/xtrem-master-data/pages__tool_resource__item____columns__title__category__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__item____lookupDialogTitle": "Sélectionner l'article", "@sage/xtrem-master-data/pages__tool_resource__item____title": "Article", "@sage/xtrem-master-data/pages__tool_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__tool_resource__location____lookupDialogTitle": "Sélectionner l'emplacement", "@sage/xtrem-master-data/pages__tool_resource__location____title": "Emplacement", "@sage/xtrem-master-data/pages__tool_resource__name____title": "Nom", "@sage/xtrem-master-data/pages__tool_resource__quantity____title": "Quantité", "@sage/xtrem-master-data/pages__tool_resource__resourceCapacity____title": "Capacité hebdomadaire", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Type catégorie coût", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoire", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costUnit__name__title__3": "Symbole", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costCategory__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costUnit__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coût indirect", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__runCost": "Opération", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__setupCost": "Réglage", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____title": "Catégories coûts ressources", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____lookupDialogTitle": "Sélectionner le groupe de ressources", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____title": "Groupe de ressources", "@sage/xtrem-master-data/pages__tool_resource__resourceImage____title": "Image", "@sage/xtrem-master-data/pages__tool_resource__saveAction____title": "Enregistrer", "@sage/xtrem-master-data/pages__tool_resource__saveSidePanel____title": "Enregistrer", "@sage/xtrem-master-data/pages__tool_resource__section____title": "Général", "@sage/xtrem-master-data/pages__tool_resource__site____columns__columns__legalCompany__id__columns__title__decimalDigits": "Décimales", "@sage/xtrem-master-data/pages__tool_resource__site____columns__columns__legalCompany__id__title": "Symbole", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__id": "Société", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__name": "Société", "@sage/xtrem-master-data/pages__tool_resource__site____lookupDialogTitle": "Sélectionner le site", "@sage/xtrem-master-data/pages__tool_resource__toolDetails____title": "Exploitation", "@sage/xtrem-master-data/pages__tool_resource__unitProduced____title": "Unité produite", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__capacity": "Capacité", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__dailyShift": "Horaires journaliers", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__day": "Jour", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift1": "Équipe 1", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift2": "Équipe 2", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift3": "Équipe 3", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift4": "Équipe 4", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift5": "Équipe 5", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____title": "Détails he<PERSON>", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____columns__title__formattedCapacity": "Capacité", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____lookupDialogTitle": "Sélectionner les horaires hebdomadaires", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line_4__title": "Décimales", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line3__title": "Type d'unité", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line4__title": "Décimales", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypePlural": "Unités de mesure", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypeSingular": "Unité de mesure", "@sage/xtrem-master-data/pages__unit_of_measure____title": "Unité de mesure", "@sage/xtrem-master-data/pages__unit_of_measure__addConversion____title": "Ajouter", "@sage/xtrem-master-data/pages__unit_of_measure__conversionBlock____title": "Conversion", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title": "Nom", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title__2": "Code", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__item__name__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__lookupDialogTitle__item__name": "Sélectionner l'article", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__2": "Unité cible", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__3": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__4": "Coefficient inversé", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__coefficient": "Coefficient", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__customer__businessEntity__name": "Client", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__fromUnit__name": "Unité de base", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__isStandard": "Standard", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__item__name": "Article", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__type": "Type flux", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____title": "Conversion", "@sage/xtrem-master-data/pages__unit_of_measure__conversionSection____title": "Conversion", "@sage/xtrem-master-data/pages__unit_of_measure__decimalDigits____title": "Décimales", "@sage/xtrem-master-data/pages__unit_of_measure__description____title": "Description", "@sage/xtrem-master-data/pages__unit_of_measure__generalSection____title": "Général", "@sage/xtrem-master-data/pages__unit_of_measure__id____title": "Code", "@sage/xtrem-master-data/pages__unit_of_measure__isActive____title": "Actif", "@sage/xtrem-master-data/pages__unit_of_measure__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__unit_of_measure__name____title": "Nom", "@sage/xtrem-master-data/pages__unit_of_measure__save____title": "Enregistrer", "@sage/xtrem-master-data/pages__unit_of_measure__symbol____title": "Symbole", "@sage/xtrem-master-data/pages__unit_of_measure__type____title": "Type d'unité", "@sage/xtrem-master-data/pages__utils__notification__custom_validation_error": "Des erreurs de validation se sont produites :\n{{#each errors}}\t- {{this}}{{#unless @last}}\n{{/unless}}{{/each}}", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__formattedCapacity__title": "Capacité", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__isFullWeek__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__optionsMenu__title": "<PERSON>ut", "@sage/xtrem-master-data/pages__weekly_shift____objectTypePlural": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__weekly_shift____objectTypeSingular": "<PERSON><PERSON><PERSON> he<PERSON>", "@sage/xtrem-master-data/pages__weekly_shift____title": "Horaires he<PERSON>", "@sage/xtrem-master-data/pages__weekly_shift___id____title": "Code", "@sage/xtrem-master-data/pages__weekly_shift__detailsBlock____title": "Détails", "@sage/xtrem-master-data/pages__weekly_shift__formattedCapacity____title": "Capacité", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__id": "Code", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____lookupDialogTitle": "Sélectionner les horaires du vendredi", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____title": "Ho<PERSON><PERSON> du vendredi", "@sage/xtrem-master-data/pages__weekly_shift__id____title": "Code", "@sage/xtrem-master-data/pages__weekly_shift__isFullWeek____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__mainBlock____title": "Général", "@sage/xtrem-master-data/pages__weekly_shift__mainSection____title": "Général", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__id": "Code", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____lookupDialogTitle": "Sélectionner les horaires du lundi", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____title": "Horaires du lundi", "@sage/xtrem-master-data/pages__weekly_shift__name____title": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__id": "Code", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____lookupDialogTitle": "Sélectionner les horaires du samedi", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__id": "Code", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____lookupDialogTitle": "Sélectionner les horaires du dimanche", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____title": "Horaires du dimanche", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__id": "Code", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____lookupDialogTitle": "Sélectionner les horaires du jeudi", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____title": "Horaires du jeudi", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__id": "Code", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____lookupDialogTitle": "Sélectionner les horaires du mardi", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____title": "Horaires du mardi", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__id": "Code", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__name": "Nom", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____lookupDialogTitle": "Sélectionner les horaires du mercredi", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____title": "Horaires du mercredi", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_content": "Vous êtes sur le point de supprimer cet enregistrement.", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-master-data/pages_currency_delete_inverse_rate_page_dialog_content": "Le taux inversé existe déjà pour la date mentionnée. Voulez-vous conserver le taux inversé ou le supprimer ?", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_content": "Vous êtes sur le point de supprimer cet enregistrement.", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_content": "Vous êtes sur le point de supprimer cet enregistrement.", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-master-data/pages_sidebar_block_title_definition": "Définition", "@sage/xtrem-master-data/pages_sidebar_block_title_price": "Éléments de prix", "@sage/xtrem-master-data/pages_sidebar_block_title_ranges": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages_sidebar_tab_title_definition": "Définition", "@sage/xtrem-master-data/pages_sidebar_tab_title_information": "Informations", "@sage/xtrem-master-data/pages_sidebar_tab_title_prices": "Éléments de prix", "@sage/xtrem-master-data/pages_sidebar_tab_title_ranges": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages_site__address_mandatory": "Affectez au moins une adresse au site.", "@sage/xtrem-master-data/pages_supplier__address_mandatory": "Affectez au moins une adresse au fournisseur.", "@sage/xtrem-master-data/pages-cancel-keep": "Conserver", "@sage/xtrem-master-data/pages-confirm-apply": "Appliquer nouveau", "@sage/xtrem-master-data/pages-confirm-apply-new": "Appliquer nouveau", "@sage/xtrem-master-data/pages-confirm-cancel": "Annuler", "@sage/xtrem-master-data/pages-confirm-continue": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-delete": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-no": "Non", "@sage/xtrem-master-data/pages-confirm-send": "Envoyer", "@sage/xtrem-master-data/pages-confirm-yes": "O<PERSON>", "@sage/xtrem-master-data/permission__convert_from_to__name": "Convertir de en", "@sage/xtrem-master-data/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/permission__create_bulk_license_plate_numbers__name": "<PERSON><PERSON><PERSON> les numéros de contenants internes en masse", "@sage/xtrem-master-data/permission__create_bulk_locations__name": "<PERSON><PERSON><PERSON> les emplacements en masse", "@sage/xtrem-master-data/permission__delete__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/permission__get_item_site_cost__name": "O<PERSON>enir coût article-site", "@sage/xtrem-master-data/permission__get_locations__name": "Obtenir les emplacements", "@sage/xtrem-master-data/permission__get_purchase_unit__name": "Obtenir l'unité achat", "@sage/xtrem-master-data/permission__get_unit_conversion_factor__name": "Obtenir le coefficient de conversion d'unité", "@sage/xtrem-master-data/permission__get_valued_item_site__name": "Obtenir article-site valorisé", "@sage/xtrem-master-data/permission__manage__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/permission__update__name": "Mettre à jour", "@sage/xtrem-master-data/sales-to-stock-unit-must-be-one": "Paramétrer le coefficient de conversion de l'unité de vente en unité de stock à 1 lorsque les unités sont identiques.", "@sage/xtrem-master-data/service_options__allocation_transfer_option__name": "Option de transfert d'allocation", "@sage/xtrem-master-data/service_options__bill_of_material_revision_service_option__name": "Option de service de révision de nomenclature", "@sage/xtrem-master-data/service_options__customer_360_view_option__name": "Option Vue client 360", "@sage/xtrem-master-data/service_options__datev_option__name": "Option DATEV", "@sage/xtrem-master-data/service_options__fifo_valuation_method_option__name": "Option de méthode de valorisation FIFO", "@sage/xtrem-master-data/service_options__intersite_stock_transfer_option__name": "Option de transfert intersite", "@sage/xtrem-master-data/service_options__landed_cost_option__name": "Option frais d'approche", "@sage/xtrem-master-data/service_options__landed_cost_order_option__name": "Option commande frais d'approche", "@sage/xtrem-master-data/service_options__landed_cost_stock_transfer_option__name": "Option de transfert de stock des frais d'approche", "@sage/xtrem-master-data/service_options__order_to_order_option__name": "Option ordre à la demande", "@sage/xtrem-master-data/service_options__phantom_item_option__name": "Option article fantôme", "@sage/xtrem-master-data/service_options__serial_number_option__name": "Option numéro de série", "@sage/xtrem-master-data/site-etension-financial-currency-not-defined": "La devise financière n'est pas définie.", "@sage/xtrem-master-data/site-extension-financial-currency-not-defined": "La devise financière n'est pas définie.", "@sage/xtrem-master-data/telephone-validation-error": "Numéro de téléphone invalide", "@sage/xtrem-master-data/update-confirmation": "Enregistrement mis à jour", "@sage/xtrem-master-data/use-existing-business-entity": "Entité commerciale trouvée : nom {{beName}}, code {{beId}} et n° de TVA {{beTaxId}}. Utiliser cette entité commerciale ?", "@sage/xtrem-master-data/value-must-be-greater-than-current-sequence": "La nouvelle valeur est inférieure à la valeur actuelle.", "@sage/xtrem-master-data/value-must-be-positive": "La valeur {{value}} doit être positive.", "@sage/xtrem-master-data/value-must-not-exceed-the-length-of-sequence-number": "La nouvelle valeur doit être inférieure à la longueur du compteur.", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__addresses__title": "Voir adresses", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__contacts__title": "Voir contacts", "@sage/xtrem-master-data/widgets__customer_contact_list____title": "Contacts clients", "@sage/xtrem-master-data/widgets__delete_note_dialog_content": "Vous êtes sur le point de supprimer cet enregistrement.", "@sage/xtrem-master-data/widgets__delete_note_dialog_title": "Confirmer la <PERSON>", "@sage/xtrem-master-data/widgets__system_version____title": "Version système", "@sage/xtrem-master-data/widgets-confirm-cancel": "Annuler", "@sage/xtrem-master-data/widgets-confirm-delete": "<PERSON><PERSON><PERSON><PERSON>"}