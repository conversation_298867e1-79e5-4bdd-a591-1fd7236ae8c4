import type { OperationGrant } from '@sage/xtrem-core';
import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    CompanyAddress,
    CompanyContact,
    Contact,
    Currency,
    Customer,
    ItemSite,
    Location,
    Supplier,
} from '../nodes/_index';

const { Legislation, Country } = xtremStructure.nodes;
const { UserPreferences, Company, Site } = xtremSystem.nodes;
const { AttachmentAssociation } = xtremUpload.nodes;
const commonOperations: OperationGrant[] = [
    {
        operations: ['lookup'],
        on: [
            () => Site,
            () => BusinessEntity,
            () => Contact,
            () => Address,
            () => Legislation,
            () => Country,
            () => Currency,
            () => AttachmentAssociation,
            () => UserPreferences,
            () => Company,
            () => ItemSite,
            () => Location,
        ],
    },
    {
        operations: ['read'],
        on: [() => BusinessEntityAddress, () => BusinessEntityContact, () => CompanyContact, () => CompanyAddress],
    },
    { operations: ['timezones'], on: [() => Site] },
];

const createOperations: OperationGrant[] = [...commonOperations, { operations: ['update'], on: [() => Site] }];

export const siteExtension = new ActivityExtension({
    extends: xtremSystem.activities.site,
    noCascadeNodes: () => [Customer, Supplier],
    __filename,
    permissions: [],
    operationGrants: {
        read: commonOperations,
        create: createOperations,
        update: commonOperations,
        delete: commonOperations,
    },
});
