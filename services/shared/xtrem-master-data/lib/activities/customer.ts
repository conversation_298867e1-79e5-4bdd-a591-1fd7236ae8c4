import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import {
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    Customer,
    ItemCustomerPrice,
    Supplier,
} from '../nodes/_index';

const coreEntities = [
    () => Customer,
    () => BusinessEntityAddress,
    () => BusinessEntityContact,
    () => ItemCustomerPrice,
];

const lookupGrants: OperationGrant[] = [
    {
        operations: ['lookup'],
        on: [...coreEntities, () => BusinessEntity, () => xtremUpload.nodes.AttachmentAssociation],
    },
    { operations: ['read'], on: [() => xtremSystem.nodes.SysNoteAssociation] },
];

const createEntityGrant = (operations: string[]) => ({
    operations,
    on: coreEntities,
});

export const customer = new Activity({
    description: 'Customer',
    node: () => Customer,
    noCascadeNodes: () => [xtremSystem.nodes.Site, Supplier],
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [...lookupGrants, { operations: ['read'], on: [() => xtremSystem.nodes.SysNote] }],
        create: [
            ...lookupGrants,
            createEntityGrant(['create', 'update']),
            { operations: ['create'], on: [() => xtremSystem.nodes.SysNote] },
        ],
        update: [
            ...lookupGrants,
            createEntityGrant(['update']),
            { operations: ['update'], on: [() => xtremSystem.nodes.SysNote] },
        ],
        delete: [
            ...lookupGrants,
            createEntityGrant(['delete']),
            { operations: ['delete'], on: [() => xtremSystem.nodes.SysNote] },
        ],
    },
});
