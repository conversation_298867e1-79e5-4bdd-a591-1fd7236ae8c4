{"name": "@sage/xtrem~z-postrequisites-test", "description": "Xtrem postrequisites test", "version": "59.0.8", "xtrem": {}, "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem", "postrequisites", "functional test"], "main": "build/index.js", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-declarations": "workspace:*", "@sage/xtrem-finance": "workspace:*", "@sage/xtrem-mailer": "workspace:*", "@sage/xtrem-manufacturing": "workspace:*", "@sage/xtrem-master-data": "workspace:*", "@sage/xtrem-purchasing": "workspace:*", "@sage/xtrem-reporting": "workspace:*", "@sage/xtrem-sales": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-structure": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-technical-data": "workspace:*", "@sage/xtrem-ui": "workspace:*"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "tsc", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "load:qa:data": "xtrem layers --load setup,qa", "start": "xtrem start", "test:functional": "xtrem test --integration", "test:functional:ci": "xtrem test --integration --ci", "view-report": "node ../../../scripts/allure/view-report.js", "xtrem": "xtrem"}}