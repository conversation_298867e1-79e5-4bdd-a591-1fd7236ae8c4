# This test aims to validate the new confirm process inside purchase return document page.
# This confirmation process can substitute the current Approval/Rejection process of the same document,
# depending of an initialization setup from the site page.
# This initial setup is based in the value of a new switch button in the management tab inside the site page.
# If the switch is ON, the approval/rejection process is active (not needed to check in this automation)
# and if the switch button is OFF, the approval/rejection process is inactive,
# therefore the Confirm process is active (what we need to check in this automation test).
# Focus on verifying the entire crud in the confirmation process,
# taking in account the values of the statuses and the displaying of different options in the header kebab menu and also in the line,
# depending of the current process step. Also, focus in the step flow updates depending on the current situation of the confirmation process.

# NOTES:
# - Use any site with no pending for approval documents to apply the change in the indicated previously switch
# to activate the confirmation process and deactivate the approval/rejection process.
# - A new P.Return document should be created. The best way to do it is to start at the beginning of the purchasing flow,
# creating a P.Order, after a receipt and then a return directly from the receipt.

@postrequisites
@distribution
Feature: postrequisites-distribution-crud-purchase-return-confirm
    Sc<PERSON>rio: 01 - Deactivate "Return" switch button on site - error message is returned if there are pending for approval documents
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed
        When the user selects the "Sites" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "Swindon"
        And the user selects the row with text "Swindon" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Site Swindon" titled page is displayed
        And the user selects the "Management" labelled tab on the main page
        And the user selects the "isPurchaseReturnApprovalManaged" bound switch field on the main page
        And the switch field is set to "ON"
        And the user clicks in the switch field
        Then a error toast containing text "You need to approve or reject pending documents before disabling the approval process." is displayed

    Scenario: 02 - Deactivate "Return" switch button on site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed
        When the user selects the "Sites" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "Stock Tranfer DE Site 1"
        And the user selects the row with text "Stock Tranfer DE Site 1" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Site Stock Tranfer DE Site 1" titled page is displayed
        And the user selects the "Management" labelled tab on the main page
        And the user selects the "isPurchaseReturnApprovalManaged" bound switch field on the main page
        And the switch field is set to "ON"
        And the user turns the switch field "OFF"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario Outline: 03 - Create a purchase order - <EnvNamePO>
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "purchasingSite" labelled reference field on the main page
        And the user writes "Stock Tranfer DE Site 1" in the reference field
        And the user selects "Stock Tranfer DE Site 1" in the reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field
        And the user selects the "Order date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Price" labelled tab on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvNamePO>
        And the user clicks the "Submit for approval" labelled business action button on the main page
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        And a toast containing text "Email sent" is displayed
        And the user clicks the "Approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the dialog on the main page
        Examples:
            | EnvNamePO    |
            | "[PO_NUM01]" |
            | "[PO_NUM02]" |
            | "[PO_NUM03]" |

    Scenario Outline: 04 - Create a receipt - <EnvNamePO>
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user filters the "number" bound column in the table field with value <EnvNamePO>
        And the user selects the row with text <EnvNamePO> in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create receipt" labelled business action button on the main page
        And the user clicks the "Create" button of the dialog on the main page
        And a toast containing text "Receipts created:" is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Progress" labelled tab on the sidebar
        And the user selects the "purchaseReceiptLines" bound table field on the sidebar
        And the user selects the row 1 of the table field
        And the user stores the value of the "Receipt number" labelled nested link field of the selected row in the table field with the key <EnvNamePR>
        Examples:
            | EnvNamePR    | EnvNamePO    |
            | "[PR_NUM01]" | "[PO_NUM01]" |
            | "[PR_NUM02]" | "[PO_NUM02]" |
            | "[PR_NUM03]" | "[PO_NUM03]" |

    Scenario Outline: 05 - Create a return - <EnvNamePT>
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "Purchase receipts" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value <EnvNamePR>
        And the user selects the row with text <EnvNamePR> in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Create return" labelled business action button on the main page
        And the dialog title is "Confirm creation"
        And the user clicks the "Create" button of the dialog on the main page
        And a toast containing text "Purchase return created:" is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Progress" labelled tab on the sidebar
        And the user selects the "purchaseReturnLines" bound table field on the sidebar
        And the user selects the row 1 of the table field
        And the user stores the value of the "Return number" labelled nested link field of the selected row in the table field with the key <EnvNamePT>
        Examples:
            | EnvNamePR    | EnvNamePT    |
            | "[PR_NUM01]" | "[PT_NUM01]" |
            | "[PR_NUM02]" | "[PT_NUM02]" |
            | "[PR_NUM03]" | "[PT_NUM03]" |

    Scenario: 06 - Verify that 'Confirm' button is visible on the Purchase return and Approve/Reject buttons are not visible with this setup
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM01]"
        And the user selects the row with text "[PT_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM01]" titled page is displayed
        And the "Confirm" labelled business action button on the main page is visible
        And the "Approve" labelled business action button on the main page is hidden
        And the "Reject" labelled business action button on the main page is hidden

    Scenario: 07 - Make sure it is not possible to Allocate stock (at line level) before confirming the Purchase return
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM01]"
        And the user selects the row with text "[PT_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM01]" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the "Allocate stock" dropdown action of the selected row in the table field is hidden

    Scenario: 08 - Confirm the Purchase return document and verify the dialog content and actions
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM01]"
        And the user selects the row with text "[PT_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM01]" titled page is displayed
        And the user clicks the "Confirm" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Confirm update"
        # @todo find a way to make it work like this "You are about to set this purchase return to "Confirmed""
        And the text in the body of the dialog contains "You are about to set this purchase return to" on the main page
        And the text in the body of the dialog contains "Confirmed" on the main page
        And the user clicks the "Cancel" button of the Confirm dialog
        And the user clicks the "Confirm" labelled business action button on the main page
        And an info dialog appears on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Pending"

    Scenario: 09 - Verify that Close return, Post stock, and Allocate stock (at line level) actions are available on a "Confirmed" purchase return
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM01]"
        And the user selects the row with text "[PT_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM01]" titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the "Close return" labelled business action button on the main page is visible
        And the "Post stock" labelled business action button on the main page is visible
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the "Allocate stock" dropdown action of the selected row in the table field is displayed

    Scenario: 10 - Ensure that lines can be added to a purchase return when the document is confirmed and line statuses are pending
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM01]"
        And the user selects the row with text "[PT_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM01]" titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Pending"
        And the user clicks the "Add lines from receipts" labelled business action button on the main page
        And the text in the header of the dialog is "Select receipt lines"
    # @todo clarify if line needs to be actually added or only to verify that the action is available and can be used

    Scenario: 11 - Ensure that lines can be edited, closed, and deleted when the document is confirmed and line status is pending
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM01]"
        And the user selects the row with text "[PT_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM01]" titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the "Close return" labelled business action button on the main page is visible
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Pending"
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "1 each"
        And the user writes "10" in the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field
        And the user presses Tab
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "10 each"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the "Delete" dropdown action of the selected row in the table field is displayed
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Continue" button of the Confirm dialog
        And the user selects the "lines" bound table field on the main page
        And the table field is empty

    Scenario: 12 - Ensure that a purchase return can be deleted when status is confirmed and line status is pending
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM01]"
        And the user selects the row with text "[PT_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM01]" titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Pending"
        And the user clicks the "Delete" labelled more actions button in the header
        And the text in the header of the dialog is "Delete record"
        And the user clicks the "Delete" button of the Confirm dialog
        And a toast containing text "Record deleted" is displayed

    Scenario: 13 - Ensure that the statuses and the step flow behavior is correct in every step -> Create
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM02]"
        And the user selects the row with text "[PT_NUM02]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM02]" titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Draft"
        And the user selects the "returnStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is current
        And the status of the "Confirm" item of the step-sequence is incomplete
        And the status of the "Allocate" item of the step-sequence is incomplete
        And the status of the "Post" item of the step-sequence is incomplete

    Scenario: 14 - Ensure that the statuses and step flow behavior is correct in every step -> Confirm
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM02]"
        And the user selects the row with text "[PT_NUM02]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM02]" titled page is displayed
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Pending"
        And the user selects the "returnStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Allocate" item of the step-sequence is incomplete
        And the status of the "Post" item of the step-sequence is incomplete

    Scenario: 15 - Ensure that the statuses and step flow behavior is correct in every step -> Allocate stock
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM02]"
        And the user selects the row with text "[PT_NUM02]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM02]" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "save" labelled business action button on a modal
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Pending"
        And the user selects the "returnStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Allocate" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is incomplete

    Scenario: 16 - Ensure that the statuses and step flow behavior is correct in every step -> Post stock
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM02]"
        And the user selects the row with text "[PT_NUM02]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM02]" titled page is displayed
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Continue" button of the Confirm dialog
        And the user refreshes the screen
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Returned"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Closed"
        And the user selects the "returnStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Allocate" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete

    Scenario: 17 - Ensure that the statuses and step flow behavior is correct in every step -> Close return
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All open statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "[PT_NUM03]"
        And the user selects the row with text "[PT_NUM03]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase return [PT_NUM03]" titled page is displayed
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Confirmed"
        And the user clicks the "Close return" labelled business action button on the main page
        And the user clicks the "Close return" button of the Confirm dialog
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Closed"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Closed"
        And the user selects the "returnStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Allocate" item of the step-sequence is incomplete
        And the status of the "Post" item of the step-sequence is incomplete

    Scenario: 18 - Reactivate "Return" switch button on site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed
        When the user selects the "Sites" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "Stock Tranfer DE Site 1"
        And the user selects the row with text "Stock Tranfer DE Site 1" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Site Stock Tranfer DE Site 1" titled page is displayed
        And the user selects the "Management" labelled tab on the main page
        And the user selects the "isPurchaseReturnApprovalManaged" bound switch field on the main page
        And the switch field is set to "OFF"
        And the user turns the switch field "ON"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
