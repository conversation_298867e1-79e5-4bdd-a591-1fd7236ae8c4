# In SDMO users can now enter a payment made for a purchase invoice on the 'Record payment' page,
# manage these payments on a dedicated 'Payment' page, and void payments when necessary.
# The purpose of this test is to validate that payments are correctly entered on the 'Record payment' page
# and that purchase credit notes are properly accounted for in this process.
# Additionally, the test must verify the accuracy of the displayed statuses of purchase invoices
# and purchase credit notes after a payment has been made and after a payment made has been voided.
# IMPORTANT: Intacct configuration status has to be not active (isIntacctServiceOptionActive in @sage/xtrem-structure/OptionManagementBase)
# IMPORTANT: Payment Tracking option status has to be active (paymentTrackingOption in @sage/xtrem-system/ServiceOptionState)
# Prerequisites: prerequisites-flow-verify-options-status

@finance
Feature: finance-flow-ap-payment-tracking-flow-record-payment-create-void

    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario Outline: 0.01 - Data creation - Purchase invoices - <EnvName>
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes <Supplier> in the reference field
        And the user selects <Supplier> in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes <Quantity> in the numeric field
        And the user selects the "Price" labelled tab on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Variance status updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Payments" labelled tab on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "No penalty no discount" in the reference field
        And the user selects "No penalty no discount" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes <AmountNoTax> in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes <Tax> in the numeric field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The purchase invoice was posted." is displayed
        Examples:
            | Quantity | EnvName              | AmountNoTax | Tax   | Supplier                 |
            | "300"    | "[ENV_PIAPPT_NUM01]" | "3000"      | "570" | "MUHOMA Technology GmbH" |
            | "200"    | "[ENV_PIAPPT_NUM02]" | "2000"      | "380" | "MUHOMA Technology GmbH" |
            | "100"    | "[ENV_PIAPPT_NUM03]" | "1000"      | "190" | "MUHOMA Technology GmbH" |

    Scenario Outline: 0.02 - Data creation - Purchase credit memo - <EnvName>
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value <DocName>
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is <DocName>
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the <PageTitle> titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the dialog title is "Create purchase credit memo"
        And the user selects the "Reason *" labelled reference field on a modal
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field
        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes <AmountNoTax> in the numeric field
        And the user clicks the "Create" button of the dialog on the main page
        And a toast containing text "Purchase credit memo created." is displayed
        And the user selects the "Total credit memo tax" labelled numeric field on the main page
        And the user writes <Tax> in the numeric field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        And a toast containing text "The purchase credit memo was posted." is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        Examples:
            | EnvName               | DocName              | PageTitle                             | AmountNoTax | Tax   |
            | "[ENV_PCMAPPT_NUM01]" | "[ENV_PIAPPT_NUM03]" | "Purchase invoice [ENV_PIAPPT_NUM03]" | "1000"      | "190" |

    Scenario: 01 - Record a payment made
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/RecordPayment"
        Then the "Record payment" titled page is displayed
        When the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Supplier *" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        And the user selects the "Bank account *" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And the user selects the "Date issued" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Payment method *" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Currency *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        And the user selects the "Payment amount *" labelled numeric field on the main page
        And the user writes "4500" in the numeric field
        And the user selects the "Transaction information" labelled text field on the main page
        And the user writes "AP payment tracking" in the text field
        And the user clicks in the "searchButton" bound button field on the main page

    Scenario: 02 - Result grid
        And the user selects the "lines" bound table field on the main page
        And the table field is not empty
        When the user filters the "Invoice number" labelled column in the table field with value "[ENV_PCMAPPT_NUM01]"
        When the user filters the "Invoice number" labelled column in the table field with value "[ENV_PIAPPT_NUM03]"
        When the user filters the "Invoice number" labelled column in the table field with value "[ENV_PIAPPT_NUM01]"
        When the user filters the "Invoice number" labelled column in the table field with value "[ENV_PIAPPT_NUM02]"
        And the user selects the row with text "[ENV_PCMAPPT_NUM01]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPPT_NUM03]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPPT_NUM01]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPPT_NUM02]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€ 930.00"
        And the user clicks the "Generate" labelled business action button on the main page
        And the dialog title is "Payments" on the main page
        And the user selects the "Financial site" labelled reference field on a modal
        And the value of the reference field is "Sandfeld"
        And the user selects the "Supplier" labelled reference field on a modal
        And the value of the reference field is "MUHOMA Technology GmbH"
        And the user selects the "Bank account" labelled reference field on a modal
        And the value of the reference field is "Bank Germany EUR"
        And the user selects the "Date issued" labelled date field on a modal
        And the value of the date field is a generated date with value "T"
        And the user selects the "Payment method" labelled dropdown-list field on a modal
        And the value of the dropdown-list field is "EFT"
        And the user selects the "Transaction information" labelled text field on a modal
        And the value of the text field is "AP payment tracking"
        And the user selects the "Currency" labelled reference field on a modal
        And the value of the reference field is "Euro"
        And the user selects the "Payment amount" labelled numeric field on a modal
        And the value of the numeric field is "4,500.00"
        And the user selects the "Lines" labelled table field on a modal
        And the user selects the row with text "[ENV_PIAPPT_NUM01]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 3,570.00"
        And the user selects the row with text "[ENV_PIAPPT_NUM02]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 930.00"
        And the user selects the row with text "[ENV_PIAPPT_NUM03]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 1,190.00"
        And the user selects the row with text "[ENV_PCMAPPT_NUM01]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ -1,190.00"
        And the user clicks the "Confirm" labelled business action button on a modal
        And the user waits for 2 seconds
        And takes a screenshot
    # @todo uncomment next line when the issue with "has-pending-promises" on buttons is fixed
    # And a toast containing text "Payment created:" is displayed

    Scenario: 03 - Store the Payment number
        # @todo remove next line when the issue with "has-pending-promises" on buttons is fixed
        And the user refreshes the screen
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Payment"
        Then the "Payments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the option menu of the table field is "Posted payments"
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AP payment tracking" in the "Reference" labelled column header of the table field
        And the value of the "Payment date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the user stores the value of the "Number" labelled nested link field of the selected row in the table field with the key "[ENV_PaymentNumber]"

    Scenario Outline: 04 - Check 'Payment' tab on the Purchase invoice page - <Number>
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value <Number>
        And the user selects the row with text <Number> in the "Number" labelled column header of the table field
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is <Amount>
        And the value of the "Status" labelled nested label field of the selected row in the table field is <Status>
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the <PageTitle> titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is <Status>
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is <StepStatus>
        And the user selects the "Payments" labelled tab on the main page
        And the user selects the "payments" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Payment number" labelled nested link field of the selected row in the table field is "[ENV_PaymentNumber]"
        And the user clicks the "Payment number" labelled nested field of the selected row in the table field
        And an info dialog appears on a full width modal
        # @todo create ER to be able to use "Payment [ENV_PaymentNumber]"
        And the user clicks the Close button of the dialog on a full width modal
        And the <PageTitle> titled page is displayed
        Examples:
            | Number               | Status           | StepStatus | PageTitle                             | Amount       |
            | "[ENV_PIAPPT_NUM01]" | "Paid"           | complete   | "Purchase invoice [ENV_PIAPPT_NUM01]" | "€ 3,570.00" |
            | "[ENV_PIAPPT_NUM02]" | "Partially paid" | current    | "Purchase invoice [ENV_PIAPPT_NUM02]" | "€ 2,380.00" |
            | "[ENV_PIAPPT_NUM03]" | "Paid"           | complete   | "Purchase invoice [ENV_PIAPPT_NUM03]" | "€ 1,190.00" |

    Scenario: 05 - Check 'Payment' tab on the Purchase credit memo page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_PCMAPPT_NUM01]"
        And the user selects the row with text "[ENV_PCMAPPT_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase credit memo [ENV_PCMAPPT_NUM01]" titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Paid"
        And the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is complete
        And the user selects the "Payments" labelled tab on the main page
        And the user selects the "payments" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Payment number" labelled nested link field of the selected row in the table field is "[ENV_PaymentNumber]"
        And the user clicks the "Payment number" labelled nested field of the selected row in the table field
        And an info dialog appears on a full width modal
        # @todo create ER to be able to use "Payment [ENV_PaymentNumber]"
        And the user clicks the Close button of the dialog on a full width modal
        And the "Purchase credit memo [ENV_PCMAPPT_NUM01]" titled page is displayed

    Scenario: 06 - 1. Check tunneling on payment page - open the page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Payment"
        Then the "Payments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the option menu of the table field is "Posted payments"
        And the user selects the row with text "[ENV_PaymentNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Payment [ENV_PaymentNumber]" titled page is displayed

    Scenario Outline: 07 - 2. Check tunneling on payment page - click the links - <Number>
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text <Number> in the "Document number" labelled column header of the table field
        And the user clicks the "Document number" labelled nested field of the selected row in the table field
        And the user presses Enter
        And the user waits 5 seconds
        And an info dialog appears on a full width modal
        And the text in the header pill label of the dialog is <Status> on a full width modal
        And the user selects the "Number" labelled text field on a full width modal
        And the value of the text field is <Number>
        And the user clicks the Close button of the dialog on a full width modal
        And the "Payment [ENV_PaymentNumber]" titled page is displayed
        Examples:
            | Number                | Status           |
            | "[ENV_PCMAPPT_NUM01]" | "Paid"           |
            | "[ENV_PIAPPT_NUM03]"  | "Paid"           |
            | "[ENV_PIAPPT_NUM02]"  | "Partially paid" |
            | "[ENV_PIAPPT_NUM01]"  | "Paid"           |

    Scenario: 08 - Void payments and check Payment page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Payment"
        Then the "Payments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the option menu of the table field is "Posted payments"
        And the user selects the row with text "[ENV_PaymentNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Payment [ENV_PaymentNumber]" titled page is displayed
        And the "Voided" labelled checkbox field on the main page is hidden
        And the "Voided on" labelled date field on the main page is hidden
        And the "Void text" labelled text field on the main page is hidden
        And the user clicks the "Void" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Void payment" on the main page
        And the user selects the "Date *" labelled date field on a modal
        And the value of the date field is a generated date with value "T"
        And the user selects the "Text" labelled text field on a modal
        And the user writes "Void auto text" in the text field
        And the user clicks the "Confirm" button of the Confirm dialog
        And the "Voided" labelled checkbox field on the main page is displayed
        And the user selects the "isVoided" bound checkbox field on the main page
        # @todo atm it doesn't work to check that this specific field is checked, find a way
        # And the value of the checkbox field is "true"
        And the checkbox field is read-only
        And the "Voided on" labelled date field on the main page is displayed
        And the user selects the "Voided on" labelled date field on the main page
        And the date field is read-only
        And the value of the date field is a generated date with value "T"
        And the "Void text" labelled text field on the main page is displayed
        And the user selects the "Void text" labelled text field on the main page
        And the text field is read-only
        And the value of the text field is "Void auto text"
        And the "Void" labelled business action button on the main page is hidden

    Scenario Outline: 09 - Check 'Payment' tab on the Purchase invoice page - <Number>
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value <Number>
        And the user selects the row with text <Number> in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the <PageTitle> titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is <Status>
        # @todo Validate that only steps 'Create' and 'Post' are now active (and not step 'Credit') - maybe here is room for improvement
        # in order to make sure there is no other step present
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the user selects the "Payments" labelled tab on the main page
        And the user selects the "payments" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "voided" labelled nested checkbox field of the selected row in the table field is "true"
        Examples:
            | Number               | Status   | PageTitle                             |
            | "[ENV_PIAPPT_NUM01]" | "Posted" | "Purchase invoice [ENV_PIAPPT_NUM01]" |
            | "[ENV_PIAPPT_NUM02]" | "Posted" | "Purchase invoice [ENV_PIAPPT_NUM02]" |
            | "[ENV_PIAPPT_NUM03]" | "Posted" | "Purchase invoice [ENV_PIAPPT_NUM03]" |

    Scenario: 10 - Check 'Payment' tab on the Purchase credit memo page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_PCMAPPT_NUM01]"
        And the user selects the row with text "[ENV_PCMAPPT_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase credit memo [ENV_PCMAPPT_NUM01]" titled page is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Posted"
        # @todo Validate that only steps 'Create' and 'Post' are now active - maybe here is room for improvement
        # in order to make sure there is no other step present
        And the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the user selects the "Payments" labelled tab on the main page
        And the user selects the "payments" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "voided" labelled nested checkbox field of the selected row in the table field is "true"
