# the purpose of this test is to verify the currency conversion  is calculated and displayed correctly
# to verify the approval and closing of a purchase order
# we use company with legislation GB and Supplier with legislation FR
# Site= Swindon | Supplier = BARRES | Exchange Rate : 1 EUR = 0.89673 GBP

@distribution
Feature:distribution-flow-purchase-order-currency-conversion

    Scenario: 01 - create a Purchase order with currency of company <> from supplier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Fill in site reference field
        And the user selects the "purchasingSite" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        # Fill in Supplier reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field

        # Add a line 1
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        # Fill in Qty on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "50" in the numeric field
        # select price tab
        And the user selects the "Price" labelled tab on the sidebar
        # Fill in Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Add tax detail
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user filters the "name" labelled column in the table field with value "UK Purchase Goods Standard Rate"
        # And the user selects the "taxReference" bound table field on a modal
        And the user selects the row with text "UK Purchase Goods Standard Rate" in the "Name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal

        # Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast with text "Record created" is displayed

        # # Wait for page to refresh
        # And the user refreshes the screen

        # Save the PO number
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_PO_CC_NUM01]"

    Scenario: 02 - Verify Currency conversion data
        # open PO created
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_PO_CC_NUM01]"
        And the user selects the row with text "[ENV_PO_CC_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # verify amounts on grid
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 15.00000"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "€ 15.00000"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 750.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "€ 150.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 900.00"
        # verify amounts on panel
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        # select price tab
        And the user selects the "Price" labelled tab on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "750.00"
        And the user selects the "Total tax" labelled numeric field on the sidebar
        And the value of the numeric field is "150.00"
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "900.00"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "672.55"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "807.06"
        And the user clicks the "Cancel" button of the dialog on the sidebar

        # verify tax detail
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the value of the "Taxable base" labelled nested reference field of the selected row in the table field is "€ 750.00"
        And the value of the "Tax rate" labelled nested reference field of the selected row in the table field is "20.00 %"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "€ 150.00"
        And the value of the "Deductible amount" labelled nested reference field of the selected row in the table field is "€ 150.00"
        And the user clicks the "ok" bound business action button on a modal

        # verify exchange rate
        Then the user selects the "Information" labelled tab on the main page
        And the user selects the "Currency" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "1 EUR = 0.89673 GBP"

        # verify totals tab
        Then the user selects the "Totals" labelled tab on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "750.00"
        And the user selects the "tax" labelled numeric field on the main page
        And the value of the numeric field is "150.00"
        And the user selects the "Including tax" labelled numeric field on the main page
        Then the value of the numeric field is "900.00"

    Scenario: 03 - Approve and close the purchase order
        # open PO created
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page

        And the user selects the row with text "[ENV_PO_CC_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Approval
        And the user clicks the "submitForApproval" labelled business action button on the main page
        And the user selects the "to" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        And a toast containing text "Email sent" is displayed
        And the user clicks the "approve" labelled business action button on the main page
        Then the user clicks the "Accept" button of the Confirm dialog
        Then a toast containing text "Approval status updated" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Approved"
        # Closing
        And the user clicks the "Close order" labelled business action button on the main page
        Then the user clicks the "Close order" button of the Confirm dialog
        Then a toast containing text "The purchase order is closed." is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Closed"

    Scenario: 04 - Verify Currency conversion data after closing
        # open PO created
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_PO_CC_NUM01]"
        And the user selects the row with text "[ENV_PO_CC_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # verify amounts on grid
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 15.00000"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 750.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "€ 150.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 900.00"
        # verify amounts on panel
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        # select price tab
        And the user selects the "Price" labelled tab on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "750.00"
        And the user selects the "Total tax" labelled numeric field on the sidebar
        And the value of the numeric field is "150.00"
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "900.00"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "672.55"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "807.06"
        And the user clicks the "Apply" button of the dialog on the sidebar

        # verify exchange rate
        Then the user selects the "Information" labelled tab on the main page
        And the user selects the "Currency" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "1 EUR = 0.89673 GBP"

        # verify totals tab
        Then the user selects the "Totals" labelled tab on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "750.00"
        And the user selects the "tax" labelled numeric field on the main page
        And the value of the numeric field is "150.00"
        And the user selects the "Including tax" labelled numeric field on the main page
        Then the value of the numeric field is "900.00"
