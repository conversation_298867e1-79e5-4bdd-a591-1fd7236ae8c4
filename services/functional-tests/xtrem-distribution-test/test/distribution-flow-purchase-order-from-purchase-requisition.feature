#This test can only be executed with sage.
#The goal of this test is to verify that the user can create the purchase order from the purchase requisition

@distribution
Feature: distribution-flow-purchase-order-from-purchase-requisition

    Scenario: verify that the user can approve purchase requisition
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PQ230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        #Define dimensions and attributes
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user stores the value of the reference field with the key "[ENV_PODD_NUM22]"
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user stores the value of the reference field with the key "[ENV_PODD_NUM23]"
        And the user selects the "Channel" labelled reference field on a modal
        And the user writes "Residential" in the reference field
        And the user selects "Residential" in the reference field
        And the user stores the value of the reference field with the key "[ENV_PODD_NUM24]"
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "Approve" labelled business action button on the main page
        Then the user clicks the "Accept" button of the Confirm dialog

    Scenario: verify that the user can create a purchase order from requisition - 1
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PQ230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "createOrder" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Purchase orders created" is displayed
        #Verify that the values on the line are correct
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ordered"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "2 each"
        And the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "$ 20.00000"
        And the value of the "Total excluding tax" labelled nested numeric field of the selected row in the table field is "$ 40.00"
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Progress" labelled tab on the sidebar
        And the user selects the "purchaseOrderLines" bound table field on the sidebar
        And the user selects the row with text "2 each" in the "orderedQuantity" bound column header of the table field
        ##Storing the value of the PO
        Then the user stores the value of the "orderNumber" labelled nested text field of the selected row in the table field with the key "[ENV_PONumber]"

    Scenario: Verify the Purchase order creation - 1
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        #########Using stored value
        And the user selects the row with text "[ENV_PONumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PONumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Draft"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Receiving site" labelled nested text field of the selected row in the table field is "TE Hampton"
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "2 each"
        And the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "$ 20.00000"
        And the value of the "Total excluding tax" labelled nested numeric field of the selected row in the table field is "$ 40.00"
        And the value of the "Total including tax" labelled nested numeric field of the selected row in the table field is "$ 40.00"
        #Verify that Purchase order inherited dimensions from Purchase requisition
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "[ENV_PODD_NUM22]"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "[ENV_PODD_NUM23]"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "[ENV_PODD_NUM24]"
        Then the user clicks the "Cancel" labelled business action button on a modal
        And the user clicks the "submitForApproval" labelled business action button on the main page
        And the user selects the "to" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        And the user waits 6 seconds
        And a toast containing text "Email sent" is displayed
        And the user clicks the "approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Approval status updated" is displayed
        And the user refreshes the screen
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Approved"

    Scenario: Verify that the user can print a purchase order - 1
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PONumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PONumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        Then the user clicks the Close button of the dialog on the main page
    #We don't want to click the link as it re direct to a page that the robot can not detect


    Scenario: verify that the user can create a purchase order from requisition - 2
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "PQ230001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PQ230001"
        And the user clicks the "Duplicate" inline action button of the selected row in the table field
        And the user selects the "Requisition date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Duplicate" labelled business action button on a modal
        And a toast with text "Record was duplicated successfully." is displayed
        # Number
        And the user selects the "Number" labelled text field on the main page
        # And the user writes "Purchase_Req_Order" in the text field
        And the user stores the value of the text field with the key "[ENV_PRQ_Number]"
        # And a toast with text "Save to create duplicate." is displayed
        # And the user clicks the "Save" labelled business action button on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        And the user selects the row with text "[ENV_PRQ_Number]" in the "Number" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Approve" dropdown action of the selected row of the table field
        And the user clicks the "Accept" button of the dialog on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user selects the row with text "[ENV_PRQ_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Create order" dropdown action of the selected row of the table field
        And the dialog title is "Confirm creation"
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Purchase orders created:" is displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "[ENV_PRQ_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Verify that the values on the line are correct
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ordered"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Supplier" labelled nested text field of the selected row in the table field is "Lenovo"
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Progress" labelled tab on the sidebar
        And the user selects the "purchaseOrderLines" bound table field on the sidebar
        ##Storing the value of the PO
        Then the user stores the value of the "orderNumber" labelled nested text field of the selected row in the table field with the key "[ENV_PO_Number]"

    Scenario: Verify the Purchase order creation - 2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        #########Using stored value
        And the user selects the row with text "[ENV_PO_Number]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PO_Number]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Draft"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Receiving site" labelled nested text field of the selected row in the table field is "TE Hampton"
        And the user clicks the "submitForApproval" labelled business action button on the main page
        And the user selects the "to" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        # And the user refreshes the screen
        And a toast containing text "Email sent" is displayed
        And the user clicks the "approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Approval status updated" is displayed
        And the user refreshes the screen
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Approved"

    Scenario: Verify that the user can print a purchase order - 2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PO_Number]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PO_Number]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        Then the user clicks the Close button of the dialog on the main page
