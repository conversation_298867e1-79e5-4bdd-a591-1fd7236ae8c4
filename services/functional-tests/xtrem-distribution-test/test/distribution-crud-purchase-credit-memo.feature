#The purpose of this test is to verify purchase credit memo creation, read, update, and deletion

@distribution
Feature: distribution-crud-purchase-credit-memo

    Scenario: Create purchase credit memo from purchase invoice
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "PI230005" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        #creating purchase credit memo
        And the user clicks the "createPurchaseCreditNote" bound business action button on the main page
        And the user selects the "reasonCreditNote" bound reference field on a modal
        #chooses increase quantity as the reason credit note and verify creation
        And the user clicks the lookup button of the reference field
        And the user selects the "reasonCreditNote" bound table field on a modal
        And the user selects the row with text "Increase quantity" in the "name" labelled column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the value of the reference field is "Increase quantity"
        When the user selects the "supplierDocumentDate" labelled date field on a modal
        And the user writes "09/20/2023" in the date field
        Then the value of the date field is "09/20/2023"
        And the user selects the "totalAmountExcludingTaxCreditNote" bound numeric field on a modal
        And the user writes "2" in the numeric field
        And the user clicks the "Create" labelled business action button on a modal
        Then a toast with text "Purchase credit memo created." is displayed
    # This will redirect the page to the newly created PCM

    Scenario: Update purchase credit memo
        # Wait for page to load
        And the user waits 5 seconds
        #updating text and numeric fields
        And the user selects the "supplierCreditMemoReference" labelled text field on the main page
        And the user writes "SupplierCreditMemo" in the text field
        And the user selects the "totalCreditMemoAmountExclTax" labelled numeric field on the main page
        And the user writes "240" in the numeric field
        And the user selects the "totalCreditMemoTax " labelled numeric field on the main page
        And the user writes "48" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCM_Number]"

    Scenario: Verify the purchase credit memo
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Verifying all field on purchase credit memo
        And the user selects the "Financial site *" labelled reference field on the main page
        And the value of the reference field is "UK LIMITED"
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the value of the reference field is "Lyreco"
        And the user selects the "reason" labelled reference field on the main page
        And the value of the reference field is "Increase quantity"
        And the user selects the "supplierCreditMemoReference " labelled text field on the main page
        And the value of the text field is "SupplierCreditMemo"
        And the user selects the "totalCreditMemoAmountExclTax " labelled numeric field on the main page
        And the value of the numeric field is "240.00"
        And the user selects the "totalCreditMemoTax" labelled numeric field on the main page
        And the value of the numeric field is "48.00"
        And the user selects the "totalVarianceAmountExclTax" labelled tile numeric field on the main page
        And the value of the tile numeric field is "£0.00"
        And the user selects the "totalTaxVariance" labelled tile numeric field on the main page
        And the value of the tile numeric field is "£0.00"
        And the user selects the "totalVarianceAmountInclTax" labelled tile numeric field on the main page
        And the value of the tile numeric field is "£0.00"

    Scenario: Delete purchase credit memo
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Delete purchase credit memo
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    #Test for actions directly from navigation panel

    Scenario: Verify that the user can set dimensions in a purchase credit memo from navigation panel
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PC240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PC240001"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the Close button of the dialog on the main page

    Scenario: Verify that the user can do send for matching on a purchase credit memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        When the user filters the "number" bound column in the table field with value "PC240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PC240001"
        And the user clicks the "Send for matching" dropdown action of the selected row of the table field
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        # Verify sending
        Then a toast containing text "email sent" is displayed

    Scenario: Verify that the user can post a purchase credit memo from navigation panel
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PC240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PC240001"
        And the user clicks the "Post" dropdown action of the selected row of the table field
        And the user clicks the "Post" button of the Confirm dialog
        # Verify posting
        Then a toast with text "The purchase credit memo was posted." is displayed


    Scenario: Verify the user is able to delete a Purchase credit memo from navigation panel
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PC240002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PC240002"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Confirm dialog
        # Verify deletion
        Then a toast with text "Record deleted" is displayed
