# The purpose of this test is to verify creation, update, deletion and all the available row actions from the main list of the purchase order
# Site : Swindon
# Supplier : Lyreco
# PO Number: PO230001

@distribution
Feature: distribution-crud-purchase-order-row-actions
    Scenario: 01 - Verify that the user can select a purchase order in the list to duplicate it
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PO230001"
        And the user selects the row with text "PO230001" in the "Number" labelled column header of the table field
        And the user clicks the "Duplicate" inline action button of the selected row in the table field

        And the user selects the "Order date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Duplicate" labelled business action button on a modal
        And a toast with text "Record was duplicated successfully." is displayed
        #And the user clicks the "Save" labelled business action button on the main page
        #Then a toast with text "Record created" is displayed
        # Save the PO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_001]"

    Scenario: 02 - Verify that the user can submit a purchase order for approval directly from main list
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        Then a toast containing text "Email sent" is displayed

    Scenario: 03 - Verify that the user can print a purchase order directly from main list
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Print" dropdown action of the selected row of the table field
        And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast containing text "The purchase order was printed." is displayed

    Scenario: 04 - Verify that the user can duplicate a purchase order directly from main list
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Duplicate" labelled button of the table field
        And a toast with text "Record was duplicated successfully." is displayed
        #Then a toast with text "Record created" is displayed
        # Save the PO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_002]"

    Scenario: 05 - Verify that the user can set dimensions in a purchase order directly from main list
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the Close button of the dialog on the main page

    Scenario: 06 - Verify that the user can approve a purchase order directly from main list
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Approve" dropdown action of the selected row of the table field
        And the dialog title is "Confirm approval"
        And the user clicks the "Accept" button of the dialog on the main page
        Then a toast with text "Approval status updated" is displayed

    Scenario: 07 - Verify that the user can reject a purchase order directly from main list
        # First we need to submit for approval/rejection the record to enable the reject action
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_002]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_002]"
        And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        # Verify email sending
        Then a toast containing text "Email sent" is displayed
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_002]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_002]"
        And the user clicks the "Reject" dropdown action of the selected row of the table field
        And the dialog title is "Confirm rejection"
        And the user clicks the "Reject" button of the dialog on the main page
        Then a toast with text "Approval status updated" is displayed

    Scenario: 08 - Verify that the user can create a receipt from a purchase order directly from main list
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Create receipt" dropdown action of the selected row of the table field
        And the dialog title is "Confirm receipt creation"
        And the user clicks the "Create" button of the dialog on the main page
        Then a toast containing text "Receipts created" is displayed

    Scenario: 09 -Verify that the user can delete a purchase order directly from main list
        # First we need to duplicate a record
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Duplicate" labelled button of the table field
        And a toast with text "Record was duplicated successfully." is displayed
        # And the user clicks the "Save" labelled business action button on the main page
        #Then a toast with text "Record created" is displayed
        # Save the PO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_003]"
        # Verify the user can now delete the purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_003]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_003]"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        # Verify deletion
        Then a toast with text "Record deleted" is displayed
    Scenario: 10 - Verify that the user can close a purchase order directly from main list
        # First we need to duplicate a record
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_001]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_001]"
        And the user clicks the "Duplicate" labelled button of the table field
        And a toast with text "Record was duplicated successfully." is displayed
        #And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        #Then a toast with text "Record created" is displayed
        # Save the PO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_004]"

        # Then we need to approve the record to enable close action
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_004]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_004]"
        And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        # Verify printing
        Then a toast containing text "Email sent" is displayed

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_004]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_004]"
        And the user clicks the "Approve" dropdown action of the selected row of the table field
        And the dialog title is "Confirm approval"
        And the user clicks the "Accept" button of the dialog on the main page
        # Verify Approval
        Then a toast with text "Approval status updated" is displayed

        # Verify the user can now close the purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_004]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_004]"
        And the user clicks the "Close order" dropdown action of the selected row of the table field
        And the dialog title is "Confirm status change"
        And the user clicks the "Close order" button of the dialog on the main page
        # Verify Approval
        Then a toast with text "The purchase order is closed." is displayed
