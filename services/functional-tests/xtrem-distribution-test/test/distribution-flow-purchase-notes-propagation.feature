# The purpose of this test is to confirm that notes are well propagated to subsequent documents for the purchase flow

@distribution

Feature: distribution-flow-purchase-notes-propagation
    Scenario: 01 - Create a Purchase Order with notes and line notes
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        # Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Purchasing site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        # Fill in Customer reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field
        # Add a line for stock item
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        # And the user presses Enter
        # Fill in Gross Price on sidebar
        And the user selects the "Price" labelled tab on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.23" in the numeric field
        # Add notes
        And the user selects the "Line notes" labelled tab on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the user writes "this is an internal line note" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_LINE_NOTES01]"
        # And the user presses Enter

        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 2 seconds
        And the user selects the "Notes" labelled tab on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the user writes "this is an internal note" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_NOTES01]"
        And the user selects the "Repeat the document notes on new documents." labelled switch field on the main page
        And the user turns the switch field "ON"
        And the user selects the "Repeat all the line notes on new documents." labelled switch field on the main page
        And the user turns the switch field "ON"

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed
        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PONUM01]"

        # Confirm the record
        And the user clicks the "Submit for approval" labelled business action button on the main page
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And an info dialog appears on the main page
        And the user clicks the "Send" button of the Confirm dialog
        # Verify sending
        Then a toast containing text "Email sent" is displayed

        # Approve the purchase order
        And the user clicks the "Approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the dialog on the main page
    Scenario: 02 - Create Purchase Order Receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        When the user filters the "number" bound column in the table field with value "[ENV_PONUM01]"
        And the user selects the row with text "[ENV_PONUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Create a purchase order receipt
        And the user clicks the "Create receipt" labelled business action button on the main page
        And the user clicks the "Create" button of the dialog on the main page

        And a toast containing text "Receipts created:" is displayed

        # Retrieve new document number of the Purchase receipt item sidebar progress tab
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Progress" labelled tab on the sidebar
        And the user selects the "purchaseReceiptLines" bound table field on the sidebar
        And the user selects the row with text "Draft" in the "Receipt status" labelled column header of the table field
        And the user stores the value of the "Receipt number" labelled nested link field of the selected row in the table field with the key "[ENV_PRNUM01]"

    Scenario: 03 - Verify the Purchase receipt has the notes propagated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRNUM01]"
        And the user selects the row with text "[ENV_PRNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify line notes
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Line notes" labelled tab on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the value of the rich text field is "[ENV_LINE_NOTES01]"
        And the user clicks the "Cancel" button of the dialog on the sidebar


        # Verify notes
        And the user selects the "Notes" labelled tab on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTES01]"
        And the user selects the "Repeat the document notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Repeat all the line notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"

    Scenario: 04 - Create a Purchase Return
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRNUM01]"
        And the user selects the row with text "[ENV_PRNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Add stock
        When the user selects the "lines" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "item" labelled column header of the table field
        # Stock allocation
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row with text "5 each" in the "quantityInStockUnit" bound column header of the table field
        And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # post stock
        And the user clicks the "Post Stock" labelled business action button on the main page
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Received"
        # create return
        And the user clicks the "Create return" labelled business action button on the main page

        Then the dialog title is "Confirm creation"
        And the user clicks the "Create" button of the Confirm dialog

        Then a toast containing text "Purchase return created:" is displayed
        # Retrieve new document number of the Purchase return item sidebar progress tab
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Progress" labelled tab on the sidebar
        And the user selects the "purchaseReturnLines" bound table field on the sidebar

        And the user selects the row with text "Draft" in the "Return status" labelled column header of the table field
        And the user stores the value of the "Return number" labelled nested link field of the selected row in the table field with the key "[ENV_PTNUM01]"
    Scenario: 05 - Verify the Purchase return has the notes propagated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PTNUM01]"
        And the user selects the row with text "[ENV_PTNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify line notes
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Line notes" labelled tab on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the value of the rich text field is "[ENV_LINE_NOTES01]"
        And the user clicks the "Cancel" button of the dialog on the sidebar
        # Verify notes
        And the user selects the "Notes" labelled tab on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTES01]"
        And the user selects the "Repeat the document notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Repeat all the line notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
