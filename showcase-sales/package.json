{"name": "@sage/xtrem~showcase-sales", "description": "Umbrella project for Xtrem development", "version": "59.0.8", "license": "UNLICENSED", "scripts": {"build": "cd .. && XTREM_SCOPES='platform|showcase-sales|[a-z0-9-]+/cli' pnpm build", "build:cache": "turbo run build", "build:modified": "pnpm -sw build:modified", "extract:demo:data": "cd main/showcase-sales-main && pnpm extract:demo:data", "extract:layer:data": "cd main/showcase-sales-main && pnpm extract:layer:data", "extract:qa:data": "cd main/showcase-sales-main && pnpm extract:qa:data", "extract:setup:data": "cd main/showcase-sales-main && pnpm extract:setup:data", "extract:test:data": "cd main/showcase-sales-main && pnpm extract:test:data", "lint": "cd .. && XTREM_SCOPES='showcase-sales' pnpm lint", "load:demo:data": "cd main/showcase-sales-main && pnpm load:demo:data", "load:layer:data": "cd main/showcase-sales-main && pnpm load:layer:data", "load:qa:data": "cd main/showcase-sales-main && pnpm load:qa:data", "load:setup:data": "cd main/showcase-sales-main && pnpm load:setup:data", "load:test:data": "cd main/showcase-sales-main && pnpm load:test:data", "postgres:clean": "pnpm -sw postgres:clean", "postgres:reset": "pnpm -sw postgres:reset", "postgres:setup": "pnpm -sw postgres:setup", "postgres:stop": "pnpm -sw postgres:stop", "schema:reset": "cd main/showcase-sales-main && pnpm schema:reset", "schema:upgrade:test": "cd main/showcase-sales-main && pnpm schema:upgrade:test", "sqs:clean": "pnpm -sw sqs:clean", "sqs:reset": "pnpm -sw sqs:reset", "sqs:setup": "pnpm -sw sqs:setup", "sqs:stop": "pnpm -sw sqs:stop", "sqs:sync": "pnpm -sw sqs:sync", "start": "cd main/showcase-sales-main && npm start", "test": "cd .. && XTREM_SCOPES='showcase-sales' pnpm test", "test:functional": "xdev run lerna-cache/test-functional.sh", "test:functional:ci": "xdev run lerna-cache/test-functional.sh :ci", "test:smoke:ci": "XTREM_CI=1 XTREM_SCOPES='showcase-sales' pnpm -sw xdev run lerna-cache/test-integration-smoke.sh"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@stylistic/eslint-plugin": "^5.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0"}}