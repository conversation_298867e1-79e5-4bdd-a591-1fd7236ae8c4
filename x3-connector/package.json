{"name": "@sage/xtrem~x3-connector", "description": "Umbrella project for X3 connector", "version": "59.0.8", "license": "UNLICENSED", "scripts": {"build": "XTREM_SCOPES='platform|x3-connector' pnpm -sw build", "build:cache": "turbo run build", "build:modified": "pnpm -sw build:modified", "extract:demo:data": "pnpm --dir main/x3-connector-main extract:demo:data", "extract:layer:data": "pnpm --dir main/x3-connector-main extract:layer:data", "extract:qa:data": "pnpm --dir main/x3-connector-main extract:qa:data", "lint": "XTREM_SCOPES=$(basename $(pwd)) pnpm -sw lint", "load:demo:data": "pnpm --dir main/x3-connector-main load:demo:data", "load:layer:data": "pnpm --dir main/x3-connector-main load:layer:data", "load:qa:data": "pnpm --dir main/x3-connector-main load:qa:data", "load:test:data": "pnpm --dir main/x3-connector-main load:test:data", "postgres:clean": "pnpm -sw postgres:clean", "postgres:reset": "pnpm -sw postgres:reset", "postgres:setup": "pnpm -sw postgres:setup", "postgres:stop": "pnpm -sw postgres:stop", "schema:reset": "pnpm --dir main/x3-connector-main schema:reset", "schema:upgrade:test": "pnpm --dir main/x3-connector-main schema:upgrade:test", "sqs:clean": "pnpm -sw sqs:clean", "sqs:reset": "pnpm -sw sqs:reset", "sqs:setup": "pnpm -sw sqs:setup", "sqs:stop": "pnpm -sw sqs:stop", "sqs:sync": "pnpm -sw sqs:sync", "start": "cd main/x3-connector-main && npm start", "test": "XTREM_SCOPES=$(basename $(pwd)) pnpm -sw test", "test:functional": "xdev:run lerna-cache/test-functional.sh", "test:functional:ci": "xdev:run lerna-cache/test-functional.sh :ci", "test:smoke:ci": "XTREM_CI=1 XTREM_SCOPES=$(basename $(pwd)) xdev:run:root lerna-cache/test-integration-smoke.sh", "xdev:run": "pnpm -sw xdev --dir $(pwd)", "xdev:run:root": "pnpm -sw xdev"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@stylistic/eslint-plugin": "^5.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0"}}