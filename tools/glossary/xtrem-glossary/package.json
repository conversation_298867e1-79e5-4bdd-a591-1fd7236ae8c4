{"name": "@sage/xtrem-glossary", "description": "Sage Glossary", "version": "59.0.8", "xtrem": {"isMain": true, "hasListeners": true, "queue": "glossary"}, "keywords": ["xtrem-service"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build", "data", "sql", "routing.json"], "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-cloud": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-import-export": "workspace:*", "@sage/xtrem-interop": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-scheduler": "workspace:*", "@sage/xtrem-service": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-standalone": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "pluralize": "^8.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-authorization-api": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-glossary-api": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/pluralize": "0.0.33", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "extract:demo:data": "xtrem layers --extract demo", "extract:layer:data": "xtrem layers --extract $XTREM_LAYER", "extract:qa:data": "xtrem layers --extract qa", "extract:setup:data": "xtrem layers --extract setup", "extract:test:data": "xtrem layers --extract test", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "load:demo:data": "xtrem layers --load setup,demo", "load:qa:data": "xtrem layers --load setup,qa", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "schema:reset": "cd main/xtrem-services-main && pnpm schema:reset", "schema:upgrade": "xtrem upgrade --run --execute", "schema:upgrade:test": "cd main/xtrem-services-main && pnpm schema:upgrade:test", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "sqs:clean": "pnpm -sw sqs:clean", "sqs:reset": "pnpm -sw sqs:reset", "sqs:setup": "pnpm -sw sqs:setup", "sqs:stop": "pnpm -sw sqs:stop", "sqs:sync": "pnpm -sw sqs:sync", "start": "xtrem start", "test": "xtrem test --unit --graphql --noTimeout --layers=setup,test", "test:ci": "xtrem test  --noTimeout --unit --ci --layers=setup,test", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "view-report": "node ../../../scripts/allure/view-report.js", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}