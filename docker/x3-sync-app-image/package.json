{"name": "@sage/x3sync-app-image", "version": "59.0.8", "buildStamp": "2025-08-28T13:01:24.733Z", "description": "", "main": "node_modules/@sage/xtrem-x3-sync/build/index.js", "scripts": {}, "keywords": [], "author": "", "license": "UNLICENSED", "dependencies": {"@sage/xtrem-x3-sync": "^59.0.8"}, "pnpm": {"overrides": {"typescript": "~5.8.3", "newrelic": "12.10.0"}, "onlyBuiltDependencies": ["@contrast/fn-inspect", "@newrelic/native-metrics", "@parcel/watcher", "@sage/bms-dashboard", "@sage/xdev", "@swc/core", "canvas", "esbuild", "nx", "oracledb", "protobufjs", "puppeteer", "re2", "sharp"]}, "packageManager": "pnpm@10.12.1"}