{"name": "@sage/xtrem-show-case-bundle", "description": "Xtrem Show Case (bundle - not any more!!)", "version": "59.0.8", "xtrem": {"isMain": true, "hasListeners": true}, "keywords": ["xtrem-application-package"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build/index.js", "build/lib/index.js", "build/lib/i18n", "build/lib/pages", "build/lib/page-extensions", "data", "lib/data-types", "lib/enums", "lib/node-extensions", "lib/nodes", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-cloud": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-interop": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-show-case": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "eslint": "^9.30.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-show-case-api": "workspace:*", "@sage/xtrem-show-case-bundle-api": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "c8": "^10.1.2", "chai": "^4.3.10", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "typescript": "~5.9.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:references": "tsc -b -v .", "clean": "rm -rf build", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "load:test:data": "xtrem layers --load setup,test", "start": "xtrem start", "test": "xtrem test --unit --timeout=480000", "test:ci": "xtrem test --unit --ci", "test:ci:integration": "xtrem test --integration --ci", "test:integration": "xtrem test --integration", "view-report": "node ../../../scripts/allure/view-report.js", "xtrem": "xtrem", "xtrem:debug": "node --inspect-brk ../../@sage/xtrem-cli/build/lib/cli.js"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}