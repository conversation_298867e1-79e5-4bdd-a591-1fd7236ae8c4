{"name": "@sage/xtrem-ui", "version": "59.0.8", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["README.md", "CHANGELOG.md", "build"], "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@ag-grid-community/client-side-row-model": "32.3.9", "@ag-grid-community/core": "32.3.9", "@ag-grid-community/csv-export": "32.3.9", "@ag-grid-community/react": "32.3.9", "@ag-grid-community/styles": "33.0.3", "@ag-grid-enterprise/column-tool-panel": "32.3.9", "@ag-grid-enterprise/core": "32.3.9", "@ag-grid-enterprise/excel-export": "32.3.9", "@ag-grid-enterprise/master-detail": "32.3.9", "@ag-grid-enterprise/menu": "32.3.9", "@ag-grid-enterprise/rich-select": "32.3.9", "@ag-grid-enterprise/row-grouping": "32.3.9", "@ag-grid-enterprise/server-side-row-model": "32.3.9", "@ag-grid-enterprise/set-filter": "32.3.9", "@ag-grid-enterprise/side-bar": "32.3.9", "@ckeditor/ckeditor5-alignment": "43.3.1", "@ckeditor/ckeditor5-basic-styles": "43.3.1", "@ckeditor/ckeditor5-block-quote": "43.3.1", "@ckeditor/ckeditor5-clipboard": "43.3.1", "@ckeditor/ckeditor5-core": "43.3.1", "@ckeditor/ckeditor5-editor-decoupled": "43.3.1", "@ckeditor/ckeditor5-editor-multi-root": "43.3.1", "@ckeditor/ckeditor5-engine": "43.3.1", "@ckeditor/ckeditor5-enter": "43.3.1", "@ckeditor/ckeditor5-essentials": "43.3.1", "@ckeditor/ckeditor5-font": "43.3.1", "@ckeditor/ckeditor5-heading": "43.3.1", "@ckeditor/ckeditor5-horizontal-line": "43.3.1", "@ckeditor/ckeditor5-html-support": "43.3.1", "@ckeditor/ckeditor5-indent": "43.3.1", "@ckeditor/ckeditor5-inspector": "^4.1.0", "@ckeditor/ckeditor5-link": "43.3.1", "@ckeditor/ckeditor5-list": "43.3.1", "@ckeditor/ckeditor5-page-break": "43.3.1", "@ckeditor/ckeditor5-paragraph": "43.3.1", "@ckeditor/ckeditor5-react": "^9.3.0", "@ckeditor/ckeditor5-select-all": "43.3.1", "@ckeditor/ckeditor5-table": "43.3.1", "@ckeditor/ckeditor5-theme-lark": "43.3.1", "@ckeditor/ckeditor5-typing": "43.3.1", "@ckeditor/ckeditor5-ui": "43.3.1", "@ckeditor/ckeditor5-undo": "43.3.1", "@ckeditor/ckeditor5-utils": "43.3.1", "@ckeditor/ckeditor5-widget": "43.3.1", "@fullcalendar/core": "^5.11.5", "@fullcalendar/daygrid": "^5.11.5", "@fullcalendar/interaction": "^5.11.5", "@fullcalendar/react": "^5.11.5", "@fullcalendar/timegrid": "^5.11.5", "@internationalized/date": "^3.5.4", "@oddbird/css-anchor-positioning": "^0.6.0", "@sage/bms-dashboard": "1.111.10", "@sage/design-tokens": "4.35.0", "@sage/lokijs": "^1.7.2", "@sage/visual-process-editor": "^1.11.4", "@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-document-editor": "workspace:*", "@sage/xtrem-filter-utils": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-ui-components": "workspace:*", "@sageai/gms-chat-ui-react": "1.23.3", "@types/react-grid-layout": "^1.3.5", "ag-charts-community": "9.3.1", "ag-charts-react": "9.3.1", "axios": "^1.11.0", "carbon-react": "153.7.0", "date-fns": "^4.0.0", "docx-preview": "^0.3.3", "downshift": "6.1.3", "draft-js": "^0.11.7", "file-type": "^21.0.0", "framer-motion": "^12.0.0", "handlebars": "^4.7.8", "i18n-js": "^4.0.0", "idb": "^8.0.0", "immer": "^9.0.16", "json-to-graphql-query": "^2.2.5", "lodash": "^4.17.21", "memoize-one": "^6.0.0", "pdfjs-dist": "~5.3.93", "pica": "^9.0.1", "react": "^18.3.1", "react-aria-components": "^1.2.1", "react-copy-to-clipboard": "^5.0.1", "react-dom": "^18.3.1", "react-grid-layout": "1.5.2", "react-is": "^18.3.1", "react-pdf": "^10.1.0", "react-redux": "^9.0.0", "reactflow": "^11.10.1", "recharts": "^3.0.0", "redux": "^4.0.4", "redux-responsive": "^4.3.8", "redux-thunk": "^2.3.0", "sanitize-html": "^2.7.2", "showdown": "^2.0.0", "stream-browserify": "^3.0.0", "styled-components": "^5.3.11", "timers-browserify": "^2.0.12", "ts-essentials": "^10.0.0", "tslib": "^2.5.0", "uid": "^1.0.0", "use-clamp-text": "^1.1.1", "use-debounce": "^10.0.3", "usehooks-ts": "^2.6.0", "utif": "^3.1.0", "utility-types": "^3.4.1", "whatwg-url": "^14.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/xtrem-cli-transformers": "workspace:*", "@sage/xtrem-static-shared": "workspace:*", "@side/jest-runtime": "^1.1.0", "@stylistic/eslint-plugin": "^5.0.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@types/express": "^5.0.0", "@types/gm": "^1.25.0", "@types/jest": "^30.0.0", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@types/pica": "^9.0.4", "@types/react": "^18.3.3", "@types/react-copy-to-clipboard": "^5.0.0", "@types/react-dom": "^18.0.0", "@types/react-redux": "^7.1.5", "@types/react-transition-group": "^4.4.10", "@types/redux-mock-store": "^1.0.0", "@types/sanitize-html": "^2.6.2", "@types/showdown": "^2.0.0", "@types/styled-components": "^5.1.29", "@types/styled-system": "^5.1.13", "@types/utif": "^3.0.5", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "adjust-sourcemap-loader": "^5.0.0", "allure-commandline": "^2.20.1", "axe-core": "^4.7.1", "babel-jest": "^30.0.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^10.0.0", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^7.0.0", "dts-generator": "^3.0.0", "esbuild-loader": "^4.3.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "express": "^5.0.0", "fake-indexeddb": "^6.0.0", "file-loader": "^6.2.0", "glob": "^11.0.0", "globals": "^16.2.0", "google-closure-compiler": "^20250820.0.0", "http-proxy-middleware": "^3.0.5", "identity-obj-proxy": "^3.0.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-reports": "^3.1.5", "jest": "^30.0.0", "jest-canvas-mock": "^2.5.2", "jest-cli": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "jest-junit": "^16.0.0", "jest-sonar-reporter": "2.0.0", "jest-styled-components": "^7.1.1", "jsdom": "^26.0.0", "last-call-webpack-plugin": "^3.0.0", "mini-css-extract-plugin": "^2.7.6", "postcss-loader": "^8.0.0", "prettier": "^3.3.3", "pretty-format": "30.0.5", "redux-mock-store": "^1.5.3", "resolve-url-loader": "^5.0.0", "sass": "^1.55.0", "sass-loader": "^16.0.0", "source-map-loader": "^5.0.0", "source-map-support": "^0.5.12", "style-loader": "^4.0.0", "svg-inline-loader": "^0.8.2", "terser-webpack-plugin": "^5.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsd": "^0.33.0", "typescript": "~5.9.0", "url-loader": "^4.1.1", "webpack": "^5.95.0", "webpack-cli": "^6.0.0", "webpack-dev-server": "^5.0.0", "webpack-merge": "^6.0.0", "webpack-notifier": "^1.7.0", "webpackbar": "^7.0.0"}, "scripts": {"build": "pnpm build:ts && pnpm build:webpack && node ./package-definition.js", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:declarations": "tsc -d --emitDeclarationOnly", "build:dev": "pnpm build:ts && pnpm build:webpack:dev && node ./package-definition.js", "build:instrumented": "pnpm build:ts && pnpm build:webpack:instrumented && node ./package-definition.js", "build:tests": "tsc -b tsconfig.test.json", "build:ts": "tsc -b -v .", "build:webpack": "webpack --config ./webpack/prod.js", "build:webpack:dev": "webpack --config ./webpack/dev.js", "build:webpack:instrumented": "webpack --config ./webpack/instrumented.js", "clean": "rm -rf build junit-report* automation/report/* .awcache .temp_cache docs tsconfig.buildinfo", "coverage-report": "node coverage.js", "dev": "pnpm build:ts && webpack serve --config ./webpack/dev.js", "lint": "eslint lib", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "lint:fix": "pnpm prettier:write && pnpm lint --fix", "prettier:check": "prettier --trailing-comma all --list-different \"lib/**/*.{ts,tsx}\"", "prettier:write": "prettier --trailing-comma all --write \"lib/**/*.{ts,tsx}\" package.json", "prod-proxy": "node webpack/prod-proxy.js", "start": "pnpm build && node serve-static", "start:dev": "tsc -d && webpack serve --config ./webpack/dev.js", "test": "cross-env DEBUG_PRINT_LIMIT=1000000 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui\" JEST_JUNIT_OUTPUT=\"./junit-report-ui.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --maxWorkers=50% --config=jest.config.js", "test:ci": "pnpm build:tests && cross-env NODE_ENV=\"test\" NO_CONSOLE=1 JEST_SUITE_NAME=\"xtrem-ui\" JEST_JUNIT_OUTPUT_NAME=\"./junit-report-ui.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --config=jest.config.js --no-cache --coverage  --maxWorkers=3", "test:concurrent": "cross-env DEBUG_PRINT_LIMIT=1000000 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui\" JEST_JUNIT_OUTPUT=\"./junit-report-ui.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --maxWorkers=4 --config=jest.config.js", "test:local": "cross-env DEBUG_PRINT_LIMIT=10 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui\" JEST_JUNIT_OUTPUT=\"./junit-report-ui.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --maxWorkers=50% --config=jest.config.js", "test:watch": "cross-env DEBUG_PRINT_LIMIT=1000000 NODE_ENV=\"test\" JEST_SUITE_NAME=\"xtrem-ui\" JEST_JUNIT_OUTPUT=\"./junit-report-ui.xml\" JEST_JUNIT_CLASSNAME=\"{classname}\" JEST_JUNIT_TITLE=\"{title}\" TZ=\"Europe/Paris\" jest --watchAll --config=jest.config.js", "watch": "webpack serve --config ./webpack/esbuild.js"}}