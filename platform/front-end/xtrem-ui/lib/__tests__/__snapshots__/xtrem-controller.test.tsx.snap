// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Xtrem controller component should render Xtrem controller with common properties 1`] = `
<div>
  <div
    class="e-xtrem-controller"
  >
    <div
      class="e-page e-page-mode-default e-page-page"
      data-pendo-page="TestPage"
      data-pendo-page-mode="ML"
      data-testid="e-page"
    >
      <main
        class="e-page-main-section"
      >
        <header
          class="e-header e-header-context-page e-no-tabs"
          data-testid="e-header"
        >
          <div
            class="e-header-title-row "
          >
            <div
              class="e-header-title-col"
            >
              <div
                class="e-header-title-container"
              >
                <div>
                  <h1
                    class="e-header-title"
                  >
                    test controller
                  </h1>
                </div>
              </div>
            </div>
            <div
              class="e-header-line-block"
            >
              <div>
                Unhandled container:
              </div>
            </div>
          </div>
        </header>
        <div
          class="e-page-body-container"
        >
          <div
            class="e-page-body e-page-body-context-page"
            data-testid="e-page-body"
          />
        </div>
      </main>
    </div>
  </div>
</div>
`;

exports[`Xtrem controller component should render Xtrem controller without PageComponent in the body when isReady is false 1`] = `
<div>
  <div
    class="e-xtrem-controller"
  />
</div>
`;

exports[`Xtrem controller component should render Xtrem controller without PageComponent in the body when there is no page definition 1`] = `
<div>
  <div
    class="e-xtrem-controller"
  />
</div>
`;
