@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';

@keyframes e-navigation-panel-mobile-content-move-in {
    0% {
        left: -100%;
    }

    100% {
        left: 0;
    }
}

.e-page-navigation-panel {
    height: 100%;
    z-index: 9;
    background: var(--colorsYang100);
    box-shadow: var(--boxShadow100);
    border-right: 1px solid var(--colorsUtilityMajor100);
    border-left: 1px solid var(--colorsUtilityMajor100);
    flex-direction: column;
    width: 320px;
    display: none;
    margin-left: 48px;

    @include small {
        position: absolute;
        box-shadow: var(--boxShadow100);
        margin-left: 0;

    }

    @include extra_small {
        width: 100%;
        position: fixed;
        animation-name: e-navigation-panel-mobile-content-move-in;
        animation-iteration-count: 1;
        animation-timing-function: ease-in;
        animation-duration: 0.3s;
        border: none;
        -webkit-transition: transform 1s;
        /* Safari */
        transition: transform 1s;
        z-index: 10;
        margin-left: 0;
    }

    .e-create-actions {
        >[data-component="split-button"]>button {
            padding-left: 4px;
            padding-right: 4px;

            [data-component="icon"] {
                margin-right: 0;
            }
        }

    }

    .e-field-header-wrapper {
        padding-bottom: 12px;
        padding-top: 12px;

        .e-table-field-title [data-element="label"] {
            display: none;
        }

        @include small {
            .e-field-title {
                display: none;
            }
        }
    }

    .e-page-navigation-panel-bulk-actions-bar {
        height: 48px;
        box-shadow: var(--boxShadow100);
        background: var(--colorsYang100);
        margin-top: 8px;
        margin-bottom: 16px;
        padding: 4px 16px;
        display: flex;
        align-items: center;
        gap: 24px;
        border-radius: var(--borderRadius100);

        &>div>div.e-button-field {
            margin-top: 0 !important;
        }

        & button {
            --colorsActionMajor500: var(--colorsUtilityMajor400);
            --colorsActionMajor600: var(--colorsUtilityMajor500);

            &~span[data-element="help"] {
                display: none;
            }

            &.e-page-navigation-panel-bulk-actions-bar-clear-selection {
                background: none;
                color: var(--colorsUtilityMajor400);
                border: none;
                padding: 0;
                font: inherit;
                cursor: pointer;
                margin-left: 24px;

                &:focus {
                    outline: solid var(--colorsSemanticFocus500);
                }
            }
        }

        & span.e-page-navigation-panel-bulk-actions-bar-selected-items,
        button.e-page-navigation-panel-bulk-actions-bar-clear-selection {
            height: 21px;
            font-family: var(--fontFamiliesDefault);
            font-style: normal;
            font-weight: 500;
            font-size: var(--fontSizes100);
            line-height: 150%;
        }

        & span.e-page-navigation-panel-bulk-actions-bar-selected-items {
            color: var(--colorsUtilityYin090);
            margin-right: 24px;
        }
    }
}


.e-page-navigation-panel.e-page-navigation-panel-full-width {
    width: calc(100% - 46px);
    @include page_responsive_container;
    border: 0;
    background-color: var(--colorsUtilityMajor025);
    box-shadow: none;
    margin: 0 22px;
    padding-right: 6px;
    padding-left: 6px;
    padding-top: 24px;
    padding-bottom: 16px;

    .e-page-navigation-panel-title {
        display: flex;
        margin-left: 4px;
        margin-right: 4px;

        .e-header-navigation-return-arrow-parent-page-container {
            display: flex;

            button {
                margin-right: 16px;
            }
        }
    }

    .e-page-navigation-panel-guide:not(:empty) {
        padding-top: 12px;
    }

    .e-page-navigation-panel-body {
        overflow: unset;
    }

    .e-page-navigation-panel-body>.e-field {
        margin-left: 4px;
        margin-right: 4px;
    }

    .e-page-navigation-panel-header {
        padding: 0;
    }

    .e-page-navigation-panel-header-top {
        padding: 16px 0;
        height: unset;
        justify-content: space-between;

        button {
            justify-self: flex-end;
        }

        .e-page-navigation-panel-select-container {
            padding: 0;
            flex: unset;
            width: 166px;
        }
    }
}

.e-page-navigation-panel-header {
    background-color: var(--colorsUtilityMajor025);
    position: relative;
    box-sizing: border-box;
    padding: 0 16px;

    @include extra_small {
        min-height: unset;
    }

    .e-page-navigation-panel-header-actions {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        margin: auto 0;
        --colorsActionMajor500: var(--colorsActionMinor500);
        --colorsActionMajor600: var(--colorsActionMinor600);

        >span {
            margin-left: 8px;
        }

        .e-business-action {
            >button {
                padding-left: 4px;
                padding-right: 4px;

                >span {
                    margin-right: 0;
                }
            }
        }
    }

    .e-page-navigation-panel-header-title {
        font-size: 16px;
        font-weight: var(--fontWeights700);
        flex: 1;
    }

    .e-page-navigation-panel-header-top {

        .e-page-navigation-panel-header-title-container {
            padding: 16px 0 var(--spacing075) 0;
            display: flex;
        }

        .e-header-navigation-return-arrow-parent-page-container {
            display: flex;

            button {
                margin-right: 16px;
            }
        }

        .e-page-navigation-panel-select-container {
            padding: 12px 0;
            flex: 1;

            .e-ui-select-dropdown {
                z-index: 2;
            }

            .e-ui-select-search-override>div {
                margin-bottom: 0;
            }
        }

        @include extra_small {
            padding: 0;
        }

        .e-page-navigation-panel-header-top-text {
            font-size: 14px;
            font-weight: var(--fontWeights700);
            font-family: var(--fontFamiliesDefault);
            padding-left: 24px;
            align-self: center;
        }

        &>button.e-navigation-panel-toggle {
            height: 64px;
            font-size: 14px;
        }
    }

    .e-page-navigation-panel-header-top-filter-label {
        border-top: 1px solid var(--colorsUtilityMajor100);
    }

    .e-filter-button,
    .e-navigation-panel-toggle {
        margin: 0;
        padding: 8px;
        position: relative;
    }
}

.e-page-navigation-panel-body {
    font-family: $fontAdelle;
    display: flex;
    height: 100%;
    width: 100%;
    overflow: hidden;

    h3 {
        text-align: center;
    }

    .e-navigation-panel-calendar-wrapper {
        background: var(--colorsYang100);
        padding: 0 16px 8px 16px;
        border-top-right-radius: var(--borderRadius100);
        border-top-left-radius: var(--borderRadius100);
    }

    .e-table-field-mobile {
        height: 100%;
        display: flex;
        flex-direction: column;

        .e-field-title {
            display: none;
        }

        .e-field-header {
            background: var(--colorsUtilityMajor025);
            padding: 8px 16px 16px 16px;
            border-bottom: 1px solid var(--colorsUtilityMajor100);
        }

        .e-field-actions-wrapper {
            display: none;
        }

        .e-table-field-mobile-rows {
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 100%;
        }

        .e-table-field-mobile-search {
            flex: 1;

            >div {
                margin-bottom: 0;
            }

            div[role='presentation'] {
                padding: 0px 12px;

                input {
                    padding: 0;
                }
            }
        }
    }

    .e-card-selected {
        background-color: var(--colorsUtilityMajor075);
        border: 1px solid var(--colorsUtilityMajor100);
    }
}
