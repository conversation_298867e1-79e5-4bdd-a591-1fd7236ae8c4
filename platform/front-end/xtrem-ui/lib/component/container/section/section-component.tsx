import type { AccessStatus } from '@sage/xtrem-shared';
import { AccordionGroup } from 'carbon-react/esm/components/accordion';
import Loader from 'carbon-react/esm/components/loader';
import * as React from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';
import * as xtremRedux from '../../../redux';
import type { ReduxResponsive } from '../../../redux/state';
import { RenderingRouter } from '../../../render/rendering-router';
import { localize } from '../../../service/i18n-service';
import type { PageArticleItem } from '../../../service/layout-types';
import { ContextType } from '../../../types';
import { getElementAccessStatus } from '../../../utils/access-utils';
import { getDataTestIdAttribute, isHidden } from '../../../utils/dom';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { getGutterSize } from '../../../utils/responsive-utils';
import { getPageDefinitionFromState } from '../../../utils/state-utils';
import { getFieldTitle } from '../../field/carbon-helpers';
import type { ComponentKey } from '../../types';
import { FieldKey } from '../../types';
import { GridRow } from '@sage/xtrem-ui-components';
import type { PageProperties } from '../container-properties';
import type { PageMode } from '../page/page-types';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { InternalSectionProperties } from './section-types';
import { useParentElementSize } from '../../../utils/hooks/effects/use-parent-element-size';

const CustomStyledAccordionGroup = styled(AccordionGroup)`
    overflow: hidden;
`;

export interface SectionComponentExternalProps {
    item: Partial<PageArticleItem>;
    screenId: string;
    contextType?: ContextType;
    availableColumns: number;
    isParentDisabled?: boolean;
    /**
     * Indicates if any of the parents in the layout structure is hidden, it is required so we can cascade
     * down the hidden status and mark the hidden inputs not focusable
     * */
    isParentHidden?: boolean;
    accessRule?: AccessStatus;
    hasFooter?: boolean;
    fixedHeight?: number;
    isUsingInfiniteScroll?: boolean;
}

export interface SectionComponentProps extends SectionComponentExternalProps {
    fieldProperties: InternalSectionProperties;
    browser?: ReduxResponsive;
    pageMode?: PageMode;
    setFieldProperties: (bind: string, state: InternalSectionProperties) => void;
    firstFieldType?: ComponentKey | null;
}

const isSectionReady = (props: SectionComponentProps): boolean =>
    !props.fieldProperties.isLazyLoaded || !!props.fieldProperties.isReady;

const hasBlocks = (props: SectionComponentProps): boolean =>
    !!props.item.$layout!.$items.find(x => x.$category === 'block');

const isSectionHeaderDisplayed = (props: SectionComponentProps): boolean => {
    if (props.fieldProperties.isLazyLoaded && !props.fieldProperties.isReady) {
        return false;
    }

    return props.pageMode === 'tabs'
        ? props.fieldProperties.isTitleHidden === false
        : !props.fieldProperties.isTitleHidden;
};

const isFullScreenIconDisplayed = (props: SectionComponentProps): boolean =>
    isSectionReady(props) && hasBlocks(props) && props.pageMode === 'default' && props.contextType === ContextType.page;

const isCollapseIconDisplayed = (props: SectionComponentProps, isFullScreen: boolean): boolean =>
    isSectionReady(props) &&
    hasBlocks(props) &&
    props.pageMode === 'default' &&
    !isFullScreen &&
    props.contextType === ContextType.page;

const canRenderSectionBody = (props: SectionComponentProps, isFullScreen: boolean): boolean =>
    (isSectionReady(props) && props.fieldProperties.isOpen !== false) || isFullScreen;

const onOpen = (props: SectionComponentProps): void => {
    props.setFieldProperties(props.item.$containerId!, { ...props.fieldProperties, isOpen: true });
};

const onClose = (props: SectionComponentProps): void => {
    props.setFieldProperties(props.item.$containerId!, { ...props.fieldProperties, isOpen: false });
};

const isSectionDisabled = (props: SectionComponentProps): boolean =>
    props.accessRule === 'unauthorized' ||
    resolveByValue({
        screenId: props.screenId,
        fieldValue: null,
        rowValue: null,
        propertyValue: props.fieldProperties.isDisabled,
        skipHexFormat: true,
    });

const isSectionHidden = (props: SectionComponentProps): boolean =>
    isHidden(props.item, props.browser) ||
    !!resolveByValue({
        propertyValue: props.fieldProperties.isHidden,
        skipHexFormat: true,
        rowValue: null,
        fieldValue: null,
        screenId: props.screenId,
    }) ||
    props.accessRule === 'unavailable';

const getFixedHeight = (props: SectionComponentProps, height: number): number | undefined => {
    // Fixed height is only applied to the section if it contains a single field (and not a block), such as a table or a code editor.
    // In this case fixedHeight is the parent element's height (whatever is available below the header)
    // OR
    // It contains two fields with medium width
    const shouldUseFixedHeight =
        (props.contextType === ContextType.page || props.contextType === ContextType.dialog) &&
        (props.item.$layout?.$items.length === 1 ||
            (props.item.$layout?.$items.length === 2 &&
                !props.item.$layout?.$items.find(i => i.$columnWidth !== 'half' || i.$isFullWidth))) &&
        props.firstFieldType &&
        props.browser?.greaterThan.s &&
        Object.values(FieldKey).includes(props.firstFieldType as FieldKey);

    if (!shouldUseFixedHeight) {
        return undefined;
    }

    const heightToUse = props.fixedHeight || height;
    // Here we need to return something not falsy.
    // AG Grid has a bug and if the grid is initiated with `autoHeight` mode, it will always remain in auto height
    if (!heightToUse) {
        return 10;
    }

    // Workflow needs less space deduction because it handles its own internal spacing better
    if (props.firstFieldType === FieldKey.Workflow) {
        if (props.hasFooter) {
            return heightToUse - 30; // More aggressive: reduced from 60
        }
        return heightToUse - 5; // More aggressive: reduced from 10
    }

    if (props.hasFooter) {
        return heightToUse - 90;
    }

    return heightToUse - 20;
};

const getComponentClass = (props: SectionComponentProps, isFullScreen: boolean): string => {
    const classes = ['e-section'];

    if (isFullScreen) {
        classes.push('e-section-fullscreen');
    }

    if (props.pageMode === 'tabs') {
        classes.push('e-single-section');
    }

    if (isSectionHidden(props)) {
        classes.push('e-hidden');
    }

    if (props.contextType) {
        classes.push(`e-section-context-${props.contextType}`);
    }

    if (props.item.$layout?.$items.length === 1 && Object.values(FieldKey).includes(props.firstFieldType as FieldKey)) {
        classes.push('e-section-single-field-child');
    }

    if (
        props.firstFieldType === FieldKey.Table ||
        props.firstFieldType === FieldKey.Tree ||
        props.firstFieldType === FieldKey.NestedGrid ||
        props.firstFieldType === FieldKey.Workflow
    ) {
        classes.push('e-section-table');
    }

    return classes.join(' ');
};

export function SectionComponent(props: SectionComponentProps): React.ReactElement {
    const [isFullScreen, setFullScreen] = React.useState<boolean>(false);
    const [containerRef, { height }] = useParentElementSize();

    const fixedHeight = getFixedHeight(props, height);

    const title = getFieldTitle(props.screenId, props.fieldProperties, null);
    const collapseSectionLabel = localize('@sage/xtrem-ui/collapse-section', 'Collapse section');
    const openSectionLabel = localize('@sage/xtrem-ui/open-section', 'Open section');
    const closeFullScreenLabel = localize('@sage/xtrem-ui/close-full-screen', 'Close fullscreen');
    const openFullScreenLabel = localize('@sage/xtrem-ui/open-full-screen', 'Open fullscreen');

    const onOpenFullscreen = (): void => setFullScreen(true);

    const onCloseFullscreen = (): void => setFullScreen(false);

    const renderNormalBody = (): React.ReactNode => {
        const gridGutter = getGutterSize(props.browser!.is);
        return (
            <GridRow
                className="e-section-body"
                margin={0}
                gutter={gridGutter}
                verticalMargin={0}
                columns={props.availableColumns}
            >
                {props.item.$layout!.$items.map(item => (
                    <RenderingRouter
                        screenId={props.screenId}
                        key={item.$containerId}
                        item={item}
                        contextType={props.contextType}
                        availableColumns={props.availableColumns}
                        isParentDisabled={props.isParentDisabled || isSectionDisabled(props)}
                        isParentHidden={props.isParentHidden || isSectionHidden(props)}
                        fixedHeight={fixedHeight}
                        isUsingInfiniteScroll={props.isUsingInfiniteScroll}
                    />
                ))}
            </GridRow>
        );
    };

    const renderAccordionBody = (): React.ReactNode => {
        return (
            <CustomStyledAccordionGroup>
                {props.item.$layout!.$items.map(item => (
                    <RenderingRouter
                        screenId={props.screenId}
                        item={item}
                        key={item.$containerId}
                        contextType={ContextType.accordion}
                        availableColumns={props.availableColumns}
                        isParentDisabled={props.isParentDisabled || isSectionDisabled(props)}
                    />
                ))}
            </CustomStyledAccordionGroup>
        );
    };

    return (
        <section
            ref={containerRef}
            className={getComponentClass(props, isFullScreen)}
            data-testid={getDataTestIdAttribute('section', title, props.item.$containerId)}
            id={props.item.$containerId}
        >
            {isSectionHeaderDisplayed(props) && (
                <div className="e-section-header">
                    {!!title && <h2 className="e-section-title">{title}</h2>}
                    {!title && <span className="e-section-title" />}
                    {isFullScreenIconDisplayed(props) &&
                        (isFullScreen ? (
                            <ButtonMinor
                                key="close"
                                data-testid="e-icon-fullscreen"
                                onClick={onCloseFullscreen}
                                iconType="close"
                                iconTooltipMessage={closeFullScreenLabel}
                                aria-label={closeFullScreenLabel}
                                size="small"
                                buttonType="tertiary"
                            />
                        ) : (
                            <ButtonMinor
                                key="open"
                                data-testid="e-icon-fullscreen"
                                onClick={onOpenFullscreen}
                                iconType="link"
                                iconTooltipMessage={openFullScreenLabel}
                                aria-label={openFullScreenLabel}
                                size="small"
                                buttonType="tertiary"
                            />
                        ))}
                    {isCollapseIconDisplayed(props, isFullScreen) &&
                        (props.fieldProperties.isOpen ? (
                            <ButtonMinor
                                key="toggle-close"
                                data-testid="e-icon-close"
                                onClick={(): void => onClose(props)}
                                iconType="chevron_down"
                                iconTooltipMessage={collapseSectionLabel}
                                aria-label={collapseSectionLabel}
                                size="small"
                                buttonType="tertiary"
                            />
                        ) : (
                            <ButtonMinor
                                key="toggle-open"
                                data-testid="e-icon-open"
                                onClick={(): void => onOpen(props)}
                                iconType="chevron_up"
                                iconTooltipMessage={openSectionLabel}
                                aria-label={openSectionLabel}
                                size="small"
                                buttonType="tertiary"
                            />
                        ))}
                </div>
            )}
            {!isSectionReady(props) && <Loader size="large" mt="24px" />}
            {canRenderSectionBody(props, isFullScreen) &&
                (props.fieldProperties.mode === 'normal' || !props.fieldProperties.mode) &&
                renderNormalBody()}
            {canRenderSectionBody(props, isFullScreen) &&
                props.fieldProperties.mode === 'accordion' &&
                renderAccordionBody()}
        </section>
    );
}

const mapStateToProps = (
    state: xtremRedux.XtremAppState,
    props: SectionComponentExternalProps,
): SectionComponentProps => {
    const elementId = props.item.$containerId!;
    const pageDefinition = getPageDefinitionFromState(props.screenId);
    const pageProperties: PageProperties = pageDefinition.metadata.uiComponentProperties[props.screenId];
    const pageMode: PageMode | undefined = pageProperties ? pageProperties.mode : 'default';
    const fieldProperties = pageDefinition.metadata.uiComponentProperties[elementId];
    const accessRule = getElementAccessStatus({
        accessBindings: pageDefinition.accessBindings || {},
        bind: elementId,
        elementProperties: fieldProperties,
        contextNode: pageProperties?.node,
        nodeTypes: state.nodeTypes,
        dataTypes: state.dataTypes,
    });

    // Checks whether the first element is a field and if so what type
    const firstFieldId = props.item.$layout?.$items[0]?.$bind;
    const firstFieldType = firstFieldId
        ? pageDefinition.metadata.uiComponentProperties[firstFieldId]._controlObjectType
        : undefined;

    return {
        ...props,
        fieldProperties,
        browser: state.browser,
        firstFieldType,
        pageMode,
        accessRule,
        setFieldProperties: xtremRedux.actions.actionStub,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: SectionComponentExternalProps,
): Partial<SectionComponentProps> => ({
    setFieldProperties: (bind: string, state: InternalSectionProperties): void => {
        dispatch(xtremRedux.actions.setFieldProperties(props.screenId, bind, state));
    },
});

export const ConnectedSectionComponent = connect(mapStateToProps, mapDispatchToProps)(SectionComponent);
