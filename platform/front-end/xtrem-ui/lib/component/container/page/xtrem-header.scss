@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';

.e-header {

    top: 0;
    position: sticky;
    background-color: var(--colorsUtilityMajor025);
    z-index: 2;

    &.e-header-context-page {
        @include page_responsive_container;

        @include extra_small {
            margin: 0;
        }
    }

    &.e-header-context-page.e-no-tabs {
        border-bottom: 2px solid var(--colorsUtilityMajor050);
    }

    @include extra_small {
        min-height: unset;
    }

    .e-header-section {
        padding-top: 16px;
    }

    .e-header-insights {
        flex: 1;
        padding-top: 16px;
        padding-left: 8px;

        .e-header-insights-icon {
            margin-right: 8px;
        }
    }

    .e-header-title-row {
        display: flex;
        justify-content: space-between;
        padding: 0 12px;

        @include small {
            padding: 0 12px 0 0;
        }

        @include extra_small {
            flex-flow: row wrap;
            border-bottom: 1px solid var(--colorsUtilityMajor100);
            justify-content: unset;
            padding: unset;
            align-items: center;

            .e-header-navigation-return-arrow-parent-page-container {
                margin-left: 8px;
            }
        }

        &.e-with-toggle-button {

            @include small {
                margin-left: 48px;
            }

            @include extra_small {
                margin-left: 48px;
            }
          }

        .e-header-dropdown-action-container {


            .e-action-popover-mobile {
                margin: 0;

                .e-action-popover-mobile-button {
                    padding: 0;
                    height: 24px;
                    line-height: 24px;
                }
            }
        }

        .e-header-line-icon-group {
            margin-top: 24px;
            display: inline-flex;

            &.e-header-line-icon-group-close {
                margin-left: 48px;
            }

            @include extra_small {
                margin-right: 0;
                margin-top: 0;
                margin-bottom: 0;
                display: flex;
                flex-flow: row wrap;
                align-content: center;
                justify-content: center;
                padding-right: 8px;

            }
        }

        .e-header-quick-action-container {

            .e-header-quick-action {
                margin-right: 16px;
            }
        }

        .e-header-title-col {
            display: inline-flex;
            flex-direction: column;
            margin-top: 16px;
            margin-bottom: 8px;
            flex: 1;
            flex-wrap: wrap;

            &.e-header-title-col-insight {
                flex: unset;
            }

            @include extra_small {
                margin-left: 4px;
                margin-right: 0;
                margin-bottom: 0;
                margin-top: 0;
            }

            *[data-element="label"],
            .common-input__label,
            .common-input__help-text {
                display: none;
            }

            [data-role="label-container"] {
                margin: 0;
            }

            .e-header-title-container {
                display: inline-flex;
                align-items: center;
                flex-wrap: wrap;

                .e-grid-column {
                    vertical-align: top;
                    margin-top: 4px;
                }
            }
        }

        .e-header-line-icon-group-close {
            @include extra_small {
                display: none;
            }
        }

        .e-header-close-icon-col {
            display: inline-flex;
            flex-direction: column;
        }

        .e-header-subtitle {
            font-weight: var(--fontWeights400);
            font-family: var(--fontFamiliesDefault);
            display: block;
            margin: 0;
            color: rgba(0, 0, 0, 0.65);
            font-size: 16px;
            line-height: 18px;
        }
    }

    &.e-header-context-dialog>.e-header-title-row {
        justify-content: flex-end;
    }

    .e-header-nav {
        position: relative;
        justify-content: space-between;
        padding-top: 8px;

        .e-xtrem-tabs .e-xtrem-tab-container {
            padding-left: 12px;

            @include extra_small {
                padding-left: 0;
            }
        }

        [role="tablist"] {
            @include extra_small {
                flex-flow: row wrap;
            }
        }

        @include print-hidden();

        @include extra_small {
            overflow-x: auto;
            width: 100vw;
            flex-flow: row wrap;
        }
    }

    .e-block-parent .e-block {
        background: transparent;
    }

    .e-header-navigation-arrow-container,
    .e-header-navigation-return-arrow-parent-page-container {
        button {
            margin-right: 16px;
        }
    }

    .e-header-section:not(:has(.e-tile-parent:not(.e-hidden))) {
        padding-bottom: 32px;
    }
}

.e-page-navigation-panel-closed .e-header-context-page {
    @include extra_small {
        padding-left: 0;
    }
}

.e-header-title {
    margin: 0 16px 0 0;
    font-family: $fontAdelle;
    font-size: 24px;
    min-height: 32px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;

    @include small {
        padding-left: 8px;
    }

    @include extra_small {
        font-size: 16px;
        margin: 0;
        line-height: unset;
    }

}

.e-header-image-container {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;

    @include extra_small {
        margin-left: 4px;
    }

    .e-header-image {
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        border-radius: 20px;
        width: 40px;
        height: 40px;
    }
}

.e-navigation-panel-toggle button {
    position: absolute;
    left: 0;
    top: 0;
    height: 66px;
    margin: 0;
    padding: 8px;
    border-right: 1px solid var(--colorsUtilityMajor100);
    border-bottom: 1px solid var(--colorsUtilityMajor100);
    border-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;

    @include small {
        z-index: 3;
    }

    @include extra_small {
        height: 48px;
        z-index: 3;
    }

}

.e-header-title-label {
    align-self: flex-start;
    margin-right: 8px;
    min-height: 32px;

    .common-input__label {
        display: none;
    }
}

.e-header-field {
    align-self: flex-start;
    margin-right: 8px;
    min-height: 32px;

    .common-input__label {
        display: none;
    }
}
