jest.mock('@sage/bms-dashboard', () => ({
    DragDropCanvasWrapper: () => {
        return <div data-testid="bms-dashboard">BMS dashboard placeholder</div>;
    },
}));
jest.mock('../../../field/label/async-label-component');

jest.mock('carbon-react/esm/components/action-popover', () => {
    return {
        ...jest.requireActual('carbon-react/esm/components/action-popover'),
        ActionPopover: ({ children, ...props }: { children: any }) => (
            <div data-testid="e-action-popover-button-mock" data-component="action-popover-button" {...props}>
                {children}
            </div>
        ),
        ActionPopoverItem: ({ children, ...props }: { children: any }) => (
            <div data-testid="e-popover-action-single-button-mock" {...props}>
                {children}
            </div>
        ),
    };
});

import {
    addBlockToState,
    addFieldToState,
    addPageControlObject,
    addSectionToState,
    getMockPageDefinition,
    getMockState,
    getMockStore,
    applyActionMocks,
    addPageActionToState,
} from '../../../../__tests__/test-helpers';

import * as dom from '../../../../utils/dom';
import * as React from 'react';
import { Provider } from 'react-redux';
import type { MockStoreEnhanced } from 'redux-mock-store';
import type { XtremAppState } from '../../../../redux';
import type { ReduxResponsive } from '../../../../redux/state';
import * as shortcutService from '../../../../service/shortcut-service';
import type { PageArticleItem } from '../../../../service/layout-types';
import type { Page } from '../../../../service/page';
import type { PageDefinition } from '../../../../service/page-definition';
import { ContextType } from '../../../../types';
import { FieldKey } from '../../../types';
import type { XtremHeaderExternalProps } from '../xtrem-header';
import { ConnectedXtremHeader } from '../xtrem-header';
import type { SectionControlObject } from '../../section/section-control-object';
import type { PageProperties, SectionProperties } from '../../container-properties';
import * as validationService from '../../../../service/validation-service';
import { cleanup, fireEvent, render } from '@testing-library/react';
import * as router from '../../../../service/router';
import type { PageDecoratorProperties } from '../page-decorator';
import { HEADER_IMAGE, HEADER_TITLE, NEW_PAGE } from '../../../../utils/constants';
import type { FileValue } from '../../../field/file/file-types';
import { navigationPanelId } from '../../navigation-panel/navigation-panel-types';
import { nestedFields } from '../../../..';
import '@testing-library/jest-dom';
import { getPagePropertiesFromState } from '../../../../utils/state-utils';

describe('Header component', () => {
    const screenId = 'TestPage';
    let mockStore: MockStoreEnhanced<XtremAppState>;
    let state: XtremAppState;
    let headerLineItem: Partial<PageArticleItem>;
    let pageDefinition: PageDefinition;
    let defaultXtremHeaderProps: XtremHeaderExternalProps;
    let subscribeSpy: jest.SpyInstance;
    let unsubscribeSpy: jest.SpyInstance;
    let sectionValidationMessageSpy: jest.SpyInstance;
    let scrollWithAnimationToSpy: jest.SpyInstance;
    let isSection2HiddenMock: jest.Mock;

    beforeEach(() => {
        isSection2HiddenMock = jest.fn().mockReturnValue(false);
        state = getMockState();
        state.screenDefinitions[screenId] = getMockPageDefinition(screenId);

        const fieldItem = addFieldToState(FieldKey.Label, state, screenId, 'field1', {});
        headerLineItem = addBlockToState(state, screenId, 'headerLine', { title: 'Test Header Line Block Title' }, [
            fieldItem,
        ]);
        addSectionToState(state, screenId, 'section1', { title: 'Section 1', isHidden: false });
        addSectionToState(state, screenId, 'section2', {
            title: 'Section 2',
            isHidden: isSection2HiddenMock,
        });
        addSectionToState(state, screenId, 'section3', { title: 'Section 3', isHidden: false, indicatorContent: '5' });
        addPageControlObject(state, screenId, {
            title: 'Test page title',
        });

        pageDefinition = state.screenDefinitions[screenId] as PageDefinition;
        pageDefinition.metadata.uiComponentProperties[navigationPanelId] = {
            mobileCard: { image: nestedFields.image({ bind: '_id' }) },
        } as any;

        pageDefinition.page = {
            ...pageDefinition.page,
            headerLineBlock: headerLineItem,
            getSerializedValues: jest.fn(),
        } as Page;

        mockStore = getMockStore(state);

        defaultXtremHeaderProps = {
            activeSection: 'section1',
            areNavigationTabsHidden: false,
            contextType: ContextType.page,
            hasNavigationPanel: false,
            headerLineBlock: headerLineItem,
            pageBodyRef: React.createRef(),
            screenId,
            availableColumns: 12,
            setActiveSection: jest.fn(),
        };
        subscribeSpy = jest.spyOn(shortcutService, 'subscribe');
        unsubscribeSpy = jest.spyOn(shortcutService, 'unsubscribe').mockImplementation();
        sectionValidationMessageSpy = jest
            .spyOn(validationService, 'getSectionValidationMessage')
            .mockImplementation(() => null);
        scrollWithAnimationToSpy = jest.spyOn(dom, 'scrollWithAnimationTo');
    });

    afterEach(() => {
        cleanup();
        jest.resetAllMocks();
        applyActionMocks();
    });

    const getXtremHeaderComponent = (
        xtremHeaderProps: Partial<XtremHeaderExternalProps> = defaultXtremHeaderProps,
    ): React.ReactElement => {
        const allXtremHeaderProps = { ...defaultXtremHeaderProps, ...xtremHeaderProps };
        return (
            <Provider store={mockStore}>
                <div ref={allXtremHeaderProps.pageBodyRef} />
                <ConnectedXtremHeader
                    activeSection={allXtremHeaderProps.activeSection}
                    areNavigationTabsHidden={allXtremHeaderProps.areNavigationTabsHidden}
                    availableColumns={12}
                    contextType={allXtremHeaderProps.contextType}
                    hasNavigationPanel={allXtremHeaderProps.hasNavigationPanel}
                    headerLineBlock={allXtremHeaderProps.headerLineBlock}
                    pageBodyRef={allXtremHeaderProps.pageBodyRef}
                    screenId={allXtremHeaderProps.screenId}
                    setActiveSection={allXtremHeaderProps.setActiveSection}
                />
            </Provider>
        );
    };

    const mount = (xtremHeaderProps: Partial<XtremHeaderExternalProps> = {}) => {
        return render(getXtremHeaderComponent(xtremHeaderProps));
    };

    describe('Snapshots', () => {
        const renderWithEnzyme = (xtremHeaderProps: Partial<XtremHeaderExternalProps>) => {
            return render(getXtremHeaderComponent(xtremHeaderProps));
        };

        const renderAndMatchSnapshot = (xtremHeaderProps: Partial<XtremHeaderExternalProps>) => {
            const wrapper = renderWithEnzyme(xtremHeaderProps);
            expect(wrapper.container).toMatchSnapshot();
            return wrapper;
        };

        it('should render with default properties', () => {
            renderAndMatchSnapshot({});
        });

        it('should render with sections without title if the context is not page', () => {
            renderAndMatchSnapshot({ contextType: ContextType.dialog });
        });

        describe('Header Titles', () => {
            beforeEach(() => {
                state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
                    ...state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId],
                    subtitle: 'My test subtitle',
                    title: 'My test title',
                } as PageDecoratorProperties<any>;
            });

            it('should render with a title', () => {
                const wrapper = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                expect(wrapper.baseElement.querySelector('.e-header-title')).toHaveTextContent('My test title');
            });

            it('should render with a subtitle', () => {
                const wrapper = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                expect(wrapper.baseElement.querySelector('.e-header-subtitle')).toHaveTextContent('My test subtitle');
            });

            it('should render title and subtitle if object type is not defined', () => {
                state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
                    ...state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId],
                    idField() {
                        return 'ID Field Value';
                    },
                } as PageDecoratorProperties<any>;
                const wrapper = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                expect(wrapper.baseElement.querySelector('.e-header-title')).toHaveTextContent('My test title');
                expect(wrapper.baseElement.querySelector('.e-header-subtitle')).toHaveTextContent('My test subtitle');
            });

            describe('given defined object type', () => {
                beforeEach(() => {
                    state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
                        ...state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId],
                        objectTypePlural: 'Object Type (Plural)',
                        objectTypeSingular: 'Object Type (Singular)',
                    } as PageDecoratorProperties<any>;
                });

                it('should render title as [objectTypePlural] and not render subtitle', () => {
                    const wrapper = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                    expect(wrapper.baseElement.querySelector('.e-header-title')).toHaveTextContent(
                        'Object Type (Plural)',
                    );
                    expect(wrapper.baseElement.querySelector('.e-header-subtitle')).toBeNull();
                });

                it('should render title as "[objectTypeSingluar] [idField]" and not render subtitle if idField is defined', () => {
                    state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
                        ...state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId],
                        idField() {
                            return 'ID Field Value';
                        },
                    } as PageDecoratorProperties<any>;
                    const wrapper = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                    expect(wrapper.baseElement.querySelector('.e-header-title')).toHaveTextContent(
                        'Object Type (Singular) ID Field Value',
                    );
                    expect(wrapper.baseElement.querySelector('.e-header-subtitle')).toBeNull();
                });
            });
        });

        it('should render with sections without title', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties.section3.title = undefined;
            renderAndMatchSnapshot({});
        });

        it('should render with sections without title if it set to be hidden', () => {
            state.screenDefinitions[screenId].metadata.uiComponentProperties[screenId] = {
                isTitleHidden: true,
            } as any;
            renderAndMatchSnapshot({});
        });

        it('should not render hidden sections', () => {
            (pageDefinition.metadata.uiComponentProperties.section1 as SectionProperties).isHidden = true;
            (pageDefinition.metadata.uiComponentProperties.section2 as SectionProperties).isHidden = true;
            (pageDefinition.metadata.uiComponentProperties.section3 as SectionProperties).isHidden = true;
            const modifiedPage: unknown = {
                ...pageDefinition.page,
                headerLineBlock: undefined,
                $businessActions: undefined,
            };
            pageDefinition.page = modifiedPage as Page;
            pageDefinition.metadata.pageActions = {};
            renderAndMatchSnapshot({ headerLineBlock: null });
        });

        it('should display the header tabs', () => {
            const { container } = mount({ areNavigationTabsHidden: false });
            const headerTabs = container.querySelectorAll('.e-header-nav');
            expect(headerTabs).toHaveLength(1);
        });

        it('should display the header tabs with validation issues', () => {
            sectionValidationMessageSpy.mockImplementation((screenId, sectionId) =>
                sectionId === 'section2' ? 'Oops some error' : null,
            );
            const { container } = mount({ areNavigationTabsHidden: false });
            expect(container.querySelectorAll('.e-xtrem-tab-item.e-xtrem-tab-item-invalid').length).toEqual(1);
        });

        it('should display the header tabs without validation issues', () => {
            sectionValidationMessageSpy.mockImplementation(() => null);
            const { container } = mount({ areNavigationTabsHidden: false });
            expect(container.querySelectorAll('.e-xtrem-tab-item.e-xtrem-tab-item-invalid').length).toEqual(0);
        });

        it('should display the header tabs with indicator content', () => {
            sectionValidationMessageSpy.mockImplementation(() => null);
            const { queryByTestId } = mount({ areNavigationTabsHidden: false });
            expect(queryByTestId('e-xtrem-tab-bind-section3-indicator-content', { exact: false })).toHaveTextContent(
                '5',
            );
        });

        it('should NOT display the header tabs', () => {
            const { container } = mount({ areNavigationTabsHidden: true });
            const headerTabs = container.querySelectorAll('.e-header-nav');
            expect(headerTabs).toHaveLength(0);
        });

        it('should render with a header section and exclude the header sections from the tab control', () => {
            (pageDefinition.metadata.uiComponentProperties[screenId] as PageProperties<Page>).headerSection = () =>
                state.screenDefinitions[screenId].metadata.controlObjects.section1 as SectionControlObject;
            const { container } = mount();
            expect(container.querySelectorAll('.e-header-section')).toHaveLength(1);
            expect(container.querySelectorAll('.e-xtrem-tab-item')).toHaveLength(2);
        });

        it('should not render with the header section if no header section is defined', () => {
            const { container } = mount();
            expect(container.querySelectorAll('.e-header-section')).toHaveLength(0);
            expect(container.querySelectorAll('.e-xtrem-tab-item')).toHaveLength(3);
        });

        it('should render with navigation panel opening button', () => {
            renderAndMatchSnapshot({ hasNavigationPanel: true });
        });

        it('should set active section the first one if the current one become hidden', async () => {
            isSection2HiddenMock.mockReturnValue(false);
            const spySetActiveSection = jest.fn();
            const { rerender } = render(
                getXtremHeaderComponent({ activeSection: 'section1', setActiveSection: spySetActiveSection }),
            );
            expect(spySetActiveSection).not.toHaveBeenCalled();
            rerender(getXtremHeaderComponent({ activeSection: 'section2', setActiveSection: spySetActiveSection }));
            expect(spySetActiveSection).not.toHaveBeenCalled();
            rerender(getXtremHeaderComponent({ activeSection: 'section3', setActiveSection: spySetActiveSection }));
            isSection2HiddenMock.mockReturnValue(true);
            rerender(getXtremHeaderComponent({ activeSection: 'section2', setActiveSection: spySetActiveSection }));
            expect(spySetActiveSection).toHaveBeenCalledWith('section1');
        });

        it('should render wizard in mobile when pageMode is wizard', () => {
            (pageDefinition.metadata.uiComponentProperties[screenId] as PageProperties<Page>).mode = 'wizard';
            const browser: Partial<ReduxResponsive> = {
                is: {
                    l: true,
                    m: false,
                    s: false,
                    xs: false,
                },
            };
            state.browser = { ...state.browser, ...browser };

            renderAndMatchSnapshot({});
        });

        it('should render the next and prev arrow control if the page has a navigation panel', () => {
            const wrapper = render(
                getXtremHeaderComponent({ contextType: ContextType.page, hasNavigationPanel: true }),
            );

            expect(wrapper.queryByTestId('e-header-navigation-arrow-container')).not.toBeNull();
        });

        it('should not render the toggle button if the page is new', () => {
            (state.screenDefinitions[screenId] as PageDefinition).queryParameters = { _id: NEW_PAGE };
            const wrapper = render(
                getXtremHeaderComponent({ contextType: ContextType.page, hasNavigationPanel: true }),
            );
            expect(wrapper.queryByTestId('nav-panel-toggle-button-open')).toBeNull();
        });

        it('should not render the next and prev arrow control if the page does not have a navigation panel', () => {
            const wrapper = render(
                getXtremHeaderComponent({ contextType: ContextType.page, hasNavigationPanel: false }),
            );

            expect(wrapper.queryByTestId('e-header-navigation-arrow-container')).toBeNull();
        });
        it('should not render the next and prev arrow control if the page has a navigation panel but is mobile', () => {
            const browser: Partial<ReduxResponsive> = {
                lessThan: {
                    l: true,
                    m: false,
                    s: false,
                    xs: false,
                },
            };
            state.browser = { ...state.browser, ...browser };
            const wrapper = render(
                getXtremHeaderComponent({ contextType: ContextType.page, hasNavigationPanel: false }),
            );

            expect(wrapper.queryByTestId('e-header-navigation-arrow-container')).toBeNull();
        });

        it('should render header field when headerField is defined', () => {
            const quickAction = addPageActionToState(state, screenId, 'quickAction', {
                title: 'Test',
                icon: 'add',
            });
            const pageProperties = getPagePropertiesFromState(screenId, state);
            pageProperties.headerQuickActions = () => [quickAction];
            (pageDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<Page>).headerField =
                () =>
                    ({
                        id: 'field1',
                        type: 'text',
                    }) as any;

            const { container } = render(getXtremHeaderComponent({ contextType: ContextType.page }));

            expect(container.querySelector('.e-header-field')).not.toBeNull();
        });

        describe('interactions', () => {
            it('should trigger the set setActiveSection and scroll the page body when the user clicks on a tab', () => {
                const spySetActiveSection = jest.fn();
                const { queryAllByRole } = mount({ setActiveSection: spySetActiveSection });
                expect(queryAllByRole('tab')).toHaveLength(3);
                expect(scrollWithAnimationToSpy).not.toHaveBeenCalled();
                fireEvent.click(queryAllByRole('tab')[1]);

                expect(scrollWithAnimationToSpy).toHaveBeenCalled();

                expect(spySetActiveSection).toHaveBeenCalled();
                expect(spySetActiveSection.mock.calls[0][0]).toBe('section2');
            });

            it('should not scroll the page body in tabs mode when the user clicks on a tab', () => {
                const spySetActiveSection = jest.fn();
                (pageDefinition.metadata.uiComponentProperties[screenId] as PageProperties<Page>).mode = 'tabs';
                mockStore = getMockStore(state);
                const { queryAllByRole } = mount({ setActiveSection: spySetActiveSection });
                expect(queryAllByRole('tab')).toHaveLength(3);
                expect(scrollWithAnimationToSpy).not.toHaveBeenCalled();
                fireEvent.click(queryAllByRole('tab')[2]);

                expect(scrollWithAnimationToSpy).not.toHaveBeenCalled();
            });

            it('should subscribe navigation shortcuts on mount', () => {
                expect(subscribeSpy).not.toHaveBeenCalled();
                mount();
                expect(subscribeSpy).toHaveBeenCalledTimes(11);
                expect(subscribeSpy.mock.calls[0][0].sort()).toEqual(['arrowup', 'escape']);
                expect(subscribeSpy.mock.calls[1][0].sort()).toEqual(['arrowdown', 'escape']);
                expect(subscribeSpy.mock.calls[2][0].sort()).toEqual(['1', 'escape']);
                expect(subscribeSpy.mock.calls[3][0].sort()).toEqual(['2', 'escape']);
                expect(subscribeSpy.mock.calls[4][0].sort()).toEqual(['3', 'escape']);
                expect(subscribeSpy.mock.calls[5][0].sort()).toEqual(['4', 'escape']);
                expect(subscribeSpy.mock.calls[6][0].sort()).toEqual(['5', 'escape']);
                expect(subscribeSpy.mock.calls[7][0].sort()).toEqual(['6', 'escape']);
                expect(subscribeSpy.mock.calls[8][0].sort()).toEqual(['7', 'escape']);
                expect(subscribeSpy.mock.calls[9][0].sort()).toEqual(['8', 'escape']);
                expect(subscribeSpy.mock.calls[10][0].sort()).toEqual(['9', 'escape']);
            });

            it('should unsubscribe navigation shortcuts on unmount', () => {
                const wrapper = mount();
                expect(unsubscribeSpy).not.toHaveBeenCalled();
                wrapper.unmount();
                expect(unsubscribeSpy).toHaveBeenCalledTimes(11);
            });

            it('should trigger the selectRecord action when the user clicks on close icon', () => {
                const closeRecordMock = jest.fn();
                jest.spyOn(router, 'getRouter').mockImplementation(() => {
                    return {
                        closeRecord: closeRecordMock,
                        hasNextRecord: jest.fn().mockResolvedValue(true),
                        hasPreviousRecord: jest.fn().mockResolvedValue(true),
                    } as any;
                });
                const { queryByTestId } = mount({ hasNavigationPanel: true });
                fireEvent.click(queryByTestId('e-page-close-button')!);
                expect(closeRecordMock).toHaveBeenCalled();
            });
        });

        describe('header images', () => {
            beforeAll(() => {
                global.atob = jest.fn().mockImplementation(e => {
                    return e;
                });
            });

            afterAll(() => {
                (global.atob as any).mockRestore();
            });

            it('should display the header image when headerImage is provided', () => {
                const fileValue: FileValue = {
                    value:
                        '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDADIiJSwlHzIsKSw4NTI7S31RS0VFS5ltc1p9tZ++u7Kfr6zI4f/zyNT/' +
                        '16yv+v/9////////wfD/////////////2wBDATU4OEtCS5NRUZP/zq/O/////////////////////////////////////////////////////////////////' +
                        '///wAARCAAYAEADAREAAhEBAxEB/8QAGQAAAgMBAAAAAAAAAAAAAAAAAQMAAgQF/8QAJRABAAIBBAEEAgMAAAAAAAAAAQIRAAMSITEEEyJBgTORUWFx/8QAFA' +
                        'EBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AOgM52xQDrjvAV5Xv0vfKUALlTQfeBm0HThMNHXkL0Lw/swN5qg' +
                        'A8yT4MCS1OEOJV8mBz9Z05yfW8iSx7p4j+jA1aD6Wj7ZMzstsfvAas4UyRHvjrAkC9KhpLMClQntlqFc2X1gUj4viwVObKrddH9YDoHvuujAEuNV+bLwFS8Xx' +
                        'dSr+Cq3Vf+4F5RgQl6ZR2p1eAzU/HX80YBYyJLCuexwJCO2O1bwCRidAfWBSctswbI12GAJT3yiwFR7+MBjGK2g/WAJR3FdF84E2rK5VR0YH/9k=',
                };

                state.screenDefinitions[screenId].values[HEADER_IMAGE] = { value: fileValue.value };
                const { container } = mount();

                const headerImage = container.querySelector('.e-header-image-container .e-header-image');
                expect(headerImage).not.toBeNull();
                expect(headerImage).toHaveStyle(`background-image: url('data:image;base64,${fileValue.value}')`);
            });

            it('should display initials when headerImage is not provided but headerTitle is provided', () => {
                state.screenDefinitions[screenId].values[HEADER_TITLE] = 'John Doe';

                const { container } = mount();

                const initials = container.querySelector('.e-header-image-container .e-portrait');
                expect(initials).not.toBeNull();
                expect(initials).toHaveTextContent('JD');
            });

            it('should display initials when no headerImage and headerTitle provided, but title is provided', () => {
                state.screenDefinitions[screenId].values[HEADER_IMAGE] = null;
                state.screenDefinitions[screenId].values[HEADER_TITLE] = null;
                (pageDefinition.metadata.uiComponentProperties[screenId] as PageProperties<Page>).title = 'John Doe';

                const { container } = mount();

                const initials = container.querySelector('.e-header-image-container .e-portrait');
                expect(initials).not.toBeNull();
                expect(initials).toHaveTextContent('JD');
            });

            it('should display the "no available image" placeholder when no title and neither headerImage nor headerTitle is provided', () => {
                state.screenDefinitions[screenId].values[HEADER_IMAGE] = null;
                state.screenDefinitions[screenId].values[HEADER_TITLE] = null;
                (pageDefinition.metadata.uiComponentProperties[screenId] as PageProperties<Page>).title = undefined;

                const { container } = mount();

                const noImagePlaceholder = container.querySelector('.e-header-image-container .e-header-image');
                expect(noImagePlaceholder).not.toBeNull();
                expect(noImagePlaceholder).toHaveAttribute('src', '/images/no-available-image.svg');
            });
        });

        describe('Quick actions merging', () => {
            it('should merge single quick action into empty dropdown when headerField exists and screen is less than l', () => {
                const quickAction = addPageActionToState(state, screenId, 'quickAction', {
                    title: 'Test',
                    icon: 'add',
                });
                const pageProperties = getPagePropertiesFromState(screenId, state);
                pageProperties.headerQuickActions = () => [quickAction];
                (pageDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<Page>).headerField =
                    () =>
                        ({
                            id: 'field1',
                            type: 'text',
                        }) as any;
                const browser: Partial<ReduxResponsive> = {
                    lessThan: {
                        l: true,
                        m: false,
                        s: false,
                        xs: false,
                    },
                };
                state.browser = { ...state.browser, ...browser };
                const { queryByTestId } = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                expect(queryByTestId('e-header-quick-action-label-test')).toBeNull();
                expect(queryByTestId('e-popover-action-single-button')).toHaveAttribute('aria-label', 'Test');
            });
            it('should merge single quick action into non-empty dropdown when headerField exists and screen is less than l', () => {
                const dropdownAction = addPageActionToState(state, screenId, 'dropdownAction', {
                    title: 'dropdown',
                    icon: 'add',
                });
                const quickAction = addPageActionToState(state, screenId, 'quickAction', {
                    title: 'quick',
                    icon: 'add',
                });
                const pageProperties = getPagePropertiesFromState(screenId, state);
                pageProperties.headerDropDownActions = (): any => [dropdownAction];
                pageProperties.headerQuickActions = () => [quickAction];

                (pageDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<Page>).headerField =
                    () =>
                        ({
                            id: 'field1',
                            type: 'text',
                        }) as any;
                const browser: Partial<ReduxResponsive> = {
                    lessThan: {
                        l: true,
                        m: false,
                        s: false,
                        xs: false,
                    },
                };
                state.browser = { ...state.browser, ...browser };
                const { queryByTestId, queryAllByTestId } = render(
                    getXtremHeaderComponent({ contextType: ContextType.page }),
                );
                expect(queryByTestId('e-header-quick-action-label-test')).toBeNull();
                const menuItems = queryAllByTestId('e-popover-action-single-button-mock');

                expect(menuItems).toHaveLength(2);
                expect(menuItems[0]).not.toHaveAttribute('aria-disabled', 'true');
                expect(menuItems[0]).toHaveTextContent('quick');
                expect(menuItems[1]).not.toHaveAttribute('aria-disabled', 'true');
                expect(menuItems[1]).toHaveTextContent('dropdown');
            });

            it('should not merge if screen is l', () => {
                const quickAction = addPageActionToState(state, screenId, 'quickAction', {
                    title: 'Test',
                    icon: 'add',
                });
                const pageProperties = getPagePropertiesFromState(screenId, state);
                pageProperties.headerQuickActions = () => [quickAction];
                (pageDefinition.metadata.uiComponentProperties[screenId] as PageDecoratorProperties<Page>).headerField =
                    () =>
                        ({
                            id: 'field1',
                            type: 'text',
                        }) as any;
                const browser: Partial<ReduxResponsive> = {
                    lessThan: {
                        l: false,
                        m: false,
                        s: false,
                        xs: false,
                    },
                };
                state.browser = { ...state.browser, ...browser };
                const { queryByTestId } = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                expect(queryByTestId('e-header-quick-action-label-test')).not.toBeNull();
                expect(queryByTestId('e-popover-action-single-button')).toBeNull();
            });

            it('should not merge if header field is not defined', () => {
                const quickAction = addPageActionToState(state, screenId, 'quickAction', {
                    title: 'Test',
                    icon: 'add',
                });
                const pageProperties = getPagePropertiesFromState(screenId, state);
                pageProperties.headerQuickActions = () => [quickAction];

                const browser: Partial<ReduxResponsive> = {
                    lessThan: {
                        l: true,
                        m: false,
                        s: false,
                        xs: false,
                    },
                };
                state.browser = { ...state.browser, ...browser };
                const { queryByTestId } = render(getXtremHeaderComponent({ contextType: ContextType.page }));
                expect(queryByTestId('e-header-quick-action-label-test')).not.toBeNull();
                expect(queryByTestId('e-popover-action-single-button')).toBeNull();
            });
        });
    });
});
