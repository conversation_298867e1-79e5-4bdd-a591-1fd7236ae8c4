// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`Header component Snapshots should not render hidden sections 1`] = `
.c1 {
  font-weight: 500;
  font-size: var(--fontSizes300);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div />
  <header
    class="e-header e-header-context-page e-no-tabs"
    data-testid="e-header"
  >
    <div
      class="e-header-title-row "
    >
      <div
        class="e-header-title-col"
      >
        <div
          class="e-header-title-container"
        >
          <div
            class="e-header-image-container"
          >
            <div
              class="e-portrait"
            >
              <div
                class="c0"
                data-component="portrait"
                shape="circle"
              >
                <div
                  class="c1"
                  data-element="initials"
                >
                  TPT
                </div>
              </div>
            </div>
          </div>
          <div>
            <h1
              class="e-header-title"
            >
              Test page title
            </h1>
          </div>
        </div>
      </div>
    </div>
  </header>
</div>
`;

exports[`Header component Snapshots should render with default properties 1`] = `
.c2 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

.c3 {
  margin-left: 4px;
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  background-color: var(--colorsSemanticNeutral500);
  min-height: 16px;
  line-height: 16px;
  font-size: 12px;
  padding: 0 8px;
}

.c1 {
  font-weight: 500;
  font-size: var(--fontSizes300);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div />
  <header
    class="e-header e-header-context-page"
    data-testid="e-header"
  >
    <div
      class="e-header-title-row "
    >
      <div
        class="e-header-title-col"
      >
        <div
          class="e-header-title-container"
        >
          <div
            class="e-header-image-container"
          >
            <div
              class="e-portrait"
            >
              <div
                class="c0"
                data-component="portrait"
                shape="circle"
              >
                <div
                  class="c1"
                  data-element="initials"
                >
                  TPT
                </div>
              </div>
            </div>
          </div>
          <div>
            <h1
              class="e-header-title"
            >
              Test page title
            </h1>
          </div>
        </div>
      </div>
      <div
        class="e-header-line-block"
      >
        <div
          class="e-grid-column e-grid-column-4 e-container-parent e-block-parent e-block-context-header"
          style="grid-column: span 4;"
        >
          <div
            class="e-block"
            data-testid="e-block-field e-field-label-testHeaderLineBlockTitle e-field-bind-headerLine"
          >
            <div
              class="e-block-header"
            >
              <h3
                class="e-block-title"
                data-testid="e-block-title"
              >
                Test Header Line Block Title
              </h3>
            </div>
            <div
              class="e-block-body"
            >
              <div
                class="e-grid-row e-grid-row-4 "
                style="grid-template-columns: repeat(4, 1fr); padding: 0px 16px 0px 16px;"
              >
                <div
                  class="e-grid-column e-grid-column-2 e-field-grid-column"
                  style="grid-column: span 2;"
                >
                  <div
                    class="e-field e-label-field e-context-header"
                    data-testid="e-label-field e-field-bind-field1"
                  >
                    <label
                      class="common-input__label"
                      data-element="label"
                      data-testid="e-field-label"
                    />
                    <span
                      class="e-pill-wrapper"
                    >
                      <span
                        class="c2"
                        data-component="pill"
                        style="cursor: default;"
                      >
                        Test value
                      </span>
                    </span>
                    <span
                      class="common-input__help-text"
                      data-element="help"
                      data-testid="e-field-helper-text"
                    >
                       
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="e-header-nav"
      data-testid="e-header-nav-tabs"
    >
      <div
        class="e-xtrem-tabs"
      >
        <div
          class="e-xtrem-tab-container"
          role="tablist"
        >
          <button
            aria-selected="true"
            class="e-xtrem-tab-item e-xtrem-tab-item-active"
            data-pendoid="sectionTab-section1"
            data-testid="e-xtrem-tab-section1 e-xtrem-tab-bind-section1"
            role="tab"
            tabindex="0"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 1
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section2"
            data-testid="e-xtrem-tab-section2 e-xtrem-tab-bind-section2"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 2
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section3"
            data-testid="e-xtrem-tab-section3 e-xtrem-tab-bind-section3"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 3
            </span>
            <span
              class="c3"
              data-component="pill"
              data-testid="e-xtrem-tab-section3-indicator-content e-xtrem-tab-bind-section3-indicator-content"
            >
              5
            </span>
          </button>
        </div>
      </div>
    </div>
  </header>
</div>
`;

exports[`Header component Snapshots should render with navigation panel opening button 1`] = `
.c2 {
  position: relative;
  color: var(--colorsYin030);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c2:hover {
  color: var(--colorsYin030);
  background-color: transparent;
}

.c2::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e907";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c3 {
  position: relative;
  color: var(--colorsYin030);
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c3:hover {
  color: var(--colorsYin030);
  background-color: transparent;
}

.c3::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e901";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c7 {
  position: relative;
  color: #335b70ff;
  background-color: transparent;
  vertical-align: middle;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 24px;
  width: 24px;
}

.c7:hover {
  color: #284859;
  background-color: transparent;
}

.c7::before {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: CarbonIcons;
  content: "\\e91e";
  font-style: normal;
  font-weight: normal;
  vertical-align: middle;
  font-size: var(--sizing250);
  line-height: var(--sizing250);
  display: block;
}

.c0 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c0.c0 {
  padding: var(--spacing000);
}

.c0:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c0:hover {
  cursor: not-allowed;
}

.c0::-moz-focus-inner {
  border: none;
}

.c0 .c1 {
  color: var(--colorsActionMinorYin030);
  background-color: transparent;
  position: relative;
}

.c0 .c1:focus {
  border: none;
}

.c6 {
  background: transparent;
  border: none;
  border-radius: var(--borderRadius050);
}

.c6.c6 {
  padding: var(--spacing000);
}

.c6:focus {
  -webkit-appearance: none;
  -webkit-box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  box-shadow: 0px 0px 0px var(--borderWidth300) var(--colorsSemanticFocus500),0px 0px 0px var(--borderWidth600) var(--colorsUtilityYin090);
  outline: transparent 3px solid;
}

.c6:hover {
  cursor: pointer;
}

.c6::-moz-focus-inner {
  border: none;
}

.c6 .c1 {
  position: relative;
}

.c6 .c1:focus {
  border: none;
}

.c8 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

.c9 {
  margin-left: 4px;
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  background-color: var(--colorsSemanticNeutral500);
  min-height: 16px;
  line-height: 16px;
  font-size: 12px;
  padding: 0 8px;
}

.c5 {
  font-weight: 500;
  font-size: var(--fontSizes300);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c4 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div />
  <header
    class="e-header e-header-context-page"
    data-testid="e-header"
  >
    <div
      class="e-header-title-row "
    >
      <div
        class="e-header-title-col"
      >
        <div
          class="e-header-title-container"
        >
          <div
            class="e-header-navigation-arrow-container"
            data-testid="e-header-navigation-arrow-container"
          >
            <button
              aria-label="Previous record"
              class="c0"
              data-component="icon-button"
              data-pendoid="headerPreviousRecord"
              data-testid="e-header-navigation-arrow-previous"
              disabled=""
              type="button"
            >
              <span
                class="c1 c2"
                color="#000000e6"
                data-component="icon"
                data-element="arrow_up"
                data-role="icon"
                disabled=""
                font-size="small"
                type="arrow_up"
              />
            </button>
            <button
              aria-label="Next record"
              class="c0"
              data-component="icon-button"
              data-pendoid="headerNextRecord"
              data-testid="e-header-navigation-arrow-next"
              disabled=""
              type="button"
            >
              <span
                class="c1 c3"
                color="#000000e6"
                data-component="icon"
                data-element="arrow_down"
                data-role="icon"
                disabled=""
                font-size="small"
                type="arrow_down"
              />
            </button>
          </div>
          <div
            class="e-header-image-container"
          >
            <div
              class="e-portrait"
            >
              <div
                class="c4"
                data-component="portrait"
                shape="circle"
              >
                <div
                  class="c5"
                  data-element="initials"
                >
                  TPT
                </div>
              </div>
            </div>
          </div>
          <div>
            <h1
              class="e-header-title"
            >
              Test page title
            </h1>
          </div>
        </div>
      </div>
      <span
        class="e-header-line-icon-group e-header-line-icon-group-close"
      >
        <div
          class="e-header-close-icon-col"
        >
          <button
            aria-label="Close record"
            class="c6"
            data-component="icon-button"
            data-pendoid="closeRecord"
            data-testid="e-page-close-button"
            type="button"
          >
            <span
              class="c1 c7"
              color="#335b70ff"
              data-component="icon"
              data-element="close"
              data-role="icon"
              font-size="small"
              type="close"
            />
          </button>
        </div>
      </span>
      <div
        class="e-header-line-block"
      >
        <div
          class="e-grid-column e-grid-column-4 e-container-parent e-block-parent e-block-context-header"
          style="grid-column: span 4;"
        >
          <div
            class="e-block"
            data-testid="e-block-field e-field-label-testHeaderLineBlockTitle e-field-bind-headerLine"
          >
            <div
              class="e-block-header"
            >
              <h3
                class="e-block-title"
                data-testid="e-block-title"
              >
                Test Header Line Block Title
              </h3>
            </div>
            <div
              class="e-block-body"
            >
              <div
                class="e-grid-row e-grid-row-4 "
                style="grid-template-columns: repeat(4, 1fr); padding: 0px 16px 0px 16px;"
              >
                <div
                  class="e-grid-column e-grid-column-2 e-field-grid-column"
                  style="grid-column: span 2;"
                >
                  <div
                    class="e-field e-label-field e-context-header"
                    data-testid="e-label-field e-field-bind-field1"
                  >
                    <label
                      class="common-input__label"
                      data-element="label"
                      data-testid="e-field-label"
                    />
                    <span
                      class="e-pill-wrapper"
                    >
                      <span
                        class="c8"
                        data-component="pill"
                        style="cursor: default;"
                      >
                        Test value
                      </span>
                    </span>
                    <span
                      class="common-input__help-text"
                      data-element="help"
                      data-testid="e-field-helper-text"
                    >
                       
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="e-header-nav"
      data-testid="e-header-nav-tabs"
    >
      <div
        class="e-xtrem-tabs"
      >
        <div
          class="e-xtrem-tab-container"
          role="tablist"
        >
          <button
            aria-selected="true"
            class="e-xtrem-tab-item e-xtrem-tab-item-active"
            data-pendoid="sectionTab-section1"
            data-testid="e-xtrem-tab-section1 e-xtrem-tab-bind-section1"
            role="tab"
            tabindex="0"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 1
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section2"
            data-testid="e-xtrem-tab-section2 e-xtrem-tab-bind-section2"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 2
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section3"
            data-testid="e-xtrem-tab-section3 e-xtrem-tab-bind-section3"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 3
            </span>
            <span
              class="c9"
              data-component="pill"
              data-testid="e-xtrem-tab-section3-indicator-content e-xtrem-tab-bind-section3-indicator-content"
            >
              5
            </span>
          </button>
        </div>
      </div>
    </div>
  </header>
</div>
`;

exports[`Header component Snapshots should render with sections without title 1`] = `
.c2 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

.c1 {
  font-weight: 500;
  font-size: var(--fontSizes300);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

<div>
  <div />
  <header
    class="e-header e-header-context-page"
    data-testid="e-header"
  >
    <div
      class="e-header-title-row "
    >
      <div
        class="e-header-title-col"
      >
        <div
          class="e-header-title-container"
        >
          <div
            class="e-header-image-container"
          >
            <div
              class="e-portrait"
            >
              <div
                class="c0"
                data-component="portrait"
                shape="circle"
              >
                <div
                  class="c1"
                  data-element="initials"
                >
                  TPT
                </div>
              </div>
            </div>
          </div>
          <div>
            <h1
              class="e-header-title"
            >
              Test page title
            </h1>
          </div>
        </div>
      </div>
      <div
        class="e-header-line-block"
      >
        <div
          class="e-grid-column e-grid-column-4 e-container-parent e-block-parent e-block-context-header"
          style="grid-column: span 4;"
        >
          <div
            class="e-block"
            data-testid="e-block-field e-field-label-testHeaderLineBlockTitle e-field-bind-headerLine"
          >
            <div
              class="e-block-header"
            >
              <h3
                class="e-block-title"
                data-testid="e-block-title"
              >
                Test Header Line Block Title
              </h3>
            </div>
            <div
              class="e-block-body"
            >
              <div
                class="e-grid-row e-grid-row-4 "
                style="grid-template-columns: repeat(4, 1fr); padding: 0px 16px 0px 16px;"
              >
                <div
                  class="e-grid-column e-grid-column-2 e-field-grid-column"
                  style="grid-column: span 2;"
                >
                  <div
                    class="e-field e-label-field e-context-header"
                    data-testid="e-label-field e-field-bind-field1"
                  >
                    <label
                      class="common-input__label"
                      data-element="label"
                      data-testid="e-field-label"
                    />
                    <span
                      class="e-pill-wrapper"
                    >
                      <span
                        class="c2"
                        data-component="pill"
                        style="cursor: default;"
                      >
                        Test value
                      </span>
                    </span>
                    <span
                      class="common-input__help-text"
                      data-element="help"
                      data-testid="e-field-helper-text"
                    >
                       
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="e-header-nav"
      data-testid="e-header-nav-tabs"
    >
      <div
        class="e-xtrem-tabs"
      >
        <div
          class="e-xtrem-tab-container"
          role="tablist"
        >
          <button
            aria-selected="true"
            class="e-xtrem-tab-item e-xtrem-tab-item-active"
            data-pendoid="sectionTab-section1"
            data-testid="e-xtrem-tab-section1 e-xtrem-tab-bind-section1"
            role="tab"
            tabindex="0"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 1
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section2"
            data-testid="e-xtrem-tab-section2 e-xtrem-tab-bind-section2"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 2
            </span>
          </button>
        </div>
      </div>
    </div>
  </header>
</div>
`;

exports[`Header component Snapshots should render with sections without title if it set to be hidden 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

.c1 {
  margin-left: 4px;
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  background-color: var(--colorsSemanticNeutral500);
  min-height: 16px;
  line-height: 16px;
  font-size: 12px;
  padding: 0 8px;
}

<div>
  <div />
  <header
    class="e-header e-header-context-page"
    data-testid="e-header"
  >
    <div
      class="e-header-title-row "
    >
      <div
        class="e-header-line-block"
      >
        <div
          class="e-grid-column e-grid-column-4 e-container-parent e-block-parent e-block-context-header"
          style="grid-column: span 4;"
        >
          <div
            class="e-block"
            data-testid="e-block-field e-field-label-testHeaderLineBlockTitle e-field-bind-headerLine"
          >
            <div
              class="e-block-header"
            >
              <h3
                class="e-block-title"
                data-testid="e-block-title"
              >
                Test Header Line Block Title
              </h3>
            </div>
            <div
              class="e-block-body"
            >
              <div
                class="e-grid-row e-grid-row-4 "
                style="grid-template-columns: repeat(4, 1fr); padding: 0px 16px 0px 16px;"
              >
                <div
                  class="e-grid-column e-grid-column-2 e-field-grid-column"
                  style="grid-column: span 2;"
                >
                  <div
                    class="e-field e-label-field e-context-header"
                    data-testid="e-label-field e-field-bind-field1"
                  >
                    <label
                      class="common-input__label"
                      data-element="label"
                      data-testid="e-field-label"
                    />
                    <span
                      class="e-pill-wrapper"
                    >
                      <span
                        class="c0"
                        data-component="pill"
                        style="cursor: default;"
                      >
                        Test value
                      </span>
                    </span>
                    <span
                      class="common-input__help-text"
                      data-element="help"
                      data-testid="e-field-helper-text"
                    >
                       
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="e-header-nav"
      data-testid="e-header-nav-tabs"
    >
      <div
        class="e-xtrem-tabs"
      >
        <div
          class="e-xtrem-tab-container"
          role="tablist"
        >
          <button
            aria-selected="true"
            class="e-xtrem-tab-item e-xtrem-tab-item-active"
            data-pendoid="sectionTab-section1"
            data-testid="e-xtrem-tab-section1 e-xtrem-tab-bind-section1"
            role="tab"
            tabindex="0"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 1
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section2"
            data-testid="e-xtrem-tab-section2 e-xtrem-tab-bind-section2"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 2
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section3"
            data-testid="e-xtrem-tab-section3 e-xtrem-tab-bind-section3"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 3
            </span>
            <span
              class="c1"
              data-component="pill"
              data-testid="e-xtrem-tab-section3-indicator-content e-xtrem-tab-bind-section3-indicator-content"
            >
              5
            </span>
          </button>
        </div>
      </div>
    </div>
  </header>
</div>
`;

exports[`Header component Snapshots should render with sections without title if the context is not page 1`] = `
.c0 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

.c1 {
  margin-left: 4px;
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsSemanticNeutral500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsSemanticNeutralYang100);
  background-color: var(--colorsSemanticNeutral500);
  min-height: 16px;
  line-height: 16px;
  font-size: 12px;
  padding: 0 8px;
}

<div>
  <div />
  <header
    class="e-header e-header-context-dialog"
    data-testid="e-header"
  >
    <div
      class="e-header-title-row "
    >
      <div
        class="e-header-line-block"
      >
        <div
          class="e-grid-column e-grid-column-4 e-container-parent e-block-parent e-block-context-header"
          style="grid-column: span 4;"
        >
          <div
            class="e-block"
            data-testid="e-block-field e-field-label-testHeaderLineBlockTitle e-field-bind-headerLine"
          >
            <div
              class="e-block-header"
            >
              <h3
                class="e-block-title"
                data-testid="e-block-title"
              >
                Test Header Line Block Title
              </h3>
            </div>
            <div
              class="e-block-body"
            >
              <div
                class="e-grid-row e-grid-row-4 "
                style="grid-template-columns: repeat(4, 1fr); padding: 0px 16px 0px 16px;"
              >
                <div
                  class="e-grid-column e-grid-column-2 e-field-grid-column"
                  style="grid-column: span 2;"
                >
                  <div
                    class="e-field e-label-field e-context-header"
                    data-testid="e-label-field e-field-bind-field1"
                  >
                    <label
                      class="common-input__label"
                      data-element="label"
                      data-testid="e-field-label"
                    />
                    <span
                      class="e-pill-wrapper"
                    >
                      <span
                        class="c0"
                        data-component="pill"
                        style="cursor: default;"
                      >
                        Test value
                      </span>
                    </span>
                    <span
                      class="common-input__help-text"
                      data-element="help"
                      data-testid="e-field-helper-text"
                    >
                       
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="e-header-nav"
      data-testid="e-header-nav-tabs"
    >
      <div
        class="e-xtrem-tabs"
      >
        <div
          class="e-xtrem-tab-container"
          role="tablist"
        >
          <button
            aria-selected="true"
            class="e-xtrem-tab-item e-xtrem-tab-item-active"
            data-pendoid="sectionTab-section1"
            data-testid="e-xtrem-tab-section1 e-xtrem-tab-bind-section1"
            role="tab"
            tabindex="0"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 1
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section2"
            data-testid="e-xtrem-tab-section2 e-xtrem-tab-bind-section2"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 2
            </span>
          </button>
          <button
            aria-selected="false"
            class="e-xtrem-tab-item"
            data-pendoid="sectionTab-section3"
            data-testid="e-xtrem-tab-section3 e-xtrem-tab-bind-section3"
            role="tab"
            tabindex="-1"
            type="button"
          >
            <span
              class="e-xtrem-tab-item-text"
            >
              Section 3
            </span>
            <span
              class="c1"
              data-component="pill"
              data-testid="e-xtrem-tab-section3-indicator-content e-xtrem-tab-bind-section3-indicator-content"
            >
              5
            </span>
          </button>
        </div>
      </div>
    </div>
  </header>
</div>
`;

exports[`Header component Snapshots should render wizard in mobile when pageMode is wizard 1`] = `
.c2 {
  font-size: 12px;
  -webkit-letter-spacing: 0.7px;
  -moz-letter-spacing: 0.7px;
  -ms-letter-spacing: 0.7px;
  letter-spacing: 0.7px;
  font-weight: 500;
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  text-align: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  border: 2px solid var(--colorsActionMajor500);
  border-radius: var(--borderRadius025);
  height: auto;
  white-space: nowrap;
  color: var(--colorsActionMajorYang100);
  color: var(--colorsUtilityYin090);
  min-height: 20px;
  line-height: 20px;
  font-size: 14px;
  padding: 0 8px;
}

.c1 {
  font-weight: 500;
  font-size: var(--fontSizes300);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  white-space: nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: inherit;
  width: inherit;
}

.c0 {
  background-color: var(--colorsUtilityReadOnly400);
  color: var(--colorsUtilityYin090);
  min-width: 40px;
  height: 40px;
  overflow: hidden;
  border-radius: var(--borderRadiusCircle);
  border: 1px solid var(--colorsUtilityReadOnly600);
  display: inline-block;
}

.c3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
  font-weight: var(--fontWeights500);
  padding: var(--spacing000);
}

.c4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: right;
  list-style-type: none;
  color: var(--colorsUtilityYin055);
  color: var(--colorsUtilityYin090);
}

.c4::before {
  content: "";
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  display: block;
  margin: 0 16px;
  border-top: var(--sizing025) dashed var(--colorsUtilityYin055);
}

.c4 span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c4:first-child {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c4:first-child::before {
  display: none;
}

.c4::before {
  border-top-color: var(--colorsUtilityYin090);
  border-top-style: solid;
}

.c7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: right;
  list-style-type: none;
  color: var(--colorsUtilityYin055);
}

.c7::before {
  content: "";
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  display: block;
  margin: 0 16px;
  border-top: var(--sizing025) dashed var(--colorsUtilityYin055);
}

.c7 span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.c7:first-child {
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
}

.c7:first-child::before {
  display: none;
}

.c5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.c6 {
  display: block;
  min-width: 16px;
  height: 16px;
  margin-right: 8px;
  text-align: center;
}

<div>
  <div />
  <header
    class="e-header e-header-context-page"
    data-testid="e-header"
  >
    <div
      class="e-header-title-row "
    >
      <div
        class="e-header-title-col"
      >
        <div
          class="e-header-title-container"
        >
          <div
            class="e-header-image-container"
          >
            <div
              class="e-portrait"
            >
              <div
                class="c0"
                data-component="portrait"
                shape="circle"
              >
                <div
                  class="c1"
                  data-element="initials"
                >
                  TPT
                </div>
              </div>
            </div>
          </div>
          <div>
            <h1
              class="e-header-title"
            >
              Test page title
            </h1>
          </div>
        </div>
      </div>
      <div
        class="e-header-line-block"
      >
        <div
          class="e-grid-column e-grid-column-4 e-container-parent e-block-parent e-block-context-header"
          style="grid-column: span 4;"
        >
          <div
            class="e-block"
            data-testid="e-block-field e-field-label-testHeaderLineBlockTitle e-field-bind-headerLine"
          >
            <div
              class="e-block-header"
            >
              <h3
                class="e-block-title"
                data-testid="e-block-title"
              >
                Test Header Line Block Title
              </h3>
            </div>
            <div
              class="e-block-body"
            >
              <div
                class="e-grid-row e-grid-row-4 "
                style="grid-template-columns: repeat(4, 1fr); padding: 0px 16px 0px 16px;"
              >
                <div
                  class="e-grid-column e-grid-column-2 e-field-grid-column"
                  style="grid-column: span 2;"
                >
                  <div
                    class="e-field e-label-field e-context-header"
                    data-testid="e-label-field e-field-bind-field1"
                  >
                    <label
                      class="common-input__label"
                      data-element="label"
                      data-testid="e-field-label"
                    />
                    <span
                      class="e-pill-wrapper"
                    >
                      <span
                        class="c2"
                        data-component="pill"
                        style="cursor: default;"
                      >
                        Test value
                      </span>
                    </span>
                    <span
                      class="common-input__help-text"
                      data-element="help"
                      data-testid="e-field-helper-text"
                    >
                       
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="e-header-wizard-steps-container"
    >
      <div
        class="e-header-wizard-steps"
        data-testid="e-dialog-header-wizard-steps"
      >
        <ol
          class="c3"
          data-component="step-sequence"
          orientation="horizontal"
        >
          <li
            class="c4"
            data-component="step-sequence-item"
            id="section1"
            orientation="horizontal"
          >
            <span
              class="c5"
            >
              <span
                class="c6"
              >
                1
              </span>
              <span>
                Section 1
              </span>
            </span>
          </li>
          <li
            class="c7"
            data-component="step-sequence-item"
            id="section2"
            orientation="horizontal"
          >
            <span
              class="c5"
            >
              <span
                class="c6"
              >
                2
              </span>
              <span>
                Section 2
              </span>
            </span>
          </li>
          <li
            class="c7"
            data-component="step-sequence-item"
            id="section3"
            orientation="horizontal"
          >
            <span
              class="c5"
            >
              <span
                class="c6"
              >
                3
              </span>
              <span>
                Section 3
              </span>
            </span>
          </li>
        </ol>
      </div>
    </div>
  </header>
</div>
`;
