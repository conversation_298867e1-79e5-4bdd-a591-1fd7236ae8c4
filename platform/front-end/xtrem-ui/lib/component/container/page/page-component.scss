@import '../../../render/style/variables.scss';
@import '../../../render/style/mixins.scss';
@import './print-record-button.scss';
@import '../page-360/page-360-switch.scss';
@import '../page-360/page-360-view.scss';
@import './wizard-steps.scss';

.e-page {
    width: 100%;
    display: flex;
    background: var(--colorsUtilityMajor025);
    flex: 1;
    overflow: hidden;

    @include small_and_below {
        display: block;
    }

    &.e-page-dialog {
        background: transparent;
        overflow: hidden;

        .e-page-main-section {
            margin: 0;
            box-shadow: none;
        }

        &.e-page-mode-wizard .e-page-main-section .e-page-body-container .e-page-body.e-page-body-context-dialog {
            padding-top: 0;
        }
    }

    .e-page-main-section {
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 100%;
        width: calc(100% - 320px);
        overflow-y: auto;
        overflow-x: hidden;
        box-shadow: var(--boxShadow100);
        margin: 0 48px;

        @include medium_and_below {
            width: calc(100% - 256px);
        }

        @include small_and_below {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        @include small {
            width: calc(100vw - 100px);
            margin: 0;
            padding: 0;
        }

        @include extra_small {
            flex: 0 0 100%;
            margin: 0;
        }

        .e-page-body-container {
            // Creates a new stacking context for the page body so that absolutely positioned elements are positioned correctly
            isolation: isolate;
            display: grid;
            width: 100%;
            height: 100%;
            min-height: 0;
            overflow-y: visible;

            @include extra_small {
                &.e-detail-panel-open {
                  z-index: 3;
                }
              }

            @media print {
                display: block;
                flex: unset;
                height: fit-content;
                overflow: unset;
            }

            /* When only first and second exist */
            &:not(:has(aside:nth-child(2))) {
                grid-template-columns: 1fr;
                grid-template-rows: 1fr;
                grid-template-areas:
                    "first";
            }

            /* When only first and third exist (second missing) */
            &:has(aside:nth-child(2)) {
                grid-template-columns: 1fr auto;
                grid-template-areas:
                    "first third";
            }

            aside:nth-child(2) {
                grid-area: third;
            }

            .e-page-body {
                flex: 1;
                overflow-x: hidden;
                overflow-y: inherit;
                margin: 0 auto;
                outline: none;
                display: flex;
                flex-flow: column nowrap;
                justify-content: space-between;
                width: 100%;
                overflow: visible;

                .e-field.e-context-dialog>div {
                    margin-bottom: 0;
                }

                &.e-page-body-context-dialog {
                    overflow: visible;
                }

                &.e-page-body-context-page {
                    padding-top: 12px;
                    background: var(--colorsYang100);
                    width: 100%;

                    &:has(.e-section-table) {
                        min-height: 480px;
                    }

                    @include extra_small {
                        padding-top: 0;
                        background: var(--colorsUtilityMajor025);
                    }
                }

                @include small_and_below {
                    box-sizing: border-box;
                    width: 100%;
                    overflow-y: visible;
                }

                @include extra_small {
                    padding: 0;
                    z-index: 1;
                }

                @media print {
                    display: block;
                    flex: unset;
                    height: fit-content;
                    overflow: unset;
                }

                // Filling empty space within the page body and pushing the sections up
                >div:last-of-type {
                    margin-bottom: auto;
                }
            }
        }

        .e-wizard-stepper .e-wizard-stepper-title {
            @include extra_small {
                display: none;
            }
        }
    }

    &.e-page-with-navigation-panel {
        position: relative;
        box-sizing: border-box;

        &.e-page-navigation-panel-open .e-page-main-section {
            margin-left: 24px;

            @include small_and_below {
                margin-left: 0;
            }
        }

        @include extra_small {
            padding-left: 0;
        }

        &.e-page-navigation-panel-open {
            .e-page-navigation-panel {
                display: flex;
            }
        }
    }
}
