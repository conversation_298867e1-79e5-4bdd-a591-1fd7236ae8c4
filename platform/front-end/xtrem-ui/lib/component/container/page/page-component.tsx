import { debounce, isEmpty, isNil } from 'lodash';
import * as React from 'react';
import { connect } from 'react-redux';
import * as xtremRedux from '../../../redux';
import { RenderingRouter } from '../../../render/rendering-router';
import { errorDialog } from '../../../service/dialog-service';
import { localize } from '../../../service/i18n-service';
import type { PageDefinition } from '../../../service/page-definition';
import { subscribe, unsubscribe } from '../../../service/shortcut-service';
import { notifyConsumerOnError } from '../../../service/telemetry-service';
import { ContextType } from '../../../types';
import { ATTACHMENT_SECTION_ID, ATTACHMENTS_ELEMENT_ID, NEW_PAGE } from '../../../utils/constants';
import { focusTopPage } from '../../../utils/dom';
import { triggerFieldEvent } from '../../../utils/events';
import { getHeaderSection, getVisibleSections } from '../../../utils/layout-utils';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { COLUMN_COUNT_HELPER_PANEL } from '../../../utils/responsive-utils';
import { getNavigationPanelState, getPagePropertiesFromPageDefinition } from '../../../utils/state-utils';
import { detailPanelId } from '../container-control-objects';
import DetailPanel from '../detail-panel/detail-panel-component';
import NavigationPanel from '../navigation-panel/navigation-panel-component';
import { PageFooter } from '../page-footer/page-footer-component';
import type { PageComponentExternalProps, PageComponentProps } from './page-types';
import { ConnectedXtremHeader } from './xtrem-header';
import DragAndDropFile from '../../ui/dnd-files/drag-and-drop-file';
import { depositFileToCollectionValue } from '../../field/multi-file-deposit/multi-file-deposit-utils';
import { getScreenElement } from '../../../service/screen-base-definition';
import type { CollectionValue } from '../../../service/collection-data-service';
import { attachmentsMimeTypes } from '@sage/xtrem-shared';
import {
    getMimeTypeFromExtension,
    isDisallowedMimeType,
    showNotAllowedTypeToast,
} from '../../../utils/file-deposit-utils';
import { schemaTypeNameFromNodeName } from '../../../utils/transformers';
import { Page360View } from './page-360-view';
import { getQueryParametersFromPath } from '../../../redux/actions';
import type { QueryParameters } from '../../../utils/types';
import ToggleNavigationPanelButton from '../navigation-panel/toggle-navigation-panel-button';

interface PageComponentState {
    dialogSectionHeight?: number;
    pageBodyHeight?: number;
}

class PageComponent extends React.Component<PageComponentProps, PageComponentState> {
    private readonly pageBodyRef: React.RefObject<HTMLDivElement>;

    private shortcutSubscriptions: number[] = [];

    private readonly parentRef = React.createRef<HTMLButtonElement>();

    constructor(props: PageComponentProps) {
        super(props);
        this.pageBodyRef = React.createRef();
        this.state = {};
    }

    handleDialogSectionHeight = debounce((): void => {
        // INFO: Magic numbers which are subtracted in the dialog component and need to be added again to set the correct height.
        const DIALOG_FOOTER_SPACING = 24;

        if (this.isFullScreenDialogContext() && this.hasChangedPageBodyHeight()) {
            const pageBodyHeight = this.pageBodyRef.current?.clientHeight || 0;
            const dialogSectionHeight = this.props.isMobileOrTabletScreen
                ? undefined
                : pageBodyHeight + DIALOG_FOOTER_SPACING;
            this.setState({ dialogSectionHeight, pageBodyHeight });
        }
    }, 250);

    componentDidMount(): void {
        focusTopPage();
        this.shortcutSubscriptions = this.props.elementsWithShortcut.map(e =>
            subscribe(e.shortcut, () =>
                triggerFieldEvent(this.props.pageDefinition.metadata.screenId, e.elementId, 'onClick'),
            ),
        );

        this.handle360ViewInUrl();
        this.handleDialogSectionHeight();
    }

    componentDidCatch(error: Error): void {
        errorDialog(this.props.pageDefinition.metadata.screenId, localize('@sage/xtrem-ui/error', 'Error'), error);
        notifyConsumerOnError(error);
    }

    componentWillUnmount(): void {
        this.shortcutSubscriptions.forEach(unsubscribe);
    }

    getActiveSectionId = (): string | undefined => {
        const headerSection = getHeaderSection(this.props.pageDefinition);

        // INFO: If full-screen dialog or mobile screen and the header section is defined, we want
        //       to skip the header section.
        if ((this.isFullScreenDialogContext() || this.props.isMobileOrTabletScreen) && !isNil(headerSection)) {
            return (
                this.props.activeSection ||
                this.props.sections.find(section => section.$containerId !== headerSection.id)?.$containerId
            );
        }

        return this.props.activeSection || (this.props.sections[0] && this.props.sections[0].$containerId);
    };

    getVisibleSections = (): HTMLDivElement[] => {
        if (this.pageBodyRef.current) {
            const sectionsArray = Array.from(this.pageBodyRef.current.childNodes) as HTMLDivElement[];
            return sectionsArray.filter(section => Array.from(section.classList).indexOf('e-hidden') === -1);
        }
        return [];
    };

    getActiveSectionIndex(): number {
        if (this.pageBodyRef.current) {
            const containerY = this.pageBodyRef.current.getBoundingClientRect().y;
            return this.getVisibleSections().findIndex(section => {
                const sectionRect = section.getBoundingClientRect();
                return sectionRect.y + sectionRect.height > containerY;
            });
        }
        return -1;
    }

    onBodyScroll = (): void => {
        if (this.pageBodyRef) {
            const visibleSections = this.getVisibleSections();
            const activeSectionIndex = this.getActiveSectionIndex();
            if (activeSectionIndex > -1) {
                const activeSectionId = visibleSections[activeSectionIndex].id;
                if (this.props.pageMode === 'default' && this.getActiveSectionId() !== activeSectionId) {
                    this.props.setActiveSection(activeSectionId);
                }
            }
        }
    };

    onFileDrop = async (files: FileList): Promise<void> => {
        const screenId = this.props.pageDefinition.metadata.screenId;
        if (!this.props.attachmentInformation || !this.props.attachmentListValue) {
            return;
        }
        const targetNode = this.props.graphApi.node(this.props.attachmentInformation?.attachmentFileNode);

        await Promise.all(
            Array.from(files).map(async (file: File): Promise<void> => {
                const mimeType = file.type || getMimeTypeFromExtension(file.name);

                if (isDisallowedMimeType(attachmentsMimeTypes, mimeType, file.name)) {
                    this.props.onTelemetryEvent?.(
                        `multiFileDepositFileMimeTypeValidationFailed-${ATTACHMENTS_ELEMENT_ID}`,
                        {
                            mimeType,
                            screenId,
                            elementId: ATTACHMENTS_ELEMENT_ID,
                        },
                    );
                    showNotAllowedTypeToast(mimeType);
                    return;
                }

                this.props.onTelemetryEvent?.(`multiFileDepositFileAdded-${ATTACHMENTS_ELEMENT_ID}`, {
                    mimeType,
                    method: 'pageDrop',
                    elementId: ATTACHMENTS_ELEMENT_ID,
                    screenId,
                });

                try {
                    await depositFileToCollectionValue({
                        value: this.props.attachmentListValue as CollectionValue,
                        file,
                        targetNodeMutations: targetNode.mutations,
                        kind: 'attachment',
                    });
                    this.props.setActiveSection(ATTACHMENT_SECTION_ID);
                } catch (err) {
                    errorDialog(
                        this.props.pageDefinition.metadata.screenId,
                        localize('@sage/xtrem-ui/file-upload-failed', 'Failed to upload file.'),
                        err,
                    );
                }
            }),
        );
        await triggerFieldEvent(screenId, ATTACHMENTS_ELEMENT_ID, 'onChange');
        this.props.setActiveSection(ATTACHMENT_SECTION_ID);
    };

    getPageClassName(): string {
        const classes: string[] = ['e-page', `e-page-mode-${this.props.pageMode || 'default'}`];
        if (this.props.hasNavigationPanel && !this.props.isNavigationPanelHidden) {
            classes.push('e-page-with-navigation-panel');
            if (
                this.props.isNavigationPanelOpened &&
                (!this.props.pageDefinition.isInDialog || this.props.isMainListDisplayedInDialog)
            ) {
                classes.push('e-page-navigation-panel-open');
            }
            if (!this.props.isNavigationPanelOpened) {
                classes.push('e-page-navigation-panel-closed');
            }
        }

        if (this.props.contextType) {
            classes.push(`e-page-${this.props.contextType}`);
        }

        if (this.hasPageFooter()) {
            classes.push('e-page-has-footer');
        }

        return classes.join(' ');
    }

    getPendoPageMode(): string {
        if (this.props.recordId && this.props.recordId !== NEW_PAGE) {
            return this.props.is360ViewOn ? '360' : 'DP';
        }
        return 'ML';
    }

    handle360ViewInUrl = (): void => {
        const path = this.props.path;
        if (!path) return;

        const pathSegments = path.split('/');

        let queryParameters: QueryParameters = {};
        if (pathSegments.length > 3) {
            queryParameters = getQueryParametersFromPath(pathSegments[3]);
        }

        const shouldEnable360 = queryParameters.view === '360';

        if (this.props.contextType === 'dialog') return;
        this.props.set360ViewState(this.props.pageDefinition.metadata.screenId, shouldEnable360);
    };

    hasPageFooter = (): boolean =>
        !isEmpty(this.props.pageDefinition.page.$.businessActions) || this.props.pageMode === 'wizard';

    hasChangedPageBodyHeight = (): boolean => this.pageBodyRef.current?.clientHeight !== this.state.pageBodyHeight;

    isDialogContext(): boolean {
        return this.props.contextType === ContextType.dialog;
    }

    isFullScreenDialogContext(): boolean {
        return this.isDialogContext() && !!this.props.isPageDisplayedInFullScreenDialog;
    }

    renderDetailPanel(): React.ReactNode {
        return this.props.pageDefinition.page.$.detailPanel ? (
            <DetailPanel
                detailPanel={this.props.pageDefinition.page.$.detailPanel}
                screenId={this.props.pageDefinition.metadata.screenId}
            />
        ) : null;
    }

    renderSections(availableColumns: number): React.ReactNode {
        const hasFooter = this.hasPageFooter();
        const headerSection = getHeaderSection(this.props.pageDefinition);
        const shouldFilterHeaderSection =
            !isNil(headerSection) && (this.isFullScreenDialogContext() || this.props.isMobileOrTabletScreen);

        const sections = shouldFilterHeaderSection
            ? this.props.sections.filter(section => section.$containerId !== headerSection.id)
            : this.props.sections;
        const sectionHeight = shouldFilterHeaderSection ? this.state.dialogSectionHeight : this.props.fixedHeight;

        return sections.map(section => {
            if (this.props.pageMode === 'default' || section.$containerId === this.getActiveSectionId()) {
                return (
                    <RenderingRouter
                        screenId={this.props.pageDefinition.metadata.screenId}
                        key={section.$containerId}
                        item={section}
                        availableColumns={availableColumns}
                        contextType={this.props.contextType || ContextType.page}
                        hasFooter={hasFooter}
                        fixedHeight={sectionHeight}
                    />
                );
            }

            return null;
        });
    }

    hasToggleButton = (): boolean => {
        const p = this.props;
        return (
            p.hasNavigationPanel &&
            !p.noHeader &&
            !p.canToggleNavigationPanel &&
            !p.isNavigationPanelOpened &&
            !p.isNavigationPanelHeaderHidden &&
            !p.isNewPage
        );
    };

    render(): React.ReactNode {
        const screenId = this.props.pageDefinition.metadata.screenId;
        const isMainPage = this.props.contextType === ContextType.page;
        const isDetailPanelOpen =
            this.props.pageDefinition.metadata.uiComponentProperties[detailPanelId] &&
            !resolveByValue({
                propertyValue: this.props.pageDefinition.metadata.uiComponentProperties[detailPanelId].isHidden,
                screenId,
                skipHexFormat: true,
                rowValue: null, // Not a nested field, no row value is available
            });
        const isDialog: boolean = this.props.contextType === ContextType.dialog;
        const availableColumns =
            this.props.availableColumns - (isDetailPanelOpen && !isDialog ? COLUMN_COUNT_HELPER_PANEL : 0);
        const businessActions = this.props.pageDefinition.page.$.businessActions;

        return (
            <DragAndDropFile
                onFileDrop={this.onFileDrop}
                isDisabled={!this.props.hasAttachmentPermission || !this.props.recordId}
            >
                <div
                    className={this.getPageClassName()}
                    data-testid="e-page"
                    data-pendo-page={screenId}
                    data-pendo-page-mode={this.getPendoPageMode()}
                >
                    {this.props.hasNavigationPanel && (
                        <NavigationPanel
                            screenId={screenId}
                            selectedRecordId={this.props.recordId}
                            fixedHeight={this.props.fixedHeight}
                        />
                    )}
                    {this.hasToggleButton() && (
                        <ToggleNavigationPanelButton
                            setNavigationPanelIsOpened={this.props.setNavigationPanelIsOpened}
                            isNavigationPanelOpened={this.props.isNavigationPanelOpened}
                            parentRef={this.parentRef}
                        />
                    )}
                    {(!this.props.hasNavigationPanel || this.props.recordId) && (
                        <main className="e-page-main-section" onScroll={this.onBodyScroll} ref={this.pageBodyRef}>
                            {!this.props.noHeader && (
                                <ConnectedXtremHeader
                                    activeSection={this.props.activeSection}
                                    areNavigationTabsHidden={this.props.areNavigationTabsHidden}
                                    availableColumns={availableColumns}
                                    contextType={this.props.contextType}
                                    hasNavigationPanel={isMainPage && this.props.hasNavigationPanel}
                                    headerLineBlock={this.props.pageDefinition.page.$.headerLineBlock}
                                    isRecordCreationPage={this.props.recordId === NEW_PAGE}
                                    pageBodyRef={this.pageBodyRef}
                                    screenId={screenId}
                                    setActiveSection={this.props.setActiveSection}
                                    hasToggleButton={this.hasToggleButton()}
                                />
                            )}

                            {!this.props.is360ViewOn && (
                                <>
                                    <div
                                        className={`e-page-body-container${isDetailPanelOpen ? ' e-detail-panel-open' : ''}`}
                                    >
                                        <div
                                            className={`e-page-body${
                                                this.props.contextType
                                                    ? ` e-page-body-context-${this.props.contextType}`
                                                    : ''
                                            }`}
                                            data-testid="e-page-body"
                                        >
                                            {this.renderSections(availableColumns)}
                                            {this.props.contextType !== ContextType.dialog && (
                                                <PageFooter
                                                    businessActions={businessActions}
                                                    contextType={this.props.contextType}
                                                    screenId={screenId}
                                                    pageMode={this.props.pageMode}
                                                />
                                            )}
                                        </div>
                                        {this.props.contextType !== ContextType.dialog && this.renderDetailPanel()}
                                    </div>
                                </>
                            )}
                            {this.props.is360ViewOn && <Page360View screenId={screenId} />}
                        </main>
                    )}
                </div>
            </DragAndDropFile>
        );
    }
}

const mapStateToProps = (state: xtremRedux.XtremAppState, props: PageComponentExternalProps): PageComponentProps => {
    const pageProperties = getPagePropertiesFromPageDefinition(props.pageDefinition);
    const screenId = props.pageDefinition.metadata.screenId;
    const navigationPanelState = getNavigationPanelState(screenId, state);
    const isNewPage = Boolean((state.screenDefinitions[screenId] as PageDefinition).queryParameters?._id === NEW_PAGE);
    const attachmentInformation = props.pageDefinition.metadata.attachmentInformation;
    const hasAttachmentPermission = attachmentInformation
        ? props.pageDefinition.accessBindings[
              schemaTypeNameFromNodeName(attachmentInformation?.attachmentAssociationNode)
          ].$create === 'authorized'
        : false;
    const screenElement = getScreenElement(props.pageDefinition);
    const attachmentListValue =
        attachmentInformation && hasAttachmentPermission
            ? state.screenDefinitions[screenId].values[ATTACHMENTS_ELEMENT_ID]
            : undefined;
    const path = state.path;
    const isMobileOrTabletScreen = state.browser.lessThan.m;

    return {
        ...props,
        canToggleNavigationPanel: state.browser.greaterThan.l,
        isNewPage,
        isNavigationPanelHeaderHidden: !!navigationPanelState?.isHeaderHidden,
        activeSection: props.selectedSection || props.pageDefinition.activeSection || null,
        recordId: props.pageDefinition.queryParameters._id ? String(props.pageDefinition.queryParameters._id) : null,
        areNavigationTabsHidden: !!pageProperties.areNavigationTabsHidden,
        hasNavigationPanel:
            (props.contextType !== 'dialog' || !!props.isMainListDisplayedInDialog) &&
            !!navigationPanelState &&
            !!pageProperties.navigationPanel,
        is360ViewOn: !!props.pageDefinition.is360ViewOn,
        isMobileOrTabletScreen,
        isNavigationPanelHidden: isNewPage || !navigationPanelState || navigationPanelState.isHidden || false,
        isNavigationPanelOpened:
            (props.contextType !== 'dialog' && state.browser.greaterThan.l) || navigationPanelState?.isOpened || false,
        sections: getVisibleSections(props.pageDefinition, state.activeDialogs, state.browser.lessThan.m),
        pageMode: pageProperties.mode || 'default',
        elementsWithShortcut: props.pageDefinition.metadata.elementsWithShortcut,
        setActiveSection: xtremRedux.actions.actionStub,
        setNavigationPanelIsOpened: xtremRedux.actions.actionStub,
        set360ViewState: xtremRedux.actions.actionStub,
        attachmentInformation,
        hasAttachmentPermission,
        graphApi: screenElement.$.graph,
        attachmentListValue,
        onTelemetryEvent: state.applicationContext?.onTelemetryEvent,
        path,
    };
};

const mapDispatchToProps = (
    dispatch: xtremRedux.AppThunkDispatch,
    props: PageComponentExternalProps,
): Partial<PageComponentProps> => ({
    setActiveSection: (sectionId: string | null): void => {
        dispatch(xtremRedux.actions.setActiveSection(props.pageDefinition.metadata.screenId, sectionId));
    },
    set360ViewState: (screenId: string, is360ViewOn: boolean): void => {
        dispatch(xtremRedux.actions.set360ViewState(screenId, is360ViewOn));
    },
    setNavigationPanelIsOpened: (isOpened: boolean): void =>
        dispatch(xtremRedux.actions.setNavigationPanelIsOpened(isOpened, props.pageDefinition.metadata.screenId)),
});

export default connect<PageComponentProps, {}, PageComponentExternalProps>(
    mapStateToProps,
    mapDispatchToProps,
)(PageComponent);
