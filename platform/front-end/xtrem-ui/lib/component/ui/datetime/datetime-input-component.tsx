import type { CalendarDate } from '@internationalized/date';
import { Time } from '@sage/xtrem-date-time';
import ButtonMinor from 'carbon-react/esm/components/button-minor';
import type { ButtonToggleGroup } from 'carbon-react/esm/components/button-toggle';
import { Checkbox } from 'carbon-react/esm/components/checkbox';
import Icon from 'carbon-react/esm/components/icon';
import Textbox from 'carbon-react/esm/components/textbox';
import React from 'react';
import { Calendar, CalendarGrid, I18nProvider } from 'react-aria-components';
import { localize } from '../../../service/i18n-service';
import { normalizeUnderscoreBind } from '../../../utils/abstract-fields-utils';
import { CalendarCellComponent } from './calendar-cell-component';
import { MonthYearHeaderComponent } from './month-year-header-component';
import { noop } from 'lodash';
import type { DatetimeComponentProps } from './datetime-types';
import { useTime } from '../time/use-time';
import { formatDatetime, makeDatetime } from './datetime-utils';
import { TimeComponent as TimeComp } from '../time/time-component';

export function DatetimeInputComponent({
    'aria-label': ariaLabel,
    date,
    elementId,
    fieldId,
    nestedRowId,
    initialDate,
    inputRef,
    isDisabled,
    isPopoverOpen,
    isReadOnly,
    isTimeZoneHidden,
    locale,
    maxDate,
    minDate,
    onDateChange,
    onPopperOpenChange,
    onTimeChange,
    rangeStartDate,
    screenId,
    size = 'medium',
    time,
    timeZone = 'UTC',
    title,
    type,
    validationError,
}: DatetimeComponentProps): JSX.Element {
    const [isManuallyClosed, setIsManuallyClosed] = React.useState(false);
    const popoverDialogRef = React.useRef<HTMLDivElement>(null);
    const inputWrapperRef = React.useRef<HTMLDivElement>(null);
    const [isInfinite, setIsInfinite] = React.useState<boolean>(!date); // If no date is selected, default to infinite
    const [isTimeZoneOpen, setIsTimeZoneOpen] = React.useState<boolean>(false);
    const id = React.useMemo(
        () => `--${screenId}-bind-${normalizeUnderscoreBind(elementId || '')}-${type}${nestedRowId}`,
        [elementId, screenId, type, nestedRowId],
    );

    const handleTimeChange = React.useCallback(
        (newTime: string | null): void => {
            onTimeChange(newTime);
        },
        [onTimeChange],
    );

    const {
        hasAmPmToggle,
        hoursRef,
        maxHours,
        minHours,
        minutesRef,
        onHoursBlur,
        onHoursChange,
        onKeyDown,
        onMinutesBlur,
        onBlurField,
        onMinutesChange,
        state,
        toggleChange,
    } = useTime({
        elementId: fieldId || `datetime-input-${Date.now()}`,
        locale,
        onChange: handleTimeChange,
        value: time?.toString(),
    });

    const handlePopoverOpenChange = React.useCallback(
        (isOpen: boolean): void => {
            if (!isOpen) {
                inputRef?.current?.blur();

                onBlurField();
                setIsManuallyClosed(true);
                setTimeout(() => setIsManuallyClosed(false), 0);
                popoverDialogRef.current?.hidePopover();
            } else {
                popoverDialogRef.current?.showPopover();
                requestAnimationFrame(() => adjustPopoverPosition());
            }

            onPopperOpenChange(isOpen, type);
        },
        [inputRef, onBlurField, onPopperOpenChange, type],
    );

    const onPopoverToggle = React.useCallback(
        (event: any) => {
            if (event.newState === 'open') {
                handlePopoverOpenChange(true);
            } else {
                handlePopoverOpenChange(false);
            }
        },
        [handlePopoverOpenChange],
    );

    const handleToggle = React.useCallback(
        (event: any): void => {
            requestAnimationFrame(() => {
                adjustPopoverPosition();
                onPopoverToggle(event);
            });
        },
        [onPopoverToggle],
    );
    const adjustPopoverPosition = (): void => {
        const pop = popoverDialogRef.current;
        const wrap = inputWrapperRef.current;
        if (!wrap || !pop) return;

        const wrapRect = wrap.getBoundingClientRect();
        const popRect = pop.getBoundingClientRect();
        const vw = window.innerWidth;
        const vh = window.innerHeight;

        let top = wrapRect.bottom;
        let left = wrapRect.left;

        if (top + popRect.height > vh) {
            top = wrapRect.top - popRect.height;
        }

        const margin = 8;
        const minLeft = margin;
        const maxLeft = Math.max(minLeft, vw - popRect.width - margin);
        left = Math.min(Math.max(left, minLeft), maxLeft);

        const minTop = margin;
        const maxTop = Math.max(minTop, vh - popRect.height - margin);
        top = Math.min(Math.max(top, minTop), maxTop);

        pop.style.top = `${top}px`;
        pop.style.left = `${left}px`;
    };

    React.useEffect(() => {
        const popoverDialogRefCopy = popoverDialogRef.current;
        const inputWrapperRefCopy = inputWrapperRef.current;

        if (!inputWrapperRefCopy || !popoverDialogRefCopy) {
            return noop;
        }

        popoverDialogRefCopy.addEventListener('toggle', handleToggle);

        return (): void => {
            popoverDialogRefCopy?.removeEventListener('toggle', handleToggle);
        };
    }, [id, handleToggle]);

    const handleDateChange = React.useCallback(
        (newDate: CalendarDate | null): void => {
            onDateChange(newDate);
            if (isInfinite) {
                setIsInfinite(false);
            }
        },
        [onDateChange, isInfinite],
    );

    React.useEffect(() => {
        if (date) {
            setIsInfinite(false);
        } else {
            setIsInfinite(true);
        }
    }, [date]);

    const isDateUnavailable = React.useCallback(
        (
            startDate: CalendarDate | null,
            minDate: CalendarDate | null | undefined,
            maxDate: CalendarDate | null | undefined,
        ) =>
            (calendarDate: CalendarDate): boolean => {
                if (minDate && calendarDate.compare(minDate) < 0) return true;
                if (maxDate && calendarDate.compare(maxDate) > 0) return true;
                if (startDate && calendarDate.compare(startDate) < 0) return true;
                return false;
            },
        [],
    );

    const rangeStartCalendarDate = React.useMemo(() => rangeStartDate, [rangeStartDate]);

    const handleFocus = React.useCallback(() => {
        if (!isManuallyClosed) {
            handlePopoverOpenChange(true);
        }
    }, [handlePopoverOpenChange, isManuallyClosed]);

    const onToggleChange = React.useCallback<NonNullable<React.ComponentProps<typeof ButtonToggleGroup>['onChange']>>(
        (_, toggle) => {
            toggleChange(toggle as 'AM' | 'PM');
        },
        [toggleChange],
    );
    React.useEffect(() => {
        if (isPopoverOpen) {
            setTimeout(() => {
                popoverDialogRef.current
                    ?.querySelector<HTMLDivElement>('.react-aria-CalendarCell[tabindex="0"]')
                    ?.focus();
            }, 50);
        }
    }, [isPopoverOpen]);

    const onInputKeyDown = React.useCallback(
        (ev: React.KeyboardEvent) => {
            if (ev.key === 'Tab') {
                handlePopoverOpenChange(false);
            }

            if (ev.key === 'Backspace' || ev.key === 'Delete') {
                handleDateChange(null);
                handleTimeChange(null);
            }

            if (ev.key.startsWith('Key') || ev.key.startsWith('Digit') || ev.key.startsWith('Numpad')) {
                // Prevent the user from typing in the input field
                ev.preventDefault();
                ev.stopPropagation();
            }
        },
        [handleDateChange, handlePopoverOpenChange, handleTimeChange],
    );

    const inputValue =
        date && time ? formatDatetime({ date: makeDatetime(date, Time.parse(time), timeZone), locale }) : '';

    const infiniteTooltipMessage =
        type === 'start'
            ? localize('@sage/xtrem-ui/infinite-indicator-start', 'No start date')
            : localize('@sage/xtrem-ui/infinite-indicator-end', 'No end date');

    return (
        <>
            {type === 'end' && (
                <I18nProvider locale={locale}>
                    <div
                        className="e-popover-dialog ag-custom-component-popup"
                        popover="auto"
                        ref={popoverDialogRef}
                        id={id}
                        data-type={type}
                    >
                        <div className="e-calendar-container" data-testid={`e-datetime-input-${type}-date-picker`}>
                            <div className="e-calendar-infinite-container">
                                <Checkbox
                                    readOnly={isReadOnly}
                                    aria-readonly={isReadOnly}
                                    disabled={isDisabled}
                                    data-testid={`e-datetime-input-${type}-date-picker-checkbox`}
                                    label={infiniteTooltipMessage}
                                    checked={isInfinite}
                                    size="small"
                                    className="e-calendar-infinite-checkbox"
                                    onChange={event => {
                                        if (isReadOnly || isDisabled) return;
                                        setIsInfinite(event.target.checked);
                                        // If is infinite set date as null
                                        if (event.target.checked) {
                                            handleDateChange(null);
                                            handleTimeChange(null);
                                        }
                                    }}
                                />
                            </div>
                            <Calendar
                                focusedValue={initialDate}
                                maxValue={maxDate}
                                minValue={minDate}
                                isDisabled={isDisabled}
                                onChange={handleDateChange}
                                isReadOnly={isReadOnly}
                                value={date}
                                isDateUnavailable={isDateUnavailable(rangeStartCalendarDate, minDate, maxDate)}
                            >
                                <MonthYearHeaderComponent isDisabled={isDisabled} />
                                <CalendarGrid className="e-calendar-grid-styled" weekdayStyle="narrow">
                                    {day => (
                                        <CalendarCellComponent
                                            day={day}
                                            rangeStartCalendarDate={rangeStartCalendarDate}
                                            selectedDate={date || undefined}
                                            timeZone={timeZone}
                                        />
                                    )}
                                </CalendarGrid>
                            </Calendar>
                        </div>

                        <div className="e-time-component-container e-time-field">
                            <div className="e-time-component-title">
                                {localize('@sage/xtrem-ui/date-time-range-time', 'Time')}
                            </div>
                            {!isTimeZoneOpen && (
                                <div className="e-time-component-wrapper">
                                    <TimeComp
                                        screenId={screenId}
                                        elementId={elementId}
                                        dataTestId={`e-datetime-input-${type}-time-input`}
                                        fieldId={fieldId}
                                        isReadOnly={isReadOnly}
                                        hoursRef={hoursRef}
                                        minutesRef={minutesRef}
                                        isDisabled={isDisabled || isInfinite}
                                        maxHours={maxHours}
                                        minHours={minHours}
                                        onHoursBlur={onHoursBlur}
                                        onHoursChange={onHoursChange}
                                        onKeyDown={onKeyDown}
                                        onMinutesBlur={onMinutesBlur}
                                        onMinutesChange={onMinutesChange}
                                        state={state}
                                        hasAmPmToggle={hasAmPmToggle}
                                        localize={localize}
                                        onToggleChange={onToggleChange}
                                    />
                                    {!isTimeZoneHidden && (
                                        <ButtonMinor
                                            data-testid={`e-time-component-open-timezone-${type}`}
                                            iconType="refresh_clock"
                                            disabled={isDisabled || isInfinite}
                                            onClick={() => setIsTimeZoneOpen(true)}
                                        />
                                    )}
                                </div>
                            )}
                            {isTimeZoneOpen && (
                                <div className="e-time-component-timezone-container">
                                    <ButtonMinor
                                        data-testid={`e-time-component-close-timezone-${type}`}
                                        iconType="clock"
                                        onClick={() => setIsTimeZoneOpen(false)}
                                    />
                                    <div className="e-time-component-timezone-input">
                                        <Textbox
                                            label={localize('@sage/xtrem-ui/date-time-range-time-zone', 'Time zone:')}
                                            labelInline
                                            id={`e-datetime-input-${type}-time-input-timezone`}
                                            data-testid={`e-datetime-input-${type}-time-input-timezone`}
                                            value={timeZone}
                                            readOnly={true}
                                            inputWidth={60}
                                            labelWidth={100}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </I18nProvider>
            )}
            <div className="e-combined-input-wrapper" popovertarget={id} ref={inputWrapperRef}>
                <Textbox
                    data-testid={`e-datetime-input-${type}`}
                    label={title}
                    aria-label={ariaLabel}
                    size={size}
                    inputIcon="calendar"
                    value={inputValue}
                    onFocus={handleFocus}
                    onClick={handleFocus}
                    ref={inputRef}
                    onKeyDown={onInputKeyDown}
                    disabled={isDisabled}
                    readOnly={isReadOnly}
                    error={validationError}
                />
                {isInfinite && type !== 'single' && (
                    <Icon
                        tooltipMessage={infiniteTooltipMessage}
                        className="e-infinite-indicator"
                        type="arrows_left_right"
                    />
                )}
            </div>
            {(type === 'start' || type === 'single') && (
                <I18nProvider locale={locale}>
                    <div
                        className="e-popover-dialog ag-custom-component-popup"
                        popover="auto"
                        ref={popoverDialogRef}
                        id={id}
                        data-type={type}
                    >
                        <div className="e-calendar-container" data-testid={`e-datetime-input-${type}-date-picker`}>
                            {type !== 'single' && (
                                <div className="e-calendar-infinite-container">
                                    <Checkbox
                                        readOnly={isReadOnly}
                                        aria-readonly={isReadOnly}
                                        disabled={isDisabled}
                                        data-testid={`e-datetime-input-${type}-date-picker-checkbox`}
                                        label={infiniteTooltipMessage}
                                        checked={isInfinite}
                                        size="small"
                                        className="e-calendar-infinite-checkbox"
                                        onChange={event => {
                                            if (isReadOnly || isDisabled) return;
                                            setIsInfinite(event.target.checked);
                                            // If is infinite set date as null
                                            if (event.target.checked) {
                                                handleDateChange(null);
                                                handleTimeChange(null);
                                            }
                                        }}
                                    />
                                </div>
                            )}
                            <Calendar
                                focusedValue={initialDate}
                                maxValue={maxDate}
                                minValue={minDate}
                                isDisabled={isDisabled}
                                onChange={handleDateChange}
                                isReadOnly={isReadOnly}
                                value={date}
                                isDateUnavailable={isDateUnavailable(rangeStartCalendarDate, minDate, maxDate)}
                            >
                                <MonthYearHeaderComponent isDisabled={isDisabled} />
                                <CalendarGrid className="e-calendar-grid-styled" weekdayStyle="narrow">
                                    {day => (
                                        <CalendarCellComponent
                                            day={day}
                                            rangeStartCalendarDate={rangeStartCalendarDate}
                                            selectedDate={date || undefined}
                                            timeZone={timeZone}
                                        />
                                    )}
                                </CalendarGrid>
                            </Calendar>
                        </div>

                        <div className="e-time-component-container e-time-field">
                            <div className="e-time-component-title">
                                {localize('@sage/xtrem-ui/date-time-range-time', 'Time')}
                            </div>
                            {!isTimeZoneOpen && (
                                <div className="e-time-component-wrapper">
                                    <TimeComp
                                        screenId={screenId}
                                        elementId={elementId}
                                        dataTestId={`e-datetime-input-${type}-time-input`}
                                        fieldId={fieldId}
                                        isReadOnly={isReadOnly}
                                        hoursRef={hoursRef}
                                        minutesRef={minutesRef}
                                        isDisabled={isDisabled || isInfinite}
                                        maxHours={maxHours}
                                        minHours={minHours}
                                        onHoursBlur={onHoursBlur}
                                        onHoursChange={onHoursChange}
                                        onKeyDown={onKeyDown}
                                        onMinutesBlur={onMinutesBlur}
                                        onMinutesChange={onMinutesChange}
                                        state={state}
                                        hasAmPmToggle={hasAmPmToggle}
                                        localize={localize}
                                        onToggleChange={onToggleChange}
                                    />
                                    {!isTimeZoneHidden && (
                                        <ButtonMinor
                                            data-testid={`e-time-component-open-timezone-${type}`}
                                            iconType="refresh_clock"
                                            disabled={isDisabled || isInfinite}
                                            onClick={() => setIsTimeZoneOpen(true)}
                                        />
                                    )}
                                </div>
                            )}
                            {isTimeZoneOpen && (
                                <div className="e-time-component-timezone-container">
                                    <ButtonMinor
                                        data-testid={`e-time-component-close-timezone-${type}`}
                                        iconType="clock"
                                        onClick={() => setIsTimeZoneOpen(false)}
                                    />
                                    <div className="e-time-component-timezone-input">
                                        <Textbox
                                            label={localize('@sage/xtrem-ui/date-time-range-time-zone', 'Time zone:')}
                                            labelInline
                                            id={`e-datetime-input-${type}-time-input-timezone`}
                                            data-testid={`e-datetime-input-${type}-time-input-timezone`}
                                            value={timeZone}
                                            readOnly={true}
                                            inputWidth={60}
                                            labelWidth={100}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </I18nProvider>
            )}
        </>
    );
}
