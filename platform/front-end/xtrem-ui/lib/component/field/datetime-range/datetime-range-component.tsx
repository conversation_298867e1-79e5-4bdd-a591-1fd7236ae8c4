import React, { useMemo, useRef } from 'react';
import { connect } from 'react-redux';
import { CarbonWrapper } from '../carbon-wrapper';
import { mapDispatchToProps, mapStateToProps } from '../field-base-component';
import type { DatetimeRangeComponentProps, DatetimeRangeValue } from './datetime-range-types';
import { localize } from '../../../service/i18n-service';
import { useDatetimeRange } from './use-datetime-range';
import {
    generateFieldId,
    getFieldIndicatorStatus,
    isFieldDisabled,
    isFieldReadOnly,
    getLabelTitle,
} from '../carbon-helpers';
import { FieldLabel, HelperText } from '../carbon-utility-components';
import { handleChange } from '../../../utils/abstract-fields-utils';
import { triggerFieldEvent, triggerNestedFieldEvent } from '../../../utils/events';
import { useFocus } from '../../../utils/hooks/effects/use-focus';
import { deepMerge } from '@sage/xtrem-shared';
import { set } from 'lodash';
import { convertDeepBindToPathNotNull } from '../../../utils/nested-field-utils';
import { areDateRangesEqual } from '../../ui/datetime/datetime-utils';
import { DatetimeInputComponent } from '../../ui/datetime/datetime-input-component';
import { resolveByValue } from '../../../utils/resolve-value-utils';
import { datePropertyValueToCalendarDate } from '../../../utils/date-utils';

export function DatetimeRangeComponent({
    locale,
    elementId,
    screenId,
    setFieldValue,
    validate,
    isInFocus,
    fieldProperties,
    isParentDisabled,
    isParentHidden,
    contextType,
    validationErrors,
    value,
    handlersArguments,
    columnDefinition,
    isNested,
    parentElementId,
    ...rest
}: DatetimeRangeComponentProps): JSX.Element {
    const containerRef = useRef<HTMLDivElement>(null);
    const startDateRef = useRef<HTMLInputElement>(null);
    const endDateRef = useRef<HTMLInputElement>(null);
    const startDateContainerRef = useRef<HTMLDivElement>(null);
    const endDateContainerRef = useRef<HTMLDivElement>(null);

    useFocus(startDateRef, isInFocus);

    const triggerChangeListener = React.useCallback(
        (newValue: DatetimeRangeValue | null) => (): void => {
            if (isNested && handlersArguments?.onChange && parentElementId) {
                const rowValue =
                    newValue !== undefined
                        ? deepMerge(
                              handlersArguments.rowValue,
                              set(
                                  {},
                                  convertDeepBindToPathNotNull(
                                      columnDefinition?.properties?.bind || fieldProperties?.bind || elementId,
                                  ),
                                  newValue,
                              ),
                          )
                        : handlersArguments?.rowValue;
                triggerNestedFieldEvent(
                    screenId,
                    parentElementId || elementId,
                    fieldProperties as any,
                    'onChange',
                    rowValue?._id,
                    rowValue,
                );
            } else {
                triggerFieldEvent(screenId, elementId, 'onChange');
            }
        },
        [
            isNested,
            handlersArguments?.onChange,
            handlersArguments?.rowValue,
            parentElementId,
            columnDefinition?.properties?.bind,
            fieldProperties,
            elementId,
            screenId,
        ],
    );

    const onChange = React.useCallback(
        (newValue: DatetimeRangeValue | null) => {
            if (areDateRangesEqual(newValue, value)) {
                return;
            }
            handleChange(elementId, newValue, setFieldValue, validate, triggerChangeListener(newValue));
        },
        [elementId, setFieldValue, triggerChangeListener, validate, value],
    );

    const minDate = useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.minDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(1970, 0, 1),
            ),
        [fieldProperties.minDate, screenId],
    );

    const maxDate = useMemo(
        () =>
            datePropertyValueToCalendarDate(
                resolveByValue({
                    propertyValue: fieldProperties.maxDate,
                    screenId,
                    rowValue: null,
                    fieldValue: null,
                    skipHexFormat: true,
                }) || new Date(2100, 11, 31),
            ),
        [fieldProperties.maxDate, screenId],
    );

    const {
        currentDates,
        endDate,
        validationError,
        endTime,
        handleEndDateChange,
        handleEndTimeChange,
        handlePopoverOpenChange,
        handleStartDateChange,
        handleStartTimeChange,
        openInputPopover,
        startDate,
        startTime,
        timeZone,
        initialDate,
    } = useDatetimeRange({
        elementId,
        fieldProperties,
        locale,
        onChange,
        screenId,
        setFieldValue,
        validate,
        value,
        minDate,
        maxDate,
    });

    const { isTitleHidden, helperText, isHelperTextHidden, isTimeZoneHidden } = fieldProperties;

    const { error, warning, info } = React.useMemo(
        () =>
            getFieldIndicatorStatus({
                validationErrors,
                screenId,
                value,
                fieldProperties,
                isParentDisabled,
                isParentHidden,
            }),
        [fieldProperties, isParentDisabled, isParentHidden, screenId, validationErrors, value],
    );

    const isDisabled = useMemo(
        () => isFieldDisabled(screenId, fieldProperties, currentDates, null),
        [currentDates, fieldProperties, screenId],
    );

    const isReadOnly = useMemo(
        () => isFieldReadOnly(screenId, fieldProperties, currentDates, null),
        [currentDates, fieldProperties, screenId],
    );

    const fieldId = useMemo(
        () => generateFieldId({ screenId, elementId, contextType, fieldProperties, isNested: false }),
        [contextType, elementId, fieldProperties, screenId],
    );

    const resolvedTitle = useMemo(() => getLabelTitle(screenId, fieldProperties, null), [fieldProperties, screenId]);

    const startLabel = localize('@sage/xtrem-ui/date-time-range-start-date', 'Start');
    const endLabel = localize('@sage/xtrem-ui/date-time-range-end-date', 'End');
    const ariaLabelTitle = resolvedTitle || localize('@sage/xtrem-ui/datetime-range-aria-label', 'Date and time range');

    const getValidationErrors = React.useCallback(() => {
        return validationError
            ? localize(
                  '@sage/xtrem-ui/datetime-range-end-date-error',
                  'You need to enter an End date later than the Start date',
              )
            : '';
    }, [validationError]);

    return (
        <CarbonWrapper
            contextType={contextType}
            elementId={elementId}
            handlersArguments={handlersArguments}
            value={value}
            screenId={screenId}
            isNested={isNested}
            parentElementId={parentElementId}
            columnDefinition={columnDefinition}
            isInFocus={isInFocus}
            fieldProperties={fieldProperties}
            isParentDisabled={isParentDisabled}
            isParentHidden={isParentHidden}
            locale={locale}
            {...rest}
            noReadOnlySupport={true}
            className="e-datetime-range-field"
            componentName="datetime-range"
        >
            {!isTitleHidden && resolvedTitle && (
                <FieldLabel label={resolvedTitle} errorMessage={error} warningMessage={warning} infoMessage={info} />
            )}
            <div className="e-datetime-range-container" ref={containerRef}>
                <div className="e-date-input-wrapper" ref={startDateContainerRef}>
                    <DatetimeInputComponent
                        inputRef={startDateRef}
                        nestedRowId={handlersArguments?.rowValue?._id ? `-${handlersArguments.rowValue._id}` : ''}
                        aria-label={`${ariaLabelTitle} - ${startLabel}`}
                        date={startDate}
                        elementId={elementId}
                        fieldId={`${fieldId}-start-date`}
                        initialDate={initialDate}
                        isDisabled={isDisabled}
                        isPopoverOpen={openInputPopover === 'start'}
                        isReadOnly={isReadOnly}
                        isTimeZoneHidden={isTimeZoneHidden}
                        locale={locale}
                        maxDate={maxDate}
                        minDate={minDate}
                        onDateChange={handleStartDateChange}
                        onPopperOpenChange={handlePopoverOpenChange}
                        onTimeChange={handleStartTimeChange}
                        rangeStartDate={null}
                        screenId={screenId}
                        time={startTime}
                        timeZone={timeZone}
                        title={startLabel}
                        type="start"
                    />
                </div>
                <div className="e-date-input-wrapper" ref={endDateContainerRef}>
                    <DatetimeInputComponent
                        inputRef={endDateRef}
                        nestedRowId={handlersArguments?.rowValue?._id ? `-${handlersArguments.rowValue._id}` : ''}
                        aria-label={`${ariaLabelTitle} - ${endLabel}`}
                        date={endDate}
                        elementId={elementId}
                        fieldId={`${fieldId}-end-date`}
                        initialDate={initialDate}
                        isDisabled={isDisabled}
                        isPopoverOpen={openInputPopover === 'end'}
                        isReadOnly={isReadOnly}
                        isTimeZoneHidden={isTimeZoneHidden}
                        locale={locale}
                        maxDate={maxDate}
                        minDate={minDate}
                        onDateChange={handleEndDateChange}
                        onPopperOpenChange={handlePopoverOpenChange}
                        onTimeChange={handleEndTimeChange}
                        rangeStartDate={startDate}
                        screenId={screenId}
                        time={endTime}
                        timeZone={timeZone}
                        title={endLabel}
                        type="end"
                        validationError={getValidationErrors()}
                    />
                </div>
            </div>
            {!isHelperTextHidden && helperText && <HelperText helperText={helperText} />}
        </CarbonWrapper>
    );
}

export const ConnectedDatetimeRangeComponent = connect(mapStateToProps(), mapDispatchToProps())(DatetimeRangeComponent);

export default ConnectedDatetimeRangeComponent;
