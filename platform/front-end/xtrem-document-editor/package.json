{"name": "@sage/xtrem-document-editor", "version": "59.0.8", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["README.md", "CHANGELOG.md", "build"], "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@ckeditor/ckeditor5-alignment": "43.3.1", "@ckeditor/ckeditor5-basic-styles": "43.3.1", "@ckeditor/ckeditor5-block-quote": "43.3.1", "@ckeditor/ckeditor5-clipboard": "43.3.1", "@ckeditor/ckeditor5-core": "43.3.1", "@ckeditor/ckeditor5-editor-multi-root": "43.3.1", "@ckeditor/ckeditor5-engine": "43.3.1", "@ckeditor/ckeditor5-enter": "43.3.1", "@ckeditor/ckeditor5-essentials": "43.3.1", "@ckeditor/ckeditor5-font": "43.3.1", "@ckeditor/ckeditor5-heading": "43.3.1", "@ckeditor/ckeditor5-horizontal-line": "43.3.1", "@ckeditor/ckeditor5-html-support": "43.3.1", "@ckeditor/ckeditor5-image": "43.3.1", "@ckeditor/ckeditor5-indent": "43.3.1", "@ckeditor/ckeditor5-inspector": "^4.1.0", "@ckeditor/ckeditor5-link": "43.3.1", "@ckeditor/ckeditor5-list": "43.3.1", "@ckeditor/ckeditor5-page-break": "43.3.1", "@ckeditor/ckeditor5-paragraph": "43.3.1", "@ckeditor/ckeditor5-react": "^9.3.0", "@ckeditor/ckeditor5-select-all": "43.3.1", "@ckeditor/ckeditor5-table": "43.3.1", "@ckeditor/ckeditor5-theme-lark": "43.3.1", "@ckeditor/ckeditor5-typing": "43.3.1", "@ckeditor/ckeditor5-ui": "43.3.1", "@ckeditor/ckeditor5-undo": "43.3.1", "@ckeditor/ckeditor5-upload": "43.3.1", "@ckeditor/ckeditor5-utils": "43.3.1", "@ckeditor/ckeditor5-widget": "43.3.1", "@sage/design-tokens": "4.35.0", "@sage/handlebars-helpers": "^1.0.1", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-filter-utils": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-ui-components": "workspace:*", "carbon-react": "153.7.0", "csstype": "^3.1.2", "draft-js": "^0.11.7", "handlebars": "^4.7.8", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-is": "^18.3.1", "stream-browserify": "^3.0.0", "styled-components": "^5.3.11", "timers-browserify": "^2.0.12", "ts-essentials": "^10.0.0", "tslib": "^2.5.0", "uid": "^1.0.0", "usehooks-ts": "^2.6.0", "utility-types": "^3.4.1", "whatwg-url": "^14.0.0", "xml-formatter": "3.6.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-static-shared": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/lodash": "^4.14.198", "@types/react": "^18.3.3", "@types/react-dom": "^18.0.0", "@types/styled-components": "^5.1.29", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "adjust-sourcemap-loader": "^5.0.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^10.0.0", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^7.0.0", "d3-time-format": "^4.1.0", "esbuild-loader": "^4.3.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "express": "^5.0.0", "file-loader": "^6.2.0", "globals": "^16.2.0", "google-closure-compiler": "^20250820.0.0", "identity-obj-proxy": "^3.0.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^5.0.0", "istanbul-reports": "^3.1.5", "jest": "^30.0.0", "jest-canvas-mock": "^2.5.2", "jest-cli": "^30.0.0", "jest-environment-node": "^30.0.0", "jest-junit": "^16.0.0", "jest-sonar-reporter": "2.0.0", "jest-styled-components": "^7.1.1", "last-call-webpack-plugin": "^3.0.0", "mini-css-extract-plugin": "^2.0.0", "postcss-loader": "^8.0.0", "prettier": "^3.3.3", "pretty-format": "30.0.5", "puppeteer": "^24.6.1", "resolve-url-loader": "^5.0.0", "rimraf": "^6.0.0", "sass": "^1.55.0", "sass-loader": "^16.0.0", "source-map-loader": "^5.0.0", "source-map-support": "^0.5.12", "style-loader": "^4.0.0", "svg-inline-loader": "^0.8.2", "terser-webpack-plugin": "^5.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "ts-patch": "^3.0.2", "tsd": "^0.33.0", "typescript": "~5.9.0", "uid": "^1.0.0", "url-loader": "^4.1.1", "uuid": "^11.0.0", "v8-to-istanbul": "^9.0.1", "webpack": "^5.95.0", "webpack-cli": "^6.0.0", "webpack-dev-server": "^5.0.0", "webpack-merge": "^6.0.0", "webpack-notifier": "^1.7.0", "webpackbar": "^7.0.0"}, "scripts": {"build": "rm -rf build && pnpm build:ts && pnpm mergeTranslationFiles && pnpm build:sass", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:sass": "sass --style=compressed lib/style.scss > build/xtrem-document-editor.css", "build:tests": "tsc -b tsconfig.test.json", "build:ts": "tspc -b -v .", "build:webpack:dev": "webpack --config ./webpack/dev.js", "build:webpack:prod": "webpack --config ./webpack/prod.js", "clean": "rm -rf build junit-report* automation/report/* .awcache coverage .temp_cache docs tsconfig.tsbuildinfo junit.xml", "jest": "jest --runInBand", "lint": "eslint lib", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "lint:fix": "pnpm prettier:write && pnpm lint --fix", "mergeTranslationFiles": "node ../../cli/xtrem-cli-transformers/build/lib/transformers/merge-translation-files-current-dir", "prettier:check": "prettier --trailing-comma all --list-different \"lib/**/*.{ts,tsx}\"", "prettier:write": "prettier --trailing-comma all --write \"lib/**/*.{ts,tsx}\" package.json", "start": "webpack serve --config ./webpack/esbuild.js", "test": "pnpm build:webpack:prod && jest --runInBand", "test:ci": "pnpm build:tests && pnpm test:unit && pnpm build:webpack:prod && jest --runInBand", "test:unit": "jest --config jest.unit.config.js --runInBand"}}