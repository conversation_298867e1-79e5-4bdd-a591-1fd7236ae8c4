{"name": "@sage/xtrem-ui-plugin-monaco", "description": "Monaco editor plugin for the Xtrem UI framework.", "version": "59.0.8", "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "files": ["build", "README.md", "CHANGELOG.md"], "main": "build/index.js", "types": "build/index.d.ts", "xtremPlugin": true, "dependencies": {"@sage/xtrem-ui": "workspace:*", "@vscode/codicons": "^0.0.39", "monaco-editor": "^0.52.0", "react": "^18.3.1", "react-monaco-editor": "^0.59.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-dev": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/react": "^18.3.3", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "copy-webpack-plugin": "^13.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "monaco-editor-webpack-plugin": "^7.0.0"}, "scripts": {"build": "xtrem build-plugin", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\""}}