{"name": "@sage/xtrem-system", "description": "XTREM System", "version": "59.0.8", "xtrem": {"isPlatform": true, "isHidden": false}, "keywords": ["xtrem-service"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-cli-layers": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-data-management": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-infrastructure-adapter": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-postgres": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-sumologic-helper": "^3.1.3", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-user-event": "^5.0.2", "axios": "^1.11.0", "express": "^5.0.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "nanoid": "^3.3.8", "postgres-array": "^3.0.2", "prettier": "^3.3.3", "semver": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/express": "^5.0.0", "@types/js-yaml": "^4.0.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/semver": "^7.5.2", "@types/sinon": "^17.0.1", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "carbon-react": "153.7.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "copyfiles": "^2.1.0", "cross-env": "^10.0.0", "draft-js": "^0.11.7", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "graphql": "^16.11.0", "graphql-yoga": "^5.14.0", "mocha": "^11.0.0", "sinon": "^21.0.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build && pnpm clean:bundle", "clean:bundle": "rm -rf test/fixtures/bundles/tmp", "copy:bundle": "pnpm clean:bundle && copyfiles -u 5 \"test/fixtures/bundles/@sage/biodegradability-test/**/*\" test/fixtures/bundles/tmp/@sage/biodegradability-test/", "copy:fixtures": "copyfiles -f test/fixtures/pages/* test/fixtures/bundles/@sage/biodegradability-test/build/lib/pages && copyfiles -f test/fixtures/stickers/* test/fixtures/bundles/@sage/biodegradability-test/build/lib/stickers &&  copyfiles -f test/fixtures/pages/* build/lib/pages && copyfiles -f test/fixtures/stickers/* build/lib/stickers && pnpm copy:bundle", "dts-bundle": "xtrem-dts-bundle", "initTenant": "xtrem tenant --init '********************************************************************************************************************************************************************************************************'", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "load:test:data": "xtrem layers --load setup,test", "postbuild": "pnpm copy:fixtures", "posttest:ci": "rm -rf build/lib/pages", "pretest": "pnpm copy:fixtures", "pretest:ci": "pnpm pretest", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "cross-env TZ=CET xtrem test --unit --graphql --layers=test", "test:ci": "cross-env TZ=CET xtrem test --unit --ci --layers=test", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "test:unit": "cross-env TZ=CET xtrem test --unit --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*", "build/lib/services/bundle-manager.*", "build/lib/services/bundles/*", "build/lib/system-upgrades/*", "build/lib/upgrades/*"]}}