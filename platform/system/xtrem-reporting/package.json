{"name": "@sage/xtrem-reporting", "description": "A Sage Xtrem Package", "version": "59.0.8", "author": "sage", "license": "UNLICENSED", "xtrem": {"isPlatform": true, "isSealed": true, "hasListeners": true, "queue": "reporting"}, "keywords": ["xtrem-application-package"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/client-sso-oidc": "^3.826.0", "@aws-sdk/client-sts": "^3.826.0", "@aws-sdk/credential-provider-node": "^3.826.0", "@aws-sdk/credential-providers": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@sage/handlebars-helpers": "^1.0.1", "@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-document-editor": "workspace:*", "@sage/xtrem-file-storage": "^6.1.5", "@sage/xtrem-filter-utils": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-infrastructure-adapter": "workspace:*", "@sage/xtrem-js": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-pdf-generator": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@sage/xtrem-ui-components": "workspace:*", "@sage/xtrem-ui-plugin-graphiql": "workspace:*", "@sage/xtrem-ui-plugin-monaco": "workspace:*", "@sage/xtrem-ui-plugin-pdf": "workspace:*", "@sage/xtrem-upload": "workspace:*", "@sage/xtrem-workflow": "workspace:*", "@xmldom/xmldom": "^0.9.0", "archiver": "^7.0.0", "axios": "^1.11.0", "graphql": "^16.11.0", "handlebars": "^4.7.8", "handlebars-utils": "^1.0.6", "jsbarcode": "^3.11.4", "json-to-graphql-query": "^2.2.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "nanoid": "^3.3.8", "node-html-parser": "^7.0.0", "pngjs": "^7.0.0", "qrcode": "^1.5.4"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-metadata-api": "workspace:*", "@sage/xtrem-reporting-api": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-upload-api": "workspace:*", "@sage/xtrem-workflow-api": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/archiver": "^6.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/pdf-parse": "^1.1.0", "@types/pngjs": "^6.0.5", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^10.8.2", "pdf-parse": "^1.1.1", "sinon": "^21.0.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:incremental": "xtrem -- compile --only-changed --skip-server --skip-api-client", "clean": "rm -rf build", "extract:test:data": "xtrem layers --extract test", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "load:demo:data": "xtrem layers --load setup,demo", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --noTimeout --graphql --unit --workflow --layers=setup,test && rm -f ./en_US", "test:ci": "xtrem test --noTimeout --graphql --unit --workflow --ci --layers=setup,test", "test:smoke": "xtrem test test/cucumber/smoke-test.feature --integration", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}