{"name": "@sage/xtrem-workflow", "description": "XTREM Worflow", "version": "59.0.8", "xtrem": {"isPlatform": true, "isSealed": true, "isService": true, "hasListeners": true, "queue": "workflow"}, "keywords": ["xtrem-service"], "author": "Sage", "license": "UNLICENSED", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "files": ["build", "data", "routing.json"], "typings": "build/package-definition.d.ts", "dependencies": {"@sage/xtrem-async-helper": "workspace:*", "@sage/xtrem-auditing": "workspace:*", "@sage/xtrem-authorization": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-customization": "workspace:*", "@sage/xtrem-dashboard": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-filter-utils": "workspace:*", "@sage/xtrem-messaging-wrapper": "^3.1.2", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-system": "workspace:*", "@sage/xtrem-ui": "workspace:*", "axios": "^1.11.0", "fast-levenshtein": "^3.0.0", "graphql": "^16.11.0", "handlebars": "^4.7.8", "lodash": "^4.17.21", "nanoid": "^3.3.8"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-auditing-api": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-dev": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-metadata-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*", "@sage/xtrem-workflow-api": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/fast-levenshtein": "^0.0.4", "@types/lodash": "^4.14.198", "@types/mime-types": "^3.0.0", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "c8": "^10.1.2", "chai": "^4.3.10", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "sinon": "^21.0.0", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "xtrem compile", "build:api": "xtrem build --only-api-client", "build:binary": "xtrem compile --binary --prod", "build:cache": "turbo run build", "clean": "rm -rf build", "extract:demo:data": "xtrem layers --extract demo", "extract:qa:data": "xtrem layers --extract qa", "extract:test:data": "xtrem layers --extract test", "lint": "xtrem lint", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "load:demo:data": "xtrem layers --load setup,demo", "load:qa:data": "xtrem layers --load setup,qa", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "qa:cucumber": "xtrem test test/cucumber/* --integration --noTimeout --layers=qa", "qa:cucumber:browser": "xtrem test test/cucumber/* --integration --browser --noTimeout --layers=qa", "sonarqube:scanner": "pnpm test:ci && pnpm dlx sonarqube-scanner && rm -rf coverage .nyc_output .scannerwork junit-report-*.xml", "start": "xtrem start", "test": "xtrem test --noTimeout --unit --graphql --workflow --layers=test", "test:ci": "xtrem test --noTimeout --unit --graphql --workflow --ci --layers=test", "test:graphql": "xtrem test --noTimeout --graphql --layers=test", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "test:unit": "xtrem test --noTimeout --unit --layers=test", "xtrem": "xtrem"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*"]}}