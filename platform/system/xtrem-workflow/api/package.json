{"name": "@sage/xtrem-workflow-api", "description": "Client API for @sage/xtrem-workflow", "version": "59.0.8", "author": "Sage", "license": "UNLICENSED", "typings": "api.d.ts", "dependencies": {"@sage/xtrem-auditing-api": "workspace:*", "@sage/xtrem-authorization-api": "workspace:*", "@sage/xtrem-client": "workspace:*", "@sage/xtrem-communication-api": "workspace:*", "@sage/xtrem-customization-api": "workspace:*", "@sage/xtrem-dashboard-api": "workspace:*", "@sage/xtrem-metadata-api": "workspace:*", "@sage/xtrem-routing-api": "workspace:*", "@sage/xtrem-system-api": "workspace:*"}}