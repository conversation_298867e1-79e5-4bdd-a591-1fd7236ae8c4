{"name": "@sage/xtrem-infrastructure-adapter", "version": "59.0.8", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-application-package"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/client-sso-oidc": "^3.826.0", "@aws-sdk/client-sts": "^3.826.0", "@aws-sdk/credential-provider-node": "^3.826.0", "@aws-sdk/credential-providers": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@sage/async-context-provider": "^6.1.2", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-deployment": "^2.5.5", "@sage/xtrem-file-storage": "^6.1.5", "@sage/xtrem-infra": "^2.4.1", "@sage/xtrem-shared": "workspace:*", "@sage/xtrem-websocket-emiter": "^5.2.0", "cookie": "1.0.2", "find-up": "^7.0.0", "jwt-decode": "^4.0.0", "nanoid": "^3.3.8", "ws": "^8.18.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/source-map-support": "^0.5.7", "@types/ws": "^8.5.11", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "c8": "^10.1.2", "chai": "^4.3.10", "cross-env": "^10.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "source-map-support": "^0.5.12", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "tsc -b -v .", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "lint": "eslint lib test", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "test": "mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-core.xml JUNIT_REPORT_NAME='xtrem-infrastructure-adapter' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "c8": {"cache": false, "all": true, "extension": [".ts", ".tsx"], "sourceMap": true, "instrument": true, "reporter": ["text-summary", "clover", "json", "lcov"], "include": ["lib/**/*.ts"], "exclude": ["test/**/*", "data/**/*", "lib/services/web-socket-manager.ts"]}}