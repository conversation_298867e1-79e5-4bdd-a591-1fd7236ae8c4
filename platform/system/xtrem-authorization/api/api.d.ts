declare module '@sage/xtrem-authorization-api-partial' {
    import type { Package as SageXtremCommunication$Package } from '@sage/xtrem-communication-api';
    import type { Package as SageXtremMetadata$Package } from '@sage/xtrem-metadata-api';
    import type {
        Company,
        Package as SageXtremSystem$Package,
        Site,
        SysClientUserSettings,
        SysClientUserSettingsBinding,
        SysClientUserSettingsInput,
        SysPackVersion,
        SysVendor,
        User,
        UserPreferences,
        UserPreferencesBinding,
        UserPreferencesInput,
    } from '@sage/xtrem-system-api';
    import type {
        AggregateQueryOperation,
        AggregateReadOperation,
        AsyncOperation,
        BinaryStream,
        ClientCollection,
        ClientNode,
        ClientNodeInput,
        CreateOperation,
        DeleteOperation,
        DuplicateOperation,
        GetDefaultsOperation,
        GetDuplicateOperation,
        Operation as Node$Operation,
        QueryOperation,
        ReadOperation,
        TextStream,
        UpdateByIdOperation,
        UpdateOperation,
        VitalClientNode,
        VitalClientNodeInput,
        integer,
    } from '@sage/xtrem-client';
    export interface SupportAccessHistoryStatus$Enum {
        open: 1;
        closed: 2;
    }
    export type SupportAccessHistoryStatus = keyof SupportAccessHistoryStatus$Enum;
    export interface SupportAccessUnit$Enum {
        minutes: 1;
        hours: 2;
        days: 3;
    }
    export type SupportAccessUnit = keyof SupportAccessUnit$Enum;
    export interface UserType$Enum {
        application: 1;
        system: 2;
    }
    export type UserType = keyof UserType$Enum;
    export interface Activity extends ClientNode {
        name: string;
        description: string;
        permissions: string[];
        package: SysPackVersion;
    }
    export interface ActivityInput extends ClientNodeInput {
        name?: string;
        description?: string;
        permissions?: string[];
        package?: integer | string;
    }
    export interface ActivityBinding extends ClientNode {
        name: string;
        description: string;
        permissions: string[];
        package: SysPackVersion;
    }
    export interface Activity$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Activity$Lookups {
        package: QueryOperation<SysPackVersion>;
    }
    export interface Activity$Operations {
        query: QueryOperation<Activity>;
        read: ReadOperation<Activity>;
        aggregate: {
            read: AggregateReadOperation<Activity>;
            query: AggregateQueryOperation<Activity>;
        };
        create: CreateOperation<ActivityInput, Activity>;
        getDuplicate: GetDuplicateOperation<Activity>;
        update: UpdateOperation<ActivityInput, Activity>;
        updateById: UpdateByIdOperation<ActivityInput, Activity>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Activity$AsyncOperations;
        lookups(dataOrId: string | { data: ActivityInput }): Activity$Lookups;
        getDefaults: GetDefaultsOperation<Activity>;
    }
    export interface GroupRole extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        groupRoleSite: GroupRoleSite;
        role: Role;
    }
    export interface GroupRoleInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        role?: integer | string;
    }
    export interface GroupRoleBinding extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        groupRoleSite: GroupRoleSite;
        role: Role;
    }
    export interface GroupRole$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface GroupRole$Lookups {
        _vendor: QueryOperation<SysVendor>;
        role: QueryOperation<Role>;
    }
    export interface GroupRole$Operations {
        query: QueryOperation<GroupRole>;
        read: ReadOperation<GroupRole>;
        aggregate: {
            read: AggregateReadOperation<GroupRole>;
            query: AggregateQueryOperation<GroupRole>;
        };
        asyncOperations: GroupRole$AsyncOperations;
        lookups(dataOrId: string | { data: GroupRoleInput }): GroupRole$Lookups;
        getDefaults: GetDefaultsOperation<GroupRole>;
    }
    export interface GroupRoleSite extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        groupRoles: ClientCollection<GroupRole>;
        groupRolesDisplay: string;
        groupSites: ClientCollection<GroupSite>;
        groupSitesDisplay: string;
        createdBy: string;
        updatedBy: string;
        updateStamp: string;
        createStamp: string;
    }
    export interface GroupRoleSiteInput extends ClientNodeInput {
        _vendor?: integer | string;
        id?: string;
        name?: string;
        groupRoles?: Partial<GroupRoleInput>[];
        groupSites?: Partial<GroupSiteInput>[];
    }
    export interface GroupRoleSiteBinding extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        groupRoles: ClientCollection<GroupRoleBinding>;
        groupRolesDisplay: string;
        groupSites: ClientCollection<GroupSiteBinding>;
        groupSitesDisplay: string;
        createdBy: string;
        updatedBy: string;
        updateStamp: string;
        createStamp: string;
    }
    export interface GroupRoleSite$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface GroupRoleSite$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface GroupRoleSite$Operations {
        query: QueryOperation<GroupRoleSite>;
        read: ReadOperation<GroupRoleSite>;
        aggregate: {
            read: AggregateReadOperation<GroupRoleSite>;
            query: AggregateQueryOperation<GroupRoleSite>;
        };
        create: CreateOperation<GroupRoleSiteInput, GroupRoleSite>;
        getDuplicate: GetDuplicateOperation<GroupRoleSite>;
        update: UpdateOperation<GroupRoleSiteInput, GroupRoleSite>;
        updateById: UpdateByIdOperation<GroupRoleSiteInput, GroupRoleSite>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: GroupRoleSite$AsyncOperations;
        lookups(dataOrId: string | { data: GroupRoleSiteInput }): GroupRoleSite$Lookups;
        getDefaults: GetDefaultsOperation<GroupRoleSite>;
    }
    export interface GroupSite extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        groupRoleSite: GroupRoleSite;
        siteGroup: SiteGroup;
    }
    export interface GroupSiteInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        siteGroup?: integer | string;
    }
    export interface GroupSiteBinding extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        groupRoleSite: GroupRoleSite;
        siteGroup: SiteGroup;
    }
    export interface GroupSite$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface GroupSite$Lookups {
        _vendor: QueryOperation<SysVendor>;
        siteGroup: QueryOperation<SiteGroup>;
    }
    export interface GroupSite$Operations {
        query: QueryOperation<GroupSite>;
        read: ReadOperation<GroupSite>;
        aggregate: {
            read: AggregateReadOperation<GroupSite>;
            query: AggregateQueryOperation<GroupSite>;
        };
        asyncOperations: GroupSite$AsyncOperations;
        lookups(dataOrId: string | { data: GroupSiteInput }): GroupSite$Lookups;
        getDefaults: GetDefaultsOperation<GroupSite>;
    }
    export interface Role extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        description: string;
        id: string;
        isBillingRole: boolean;
        roles: ClientCollection<RoleToRole>;
        activities: ClientCollection<RoleActivity>;
        createdBy: string;
        updatedBy: string;
    }
    export interface RoleInput extends ClientNodeInput {
        _vendor?: integer | string;
        isActive?: boolean | string;
        name?: string;
        description?: string;
        id?: string;
        isBillingRole?: boolean | string;
        roles?: Partial<RoleToRoleInput>[];
        activities?: Partial<RoleActivityInput>[];
    }
    export interface RoleBinding extends ClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        name: string;
        description: string;
        id: string;
        isBillingRole: boolean;
        roles: ClientCollection<RoleToRoleBinding>;
        activities: ClientCollection<RoleActivityBinding>;
        createdBy: string;
        updatedBy: string;
    }
    export interface Role$Queries {
        all: Node$Operation<
            {
                isActive: boolean | string;
            },
            {
                _id: string;
                name: string;
                id: string;
                isBillingRole: boolean;
                createdBy: string;
                createStamp: string;
                updatedBy: string;
                updateStamp: string;
                isVendor: string;
            }[]
        >;
        getEffectivePermissions: Node$Operation<
            {
                role: string;
                nodeName?: string;
                activityName?: string;
                grantedPermission?: string;
            },
            {
                roleName: string;
                grantRoleName: string;
                activityName: string;
                permission: string;
                grantsTo: string;
                grantedPermission: string;
            }[]
        >;
    }
    export interface Role$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface Role$Lookups {
        _vendor: QueryOperation<SysVendor>;
    }
    export interface Role$Operations {
        query: QueryOperation<Role>;
        read: ReadOperation<Role>;
        aggregate: {
            read: AggregateReadOperation<Role>;
            query: AggregateQueryOperation<Role>;
        };
        queries: Role$Queries;
        create: CreateOperation<RoleInput, Role>;
        getDuplicate: GetDuplicateOperation<Role>;
        duplicate: DuplicateOperation<string, RoleInput, Role>;
        update: UpdateOperation<RoleInput, Role>;
        updateById: UpdateByIdOperation<RoleInput, Role>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: Role$AsyncOperations;
        lookups(dataOrId: string | { data: RoleInput }): Role$Lookups;
        getDefaults: GetDefaultsOperation<Role>;
    }
    export interface RoleActivity extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        role: Role;
        activity: Activity;
        hasAllPermissions: boolean;
        permissions: string[];
        getPermissions: string[];
        isActive: boolean;
    }
    export interface RoleActivityInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        activity?: integer | string;
        hasAllPermissions?: boolean | string;
        permissions?: string[];
        isActive?: boolean | string;
    }
    export interface RoleActivityBinding extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        role: Role;
        activity: Activity;
        hasAllPermissions: boolean;
        permissions: string[];
        getPermissions: string[];
        isActive: boolean;
    }
    export interface RoleActivity$Mutations {
        roleActivitiesCreateUpdate: Node$Operation<
            {
                roleActivities?: {
                    activity?: integer | string;
                    permissions?: string[];
                    isActive?: boolean | string;
                    hasAllPermissions?: boolean | string;
                }[];
                roleSysId?: string | null;
                roleId?: string;
                roleName?: string;
            },
            boolean
        >;
    }
    export interface RoleActivity$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface RoleActivity$Lookups {
        _vendor: QueryOperation<SysVendor>;
        activity: QueryOperation<Activity>;
    }
    export interface RoleActivity$Operations {
        query: QueryOperation<RoleActivity>;
        read: ReadOperation<RoleActivity>;
        aggregate: {
            read: AggregateReadOperation<RoleActivity>;
            query: AggregateQueryOperation<RoleActivity>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        mutations: RoleActivity$Mutations;
        asyncOperations: RoleActivity$AsyncOperations;
        lookups(dataOrId: string | { data: RoleActivityInput }): RoleActivity$Lookups;
        getDefaults: GetDefaultsOperation<RoleActivity>;
    }
    export interface RoleToRole extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        roleOrigin: Role;
        role: Role;
    }
    export interface RoleToRoleInput extends VitalClientNodeInput {
        _vendor?: integer | string;
        role?: integer | string;
    }
    export interface RoleToRoleBinding extends VitalClientNode {
        _vendor: SysVendor;
        _updateUser: User;
        _createUser: User;
        roleOrigin: Role;
        role: Role;
    }
    export interface RoleToRole$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface RoleToRole$Lookups {
        _vendor: QueryOperation<SysVendor>;
        role: QueryOperation<Role>;
    }
    export interface RoleToRole$Operations {
        query: QueryOperation<RoleToRole>;
        read: ReadOperation<RoleToRole>;
        aggregate: {
            read: AggregateReadOperation<RoleToRole>;
            query: AggregateQueryOperation<RoleToRole>;
        };
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: RoleToRole$AsyncOperations;
        lookups(dataOrId: string | { data: RoleToRoleInput }): RoleToRole$Lookups;
        getDefaults: GetDefaultsOperation<RoleToRole>;
    }
    export interface SiteGroup extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        isLegalCompany: boolean;
        hierarchyChartContent: TextStream;
        sites: ClientCollection<SiteGroupToSite>;
        siteGroups: ClientCollection<SiteGroupToSiteGroup>;
        createdBy: string;
        updatedBy: string;
    }
    export interface SiteGroupInput extends ClientNodeInput {
        id?: string;
        isActive?: boolean | string;
        name?: string;
        isLegalCompany?: boolean | string;
        sites?: Partial<SiteGroupToSiteInput>[];
        siteGroups?: Partial<SiteGroupToSiteGroupInput>[];
    }
    export interface SiteGroupBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        name: string;
        isLegalCompany: boolean;
        hierarchyChartContent: TextStream;
        sites: ClientCollection<SiteGroupToSiteBinding>;
        siteGroups: ClientCollection<SiteGroupToSiteGroupBinding>;
        createdBy: string;
        updatedBy: string;
    }
    export interface SiteGroup$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SiteGroup$Operations {
        query: QueryOperation<SiteGroup>;
        read: ReadOperation<SiteGroup>;
        aggregate: {
            read: AggregateReadOperation<SiteGroup>;
            query: AggregateQueryOperation<SiteGroup>;
        };
        create: CreateOperation<SiteGroupInput, SiteGroup>;
        getDuplicate: GetDuplicateOperation<SiteGroup>;
        duplicate: DuplicateOperation<string, SiteGroupInput, SiteGroup>;
        update: UpdateOperation<SiteGroupInput, SiteGroup>;
        updateById: UpdateByIdOperation<SiteGroupInput, SiteGroup>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SiteGroup$AsyncOperations;
        getDefaults: GetDefaultsOperation<SiteGroup>;
    }
    export interface SiteGroupToSite extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        siteGroup: SiteGroup;
        site: Site;
        isValid: boolean;
        dateAdd: string;
    }
    export interface SiteGroupToSiteInput extends VitalClientNodeInput {
        site?: integer | string;
        isValid?: boolean | string;
        dateAdd?: string;
    }
    export interface SiteGroupToSiteBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        siteGroup: SiteGroup;
        site: Site;
        isValid: boolean;
        dateAdd: string;
    }
    export interface SiteGroupToSite$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SiteGroupToSite$Lookups {
        site: QueryOperation<Site>;
    }
    export interface SiteGroupToSite$Operations {
        query: QueryOperation<SiteGroupToSite>;
        read: ReadOperation<SiteGroupToSite>;
        aggregate: {
            read: AggregateReadOperation<SiteGroupToSite>;
            query: AggregateQueryOperation<SiteGroupToSite>;
        };
        create: CreateOperation<SiteGroupToSiteInput, SiteGroupToSite>;
        getDuplicate: GetDuplicateOperation<SiteGroupToSite>;
        duplicate: DuplicateOperation<string, SiteGroupToSiteInput, SiteGroupToSite>;
        update: UpdateOperation<SiteGroupToSiteInput, SiteGroupToSite>;
        updateById: UpdateByIdOperation<SiteGroupToSiteInput, SiteGroupToSite>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SiteGroupToSite$AsyncOperations;
        lookups(dataOrId: string | { data: SiteGroupToSiteInput }): SiteGroupToSite$Lookups;
        getDefaults: GetDefaultsOperation<SiteGroupToSite>;
    }
    export interface SiteGroupToSiteGroup extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        siteGroupOrigin: SiteGroup;
        siteGroup: SiteGroup;
    }
    export interface SiteGroupToSiteGroupInput extends VitalClientNodeInput {
        siteGroup?: integer | string;
    }
    export interface SiteGroupToSiteGroupBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        siteGroupOrigin: SiteGroup;
        siteGroup: SiteGroup;
    }
    export interface SiteGroupToSiteGroup$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SiteGroupToSiteGroup$Lookups {
        siteGroup: QueryOperation<SiteGroup>;
    }
    export interface SiteGroupToSiteGroup$Operations {
        query: QueryOperation<SiteGroupToSiteGroup>;
        read: ReadOperation<SiteGroupToSiteGroup>;
        aggregate: {
            read: AggregateReadOperation<SiteGroupToSiteGroup>;
            query: AggregateQueryOperation<SiteGroupToSiteGroup>;
        };
        create: CreateOperation<SiteGroupToSiteGroupInput, SiteGroupToSiteGroup>;
        getDuplicate: GetDuplicateOperation<SiteGroupToSiteGroup>;
        duplicate: DuplicateOperation<string, SiteGroupToSiteGroupInput, SiteGroupToSiteGroup>;
        update: UpdateOperation<SiteGroupToSiteGroupInput, SiteGroupToSiteGroup>;
        updateById: UpdateByIdOperation<SiteGroupToSiteGroupInput, SiteGroupToSiteGroup>;
        delete: DeleteOperation<{}>;
        deleteById: DeleteOperation<string>;
        asyncOperations: SiteGroupToSiteGroup$AsyncOperations;
        lookups(dataOrId: string | { data: SiteGroupToSiteGroupInput }): SiteGroupToSiteGroup$Lookups;
        getDefaults: GetDefaultsOperation<SiteGroupToSiteGroup>;
    }
    export interface SupportAccessHistory extends ClientNode {
        _updateUser: User;
        _createUser: User;
        status: SupportAccessHistoryStatus;
        timeToClose: integer;
        startTime: string;
        endTime: string;
        isReadOnlyAccess: boolean;
    }
    export interface SupportAccessHistoryInput extends ClientNodeInput {
        startTime?: string;
        endTime?: string;
        isReadOnlyAccess?: boolean | string;
    }
    export interface SupportAccessHistoryBinding extends ClientNode {
        _updateUser: User;
        _createUser: User;
        status: SupportAccessHistoryStatus;
        timeToClose: integer;
        startTime: string;
        endTime: string;
        isReadOnlyAccess: boolean;
    }
    export interface SupportAccessHistory$Queries {
        checkSupportAccessOpen: Node$Operation<
            {},
            {
                isOpen: boolean;
                timeToClose: integer;
            }
        >;
    }
    export interface SupportAccessHistory$Mutations {
        allowAccess: Node$Operation<
            {
                forTime?: integer | string;
                units?: SupportAccessUnit;
                isReadOnlyAccess?: boolean | string;
            },
            SupportAccessHistory
        >;
        extendAccess: Node$Operation<
            {
                forTime?: integer | string;
                units?: SupportAccessUnit;
            },
            SupportAccessHistory
        >;
        revokeAccess: Node$Operation<{}, SupportAccessHistory>;
    }
    export interface SupportAccessHistory$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface SupportAccessHistory$Operations {
        query: QueryOperation<SupportAccessHistory>;
        read: ReadOperation<SupportAccessHistory>;
        aggregate: {
            read: AggregateReadOperation<SupportAccessHistory>;
            query: AggregateQueryOperation<SupportAccessHistory>;
        };
        queries: SupportAccessHistory$Queries;
        mutations: SupportAccessHistory$Mutations;
        asyncOperations: SupportAccessHistory$AsyncOperations;
        getDefaults: GetDefaultsOperation<SupportAccessHistory>;
    }
    export interface UserBillingRole extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        role: Role;
    }
    export interface UserBillingRoleInput extends VitalClientNodeInput {
        role?: integer | string;
    }
    export interface UserBillingRoleBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        user: User;
        role: Role;
    }
    export interface UserBillingRole$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UserBillingRole$Lookups {
        role: QueryOperation<Role>;
    }
    export interface UserBillingRole$Operations {
        query: QueryOperation<UserBillingRole>;
        read: ReadOperation<UserBillingRole>;
        aggregate: {
            read: AggregateReadOperation<UserBillingRole>;
            query: AggregateQueryOperation<UserBillingRole>;
        };
        asyncOperations: UserBillingRole$AsyncOperations;
        lookups(dataOrId: string | { data: UserBillingRoleInput }): UserBillingRole$Lookups;
        getDefaults: GetDefaultsOperation<UserBillingRole>;
    }
    export interface UserGroup extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        user: User;
        group: GroupRoleSite;
        createdBy: string;
        updatedBy: string;
        updateStamp: string;
        createStamp: string;
    }
    export interface UserGroupInput extends VitalClientNodeInput {
        isActive?: boolean | string;
        group?: integer | string;
    }
    export interface UserGroupBinding extends VitalClientNode {
        _updateUser: User;
        _createUser: User;
        isActive: boolean;
        user: User;
        group: GroupRoleSite;
        createdBy: string;
        updatedBy: string;
        updateStamp: string;
        createStamp: string;
    }
    export interface UserGroup$AsyncOperations {
        asyncExport: AsyncOperation<
            {
                id?: string;
                filter?: string;
            },
            string
        >;
    }
    export interface UserGroup$Lookups {
        group: QueryOperation<GroupRoleSite>;
    }
    export interface UserGroup$Operations {
        query: QueryOperation<UserGroup>;
        read: ReadOperation<UserGroup>;
        aggregate: {
            read: AggregateReadOperation<UserGroup>;
            query: AggregateQueryOperation<UserGroup>;
        };
        asyncOperations: UserGroup$AsyncOperations;
        lookups(dataOrId: string | { data: UserGroupInput }): UserGroup$Lookups;
        getDefaults: GetDefaultsOperation<UserGroup>;
    }
    export interface CompanyExtension {
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
    }
    export interface CompanyInputExtension {
        id?: string;
        isActive?: boolean | string;
    }
    export interface CompanyBindingExtension {
        _updateUser: User;
        _createUser: User;
        id: string;
        isActive: boolean;
        sites: ClientCollection<Site>;
    }
    export interface SiteExtension {
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
    }
    export interface SiteInputExtension {
        id?: string;
        name?: string;
        description?: string;
        isActive?: boolean | string;
        legalCompany?: integer | string;
    }
    export interface SiteBindingExtension {
        _updateUser: User;
        _createUser: User;
        id: string;
        name: string;
        description: string;
        isActive: boolean;
        legalCompany: Company;
        linkedSites: ClientCollection<Site>;
        siteGroups: ClientCollection<SiteGroup>;
        groupRoleSites: ClientCollection<GroupRoleSite>;
    }
    export interface UserExtension {
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        preferences: UserPreferences;
        clientSettings: ClientCollection<SysClientUserSettings>;
        billingRole: UserBillingRole;
        authorizationGroup: ClientCollection<UserGroup>;
        groupDisplay: string;
        isOperatorUser: boolean;
    }
    export interface UserInputExtension {
        email?: string;
        firstName?: string;
        lastName?: string;
        isActive?: boolean | string;
        photo?: BinaryStream;
        userType?: UserType;
        isAdministrator?: boolean | string;
        isDemoPersona?: boolean | string;
        isApiUser?: boolean | string;
        operatorCode?: string;
        preferences?: UserPreferencesInput;
        clientSettings?: Partial<SysClientUserSettingsInput>[];
        billingRole?: UserBillingRoleInput;
        authorizationGroup?: Partial<UserGroupInput>[];
        isOperatorUser?: boolean | string;
    }
    export interface UserBindingExtension {
        _updateUser: User;
        _createUser: User;
        email: string;
        firstName: string;
        lastName: string;
        isActive: boolean;
        photo: BinaryStream;
        displayName: string;
        userType: UserType;
        isAdministrator: boolean;
        isDemoPersona: boolean;
        isApiUser: boolean;
        operatorCode: string;
        preferences: UserPreferencesBinding;
        clientSettings: ClientCollection<SysClientUserSettingsBinding>;
        billingRole: UserBillingRoleBinding;
        authorizationGroup: ClientCollection<UserGroupBinding>;
        groupDisplay: string;
        isOperatorUser: boolean;
    }
    export interface UserExtension$Queries {
        all: Node$Operation<
            {
                isActive: boolean | string;
            },
            {
                _id: string;
                name: string;
                displayName: string;
                email: string;
                isWelcomeMailSent: boolean;
                groupDisplay: string;
                createdBy: string;
                createStamp: string;
                updatedBy: string;
                updateStamp: string;
            }[]
        >;
    }
    export interface UserExtension$AsyncOperations {
        sendBulkWelcomeMail: AsyncOperation<
            {
                filter?: string;
            },
            boolean
        >;
    }
    export interface UserExtension$Operations {
        queries: UserExtension$Queries;
        asyncOperations: UserExtension$AsyncOperations;
        getDefaults: GetDefaultsOperation<User>;
    }
    export interface Package {
        '@sage/xtrem-authorization/Activity': Activity$Operations;
        '@sage/xtrem-authorization/GroupRole': GroupRole$Operations;
        '@sage/xtrem-authorization/GroupRoleSite': GroupRoleSite$Operations;
        '@sage/xtrem-authorization/GroupSite': GroupSite$Operations;
        '@sage/xtrem-authorization/Role': Role$Operations;
        '@sage/xtrem-authorization/RoleActivity': RoleActivity$Operations;
        '@sage/xtrem-authorization/RoleToRole': RoleToRole$Operations;
        '@sage/xtrem-authorization/SiteGroup': SiteGroup$Operations;
        '@sage/xtrem-authorization/SiteGroupToSite': SiteGroupToSite$Operations;
        '@sage/xtrem-authorization/SiteGroupToSiteGroup': SiteGroupToSiteGroup$Operations;
        '@sage/xtrem-authorization/SupportAccessHistory': SupportAccessHistory$Operations;
        '@sage/xtrem-authorization/UserBillingRole': UserBillingRole$Operations;
        '@sage/xtrem-authorization/UserGroup': UserGroup$Operations;
    }
    export interface GraphApi
        extends Package,
            SageXtremCommunication$Package,
            SageXtremMetadata$Package,
            SageXtremSystem$Package {}
}
declare module '@sage/xtrem-authorization-api' {
    export type * from '@sage/xtrem-authorization-api-partial';
}
declare module '@sage/xtrem-communication-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-authorization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-metadata-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-authorization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type { GraphApi as GraphApiExtension } from '@sage/xtrem-authorization-api';
    export interface GraphApi extends GraphApiExtension {}
}
declare module '@sage/xtrem-system-api-partial' {
    import type {
        CompanyBindingExtension,
        CompanyExtension,
        CompanyInputExtension,
        SiteBindingExtension,
        SiteExtension,
        SiteInputExtension,
        UserBindingExtension,
        UserExtension,
        UserExtension$AsyncOperations,
        UserExtension$Operations,
        UserExtension$Queries,
        UserInputExtension,
    } from '@sage/xtrem-authorization-api';
    export interface Company extends CompanyExtension {}
    export interface CompanyBinding extends CompanyBindingExtension {}
    export interface CompanyInput extends CompanyInputExtension {}
    export interface Site extends SiteExtension {}
    export interface SiteBinding extends SiteBindingExtension {}
    export interface SiteInput extends SiteInputExtension {}
    export interface User extends UserExtension {}
    export interface UserBinding extends UserBindingExtension {}
    export interface UserInput extends UserInputExtension {}
    export interface User$Queries extends UserExtension$Queries {}
    export interface User$AsyncOperations extends UserExtension$AsyncOperations {}
    export interface User$Operations extends UserExtension$Operations {}
}
