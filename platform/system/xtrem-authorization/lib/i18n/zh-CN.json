{"@sage/xtrem-authorization/activity__group_role_site__name": "组角色地点", "@sage/xtrem-authorization/activity__role__name": "角色", "@sage/xtrem-authorization/activity__site_group__name": "地点组", "@sage/xtrem-authorization/activity__support_access_history__name": "支持访问历史", "@sage/xtrem-authorization/activity__user__name": "用户", "@sage/xtrem-authorization/cannot-delete-site-from-site-group": "该地点无法从连接的地点组中删除。", "@sage/xtrem-authorization/cannot-delete-site-group": "无法删除一个关联的地点组。", "@sage/xtrem-authorization/data_types__name_array_data_type__name": "名称列数据类型", "@sage/xtrem-authorization/data_types__string_data_type__name": "字符串数据类型", "@sage/xtrem-authorization/data_types__support_access_history_status_enum__name": "支持访问历史状态枚举", "@sage/xtrem-authorization/data_types__support_access_unit_enum__name": "支持访问单位枚举", "@sage/xtrem-authorization/data_types__user_type_enum__name": "用户类型枚举", "@sage/xtrem-authorization/delete-confirmation": "记录已删除", "@sage/xtrem-authorization/delete-dialog-content": "您将要删除该组。", "@sage/xtrem-authorization/delete-group": "确认删除", "@sage/xtrem-authorization/duplication-confirmation": "记录已复制", "@sage/xtrem-authorization/enums__support_access_history_status__closed": "关闭", "@sage/xtrem-authorization/enums__support_access_history_status__open": "打开", "@sage/xtrem-authorization/enums__support_access_unit__days": "日", "@sage/xtrem-authorization/enums__support_access_unit__hours": "时", "@sage/xtrem-authorization/enums__support_access_unit__minutes": "分", "@sage/xtrem-authorization/enums__user_type__application": "应用程序", "@sage/xtrem-authorization/enums__user_type__system": "系统", "@sage/xtrem-authorization/menu_item__support": "支持", "@sage/xtrem-authorization/menu_item__user-data": "用户与安全", "@sage/xtrem-authorization/node-extensions__site_extension__property__groupRoleSites": "组角色地点", "@sage/xtrem-authorization/node-extensions__site_extension__property__siteGroups": "地点组", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail": "批量发送欢迎邮件", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail__failed": "批量欢迎邮件发送失败。", "@sage/xtrem-authorization/node-extensions__user_extension__property__authorizationGroup": "授权组", "@sage/xtrem-authorization/node-extensions__user_extension__property__billingRole": "开票角色", "@sage/xtrem-authorization/node-extensions__user_extension__property__groupDisplay": "组显示", "@sage/xtrem-authorization/node-extensions__user_extension__property__objectGrants": "对象授予", "@sage/xtrem-authorization/node-extensions__user_extension__query__all": "全部", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__failed": "全部失败。", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__parameter__isActive": "激活的", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__activity__node_name": "业务活动", "@sage/xtrem-authorization/nodes__activity__property__description": "描述", "@sage/xtrem-authorization/nodes__activity__property__name": "名称", "@sage/xtrem-authorization/nodes__activity__property__package": "程序包", "@sage/xtrem-authorization/nodes__activity__property__permissions": "许可", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role__node_name": "组角色", "@sage/xtrem-authorization/nodes__group_role__property__groupRoleSite": "地点组角色", "@sage/xtrem-authorization/nodes__group_role__property__role": "角色", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role_site__node_name": "地点组角色", "@sage/xtrem-authorization/nodes__group_role_site__property__createdBy": "创建者为", "@sage/xtrem-authorization/nodes__group_role_site__property__createStamp": "创建标记", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRoles": "组角色", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRolesDisplay": "组角色显示", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSites": "地点组", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSitesDisplay": "地点组显示", "@sage/xtrem-authorization/nodes__group_role_site__property__id": "ID", "@sage/xtrem-authorization/nodes__group_role_site__property__name": "名称", "@sage/xtrem-authorization/nodes__group_role_site__property__updatedBy": "更新者为", "@sage/xtrem-authorization/nodes__group_role_site__property__updateStamp": "更新标记", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_site__node_name": "地点组", "@sage/xtrem-authorization/nodes__group_site__property__groupRoleSite": "地点组角色", "@sage/xtrem-authorization/nodes__group_site__property__siteGroup": "地点组", "@sage/xtrem-authorization/nodes__restricted_node__node_name": "受限节点", "@sage/xtrem-authorization/nodes__restricted_node__property__userGrants": "用户许可", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__node_name": "基于用户许可的受限节点", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__accessMap": "访问地图", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__object": "对象", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__user": "用户", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role__node_name": "角色", "@sage/xtrem-authorization/nodes__role__property__activities": "业务活动", "@sage/xtrem-authorization/nodes__role__property__createdBy": "创建者为", "@sage/xtrem-authorization/nodes__role__property__description": "描述", "@sage/xtrem-authorization/nodes__role__property__id": "ID", "@sage/xtrem-authorization/nodes__role__property__isActive": "激活的", "@sage/xtrem-authorization/nodes__role__property__isBillingRole": "开票角色", "@sage/xtrem-authorization/nodes__role__property__name": "名称", "@sage/xtrem-authorization/nodes__role__property__roles": "角色", "@sage/xtrem-authorization/nodes__role__property__setupId": "设置ID", "@sage/xtrem-authorization/nodes__role__property__updatedBy": "更新者为", "@sage/xtrem-authorization/nodes__role__query__all": "全部", "@sage/xtrem-authorization/nodes__role__query__all__failed": "全部失败。", "@sage/xtrem-authorization/nodes__role__query__all__parameter__isActive": "激活的", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__failed": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__activityName": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__grantedPermission": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__nodeName": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__role": "", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate": "角色活动创建更新", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__failed": "角色活动创建更新失败。", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleActivities": "角色活动", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleId": "角色ID", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleName": "角色名称", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleSysId": "角色系统ID", "@sage/xtrem-authorization/nodes__role_activity__node_name": "角色业务活动", "@sage/xtrem-authorization/nodes__role_activity__property__activity": "业务活动", "@sage/xtrem-authorization/nodes__role_activity__property__getPermissions": "得到许可", "@sage/xtrem-authorization/nodes__role_activity__property__hasAllPermissions": "具有所有许可", "@sage/xtrem-authorization/nodes__role_activity__property__isActive": "激活的", "@sage/xtrem-authorization/nodes__role_activity__property__permissions": "许可", "@sage/xtrem-authorization/nodes__role_activity__property__role": "角色", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_to_role__node_name": "角色到角色", "@sage/xtrem-authorization/nodes__role_to_role__property__role": "角色", "@sage/xtrem-authorization/nodes__role_to_role__property__roleOrigin": "角色来源", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group__node_name": "地点组", "@sage/xtrem-authorization/nodes__site_group__property__createdBy": "创建者为", "@sage/xtrem-authorization/nodes__site_group__property__hierarchyChartContent": "层次结构图表内容", "@sage/xtrem-authorization/nodes__site_group__property__id": "ID", "@sage/xtrem-authorization/nodes__site_group__property__isActive": "激活的", "@sage/xtrem-authorization/nodes__site_group__property__isLegalCompany": "公司", "@sage/xtrem-authorization/nodes__site_group__property__name": "名称", "@sage/xtrem-authorization/nodes__site_group__property__siteGroups": "地点组", "@sage/xtrem-authorization/nodes__site_group__property__sites": "地点", "@sage/xtrem-authorization/nodes__site_group__property__updatedBy": "更新者为", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site__node_name": "地点组到地点", "@sage/xtrem-authorization/nodes__site_group_to_site__property__dateAdd": "添加日期", "@sage/xtrem-authorization/nodes__site_group_to_site__property__isValid": "有效的", "@sage/xtrem-authorization/nodes__site_group_to_site__property__site": "地点", "@sage/xtrem-authorization/nodes__site_group_to_site__property__siteGroup": "地点组", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site_group__node_name": "地点组到地点组", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroup": "地点组", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroupOrigin": "地点组来源", "@sage/xtrem-authorization/nodes__support_access__cannot_allow_support_access_because_there_is_a_open_session": "已经有一个打开的会话。请联系您的系统管理员。", "@sage/xtrem-authorization/nodes__support_access__end_time_must_be_greater_than_start_time": "录入时间必须晚于起始时间。", "@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0": "录入的数字必须大于0。", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_extend_the_access": "没有打开的会话来扩展访问权限。请联系您的系统管理员。", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_revoke_the_access": "没有打开的会话来撤销访问权限。请联系您的系统管理员。", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess": "允许访问", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__failed": "允许访问失败。", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__forTime": "针对时间", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__isReadOnlyAccess": "只读访问", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__units": "单位", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess": "扩展访问", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__failed": "扩展访问失败。", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__forTime": "针对时间", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__units": "单位", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess": "取消访问", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess__failed": "撤销访问失败。", "@sage/xtrem-authorization/nodes__support_access_history__node_name": "支持访问历史", "@sage/xtrem-authorization/nodes__support_access_history__property__endTime": "结束时间", "@sage/xtrem-authorization/nodes__support_access_history__property__isReadOnlyAccess": "只读访问", "@sage/xtrem-authorization/nodes__support_access_history__property__startTime": "起始时间", "@sage/xtrem-authorization/nodes__support_access_history__property__status": "状态", "@sage/xtrem-authorization/nodes__support_access_history__property__timeToClose": "关闭时间", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen": "检查是否开启支持访问", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen__failed": "检查支持访问打开失败。", "@sage/xtrem-authorization/nodes__user__cannot_change_own_rights": "不允许您修改自己的授权组。", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_billing_role__node_name": "用户开票角色", "@sage/xtrem-authorization/nodes__user_billing_role__property__role": "角色", "@sage/xtrem-authorization/nodes__user_billing_role__property__user": "用户", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport": "导出", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__filter": "筛选", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_group__node_name": "用户组", "@sage/xtrem-authorization/nodes__user_group__property__createdBy": "创建者为", "@sage/xtrem-authorization/nodes__user_group__property__createStamp": "创建标记", "@sage/xtrem-authorization/nodes__user_group__property__group": "组", "@sage/xtrem-authorization/nodes__user_group__property__isActive": "激活的", "@sage/xtrem-authorization/nodes__user_group__property__updatedBy": "更新者为", "@sage/xtrem-authorization/nodes__user_group__property__updateStamp": "更新标记", "@sage/xtrem-authorization/nodes__user_group__property__user": "用户", "@sage/xtrem-authorization/operation-not-allowed-on-object": "不允许对该对象进行操作", "@sage/xtrem-authorization/package__name": "授权", "@sage/xtrem-authorization/pages__group_list____title": "组", "@sage/xtrem-authorization/pages__group_list__addNewGroup____title": "新组", "@sage/xtrem-authorization/pages__group_list__groups____columns__title": "创建者为", "@sage/xtrem-authorization/pages__group_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__2": "更新者为", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__name": "名称", "@sage/xtrem-authorization/pages__group_list__groups____dropdownActions__title": "删除角色", "@sage/xtrem-authorization/pages__group_list__groupsBlock____title": "用户组", "@sage/xtrem-authorization/pages__group_role_site____objectTypePlural": "授权组", "@sage/xtrem-authorization/pages__group_role_site____objectTypeSingular": "授权组", "@sage/xtrem-authorization/pages__group_role_site____subtitle": "创建一个组来授予用户业务活动和数据访问的权限。", "@sage/xtrem-authorization/pages__group_role_site____title": "授权组", "@sage/xtrem-authorization/pages__group_role_site___id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__allRoles____title": "选择所有角色", "@sage/xtrem-authorization/pages__group_role_site__allSiteGroup____title": "选择所有地点组", "@sage/xtrem-authorization/pages__group_role_site__associatedUserSection____title": "用户", "@sage/xtrem-authorization/pages__group_role_site__confirm____title": "保存", "@sage/xtrem-authorization/pages__group_role_site__customSave____title": "保存", "@sage/xtrem-authorization/pages__group_role_site__customSaveAction____title": "保存", "@sage/xtrem-authorization/pages__group_role_site__editGroupRoleSite____title": "编辑授权组", "@sage/xtrem-authorization/pages__group_role_site__groupInfoBlock____title": "组信息", "@sage/xtrem-authorization/pages__group_role_site__groupList____title": "查看授权组", "@sage/xtrem-authorization/pages__group_role_site__groupRoleBlock____title": "用户角色", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__description": "描述", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__name": "名称", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____title": "角色", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role": "名称", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__description": "描述", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____dropdownActions__title": "业务活动", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____title": "角色和业务活动", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__name": "名称", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____lookupDialogTitle": "选择用户角色", "@sage/xtrem-authorization/pages__group_role_site__groupSiteBlock____title": "地点组", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__name": "名称", "@sage/xtrem-authorization/pages__group_role_site__groupSites____title": "地点", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__name": "名称", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____lookupDialogTitle": "选择地点组", "@sage/xtrem-authorization/pages__group_role_site__id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__infoSection____title": "常规", "@sage/xtrem-authorization/pages__group_role_site__name____title": "名称", "@sage/xtrem-authorization/pages__group_role_site__rolesActivitiesSection____title": "角色和业务活动", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title": "创建者为", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__2": "更新者为", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__displayName": "用户名称", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__isActive": "激活的", "@sage/xtrem-authorization/pages__group_role_site__users____title": "用户", "@sage/xtrem-authorization/pages__group_role_site_list____title": "授权组清单", "@sage/xtrem-authorization/pages__group_role_site_list__addNewGroup____title": "添加组", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title": "创建者为", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__2": "更新者为", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__name": "名称", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title": "编辑", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title__2": "删除", "@sage/xtrem-authorization/pages__group_role_site_list__groups____title": "授权组", "@sage/xtrem-authorization/pages__group_role_site_list__groupsBlock____title": "用户组", "@sage/xtrem-authorization/pages__group_role_site_new_group_title": "新建授权组", "@sage/xtrem-authorization/pages__new_group__group_created": "已创建授权组{{newGroupId}}", "@sage/xtrem-authorization/pages__new_group_panel____subtitle": "创建一个组来授予用户业务活动和数据访问的权限。", "@sage/xtrem-authorization/pages__new_group_panel____title": "新组", "@sage/xtrem-authorization/pages__new_group_panel__allRoles____title": "所有角色", "@sage/xtrem-authorization/pages__new_group_panel__allSiteAndSiteGroup____title": "所有地点/地点组", "@sage/xtrem-authorization/pages__new_group_panel__cancelAction____title": "取消", "@sage/xtrem-authorization/pages__new_group_panel__groupInfoBlock____title": "组信息", "@sage/xtrem-authorization/pages__new_group_panel__id____title": "ID", "@sage/xtrem-authorization/pages__new_group_panel__name____title": "名称", "@sage/xtrem-authorization/pages__new_group_panel__saveNewGroup____title": "保存", "@sage/xtrem-authorization/pages__new_group_panel__selectionSection____title": "关联角色和地点/地点组", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__name": "名称", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__name": "名称", "@sage/xtrem-authorization/pages__operator_user_panel____title": "重置操作员代码", "@sage/xtrem-authorization/pages__operator_user_panel__cancel____title": "取消", "@sage/xtrem-authorization/pages__operator_user_panel__confirm____title": "确认", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeBlock____title": "操作员代码版块", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeInput____title": "新的操作员代码", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeSection____title": "操作员代码选择", "@sage/xtrem-authorization/pages__role_detail____subtitle": "角色", "@sage/xtrem-authorization/pages__role_detail____title": "角色", "@sage/xtrem-authorization/pages__role_detail__activitySection____title": "业务活动", "@sage/xtrem-authorization/pages__role_detail__cancel____title": "取消", "@sage/xtrem-authorization/pages__role_detail__confirm____title": "保存", "@sage/xtrem-authorization/pages__role_detail__not_updated": "许可更新失败。", "@sage/xtrem-authorization/pages__role_detail__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_detail_updated": "许可已更新。.", "@sage/xtrem-authorization/pages__role_list____title": "角色", "@sage/xtrem-authorization/pages__role_list__addNewRole____title": "添加角色", "@sage/xtrem-authorization/pages__role_list__duplicate": "复制", "@sage/xtrem-authorization/pages__role_list__field____columns__title___id": "ID", "@sage/xtrem-authorization/pages__role_list__field____columns__title__id": "ID", "@sage/xtrem-authorization/pages__role_list__field____columns__title__name": "名称", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title": "编辑角色", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__2": "复制角色", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__3": "查看组", "@sage/xtrem-authorization/pages__role_list__field____dropdownActions__title__4": "删除角色", "@sage/xtrem-authorization/pages__role_list__fieldBlock____title": "角色行", "@sage/xtrem-authorization/pages__role_list__id____title": "ID", "@sage/xtrem-authorization/pages__role_list__name____title": "名称", "@sage/xtrem-authorization/pages__role_list__roles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__createdByUserAndStamp": "创建者为", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__isBillingRole": "开票角色", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__name": "名称", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__updateByUserAndStamp": "更新者为", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title": "编辑", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__2": "删除", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__3": "复制", "@sage/xtrem-authorization/pages__role_list__rolesBlock____title": "角色行", "@sage/xtrem-authorization/pages__role_list__roleSection____title": "角色", "@sage/xtrem-authorization/pages__role_setup": "角色已创建", "@sage/xtrem-authorization/pages__role_setup____title": "新建角色", "@sage/xtrem-authorization/pages__role_setup__cancel____title": "取消", "@sage/xtrem-authorization/pages__role_setup__confirm____title": "保存", "@sage/xtrem-authorization/pages__role_setup__duplicated_id": "录入的ID已存在，请选择另一个ID。", "@sage/xtrem-authorization/pages__role_setup__id____title": "ID", "@sage/xtrem-authorization/pages__role_setup__name____title": "名称", "@sage/xtrem-authorization/pages__role_setup__next____title": "继续", "@sage/xtrem-authorization/pages__role_setup__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_setup__packageSelect____title": "程序包", "@sage/xtrem-authorization/pages__role_setup__roleBlock____title": "角色信息", "@sage/xtrem-authorization/pages__role_setup_failed": "角色创建失败。", "@sage/xtrem-authorization/pages__site_group____subtitle": "地点组", "@sage/xtrem-authorization/pages__site_group____title": "地点组", "@sage/xtrem-authorization/pages__site_group___id____title": "ID", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupBlock____title": "授权组", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupSection____title": "用户组", "@sage/xtrem-authorization/pages__site_group__chartBlock____title": "组织", "@sage/xtrem-authorization/pages__site_group__confirm____title": "保存", "@sage/xtrem-authorization/pages__site_group__customSave____title": "保存", "@sage/xtrem-authorization/pages__site_group__customSaveAction____title": "保存", "@sage/xtrem-authorization/pages__site_group__editSiteGroup____title": "编辑地点组", "@sage/xtrem-authorization/pages__site_group__generalBlock____title": "地点组", "@sage/xtrem-authorization/pages__site_group__generalSection____title": "常规", "@sage/xtrem-authorization/pages__site_group__groups____columns__title": "创建者为", "@sage/xtrem-authorization/pages__site_group__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__2": "更新者为", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__name": "名称", "@sage/xtrem-authorization/pages__site_group__groups____title": "授权组", "@sage/xtrem-authorization/pages__site_group__hierarchyChartContent____title": "组织", "@sage/xtrem-authorization/pages__site_group__id____title": "ID", "@sage/xtrem-authorization/pages__site_group__isActive____title": "激活的", "@sage/xtrem-authorization/pages__site_group__isLegalCompany____title": "公司", "@sage/xtrem-authorization/pages__site_group__name____title": "名称", "@sage/xtrem-authorization/pages__site_group__siteGroupList____title": "地点组列表", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__name": "名称", "@sage/xtrem-authorization/pages__site_group__siteGroupsBlock____title": "地点组", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__name": "名称", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____lookupDialogTitle": "选择地点组", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site___id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__name": "名称", "@sage/xtrem-authorization/pages__site_group__sitesBlock____title": "地点", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__name": "名称", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____lookupDialogTitle": "选择地点", "@sage/xtrem-authorization/pages__site_group__toggleChart____helperText": "允许用户切换网格或图表显示", "@sage/xtrem-authorization/pages__site_group__toggleChart____title": "显示选项", "@sage/xtrem-authorization/pages__site_group__toggleChartBlock____title": "切换图表/网格", "@sage/xtrem-authorization/pages__site_group_chart": "图表", "@sage/xtrem-authorization/pages__site_group_grid": "表", "@sage/xtrem-authorization/pages__site_group_list____title": "地点组", "@sage/xtrem-authorization/pages__site_group_list__addNewSiteGroup____title": "新建地点组", "@sage/xtrem-authorization/pages__site_group_list__createSite____title": "创建地点", "@sage/xtrem-authorization/pages__site_group_list__createSiteGroup____title": "创建地点组", "@sage/xtrem-authorization/pages__site_group_list__fieldBlock____title": "地点组", "@sage/xtrem-authorization/pages__site_group_list__groupList____title": "地点组列表", "@sage/xtrem-authorization/pages__site_group_list__section____title": "地点组", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id__2": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name": "名称", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name__2": "名称", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title": "编辑", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title__2": "删除", "@sage/xtrem-authorization/pages__site_group_new_title": "新建地点组", "@sage/xtrem-authorization/pages__support_access_history____title": "支持访问", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title___id": "ID", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__endTime": "结束时间", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__isReadOnlyAccess": "只读访问", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__startTime": "起始时间", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__status": "状态", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____title": "支持访问历史", "@sage/xtrem-authorization/pages__support_access_history__allow_access_button_text": "允许访问", "@sage/xtrem-authorization/pages__support_access_history__description____content": "指定用户访问的持续时间。", "@sage/xtrem-authorization/pages__support_access_history__dummy____content": "", "@sage/xtrem-authorization/pages__support_access_history__extend_access_button_text": "扩展访问", "@sage/xtrem-authorization/pages__support_access_history__forTime____title": "编号", "@sage/xtrem-authorization/pages__support_access_history__infoSection____title": "常规", "@sage/xtrem-authorization/pages__support_access_history__isReadOnlyAccess____title": "只读访问", "@sage/xtrem-authorization/pages__support_access_history__isSupportAccessOpen____title": "只读访问", "@sage/xtrem-authorization/pages__support_access_history__please_fill_the_number_and_unit_field": "录入数量和单位", "@sage/xtrem-authorization/pages__support_access_history__revoke_access_button_text": "取消访问", "@sage/xtrem-authorization/pages__support_access_history__supportAccessBlock____title": "允许Sage支持访问", "@sage/xtrem-authorization/pages__support_access_history__supportAccessHistoryBlock____title": "支持访问历史", "@sage/xtrem-authorization/pages__support_access_history__unit____title": "单位", "@sage/xtrem-authorization/pages__user____navigationPanel__bulkActions__title": "发送欢迎邮件", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__image__title": "图片", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line2__title": "Email", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line3__title": "API用户", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__title__title": "名字", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__titleRight__title": "姓氏", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title": "全部", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__2": "交互式", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__3": "第三方应用", "@sage/xtrem-authorization/pages__user____objectTypePlural": "用户", "@sage/xtrem-authorization/pages__user____objectTypeSingular": "用户", "@sage/xtrem-authorization/pages__user____subtitle": "用户", "@sage/xtrem-authorization/pages__user____title": "用户", "@sage/xtrem-authorization/pages__user___id____title": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdBy": "创建者为", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdByUserAndStamp": "创建者为", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__name": "名称", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updateByUserAndStamp": "更新者为", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updatedBy": "更新者为", "@sage/xtrem-authorization/pages__user__associatedRoles____title": "角色和业务活动", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdBy": "创建者为", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdByUserAndStamp": "创建者为", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__name": "名称", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updateByUserAndStamp": "更新者为", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updatedBy": "更新者为", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____title": "地点组", "@sage/xtrem-authorization/pages__user__associatedSiteGroupsSection____title": "地点组", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__2": "名称", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__3": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group___id": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupRolesDisplay": "角色", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupSitesDisplay": "地点组", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__id": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__name": "名称", "@sage/xtrem-authorization/pages__user__authorizationGroup____title": "授权组", "@sage/xtrem-authorization/pages__user__editUser____title": "编辑用户", "@sage/xtrem-authorization/pages__user__email____title": "Email", "@sage/xtrem-authorization/pages__user__firstName____title": "名字", "@sage/xtrem-authorization/pages__user__generalSection____title": "常规", "@sage/xtrem-authorization/pages__user__idBlock____title": "ID", "@sage/xtrem-authorization/pages__user__importExportDateFormat____helperText": "默认值将是 ", "@sage/xtrem-authorization/pages__user__importExportDateFormat____title": "导入/导出日期格式", "@sage/xtrem-authorization/pages__user__importExportDelimiter____helperText": "默认值将是 ", "@sage/xtrem-authorization/pages__user__importExportDelimiter____title": "导入/导出分隔符", "@sage/xtrem-authorization/pages__user__isActive____title": "激活的", "@sage/xtrem-authorization/pages__user__isAdministrator____title": "管理员", "@sage/xtrem-authorization/pages__user__isApiUser____title": "API用户", "@sage/xtrem-authorization/pages__user__isDemoPersona____title": "演示人物", "@sage/xtrem-authorization/pages__user__isExternal____title": "外部的", "@sage/xtrem-authorization/pages__user__isOperatorUser____title": "Pin身份验证", "@sage/xtrem-authorization/pages__user__isWelcomeMailSent____title": "发送欢迎邮件", "@sage/xtrem-authorization/pages__user__lastName____title": "姓氏", "@sage/xtrem-authorization/pages__user__list_bulk_send_welcome_mail": "发送电子邮件", "@sage/xtrem-authorization/pages__user__operatorCode____title": "PIN代码", "@sage/xtrem-authorization/pages__user__preferencesBlock____title": "首选项", "@sage/xtrem-authorization/pages__user__preferencesSection____title": "首选项", "@sage/xtrem-authorization/pages__user__resetOperatorCodePage____title": "PIN代码", "@sage/xtrem-authorization/pages__user__role____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__role____columns__title__name": "名称", "@sage/xtrem-authorization/pages__user__role____lookupDialogTitle": "选择开票角色", "@sage/xtrem-authorization/pages__user__role____title": "开票角色", "@sage/xtrem-authorization/pages__user__rolesActivitiesSection____title": "角色和业务活动", "@sage/xtrem-authorization/pages__user__save____title": "保存", "@sage/xtrem-authorization/pages__user__send_welcome_mail": "发送邮件", "@sage/xtrem-authorization/pages__user__send_welcome_mail_button": "发送", "@sage/xtrem-authorization/pages__user__send_welcome_mail_dialog_content": "您将要给用户发送一封欢迎邮件。", "@sage/xtrem-authorization/pages__user__userAuthorizationInformationBlock____title": "授权组", "@sage/xtrem-authorization/pages__user__userGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__name": "名称", "@sage/xtrem-authorization/pages__user__userGroups____lookupDialogTitle": "选择授权组", "@sage/xtrem-authorization/pages__user__userGroups____title": "授权组", "@sage/xtrem-authorization/pages__user__userInformationBlock____title": "用户信息", "@sage/xtrem-authorization/pages__user__userList____title": "查看用户", "@sage/xtrem-authorization/pages__user__userPhotoBlock____title": "照片", "@sage/xtrem-authorization/pages__user_group_list____title": "用户组", "@sage/xtrem-authorization/pages__user_group_list__groupList____title": "组列表", "@sage/xtrem-authorization/pages__user_group_list__title": "用户组", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title": "创建者为", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__2": "更新者为", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__displayName": "用户名称", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__isActive": "激活的", "@sage/xtrem-authorization/pages__user_group_list__usersBlock____title": "用户组", "@sage/xtrem-authorization/pages__user_list____title": "用户列表", "@sage/xtrem-authorization/pages__user_list__addNewUser____title": "添加用户", "@sage/xtrem-authorization/pages__user_list__fieldBlock____title": "用户行", "@sage/xtrem-authorization/pages__user_list__sendBulkWelcomeMail____title": "发送欢迎电子邮件", "@sage/xtrem-authorization/pages__user_list__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user_list__users____columns__title__createdBy": "创建者为", "@sage/xtrem-authorization/pages__user_list__users____columns__title__displayName": "用户名称", "@sage/xtrem-authorization/pages__user_list__users____columns__title__email": "Email", "@sage/xtrem-authorization/pages__user_list__users____columns__title__groupDisplay": "授权组", "@sage/xtrem-authorization/pages__user_list__users____columns__title__isWelcomeMailSent": "已发送欢迎电子邮件", "@sage/xtrem-authorization/pages__user_list__users____columns__title__updatedBy": "更新者为", "@sage/xtrem-authorization/pages__user_list__users____dropdownActions__title": "编辑", "@sage/xtrem-authorization/pages__user_list_bulk_send__welcome_mail_dialog_content": "您将向所有选定的用户发送一封欢迎电子邮件。", "@sage/xtrem-authorization/pages_role_list_delete_confirmation": "确认删除", "@sage/xtrem-authorization/pages_role_list_delete_message": "您将要删除角色{{role}}。", "@sage/xtrem-authorization/pages_user_list_bulk_send__welcome_mail_button": "发送", "@sage/xtrem-authorization/pages_user_list_bulk_send_success": "已发送欢迎电子邮件", "@sage/xtrem-authorization/pages-confirm-cancel": "取消", "@sage/xtrem-authorization/permission__all__name": "全部", "@sage/xtrem-authorization/permission__allow_access__name": "允许访问", "@sage/xtrem-authorization/permission__create__name": "创建", "@sage/xtrem-authorization/permission__delete__name": "删除", "@sage/xtrem-authorization/permission__extend_access__name": "扩展访问", "@sage/xtrem-authorization/permission__is_support_access_open__name": "是支持访问开放的", "@sage/xtrem-authorization/permission__lookup__name": "查找", "@sage/xtrem-authorization/permission__manage__name": "管理", "@sage/xtrem-authorization/permission__read__name": "读取", "@sage/xtrem-authorization/permission__revoke_access__name": "取消访问", "@sage/xtrem-authorization/permission__update__name": "更新", "@sage/xtrem-authorization/reset-operator-code": "重置", "@sage/xtrem-authorization/role_detail_page_is_not_read_only": "取消", "@sage/xtrem-authorization/role_detail_page_is_read_only": "确定", "@sage/xtrem-authorization/service_options__auth_revoke_full_access_service_option__name": "授权撤销完全访问服务选项", "@sage/xtrem-authorization/service_options__authorization_service_option__name": "授权服务选项", "@sage/xtrem-authorization/shared_common_package_xtrem_inventory": "存货", "@sage/xtrem-authorization/stickers__demo_persona_sticker____title": "演示人物", "@sage/xtrem-authorization/stickers__demo_persona_sticker__section____title": "人物", "@sage/xtrem-authorization/stickers__demo_persona_sticker__selectionPersona____title": "演示人物", "@sage/xtrem-authorization/validate_administrator_only": "只有管理员可以创建或更新此资源。"}