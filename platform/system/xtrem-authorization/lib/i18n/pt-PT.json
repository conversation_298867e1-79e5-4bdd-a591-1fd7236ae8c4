{"@sage/xtrem-authorization/activity__group_role_site__name": "Papel de grupo estab", "@sage/xtrem-authorization/activity__role__name": "Papel", "@sage/xtrem-authorization/activity__site_group__name": "Estab. grupo", "@sage/xtrem-authorization/activity__support_access_history__name": "Histórico de acesso ao suporte", "@sage/xtrem-authorization/activity__user__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/cannot-delete-site-from-site-group": "O estab. atual não pode ser eliminado dos grupos de estabs. ligados.", "@sage/xtrem-authorization/cannot-delete-site-group": "Um grupo de estabs. vinculado não pode ser eliminado.", "@sage/xtrem-authorization/data_types__name_array_data_type__name": "Tipo de dados da matriz de nomes", "@sage/xtrem-authorization/data_types__string_data_type__name": "Tipo de dados String", "@sage/xtrem-authorization/data_types__support_access_history_status_enum__name": "Enum status do histórico de acesso de suporte", "@sage/xtrem-authorization/data_types__support_access_unit_enum__name": "Enum da unidade de acesso de suporte", "@sage/xtrem-authorization/data_types__user_type_enum__name": "Enum do tipo de utilizador", "@sage/xtrem-authorization/delete-confirmation": "<PERSON><PERSON> eliminado", "@sage/xtrem-authorization/delete-dialog-content": "Está prestes a eliminar este grupo.", "@sage/xtrem-authorization/delete-group": "Confirmar elimina<PERSON>", "@sage/xtrem-authorization/duplication-confirmation": "<PERSON>tro duplicado", "@sage/xtrem-authorization/enums__support_access_history_status__closed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/enums__support_access_history_status__open": "Abrir", "@sage/xtrem-authorization/enums__support_access_unit__days": "<PERSON><PERSON>", "@sage/xtrem-authorization/enums__support_access_unit__hours": "<PERSON><PERSON>", "@sage/xtrem-authorization/enums__support_access_unit__minutes": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/enums__user_type__application": "Aplicação", "@sage/xtrem-authorization/enums__user_type__system": "Sistema", "@sage/xtrem-authorization/menu_item__support": "Suporte", "@sage/xtrem-authorization/menu_item__user-data": "Usuários e segurança", "@sage/xtrem-authorization/node-extensions__site_extension__property__groupRoleSites": "Estabs. de papeis de grupo", "@sage/xtrem-authorization/node-extensions__site_extension__property__siteGroups": "Grupos de estabs", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail": "Enviar e-mail de boas-vindas em massa", "@sage/xtrem-authorization/node-extensions__user_extension__bulkMutation__sendBulkWelcomeMail__failed": "Falha no envio do e-mail de boas-vindas em massa.", "@sage/xtrem-authorization/node-extensions__user_extension__property__authorizationGroup": "Grupo de autorização", "@sage/xtrem-authorization/node-extensions__user_extension__property__billingRole": "Papel de faturação", "@sage/xtrem-authorization/node-extensions__user_extension__property__groupDisplay": "Visualização de grupo", "@sage/xtrem-authorization/node-extensions__user_extension__property__objectGrants": "Autorizações de objectos", "@sage/xtrem-authorization/node-extensions__user_extension__query__all": "<PERSON><PERSON>", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__failed": "<PERSON><PERSON> falhou", "@sage/xtrem-authorization/node-extensions__user_extension__query__all__parameter__isActive": "Ativo", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__activity__node_name": "Atividade", "@sage/xtrem-authorization/nodes__activity__property__description": "Descrição", "@sage/xtrem-authorization/nodes__activity__property__name": "Nome", "@sage/xtrem-authorization/nodes__activity__property__package": "Embalagem", "@sage/xtrem-authorization/nodes__activity__property__permissions": "Permissões", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__group_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role__node_name": "Papel de grupo", "@sage/xtrem-authorization/nodes__group_role__property__groupRoleSite": "Papel do grupo de estabs.", "@sage/xtrem-authorization/nodes__group_role__property__role": "Papel", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__group_role_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_role_site__node_name": "Papel do grupo de estabs.", "@sage/xtrem-authorization/nodes__group_role_site__property__createdBy": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/nodes__group_role_site__property__createStamp": "<PERSON><PERSON><PERSON> selo", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRoles": "Papeis de grupo", "@sage/xtrem-authorization/nodes__group_role_site__property__groupRolesDisplay": "Mostrar papéis de grupo", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSites": "Grupos de estabs", "@sage/xtrem-authorization/nodes__group_role_site__property__groupSitesDisplay": "Visualização do grupo de estabs.", "@sage/xtrem-authorization/nodes__group_role_site__property__id": "ID", "@sage/xtrem-authorization/nodes__group_role_site__property__name": "Nome", "@sage/xtrem-authorization/nodes__group_role_site__property__updatedBy": "Atualizado por", "@sage/xtrem-authorization/nodes__group_role_site__property__updateStamp": "<PERSON><PERSON><PERSON><PERSON> selo", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__group_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__group_site__node_name": "Estab. grupo", "@sage/xtrem-authorization/nodes__group_site__property__groupRoleSite": "Papel do grupo de estabs.", "@sage/xtrem-authorization/nodes__group_site__property__siteGroup": "Estab. grupo", "@sage/xtrem-authorization/nodes__restricted_node__node_name": "<PERSON><PERSON>rito", "@sage/xtrem-authorization/nodes__restricted_node__property__userGrants": "Permissões de usuário", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__node_name": "Nó restrito com base nas permissões de usuário", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__accessMap": "Mapa de acesso", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__object": "Objecto", "@sage/xtrem-authorization/nodes__restricted_node_user_grant__property__user": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role__node_name": "Papel", "@sage/xtrem-authorization/nodes__role__property__activities": "Atividades", "@sage/xtrem-authorization/nodes__role__property__createdBy": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/nodes__role__property__description": "Descrição", "@sage/xtrem-authorization/nodes__role__property__id": "ID", "@sage/xtrem-authorization/nodes__role__property__isActive": "Ativo", "@sage/xtrem-authorization/nodes__role__property__isBillingRole": "Papel de faturação", "@sage/xtrem-authorization/nodes__role__property__name": "Nome", "@sage/xtrem-authorization/nodes__role__property__roles": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__property__setupId": "ID da configiguração", "@sage/xtrem-authorization/nodes__role__property__updatedBy": "Atualizado por", "@sage/xtrem-authorization/nodes__role__query__all": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role__query__all__failed": "<PERSON><PERSON> fal<PERSON>.", "@sage/xtrem-authorization/nodes__role__query__all__parameter__isActive": "Ativo", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__failed": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__activityName": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__grantedPermission": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__nodeName": "", "@sage/xtrem-authorization/nodes__role__query__getEffectivePermissions__parameter__role": "", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__role_activity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate": "Atividades papeis criar atual<PERSON>r", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__failed": "Falha na atualização de criação de atividades de função.", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleActivities": "Atividades papeis", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleId": "Papel ID", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleName": "Nome do papel", "@sage/xtrem-authorization/nodes__role_activity__mutation__roleActivitiesCreateUpdate__parameter__roleSysId": "Papel sistema ID", "@sage/xtrem-authorization/nodes__role_activity__node_name": "Actividade do papel", "@sage/xtrem-authorization/nodes__role_activity__property__activity": "Atividade", "@sage/xtrem-authorization/nodes__role_activity__property__getPermissions": "Obter permiss<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__role_activity__property__hasAllPermissions": "Com todas as permissões", "@sage/xtrem-authorization/nodes__role_activity__property__isActive": "Ativo", "@sage/xtrem-authorization/nodes__role_activity__property__permissions": "Permissões", "@sage/xtrem-authorization/nodes__role_activity__property__role": "Papel", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__role_to_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__role_to_role__node_name": "Papel a papel", "@sage/xtrem-authorization/nodes__role_to_role__property__role": "Papel", "@sage/xtrem-authorization/nodes__role_to_role__property__roleOrigin": "Papel de origem", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group__node_name": "Estab. grupo", "@sage/xtrem-authorization/nodes__site_group__property__createdBy": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/nodes__site_group__property__hierarchyChartContent": "Conteúdo do gráfico hierárquico", "@sage/xtrem-authorization/nodes__site_group__property__id": "ID", "@sage/xtrem-authorization/nodes__site_group__property__isActive": "Ativo", "@sage/xtrem-authorization/nodes__site_group__property__isLegalCompany": "Sociedade", "@sage/xtrem-authorization/nodes__site_group__property__name": "Nome", "@sage/xtrem-authorization/nodes__site_group__property__siteGroups": "Grupos de estabs", "@sage/xtrem-authorization/nodes__site_group__property__sites": "Estabelecimentos", "@sage/xtrem-authorization/nodes__site_group__property__updatedBy": "Atualizado por", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__site_group_to_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site__node_name": "Estab. grupo para estab.", "@sage/xtrem-authorization/nodes__site_group_to_site__property__dateAdd": "Data de adição", "@sage/xtrem-authorization/nodes__site_group_to_site__property__isValid": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__site_group_to_site__property__site": "Estabelecimento", "@sage/xtrem-authorization/nodes__site_group_to_site__property__siteGroup": "Estab. grupo", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__site_group_to_site_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__site_group_to_site_group__node_name": "Estab. grupo para estab grupo", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroup": "Estab. grupo", "@sage/xtrem-authorization/nodes__site_group_to_site_group__property__siteGroupOrigin": "Lista de grupos de estabs.", "@sage/xtrem-authorization/nodes__support_access__cannot_allow_support_access_because_there_is_a_open_session": "Já existe uma sessão aberta. Contacte o administrador do seu sistema.", "@sage/xtrem-authorization/nodes__support_access__end_time_must_be_greater_than_start_time": "Introduza um tempo final maior do que o tempo inicial.", "@sage/xtrem-authorization/nodes__support_access__number_must_be_greater_than_0": "Introduza uma quantidade superior a 0.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_extend_the_access": "Não existe uma sessão aberta para estender o acesso. Contacte o administrador do seu sistema.", "@sage/xtrem-authorization/nodes__support_access__there_is_no_open_session_to_revoke_the_access": "Não existe uma sessão aberta para revogar o acesso. Contacte o administrador do seu sistema.", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__support_access_history__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__failed": "Falha na permissão de acesso.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__forTime": "Por tempo", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__isReadOnlyAccess": "Paginas apenas-leitura", "@sage/xtrem-authorization/nodes__support_access_history__mutation__allowAccess__parameter__units": "Unidades", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess": "Estender o acesso", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__failed": "<PERSON>alha no acesso al<PERSON>gado.", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__forTime": "Por tempo", "@sage/xtrem-authorization/nodes__support_access_history__mutation__extendAccess__parameter__units": "Unidades", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/nodes__support_access_history__mutation__revokeAccess__failed": "Falha na revogação do acesso.", "@sage/xtrem-authorization/nodes__support_access_history__node_name": "Histórico de acesso ao suporte", "@sage/xtrem-authorization/nodes__support_access_history__property__endTime": "Hora fim", "@sage/xtrem-authorization/nodes__support_access_history__property__isReadOnlyAccess": "Paginas apenas-leitura", "@sage/xtrem-authorization/nodes__support_access_history__property__startTime": "Hora iní<PERSON>", "@sage/xtrem-authorization/nodes__support_access_history__property__status": "Status", "@sage/xtrem-authorization/nodes__support_access_history__property__timeToClose": "<PERSON><PERSON>", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen": "Verificar o acesso de suporte aberto", "@sage/xtrem-authorization/nodes__support_access_history__query__checkSupportAccessOpen__failed": "Falha na abertura do acesso ao suporte de verificação.", "@sage/xtrem-authorization/nodes__user__cannot_change_own_rights": "Não está autorizado a modificar o seu próprio grupo de autorização.", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__user_billing_role__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_billing_role__node_name": "Papel do utilizador de faturação", "@sage/xtrem-authorization/nodes__user_billing_role__property__role": "Papel", "@sage/xtrem-authorization/nodes__user_billing_role__property__user": "Utilizador", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-authorization/nodes__user_group__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-authorization/nodes__user_group__node_name": "Grupo de usuários", "@sage/xtrem-authorization/nodes__user_group__property__createdBy": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/nodes__user_group__property__createStamp": "<PERSON><PERSON><PERSON> selo", "@sage/xtrem-authorization/nodes__user_group__property__group": "Grupo", "@sage/xtrem-authorization/nodes__user_group__property__isActive": "Ativo", "@sage/xtrem-authorization/nodes__user_group__property__updatedBy": "Atualizado por", "@sage/xtrem-authorization/nodes__user_group__property__updateStamp": "<PERSON><PERSON><PERSON><PERSON> selo", "@sage/xtrem-authorization/nodes__user_group__property__user": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/operation-not-allowed-on-object": "Esta operação não é permitida neste objecto.", "@sage/xtrem-authorization/package__name": "Autorização", "@sage/xtrem-authorization/pages__group_list____title": "Grupos", "@sage/xtrem-authorization/pages__group_list__addNewGroup____title": "Novo grupo", "@sage/xtrem-authorization/pages__group_list__groups____columns__title": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__group_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__2": "Atualizado por", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_list__groups____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__group_list__groups____dropdownActions__title": "Eliminar papel", "@sage/xtrem-authorization/pages__group_list__groupsBlock____title": "Grupos de usuários", "@sage/xtrem-authorization/pages__group_role_site____objectTypePlural": "Grupos de autorização", "@sage/xtrem-authorization/pages__group_role_site____objectTypeSingular": "Grupo de autorização", "@sage/xtrem-authorization/pages__group_role_site____subtitle": "Crie um grupo para conceder aos usuários atividades e direitos de acesso aos dados.", "@sage/xtrem-authorization/pages__group_role_site____title": "Grupo de autorização", "@sage/xtrem-authorization/pages__group_role_site___id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__allRoles____title": "Selecione todas as funções", "@sage/xtrem-authorization/pages__group_role_site__allSiteGroup____title": "Selecione todos os grupos de sites", "@sage/xtrem-authorization/pages__group_role_site__associatedUserSection____title": "Usuários", "@sage/xtrem-authorization/pages__group_role_site__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__group_role_site__customSave____title": "Guardar", "@sage/xtrem-authorization/pages__group_role_site__customSaveAction____title": "Guardar", "@sage/xtrem-authorization/pages__group_role_site__editGroupRoleSite____title": "Editar grupo de autorização", "@sage/xtrem-authorization/pages__group_role_site__groupInfoBlock____title": "Informação do grupo", "@sage/xtrem-authorization/pages__group_role_site__groupList____title": "Ver grupos de autorização", "@sage/xtrem-authorization/pages__group_role_site__groupRoleBlock____title": "Funções do usuário", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__description": "Descrição", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____columns__title__role__name": "Nome", "@sage/xtrem-authorization/pages__group_role_site__groupRoles____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role": "Nome", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__description": "Descrição", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____columns__title__role__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____dropdownActions__title": "Atividades", "@sage/xtrem-authorization/pages__group_role_site__groupRolesActivities____title": "Funções e atividades", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__group_role_site__groupRolesMultiReference____lookupDialogTitle": "Selecione todas as funções", "@sage/xtrem-authorization/pages__group_role_site__groupSiteBlock____title": "Grupos de estabs.", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSites____columns__title__siteGroup__name": "Nome", "@sage/xtrem-authorization/pages__group_role_site__groupSites____title": "Estabelecimentos", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__group_role_site__groupSitesMultiReference____lookupDialogTitle": "Selecione todos os grupos de sites", "@sage/xtrem-authorization/pages__group_role_site__id____title": "ID", "@sage/xtrem-authorization/pages__group_role_site__infoSection____title": "G<PERSON>", "@sage/xtrem-authorization/pages__group_role_site__name____title": "Nome", "@sage/xtrem-authorization/pages__group_role_site__rolesActivitiesSection____title": "Funções e atividades", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__2": "Atualizado por", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__displayName": "Nome de utilizador", "@sage/xtrem-authorization/pages__group_role_site__users____columns__title__isActive": "Ativo", "@sage/xtrem-authorization/pages__group_role_site__users____title": "Usuários", "@sage/xtrem-authorization/pages__group_role_site_list____title": "Lista de grupos de autorização", "@sage/xtrem-authorization/pages__group_role_site_list__addNewGroup____title": "Acrescentar grupo", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__2": "Atualizado por", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__group_role_site_list__groups____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__group_role_site_list__groups____dropdownActions__title__2": "Eliminar", "@sage/xtrem-authorization/pages__group_role_site_list__groups____title": "Grupo de autorização", "@sage/xtrem-authorization/pages__group_role_site_list__groupsBlock____title": "Grupos de usuários", "@sage/xtrem-authorization/pages__group_role_site_new_group_title": "Novo grupo de autorização", "@sage/xtrem-authorization/pages__new_group__group_created": "Grupo de autorização {{newGroupId}} criado", "@sage/xtrem-authorization/pages__new_group_panel____subtitle": "Crie um grupo para conceder aos usuários atividades e direitos de acesso aos dados.", "@sage/xtrem-authorization/pages__new_group_panel____title": "Novo grupo", "@sage/xtrem-authorization/pages__new_group_panel__allRoles____title": "<PERSON><PERSON> as funções", "@sage/xtrem-authorization/pages__new_group_panel__allSiteAndSiteGroup____title": "Todos os estabs./grupos de sites", "@sage/xtrem-authorization/pages__new_group_panel__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__new_group_panel__groupInfoBlock____title": "Informação do grupo", "@sage/xtrem-authorization/pages__new_group_panel__id____title": "ID", "@sage/xtrem-authorization/pages__new_group_panel__name____title": "Nome", "@sage/xtrem-authorization/pages__new_group_panel__saveNewGroup____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__new_group_panel__selectionSection____title": "Associar papeis e estabs.grupos de estabs.", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectRole____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title___id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__id": "ID", "@sage/xtrem-authorization/pages__new_group_panel__selectSiteAndSiteGroup____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__operator_user_panel____title": "Repor o código do operador", "@sage/xtrem-authorization/pages__operator_user_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__operator_user_panel__confirm____title": "Confirmar", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeBlock____title": "Bloco de códigos do operador", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeInput____title": "Novo código de operador", "@sage/xtrem-authorization/pages__operator_user_panel__operatorCodeSection____title": "Secção do código do operador", "@sage/xtrem-authorization/pages__role_detail____subtitle": "Papel", "@sage/xtrem-authorization/pages__role_detail____title": "Papel", "@sage/xtrem-authorization/pages__role_detail__activitySection____title": "Atividades", "@sage/xtrem-authorization/pages__role_detail__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_detail__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__role_detail__not_updated": "A atualização das permissões falhou.", "@sage/xtrem-authorization/pages__role_detail__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_detail__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_detail_updated": "Permissões atualizadas.", "@sage/xtrem-authorization/pages__role_list____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__addNewRole____title": "Acrescentar linha", "@sage/xtrem-authorization/pages__role_list__duplicate": "Dup<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__id____title": "ID", "@sage/xtrem-authorization/pages__role_list__name____title": "Nome", "@sage/xtrem-authorization/pages__role_list__roles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__createdByUserAndStamp": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__isBillingRole": "Papel de faturação", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__role_list__roles____columns__title__updateByUserAndStamp": "Atualizado por", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__2": "Eliminar", "@sage/xtrem-authorization/pages__role_list__roles____dropdownActions__title__3": "Dup<PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_list__rolesBlock____title": "<PERSON><PERSON> p<PERSON>", "@sage/xtrem-authorization/pages__role_list__roleSection____title": "Papel", "@sage/xtrem-authorization/pages__role_setup": "Função criada", "@sage/xtrem-authorization/pages__role_setup____title": "Novo papel", "@sage/xtrem-authorization/pages__role_setup__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__role_setup__duplicated_id": "O ID introduzido já existe. Seleccione outro ID.", "@sage/xtrem-authorization/pages__role_setup__id____title": "ID", "@sage/xtrem-authorization/pages__role_setup__name____title": "Nome", "@sage/xtrem-authorization/pages__role_setup__next____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__role_setup__packageBlock1____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock10____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock11____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock12____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock13____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock14____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock15____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock16____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock17____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock18____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock19____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock2____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock20____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock3____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock4____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock5____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock6____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock7____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock8____title": "", "@sage/xtrem-authorization/pages__role_setup__packageBlock9____title": "", "@sage/xtrem-authorization/pages__role_setup__packageSelect____title": "Embalagens", "@sage/xtrem-authorization/pages__role_setup__roleBlock____title": "Informação do papel", "@sage/xtrem-authorization/pages__role_setup_failed": "A criação da função falhou.", "@sage/xtrem-authorization/pages__site_group____subtitle": "Estab. grupo", "@sage/xtrem-authorization/pages__site_group____title": "Estab. grupo", "@sage/xtrem-authorization/pages__site_group___id____title": "ID", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupBlock____title": "Grupo de autorização", "@sage/xtrem-authorization/pages__site_group__associatedAuthorizationGroupSection____title": "Grupos de usuários", "@sage/xtrem-authorization/pages__site_group__chartBlock____title": "Organização", "@sage/xtrem-authorization/pages__site_group__confirm____title": "Guardar", "@sage/xtrem-authorization/pages__site_group__customSave____title": "Guardar", "@sage/xtrem-authorization/pages__site_group__customSaveAction____title": "Guardar", "@sage/xtrem-authorization/pages__site_group__editSiteGroup____title": "Editar grupo de sites", "@sage/xtrem-authorization/pages__site_group__generalBlock____title": "Estab. grupo", "@sage/xtrem-authorization/pages__site_group__generalSection____title": "G<PERSON>", "@sage/xtrem-authorization/pages__site_group__groups____columns__title": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__site_group__groups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__2": "Atualizado por", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__groups____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__site_group__groups____title": "Grupo de autorização", "@sage/xtrem-authorization/pages__site_group__hierarchyChartContent____title": "Organização", "@sage/xtrem-authorization/pages__site_group__id____title": "ID", "@sage/xtrem-authorization/pages__site_group__isActive____title": "Ativo", "@sage/xtrem-authorization/pages__site_group__isLegalCompany____title": "Sociedade", "@sage/xtrem-authorization/pages__site_group__name____title": "Nome", "@sage/xtrem-authorization/pages__site_group__siteGroupList____title": "Lista de grupos de sites", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup___id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroups____columns__title__siteGroup__name": "Nome", "@sage/xtrem-authorization/pages__site_group__siteGroupsBlock____title": "Grupos de estabs", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__site_group__siteGroupsMultiReference____lookupDialogTitle": "Selecione todos os grupos de sites", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site___id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__id": "ID", "@sage/xtrem-authorization/pages__site_group__sites____columns__title__site__name": "Nome", "@sage/xtrem-authorization/pages__site_group__sitesBlock____title": "Estabelecimentos", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__site_group__sitesMultiReference____lookupDialogTitle": "Selecionar sites", "@sage/xtrem-authorization/pages__site_group__toggleChart____helperText": "Permitir que os usuários alternem a exibição da grade ou do gráfico", "@sage/xtrem-authorization/pages__site_group__toggleChart____title": "Opções de exibição", "@sage/xtrem-authorization/pages__site_group__toggleChartBlock____title": "Alternar gráfico/grade", "@sage/xtrem-authorization/pages__site_group_chart": "Gráfico", "@sage/xtrem-authorization/pages__site_group_grid": "Quadro", "@sage/xtrem-authorization/pages__site_group_list____title": "Grupos de estabs.", "@sage/xtrem-authorization/pages__site_group_list__addNewSiteGroup____title": "Novo grupo de sites", "@sage/xtrem-authorization/pages__site_group_list__createSite____title": "<PERSON>riar estab.", "@sage/xtrem-authorization/pages__site_group_list__createSiteGroup____title": "Criar grupo de estabs.", "@sage/xtrem-authorization/pages__site_group_list__fieldBlock____title": "Estab. grupo", "@sage/xtrem-authorization/pages__site_group_list__groupList____title": "Lista de grupos de sites", "@sage/xtrem-authorization/pages__site_group_list__section____title": "Grupos de estabs.", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title___id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__id__2": "ID", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name": "Nome", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__columns__title__name__2": "Nome", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__site_group_list__siteGroups____levels__dropdownActions__title__2": "Eliminar", "@sage/xtrem-authorization/pages__site_group_new_title": "Novo grupo de sites", "@sage/xtrem-authorization/pages__support_access_history____title": "Acesso ao suporte", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title___id": "ID", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__endTime": "Hora de fim", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__isReadOnlyAccess": "Paginas apenas-leitura", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__startTime": "Hora iní<PERSON>", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____columns__title__status": "Status", "@sage/xtrem-authorization/pages__support_access_history__accessHistory____title": "Histórico de acesso ao suporte", "@sage/xtrem-authorization/pages__support_access_history__allow_access_button_text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__support_access_history__description____content": "Especificar a duração do acesso do usuário.", "@sage/xtrem-authorization/pages__support_access_history__dummy____content": "", "@sage/xtrem-authorization/pages__support_access_history__extend_access_button_text": "Estender o acesso", "@sage/xtrem-authorization/pages__support_access_history__forTime____title": "Número", "@sage/xtrem-authorization/pages__support_access_history__infoSection____title": "G<PERSON>", "@sage/xtrem-authorization/pages__support_access_history__isReadOnlyAccess____title": "Paginas apenas-leitura", "@sage/xtrem-authorization/pages__support_access_history__isSupportAccessOpen____title": "Paginas apenas-leitura", "@sage/xtrem-authorization/pages__support_access_history__please_fill_the_number_and_unit_field": "Introduzir um número e uma unidade.", "@sage/xtrem-authorization/pages__support_access_history__revoke_access_button_text": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__support_access_history__supportAccessBlock____title": "Permitir o acesso ao suporte Sage", "@sage/xtrem-authorization/pages__support_access_history__supportAccessHistoryBlock____title": "Histórico de acesso ao suporte", "@sage/xtrem-authorization/pages__support_access_history__unit____title": "Unidades", "@sage/xtrem-authorization/pages__user____navigationPanel__bulkActions__title": "Enviar e-mail de boas-vindas", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__image__title": "Imagem", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line2__title": "E-mail", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__line3__title": "Usuário de API", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__title__title": "Nome", "@sage/xtrem-authorization/pages__user____navigationPanel__listItem__titleRight__title": "Sobrenome", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__2": "Interativo", "@sage/xtrem-authorization/pages__user____navigationPanel__optionsMenu__title__3": "Aplicação de terceiros", "@sage/xtrem-authorization/pages__user____objectTypePlural": "Usuários", "@sage/xtrem-authorization/pages__user____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____subtitle": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user____title": "Usuários", "@sage/xtrem-authorization/pages__user___id____title": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdBy": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__createdByUserAndStamp": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updateByUserAndStamp": "Atualizado por", "@sage/xtrem-authorization/pages__user__associatedRoles____columns__title__updatedBy": "Atualizado por", "@sage/xtrem-authorization/pages__user__associatedRoles____title": "Funções e atividades", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdBy": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__createdByUserAndStamp": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updateByUserAndStamp": "Atualizado por", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____columns__title__updatedBy": "Atualizado por", "@sage/xtrem-authorization/pages__user__associatedSiteGroups____title": "Grupos de estabs", "@sage/xtrem-authorization/pages__user__associatedSiteGroupsSection____title": "Grupos de estabs.", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__2": "Nome", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__columns__group__id__title__3": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group___id": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupRolesDisplay": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__groupSitesDisplay": "Estab. grupo", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__id": "ID", "@sage/xtrem-authorization/pages__user__authorizationGroup____columns__title__group__name": "Nome", "@sage/xtrem-authorization/pages__user__authorizationGroup____title": "Grupos de autorização", "@sage/xtrem-authorization/pages__user__editUser____title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__user__email____title": "E-mail", "@sage/xtrem-authorization/pages__user__firstName____title": "Nome", "@sage/xtrem-authorization/pages__user__generalSection____title": "G<PERSON>", "@sage/xtrem-authorization/pages__user__idBlock____title": "ID", "@sage/xtrem-authorization/pages__user__importExportDateFormat____helperText": "O valor predefinido será ", "@sage/xtrem-authorization/pages__user__importExportDateFormat____title": "Importação/exportação formato de data", "@sage/xtrem-authorization/pages__user__importExportDelimiter____helperText": "O valor predefinido será ", "@sage/xtrem-authorization/pages__user__importExportDelimiter____title": "Delimitador de importação/exportação", "@sage/xtrem-authorization/pages__user__isActive____title": "Ativo", "@sage/xtrem-authorization/pages__user__isAdministrator____title": "Administrador", "@sage/xtrem-authorization/pages__user__isApiUser____title": "Usuário de API", "@sage/xtrem-authorization/pages__user__isDemoPersona____title": "Persona de demonstração", "@sage/xtrem-authorization/pages__user__isExternal____title": "Externo", "@sage/xtrem-authorization/pages__user__isOperatorUser____title": "Pin de autenticação", "@sage/xtrem-authorization/pages__user__isWelcomeMailSent____title": "Enviar e-mail de boas-vindas", "@sage/xtrem-authorization/pages__user__lastName____title": "Sobrenome", "@sage/xtrem-authorization/pages__user__list_bulk_send_welcome_mail": "Enviar email", "@sage/xtrem-authorization/pages__user__operatorCode____title": "Código PIN", "@sage/xtrem-authorization/pages__user__preferencesBlock____title": "Preferências", "@sage/xtrem-authorization/pages__user__preferencesSection____title": "Preferências", "@sage/xtrem-authorization/pages__user__resetOperatorCodePage____title": "Código PIN", "@sage/xtrem-authorization/pages__user__role____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__role____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__user__role____lookupDialogTitle": "Selecionar o papel de facturação", "@sage/xtrem-authorization/pages__user__role____title": "Papel de faturação", "@sage/xtrem-authorization/pages__user__rolesActivitiesSection____title": "Funções e atividades", "@sage/xtrem-authorization/pages__user__save____title": "Guardar", "@sage/xtrem-authorization/pages__user__send_welcome_mail": "Enviar email", "@sage/xtrem-authorization/pages__user__send_welcome_mail_button": "Enviar", "@sage/xtrem-authorization/pages__user__send_welcome_mail_dialog_content": "<PERSON><PERSON><PERSON> prestes a enviar um e-mail de boas-vindas para o usuário.", "@sage/xtrem-authorization/pages__user__userAuthorizationInformationBlock____title": "Grupo de autorização", "@sage/xtrem-authorization/pages__user__userGroups____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__id": "ID", "@sage/xtrem-authorization/pages__user__userGroups____columns__title__name": "Nome", "@sage/xtrem-authorization/pages__user__userGroups____lookupDialogTitle": "Seleccionar grupo de autorização", "@sage/xtrem-authorization/pages__user__userGroups____title": "Grupo de autorização", "@sage/xtrem-authorization/pages__user__userInformationBlock____title": "Informação usuário", "@sage/xtrem-authorization/pages__user__userList____title": "Ver usuários", "@sage/xtrem-authorization/pages__user__userPhotoBlock____title": "Foto", "@sage/xtrem-authorization/pages__user_group_list____title": "Grupos de usuários", "@sage/xtrem-authorization/pages__user_group_list__groupList____title": "Lista de grupo", "@sage/xtrem-authorization/pages__user_group_list__title": "Grupos de usuários", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__2": "Atualizado por", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__displayName": "Nome de usuário", "@sage/xtrem-authorization/pages__user_group_list__users____columns__title__isActive": "Activo", "@sage/xtrem-authorization/pages__user_group_list__usersBlock____title": "Grupos de usuários", "@sage/xtrem-authorization/pages__user_list____title": "Lista de usuários", "@sage/xtrem-authorization/pages__user_list__addNewUser____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/pages__user_list__fieldBlock____title": "<PERSON><PERSON>uá<PERSON>", "@sage/xtrem-authorization/pages__user_list__sendBulkWelcomeMail____title": "Enviar e-mail de boas-vindas", "@sage/xtrem-authorization/pages__user_list__users____columns__title___id": "ID", "@sage/xtrem-authorization/pages__user_list__users____columns__title__createdBy": "<PERSON><PERSON><PERSON> por", "@sage/xtrem-authorization/pages__user_list__users____columns__title__displayName": "Nome de utilizador", "@sage/xtrem-authorization/pages__user_list__users____columns__title__email": "E-mail", "@sage/xtrem-authorization/pages__user_list__users____columns__title__groupDisplay": "Grupo de autorização", "@sage/xtrem-authorization/pages__user_list__users____columns__title__isWelcomeMailSent": "E-mail de boas-vindas enviado", "@sage/xtrem-authorization/pages__user_list__users____columns__title__updatedBy": "Atualizado por", "@sage/xtrem-authorization/pages__user_list__users____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-authorization/pages__user_list_bulk_send__welcome_mail_dialog_content": "<PERSON><PERSON><PERSON> prestes a enviar um e-mail de boas-vindas para o usuário.", "@sage/xtrem-authorization/pages_role_list_delete_confirmation": "Confirmar elimina<PERSON>", "@sage/xtrem-authorization/pages_role_list_delete_message": "<PERSON><PERSON><PERSON> prestes a eliminar o papel {{role}}.", "@sage/xtrem-authorization/pages_user_list_bulk_send__welcome_mail_button": "Enviar", "@sage/xtrem-authorization/pages_user_list_bulk_send_success": "E-mails de boas-vindas enviados", "@sage/xtrem-authorization/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__all__name": "<PERSON><PERSON>", "@sage/xtrem-authorization/permission__allow_access__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__delete__name": "Eliminar", "@sage/xtrem-authorization/permission__extend_access__name": "Estender o acesso", "@sage/xtrem-authorization/permission__is_support_access_open__name": "Verificar o acesso de suporte aberto", "@sage/xtrem-authorization/permission__lookup__name": "Verificação", "@sage/xtrem-authorization/permission__manage__name": "Gestão", "@sage/xtrem-authorization/permission__read__name": "<PERSON>r", "@sage/xtrem-authorization/permission__revoke_access__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/permission__update__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/reset-operator-code": "Reset", "@sage/xtrem-authorization/role_detail_page_is_not_read_only": "<PERSON><PERSON><PERSON>", "@sage/xtrem-authorization/role_detail_page_is_read_only": "OK", "@sage/xtrem-authorization/service_options__auth_revoke_full_access_service_option__name": "Revogar autorização opção de serviço de acesso total", "@sage/xtrem-authorization/service_options__authorization_service_option__name": "Opção de serviço de autorização", "@sage/xtrem-authorization/shared_common_package_xtrem_inventory": "Stocks", "@sage/xtrem-authorization/stickers__demo_persona_sticker____title": "Persona de demonstração", "@sage/xtrem-authorization/stickers__demo_persona_sticker__section____title": "<PERSON>a", "@sage/xtrem-authorization/stickers__demo_persona_sticker__selectionPersona____title": "Persona de demonstração", "@sage/xtrem-authorization/validate_administrator_only": "Apenas os administradores podem criar ou atualizar este recurso."}