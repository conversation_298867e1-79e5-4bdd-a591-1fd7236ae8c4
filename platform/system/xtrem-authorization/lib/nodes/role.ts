import { asyncArray, Collection, Context, datetime, decorators, LocalizeLocale, Node } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { validateIsAdminForUpdate } from '../functions';
import * as xtremAuthorization from '../index';

@decorators.node<Role>({
    package: 'xtrem-authorization',
    storage: 'sql',
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isSetupNode: true,
    isCached: true,
    canDuplicate: true,
    indexes: [{ orderBy: { id: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    async saveBegin() {
        await validateIsAdminForUpdate(this.$.context);
    },
    async saveEnd() {
        await xtremAuthorization.services.SysAccessRightsManager.invalidateUserAccessCache(this.$.context);
    },
})
export class Role extends Node {
    @decorators.booleanProperty<Role, 'isActive'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: true,
    })
    readonly isActive: Promise<boolean>;

    @decorators.stringProperty<Role, 'name'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly name: Promise<string>;

    @decorators.stringProperty<Role, 'description'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.description,
        defaultValue: '',
    })
    readonly description: Promise<string>;

    @decorators.stringProperty<Role, 'id'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.id,
        duplicateRequiresPrompt: true,
        duplicatedValue: '',
    })
    readonly id: Promise<string>;

    @decorators.booleanProperty<Role, 'isBillingRole'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: false,
    })
    readonly isBillingRole: Promise<boolean>;

    @decorators.collectionProperty<Role, 'roles'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'roleOrigin',
        isVital: true,
        node: () => xtremAuthorization.nodes.RoleToRole,
    })
    readonly roles: Collection<xtremAuthorization.nodes.RoleToRole>;

    @decorators.collectionProperty<Role, 'activities'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'role',
        isVital: true,
        node: () => xtremAuthorization.nodes.RoleActivity,
    })
    readonly activities: Collection<xtremAuthorization.nodes.RoleActivity>;

    /**
     * createdBy user displayName
     */
    @decorators.stringProperty<Role, 'createdBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.createdBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly createdBy: Promise<string>;

    /**
     * updatedBy user displayName
     */
    @decorators.stringProperty<Role, 'updatedBy'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremSystem.dataTypes.name,
        async computeValue() {
            return ((await this.$.updatedBy) as xtremSystem.nodes.User).displayName;
        },
    })
    readonly updatedBy: Promise<string>;

    /**
     *  get All Entities of the current instance
     *  TODO : datetime ?
     * @param context
     * @returns
     */
    @decorators.query<typeof Role, 'all'>({
        isPublished: true,
        parameters: [{ name: 'isActive', type: 'boolean', isMandatory: true }],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    _id: 'string',
                    name: 'string',
                    id: 'string',
                    isBillingRole: 'boolean',
                    createdBy: 'string',
                    createStamp: 'string',
                    updatedBy: 'string',
                    updateStamp: 'string',
                    isVendor: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static all(context: Context, isActive: boolean): Promise<xtremAuthorization.sharedFunctions.interfaces.AllRoles[]> {
        return context
            .query(Role, { filter: { isActive } })
            .map(async (element: Role) => {
                return {
                    _id: String(element._id),
                    name: await element.name,
                    id: await element.id,
                    isBillingRole: await element.isBillingRole,
                    createdBy: await ((await element.$.createdBy) as xtremSystem.nodes.User).displayName,
                    createStamp: ((await element.$.createStamp) as datetime).format(
                        context.currentLocale as LocalizeLocale,
                        'DD-MM-YYYY HH:mm:ss',
                    ),
                    updatedBy: await ((await element.$.updatedBy) as xtremSystem.nodes.User).displayName,
                    updateStamp: ((await element.$.updateStamp) as datetime).format(
                        context.currentLocale as LocalizeLocale,
                        'DD-MM-YYYY HH:mm:ss',
                    ),
                    isVendor: (await element._vendor) ? '1' : '0',
                };
            })
            .toArray();
    }

    /**
     *  Get effective permissions for a given role.
     *  Optionally filter by node name and activity name.
     *  @param context - The request context.
     *  @param role - The role to get permissions for.
     *  @param nodeName - (Optional) The node name to filter permissions.
     *  @param activityName - (Optional) The activity name to filter permissions.
     *  @returns Array of effective permissions for the role.
     */
    @decorators.query<typeof Role, 'getEffectivePermissions'>({
        isPublished: true,
        parameters: [
            { name: 'role', type: 'reference', isMandatory: true, node: () => xtremAuthorization.nodes.Role },
            { name: 'nodeName', type: 'string', isMandatory: false },
            { name: 'activityName', type: 'string', isMandatory: false },
            { name: 'grantedPermission', type: 'string', isMandatory: false },
        ],
        return: {
            type: 'array',
            item: {
                type: 'object',
                properties: {
                    roleName: 'string',
                    grantRoleName: 'string',
                    activityName: 'string',
                    permission: 'string',
                    grantsTo: 'string',
                    grantedPermission: 'string',
                },
            },
            isMandatory: true,
        },
    })
    static async getEffectivePermissions(
        context: Context,
        role: Role,
        nodeName?: string,
        activityName?: string,
        grantedPermission?: string,
    ): Promise<
        {
            roleName: string;
            grantRoleName: string;
            activityName: string;
            permission: string;
            grantsTo: string;
            grantedPermission: string;
        }[]
    > {
        const result: {
            roleName: string;
            grantRoleName: string;
            activityName: string;
            permission: string;
            grantsTo: string;
            grantedPermission: string;
        }[] = [];

        const roleName = await role.name;
        const activities = context.getActivities();

        const processedRoles: number[] = [];
        const walk = async (r: Role): Promise<void> => {
            if (processedRoles.includes(r._id)) return;

            // If the role is inactive nothing to do
            if (!(await r.isActive)) {
                processedRoles.push(r._id);
                return;
            }

            await r.activities.forEach(async roleActivity => {
                // If the role activity is inactive nothing to do
                if (!(await roleActivity.isActive)) return;
                const activity = activities[await (await roleActivity.activity).name];

                if (!activity) {
                    return;
                }
                const { flattenedPermissions } = activity;

                // get the permissions allocated to the role activity
                const roleActivityPermissions = [...((await roleActivity.getPermissions) || [])];

                // Add all permissions granted by the each role activity
                await asyncArray(roleActivityPermissions ?? []).forEach(async permission => {
                    const operationNodeDict = flattenedPermissions[permission] || {};

                    // add the permissions per operation and list of nodes
                    await asyncArray(Object.keys(operationNodeDict)).forEach(async operation => {
                        await asyncArray(operationNodeDict[operation]).forEach(async node => {
                            result.push({
                                roleName,
                                grantRoleName: await r.name,
                                activityName: await (await roleActivity.activity).name,
                                permission,
                                grantsTo: node,
                                grantedPermission: operation,
                            });
                        });
                    });
                });
            });

            processedRoles.push(r._id);
            // Recursively add sub roles
            await r.roles.forEach(async subRole => walk(await subRole.role));
        };

        await walk(role);

        return result.filter(
            r =>
                (nodeName ? r.grantsTo === nodeName : true) &&
                (activityName ? r.activityName === activityName : true) &&
                (grantedPermission ? r.grantedPermission === grantedPermission : true),
        );
    }
}
