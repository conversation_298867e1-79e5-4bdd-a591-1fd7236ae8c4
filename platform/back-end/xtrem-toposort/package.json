{"name": "@sage/xtrem-toposort", "version": "59.0.8", "description": "Tools for topological sorting", "main": "build/index.js", "typings": "build/package-definition.d.ts", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "author": "Sage", "license": "UNLICENSED", "bugs": {"url": "https://github.com/Sage-ERP-X3/xtrem-platform/issues"}, "homepage": "https://github.com/Sage-ERP-X3/xtrem-platform#readme", "dependencies": {"lodash": "^4.17.21"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "cross-env": "^10.0.0", "dts-generator": "^3.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z 'build/**/*.js'", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "dts-bundle": "xtrem-dts-bundle", "dts-gen": "dts-generator --name @sage/xtrem-core --project . --out build/xtrem-core.d.ts --main @sage/xtrem-core/index > dts-generator.log", "lint": "eslint lib test", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "posttest:ci": "rm -rf build/lib/pages", "test": "mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:ci": "cross-env JUNIT_REPORT_PATH=junit-report-toposort.xml JUNIT_REPORT_NAME='xtrem-toposort' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha  --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter", "test:ci:allDatabases": "pnpm test:ci", "test:coverage": "c8 --reporter=html mocha --recursive \"test/**/*@(-|.)test.ts\" --exit", "test:debug": "mocha  --inspect-brk --recursive \"test/**/*@(-|.)test.ts\" --exit"}}