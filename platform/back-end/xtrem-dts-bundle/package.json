{"name": "@sage/xtrem-dts-bundle", "description": "helper for migration to async/await", "version": "59.0.8", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "types": "build/index.d.ts", "bin": {"xtrem-dts-bundle": "./bin/xtrem-dts-bundle"}, "dependencies": {"glob": "^11.0.0", "typescript": "~5.9.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@stylistic/eslint-plugin": "^5.0.0", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0"}, "scripts": {"build": "pnpm clean && tsc -b -v .", "build:binary": "pnpm clean && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint lib", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "test": "echo \"Error: no test specified for xtrem-dts-bundle\" && exit 0", "test:ci": "echo \"Error: no test specified for xtrem-dts-bundle\" && exit 0", "xtrem-dts-bundle": "./bin/xtrem-dts-bundle"}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}