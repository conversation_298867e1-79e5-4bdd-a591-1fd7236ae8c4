{"name": "@sage/xtrem~platform", "description": "Umbrella project for Xtrem development", "version": "59.0.8", "license": "UNLICENSED", "scripts": {"build": "cd .. && pnpm build:platform", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "lint": "cd .. && pnpm lint:platform", "test": "cd .. && pnpm test:platform"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@stylistic/eslint-plugin": "^5.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0"}}