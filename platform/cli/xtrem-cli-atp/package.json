{"name": "@sage/xtrem-cli-atp", "description": "Xtrem CLI ATP", "version": "59.0.8", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-cli"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build", "resources"], "dependencies": {"@axe-core/webdriverio": "^4.10.1", "@cucumber/cucumber": "^8.11.1", "@cucumber/gherkin": "^26.2.0", "@cucumber/messages": "^22.0.0", "@cucumber/tag-expressions": "^6.2.0", "@sage/cucumber-steps-parser": "^3.1.0", "@sage/xtrem-cli-dev": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-service": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@wdio/allure-reporter": "8.12.1", "@wdio/cli": "8.12.1", "@wdio/config": "8.12.1", "@wdio/cucumber-framework": "8.12.1", "@wdio/devtools-service": "8.12.1", "@wdio/dot-reporter": "8.12.1", "@wdio/globals": "8.12.1", "@wdio/junit-reporter": "8.12.1", "@wdio/local-runner": "8.12.1", "@wdio/shared-store-service": "8.12.1", "@wdio/spec-reporter": "8.12.1", "@wdio/types": "8.10.4", "@wdio/utils": "8.12.1", "allure-commandline": "^2.29.0", "axios": "^1.11.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "chalk": "^4.0.0", "diff": "^8.0.0", "dotenv": "^17.0.0", "eslint": "^9.30.1", "glob": "^11.0.0", "image-size": "^2.0.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^5.0.0", "istanbul-reports": "^3.1.5", "lighthouse": "12.8.1", "lodash": "^4.17.21", "looks-same": "^10.0.0", "moment": "^2.29.4", "pdf-parse-new": "^1.3.7", "typescript": "~5.9.0", "uuid": "^11.0.0", "wdio-cucumberjs-json-reporter": "5.1.7", "webdriverio": "8.12.1", "xml2js": "^0.6.0", "yargs": "^18.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-async-helper": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/eslint": "^9.6.1", "@types/gm": "^1.25.0", "@types/istanbul-lib-coverage": "^2.0.3", "@types/istanbul-lib-instrument": "^1.7.4", "@types/istanbul-lib-report": "^3.0.0", "@types/istanbul-lib-source-maps": "^4.0.1", "@types/istanbul-reports": "^3.0.1", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@types/uuid": "^10.0.0", "@types/xml2js": "^0.4.11", "@types/yargs": "^17.0.24", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle && pnpm extract-step-definitions", "build:binary": "pnpm clean && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "extract-step-definitions": "ts-node --transpile-only extract-step-definitions.ts", "lint": "eslint lib", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\""}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}