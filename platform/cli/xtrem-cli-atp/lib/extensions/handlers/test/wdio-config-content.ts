import { ExecutionMode, printInfo, printSuccess, printWarning, safeRmSync } from '@sage/xtrem-cli-lib';
import { Test, Uuid } from '@sage/xtrem-core';
import { addAttachment } from '@wdio/allure-reporter';
import { WebDriverLogTypes } from '@wdio/types/build/Options';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as glob from 'glob';
import * as os from 'os';
import * as path from 'path';
import { targetBaseUrl } from '../../../utils/config';
import { updateAllJUnitFilesInFolder } from './analytics-junit-parser';
import { testGlobals } from './step-definitions/component/test-globals';
import { atpEnv, takeScreenshot } from './step-definitions/step-definitions-utils';

const filePath = path.resolve(__dirname, '../../../../../parameters-atp');

if (fs.existsSync(filePath)) {
    const parameters = dotenv.config({ override: true, path: filePath });
    if (parameters.error) throw parameters.error;
}

const tagExpression = process.env.CUCUMBER_TAGS || undefined;

const getCfgBaseUrl = () => {
    const url = targetBaseUrl();
    return url || 'http://127.0.0.1:8240';
};

export const cfgBaseUrl = getCfgBaseUrl();
process.env.stopTestOnError = process.env.stopTestOnError || 'false';
const stopTestOnError = process.env.stopTestOnError !== 'false';
//
process.env.WDIO_LOG_LEVEL = process.env.WDIO_LOG_LEVEL || 'warn';

process.env.testUser = process.env.testUser || 'Test, Unit';
const retries = Number(process.env.retries) ? Number(process.env.retries) : 0;

if (!atpEnv.isValidCucumberTimeout) {
    // eslint-disable-next-line no-console
    console.log('\x1b[31m%s\x1b[0m', 'timeoutCucumber parameter should be greater than other timeout parameter values');
    process.exit(1);
}

let newDir: string;
newDir = Test.getDirectory(process.cwd(), 'package.json');
if (newDir === '') {
    newDir = Test.getDirectory(path.join(process.cwd(), '../'), 'package.json');
}
if (newDir === '') {
    newDir = Test.getDirectory(path.join(process.cwd(), '../../'), 'package.json');
}

const cfgOutputDir = path.resolve(`${newDir}/test-report`);

const cfgReportDir = path.resolve(__dirname, 'report');
const stepDefinitionFiles = [
    // eslint-disable-next-line no-path-concat
    ...glob.sync(`${__dirname + path.sep}**${path.sep}*.step.js`),
    // eslint-disable-next-line no-path-concat
    ...glob.sync(`${__dirname + path.sep}**${path.sep}*.tags.js`),
];

const getCurrentLogFile = () => {
    const logFiles = fs
        .readdirSync(cfgOutputDir)
        .sort()
        .filter(f => f.match(/wdio-0-[0-9]+.log/));
    return path.join(cfgOutputDir, logFiles[logFiles.length - 1]);
};

if (!fs.existsSync(cfgOutputDir)) {
    fs.mkdirSync(cfgOutputDir);
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface ConfigWebdriverIO extends WebdriverIO.Config {
    capabilities: WebDriver.DesiredCapabilities[];
    cucumberOpts: any; // Workaround until https://github.com/webdriverio/webdriverio/pull/4318 is released.
    connectionRetryTimeout: number;
}

// To keep : used by cache mechanism in integration-test.ts
const cucumberJsonTestResults = path.resolve(cfgOutputDir, 'cucumber');
if (!fs.existsSync(cucumberJsonTestResults)) {
    fs.mkdirSync(cucumberJsonTestResults);
}

// allure results
const allureJsonTestResults = path.resolve(cfgOutputDir, 'allure-results');
if (!fs.existsSync(allureJsonTestResults)) {
    fs.mkdirSync(allureJsonTestResults);
}

const cucumberProgressResults = path.resolve(cfgOutputDir, 'cucumber-progress-results');
if (!fs.existsSync(cucumberProgressResults)) {
    fs.mkdirSync(cucumberProgressResults);
}

const cucumberReporterRelativePath = `${cucumberJsonTestResults.replace(`${process.cwd()}/`, '')}/`;
const junitTestResults = path.resolve(cfgOutputDir, 'junit');
const allureReporterRelativePath = `${allureJsonTestResults.replace(`${newDir}/`, '')}/`;

const config: WebdriverIO.Config = {
    before: async () => {
        await browser.setPermissions({ name: 'clipboard-read' }, 'granted');
        await browser.setPermissions({ name: 'clipboard-write' }, 'granted');
    },
    // ...
    /**
     * The number of times to retry the entire specfile when it fails as a whole
     */
    specFileRetries: 0,
    /**
     * Delay in seconds between the spec file retry attempts
     */
    specFileRetriesDelay: 0,
    /**
     * Retried specfiles are inserted at the beginning of the queue and retried immediately
     */
    specFileRetriesDeferred: false,
    //
    // ====================
    // Runner Configuration
    // ====================
    //
    // WebdriverIO allows it to run your tests in arbitrary locations (e.g. locally or
    // on a remote machine).
    runner: 'local',
    //
    // The number of times to retry the entire specFile when it fails as a whole
    // specFileRetries: 1,
    //
    // Test reporter for stdout.
    // The only one supported by default is 'dot'
    // see also: https://webdriver.io/docs/dot-reporter.html
    reporters: [
        'spec',
        [
            'cucumberjs-json',
            {
                language: 'en',
                jsonFolder: cucumberReporterRelativePath,
            },
        ],
        [
            'allure',
            {
                outputDir: allureReporterRelativePath,
                disableWebdriverStepsReporting: true,
                disableWebdriverScreenshotsReporting: false,
                useCucumberStepReporter: true,
                reportedEnvironmentVars: {
                    TARGET_URL: process.env.TARGET_URL === undefined ? '' : process.env.TARGET_URL,
                    loginUserName: process.env.loginUserName === undefined ? '' : process.env.loginUserName,
                    loginPassword: process.env.loginPassword === undefined ? '' : '***********',
                    loginUserName2: process.env.loginUserName2 === undefined ? '' : process.env.loginUserName2,
                    loginPassword2: process.env.loginPassword2 === undefined ? '' : '***********',
                    loginUserName3: process.env.loginUserName3 === undefined ? '' : process.env.loginUserName3,
                    loginPassword3: process.env.loginPassword3 === undefined ? '' : '***********',
                    tenantName: process.env.tenantName === undefined ? '' : process.env.tenantName,
                    tenantAppName: process.env.tenantAppName === undefined ? '' : process.env.tenantAppName,
                    endPointName1: process.env.endPointName1 === undefined ? '' : process.env.endPointName1,
                    CUCUMBER_TAGS: process.env.CUCUMBER_TAGS === undefined ? '' : process.env.CUCUMBER_TAGS,
                    XTREM_TEST_MAX_INSTANCES:
                        process.env.XTREM_TEST_MAX_INSTANCES === undefined ? '' : process.env.XTREM_TEST_MAX_INSTANCES,
                    testUser: process.env.testUser === undefined ? '' : process.env.testUser,
                    timeout: String(atpEnv.timeout),
                    timeoutWaitFor: String(atpEnv.timeoutWaitFor),
                    timeoutWaitForLoading: String(atpEnv.timeoutWaitForLoading),
                    timeoutLocks: String(atpEnv.timeoutLocks),
                    timeoutCucumber: String(atpEnv.timeoutCucumber),
                    portNumber: process.env.portNumber === undefined ? '' : process.env.portNumber,
                    stopTestOnError: process.env.stopTestOnError === undefined ? '' : process.env.stopTestOnError,
                    toastFallback: process.env.toastFallback === undefined ? '' : process.env.toastFallback,
                    downloadFolder: atpEnv.downloadFolder === undefined ? '' : atpEnv.downloadFolder,
                    os_platform: os.platform(),
                    os_release: os.release(),
                    os_version: os.version(),
                    node_version: process.version,
                },
            },
        ],
    ],
    maxInstances: Number(process.env.XTREM_TEST_MAX_INSTANCES || 1),
    capabilities: [
        {
            browserName: 'chrome',
            'goog:chromeOptions': {
                prefs: {
                    'download.default_directory': path.resolve(atpEnv.downloadFolder),
                    'profile.default_content_settings.popups': false,
                    'download.prompt_for_download': false,
                    'download.directory_upgrade': true,
                },
                args: [
                    '--disable-web-security',
                    '--allow-file-access-from-files',
                    '--allow-running-insecure-content',
                    '--window-size=1920,1080',
                    '--disable-search-engine-choice-screen',
                    '--disable-gpu',
                    '--no-sandbox',
                    '--font-render-hinting=full',
                    '--force-device-scale-factor=1.0',
                    '--lang=en-GB',
                    '--unsafely-treat-insecure-origin-as-secure=http://sdmo-xtrem.localhost.dev-sagextrem.com:8240',
                    '--hide-scrollbars',
                    '--no-wasm-code-gc',
                ],
            },
        },
    ],
    logLevel: process.env.WDIO_LOG_LEVEL as WebDriverLogTypes,
    outputDir: cfgOutputDir,
    bail: 0,
    baseUrl: cfgBaseUrl,
    waitforTimeout: atpEnv.timeoutWaitFor,
    connectionRetryTimeout: atpEnv.timeoutWaitFor,
    connectionRetryCount: 3,
    services: ['devtools', 'shared-store'],
    framework: 'cucumber',
    cucumberOpts: {
        scenarioLevelReporter: false,
        backtrace: true, // <boolean> show full backtrace for errors
        require: stepDefinitionFiles,
        // compiler: ['ts:ts-node/register', 'ts:source-map-support/register'],
        // EXTENSION after requiring MODULE (repeatable)
        failFast: stopTestOnError, // <boolean> abort the run on first failure
        // format, optionally supply PATH to redirect
        // formatter output (repeatable)
        snippets: true, // <boolean> hide step definition snippets for pending
        // steps
        source: true, // <boolean> hide source uris
        profile: [], // <string[]> (name) specify the profile to use
        strict: true, // <boolean> fail if there are any undefined or pending
        // steps
        // tags matching the expression
        tagsInTitle: false,
        timeout: atpEnv.timeoutCucumber, // <number> timeout for step definitions
        ignoreUndefinedDefinitions: false, // <boolean> Enable this config to
        // treat undefined definitions as
        // warnings.
        failAmbiguousDefinitions: true,
        requireModule: [],
        tagExpression,
        retry: retries, // retries // Number of times to retry failing scenarios
    },
    //
    // =====
    // Hooks
    // =====
    // WebdriverIO provides several hooks you can use to interfere with the test process in order to enhance
    // it and to build services around it. You can either apply a single function or an array of
    // methods to it. If one of them returns with a promise, WebdriverIO will wait until that promise got
    // resolved to continue.
    /**
     * Gets executed once before all workers get launched.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     */
    onPrepare: () => {
        // eslint-disable-next-line no-console
        console.log(browser.capabilities);
        if (!process.env.KEEP_REPORT) {
            safeRmSync(cfgReportDir, { recursive: true });
            fs.mkdirSync(cfgReportDir);
        }
        // await browser.setTimeout({ implicit: atpEnv.timeout });
    },
    /**
     * Gets executed just before initializing the webdriver session and test framework. It allows you
     * to manipulate configurations depending on the capability or spec.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs List of spec file paths that are to be run
     */
    beforeSession: () => {},

    beforeFeature: (uri, feature) => {
        if (feature) {
            printInfo(ExecutionMode.STANDALONE, feature.name);
            process.env.FEATURE_PATH = uri;
        }
    },

    beforeScenario: async world => {
        if (world) {
            printInfo(ExecutionMode.STANDALONE, `    ${world.pickle.name}`);
            testGlobals._CURRENT_SCENARIO = world.pickle.name;
            testGlobals._CURRENT_SCENARIO_STEPS = world.pickle.name;
            testGlobals._CURRENT_SCENARIO_PATH = path.resolve(newDir, world.pickle.uri);
            testGlobals._CURRENT_STEP_ID = 0;
            await browser.execute('window.__ACTIVATE_CONSOLE_WATCH && window.__ACTIVATE_CONSOLE_WATCH();');
        }
    },
    /**
     * Runs before a Cucumber step
     */
    beforeStep(step) {
        testGlobals._CURRENT_STEP_SCREENSHOT = false;
        fs.writeFileSync(getCurrentLogFile(), '');
        if (step /* &&  step.keyword !== 'Hook' && !process.env.XTREM_SKIP_ERROR_TRACE*/) {
            const newStep: any = step;
            testGlobals._CURRENT_STEP = `${newStep.keyword} ${step.text}`;
            testGlobals._CURRENT_SCENARIO_STEPS = `${testGlobals._CURRENT_SCENARIO_STEPS}\n${newStep.keyword} ${step.text}`;
            testGlobals._CURRENT_STEP_ID = (testGlobals._CURRENT_STEP_ID || 0) + 1;
        }
    },

    afterStep: async (step, context: any, result: { error?: any; result?: any; passed: boolean; duration: number }) => {
        const newStep: any = step;
        if (step && result.passed) {
            printSuccess(ExecutionMode.STANDALONE, `        ${newStep.keyword} ${step.text}`);
        } else if (step && !result.passed) {
            printWarning(ExecutionMode.STANDALONE, `        ${newStep.keyword} ${step.text}`);
            if (result.error) {
                printWarning(ExecutionMode.STANDALONE, result.error);
            }
        }
        // For debugging purpose - do not merge it activated
        // const visitorId = await browser.execute('window.pendo?.visitorId || null;');
        // if (visitorId){
        //     console.log(`===============PENDO VISITOR ID===================\n${visitorId}\n==================`)
        // }

        const logContent = fs.readFileSync(getCurrentLogFile(), 'utf-8').split('\0').join('');
        if (logContent !== '') {
            addAttachment('Console Log', logContent, 'text/plain');
        }

        if (!result.passed) {
            if (!testGlobals._CURRENT_STEP_SCREENSHOT) {
                await takeScreenshot();
            }

            const log = await browser.execute('window.__PRINT_CONSOLE_HISTORY();');
            if (typeof log === 'string' && log.toString() !== '') {
                addAttachment('Console Log', log, 'text/plain');
            }
        }
    },

    afterFeature: () => {
        // eslint-disable-next-line no-console
        console.log('\n');
    },

    afterScenario: async (world, result) => {
        try {
            if (!result.passed && testGlobals._CURRENT_STEP_SCREENSHOT) {
                await takeScreenshot();
            }

            // Disables the alert dialog that would prevent the page from being closed.
            await browser.execute('window.__REMOVE_UNLOAD_LISTENER && window.__REMOVE_UNLOAD_LISTENER();');
            if (!result.passed /* && !process.env.XTREM_SKIP_ERROR_TRACE */) {
                printWarning(
                    ExecutionMode.STANDALONE,
                    `=====================${(browser as any)._CURRENT_SCENARIO}==================================`,
                );
                printWarning(ExecutionMode.STANDALONE, '=======================================================');
            }

            await browser.execute('window.localStorage.clear();');

            // Resets client settings after the scenario
            await browser.execute(
                // eslint-disable-next-line @stylistic/quotes
                `window.fetch('/metadata/', { method:'post', body: JSON.stringify({ query: 'mutation {clientUserSettings {resetClientSettings}}' }), headers: { 'Content-Type': 'application/json' }});`,
            );
            const coverageResult = await browser.execute('return window.__coverage__;');
            if (coverageResult) {
                const targetFileName = `${Uuid.generate()}.json`;
                const targetPath = path.resolve(newDir, 'coverage', targetFileName);
                fs.writeFileSync(targetPath, JSON.stringify(coverageResult), 'utf-8');
            }
        } catch {
            // eslint-disable-next-line no-console
            console.log('Failed to execute after scenario hook.');
        }
        // eslint-disable-next-line no-console
        console.log('\n');
    },

    /**
     * Runs after a WebdriverIO command gets executed
     * @param {String} commandName hook command name
     * @param {Array} args arguments that command would receive
     * @param {Number} result 0 - command success, 1 - command error
     * @param {Object} error error object if any
     */
    afterCommand: (commandName, args, result, error: Error) => {
        if (result === 1) {
            // eslint-disable-next-line no-console
            console.log(`error: ${error}`);
        }
    },

    /**
     * Gets executed after all workers got shut down and the process is about to exit. An error
     * thrown in the onComplete hook will result in the test run failing.
     * @param {Object} exitCode 0 - success, 1 - fail
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {<Object>} results object containing test results
     */
    onComplete: async () => {
        if (process.argv.includes('--ci')) {
            await updateAllJUnitFilesInFolder(junitTestResults);
        }
    },
};

const accessibilityJsonTestResults = path.resolve(cfgOutputDir, 'accessibility');
testGlobals.accessibility_json_path = accessibilityJsonTestResults;

// eslint-disable-next-line no-console
console.log(`Cucumber report JSON files will be written to ${cucumberJsonTestResults}`);
// eslint-disable-next-line no-console
console.log(`Allure results files will be written to ${allureReporterRelativePath}`);
if (process.argv.includes('--ci')) {
    if (!fs.existsSync(junitTestResults)) {
        fs.mkdirSync(junitTestResults);
    }
    // eslint-disable-next-line no-console
    console.log(`JUnit XML files will be written to ${junitTestResults}`);
    config.reporters?.push([
        'junit',
        {
            outputDir: junitTestResults,
            suiteNameFormat: /^[\\w\\W]+$/,
            addFileAttribute: false,
            packageName: 'integration',
            errorOptions: {
                error: 'message',
                failure: 'message',
                stacktrace: 'stack',
            },
            outputFileFormat(options: any) {
                return `junit-report-${new Date().getTime()}-${options.cid}.xml`;
            },
        },
    ]);
} else {
    config.reporters?.push('spec');
}
export { cfgOutputDir, config };
