/* eslint-disable no-restricted-syntax */
import { timeoutWaitFor, valueCheckTimeout } from '../index';

export const expectElementToBeDisplayed = async ({
    selector,
    reverse = false,
    name,
}: {
    selector: string;
    reverse?: boolean;
    name?: string;
}) => {
    const selectorToUse = await browser.$(selector);
    try {
        await selectorToUse.waitForDisplayed({ timeout: valueCheckTimeout, reverse });
    } catch {
        throw new Error(
            `Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${selector} \n ${name} `,
        );
    }
};

export const waitForElementToBeDisplayed = async ({
    name,
    selector,
    reverse = false,
}: {
    name: string;
    selector: string;
    reverse?: boolean;
}) => {
    const element = await browser.$(selector);
    await element.waitForDisplayed({
        reverse,
        timeout: timeoutWaitFor,
        timeoutMsg: `Expected element${
            reverse ? ' not ' : ' '
        }to be displayed: ${name} in ${timeoutWaitFor} ms.\nSelector: ${selector})`,
    });
};

export const waitForElementToBeFound = async ({
    name,
    selector,
    reverse = false,
    timeout = 10000,
}: {
    name: string;
    selector: string;
    reverse?: boolean;
    timeout?: number;
}) => {
    const elements = await browser.$$(selector);
    if (elements.length > 1) {
        // fix XT-95813
        await browser.waitUntil(
            async () => {
                let firstFound;
                for (const element of elements) {
                    if (await element.isDisplayed()) {
                        firstFound = element;
                        break;
                    }
                }
                return firstFound;
            },
            { timeout },
        );
    } else {
        // old code
        const element = await browser.$(selector);
        await element.waitForDisplayed({
            reverse,
            timeout,
            timeoutMsg: `Expected element could not be found: ${name}.\nSelector: ${selector}`,
        });
    }
};

export const waitForElementNotToBeDisplayed = async (name: string, selector: string) => {
    await waitForElementToBeDisplayed({ name, selector, reverse: true });
};

export const waitForElementToExist = async ({
    name,
    selector,
    reverse = false,
}: {
    name: string;
    selector: string;
    reverse?: boolean;
}) => {
    const element = await browser.$(selector);
    await element.waitForExist({
        reverse,
        timeout: timeoutWaitFor,
        timeoutMsg: `Expected element${
            reverse ? ' not ' : ' '
        }to exist: ${name} in ${timeoutWaitFor} ms.\nSelector: ${selector}`,
    });
};

export const waitForElementNotToExist = async (name: string, selector: string) => {
    await waitForElementToExist({ name, selector, reverse: true });
};

export const safeClick = (element: WebdriverIO.Element) => browser.execute(el => el.click(), element);

export const scrollIntoViewViaJS = async (selector: string) => {
    await browser.execute(sel => {
        const el = document.querySelector(sel);
        if (el) el.scrollIntoView();
    }, selector);
};

export const scrollElementIntoMainView = async (
    selector: string,
    mainSelector = '.e-page-main-section',
    headerSelector = '.e-header',
    footerSelector = '.e-page-footer-container',
): Promise<void> => {
    if (typeof selector === 'string' && selector.trim().startsWith('/')) return;

    await browser.waitUntil(
        async () => {
            const el = await $(selector);
            return el && el.isExisting();
        },
        { timeout: 3000 },
    );

    await browser.executeAsync(
        (elSel, mainSel, headerSel, footerSel, done) => {
            const el = document.querySelector(elSel) as HTMLElement | null;
            const main = document.querySelector(mainSel) as HTMLElement | null;
            if (!el || !main) {
                done(true);
                return;
            }

            const header = headerSel ? (document.querySelector(headerSel) as HTMLElement | null) : null;
            const footer = footerSel ? (document.querySelector(footerSel) as HTMLElement | null) : null;
            const headerH = header ? header.getBoundingClientRect().height : 0;
            const footerH = footer ? footer.getBoundingClientRect().height : 0;
            const mainRect = main.getBoundingClientRect();
            const elRect = el.getBoundingClientRect();

            const visibleTop = mainRect.top + headerH;
            const visibleBottom = mainRect.bottom - footerH;

            if (elRect.top >= visibleTop && elRect.bottom <= visibleBottom) {
                done(true);
                return;
            }

            let targetScrollTop = main.scrollTop;

            if (elRect.top < visibleTop) {
                targetScrollTop = Math.max(main.scrollTop - (visibleTop - elRect.top), 0);
            } else if (elRect.bottom > visibleBottom) {
                targetScrollTop = Math.min(
                    main.scrollTop + (elRect.bottom - visibleBottom),
                    Math.max(0, main.scrollHeight - main.clientHeight),
                );
            }

            main.scrollTop = targetScrollTop;
            requestAnimationFrame(() => done(true));
        },
        selector,
        mainSelector,
        headerSelector,
        footerSelector,
    );
};
