/* eslint-disable class-methods-use-this */

import { trimEnd } from 'lodash';
import { Key } from 'webdriverio';
import * as utils from '../../../../step-definitions-utils';
import { scrollElementIntoMainView } from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import {
    changeDateWithFormat,
    formatDate,
    formatDateNth,
    formatDateToDateString,
    getUserLocale,
    openDatePicker,
} from '../../date-utils';
import { waitForPromises } from '../../wait-util';
import { isRowExists, scrollToTableRow, waitForTableToBePopulated } from '../tableUtils';
import { rowIndexSelector } from './table-nested-field-utils';

const SELECTORS = {
    datePickerContext: (startOrEnd: 'start' | 'end') =>
        utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: `e-datetime-input-${startOrEnd}-date-picker`,
        }),
    dateTime: (startEnd: 'start' | 'end' | 'start and end') =>
        startEnd === 'start and end'
            ? ''
            : utils.getDataTestIdSelector({
                  domSelector: 'input',
                  dataTestIdValue: `e-datetime-input-${startEnd}`,
              }),
    timeZoneInput: (startOrEnd: 'start' | 'end') =>
        utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${startOrEnd}-time-input-timezone`,
        }),
};

// eslint-disable-next-line @typescript-eslint/naming-convention
interface INestedFieldObjectConstructor {
    tableSelector: string;
    columnName: string;
    nestedLookupStrategy: utils.LookupStrategy;
    rowNumber: number;
    isFloatingRow?: boolean;
    fieldType?: utils.FieldTypes;
}

export class NestedFieldObject extends AbstractPageObject {
    private tableSelector: string;

    constructor({
        tableSelector,
        columnName,
        nestedLookupStrategy,
        rowNumber,
        isFloatingRow,
        fieldType,
    }: INestedFieldObjectConstructor) {
        super(
            `${tableSelector} ${rowIndexSelector(rowNumber, isFloatingRow)} ${utils.getTableNestedFieldSelector({ lookupStrategy: nestedLookupStrategy, identifier: columnName, fieldType })}`,
        );
        this.tableSelector = tableSelector;
    }

    override expectBackgroundColor: utils.AsyncObjectStringStringBooleanReturnVoid = async ({
        toBe,
        cssSelector = 'div',
        ignoreContext = false,
    }) => {
        await super.expectBackgroundColor({ toBe, cssSelector, ignoreContext });
    };

    expectColor: utils.AsyncObjectStringStringBooleanReturnVoid = async ({
        toBe,
        cssSelector = 'div',
        ignoreContext = false,
    }) => {
        await super.expectValue({ toBe, cssSelector, ignoreContext });
    };

    waitForTableStopLoading = async () => {
        const loadingIndicator = await this.find('.ag-loading');
        await loadingIndicator.waitForExist({
            reverse: true,
            timeout: this.timeoutWaitFor,
            timeoutMsg: 'table still loading',
        });
        await waitForPromises(500, 'waiting for table stop loading');
    };

    getNestedValue = async (fieldType: utils.NestedFieldTypes): Promise<string> => {
        try {
            await browser.execute(
                `const node = document.querySelector('${this.cssSelector}'); node ? node.scrollIntoViewIfNeeded() : true;`,
            );
            await waitForPromises(500, 'waiting for scroll to element');
        } catch (error) {
            throw new Error(`Could not scroll to element: ${this.cssSelector}`);
        }
        const element = await this.get();
        const elementValue =
            fieldType === utils.fieldTypes.progress ? await element.getValue() : await element.getText();
        return elementValue;
    };

    expectNestedValue = async ({
        toBe,
        fieldType,
        rowNumber = null,
        cssSelector = '',
        ignoreContext = false,
        dateIsGenerated = false,
    }: {
        toBe: string;
        fieldType: utils.NestedFieldTypes;
        columnName: string;
        rowNumber?: number | null;
        cssSelector?: string;
        ignoreContext?: boolean;
        dateIsGenerated?: boolean;
    }): Promise<void> => {
        if (rowNumber !== null) await waitForTableToBePopulated(this.tableSelector);
        await scrollToTableRow(this.cssSelector, rowNumber);

        switch (fieldType) {
            case utils.fieldTypes.checkbox:
                await this.expectCheckedValue(toBe);
                break;
            case utils.fieldTypes.date: {
                if (dateIsGenerated) {
                    await this.generateDateVerify({ dateFormat: toBe });
                } else {
                    // Depending on the focus state, the date field can be an input or a div
                    const dateElement = await this.findAll(`${cssSelector} input`, ignoreContext);
                    if (dateElement.length > 0) {
                        await this.expectValue({ toBe, cssSelector: `${cssSelector} input`, ignoreContext });
                    } else {
                        await this.expectTextContent({ toBe, ignoreCase: false, cssSelector, ignoreContext });
                    }
                }
                break;
            }
            case utils.fieldTypes.aggregate:
            case utils.fieldTypes.count:
            case utils.fieldTypes.dropdownList:
            case utils.fieldTypes.filterSelect:
            case utils.fieldTypes.icon:
            case utils.fieldTypes.image:
            case utils.fieldTypes.label:
            case utils.fieldTypes.link:
            case utils.fieldTypes.numeric:
            case utils.fieldTypes.radio:
            case utils.fieldTypes.reference:
            case utils.fieldTypes.relativeDate:
            case utils.fieldTypes.select:
            case utils.fieldTypes.switch:
            case utils.fieldTypes.text:
                await this.expectTextContent({ toBe, ignoreCase: false, cssSelector, ignoreContext });
                break;
            case utils.fieldTypes.progress:
                await this.expectNestedProgressValue({ toBe, cssSelector, ignoreContext });
                break;
            default:
                throw new Error(`Invalid fieldType type: ${fieldType}`);
        }
    };

    expectNestedValueContainsPattern = async ({
        toBe,
        rowNumber = null,
        cssSelector = '',
        ignoreContext = false,
    }: {
        toBe: string;
        columnName: string;
        rowNumber?: number | null;
        cssSelector?: string;
        ignoreContext?: boolean;
    }): Promise<void> => {
        if (rowNumber !== null) await waitForTableToBePopulated(this.tableSelector);
        await scrollToTableRow(this.cssSelector, rowNumber!);

        let textToCheck = toBe
            .replace(/\n/g, '')
            .replace(/{\s+/g, '{')
            .replace(/"\s+/g, '"')
            .replace(/:\s+/g, ':')
            .replace(/\*\s+/g, '*')
            .replace(/,\s+/g, ',')
            .replace(/}\s+/g, '}');
        // /s+}/g is apparently a redos risk so replaced with the following do deal with "[number]   }" strings.
        const regexCheck = /\d\s+}/g.exec(textToCheck);
        if (regexCheck) {
            // eslint-disable-next-line no-restricted-syntax
            for (const match of regexCheck) {
                textToCheck = textToCheck.replace(match, match.replace(/\s+/g, ''));
            }
        }
        await this.expectTextContainsPattern({
            toBe: textToCheck,
            ignoreCase: false,
            cssSelector,
            ignoreContext,
        });
    };

    expectNestedTimeStamp = async ({
        toBe,
        rowNumber = null,
        cssSelector = '',
        ignoreContext = false,
    }: {
        toBe: string;
        columnName: string;
        rowNumber?: number | null;
        cssSelector?: string;
        ignoreContext?: boolean;
    }): Promise<void> => {
        if (rowNumber !== null) await waitForTableToBePopulated(this.tableSelector);
        await scrollToTableRow(this.cssSelector, rowNumber!);

        const lang = await getUserLocale();
        const dateFormatMatcher =
            /(\d{4}|\(?Y(?:-\d|\+\d)?\)?)-(\d{2}|\(?M(?:-\d|\+\d)?\)?)-(\d{2}|\(?T(?:-\d|\+\d)?\)?)/g;

        const dateArray = dateFormatMatcher.exec(toBe);
        let dateToCheck = toBe;
        if (dateArray) {
            dateToCheck = formatDateToDateString({ year: dateArray[1], month: dateArray[2], day: dateArray[3], lang });
        }

        const dateField = formatDateNth(dateToCheck, lang);
        let generatedDate = changeDateWithFormat(dateField, lang);
        generatedDate = formatDate(new Date(generatedDate), 'en-CA');

        await this.expectTextContains({ toBe: generatedDate, ignoreCase: false, cssSelector, ignoreContext });
    };

    writeTableNestedFieldValue = async ({
        value,
        fieldType,
        floatingRow,
    }: {
        value: string;
        fieldType: utils.NestedFieldTypes;
        floatingRow?: string | null;
    }): Promise<void> => {
        try {
            if (floatingRow && (await isRowExists())) {
                // to translate the floating bar by using the row-index="0" selector if there is a row index 0 in the table
                await this.scrollTo({
                    selector: this.cssSelector.replace(`row-index="t-0"`, `row-index="0"`).toString(),
                    ignoreContext: true,
                });
            }

            await waitForPromises(500, 'waiting for scroll to element');
        } catch (error) {
            throw new Error(`Could not scroll to element: ${this.cssSelector}`);
        }

        const element = await $(this.cssSelector);
        try {
            await waitForPromises(100, 'waiting moving to element');
            await element.moveTo();
            await utils.waitForElementToExist({
                name: 'field',
                selector: this.cssSelector,
            });
            await utils.waitForElementToBeDisplayed({
                name: 'field',
                selector: this.cssSelector,
            });
            await waitForPromises(1000, 'waiting moving to element');
            await browser.execute(elem => elem.scrollIntoView(), element);
            await browser.execute(`document.querySelector('${element.selector}').click()`);
            await browser.execute(`document.querySelector('${element.selector}').click()`);
            await waitForPromises(1000, 'waiting moving to element');
        } catch (error) {
            console.log('Could not click element ', element.selector, '...Continue...', error);
        }

        switch (fieldType) {
            case utils.fieldTypes.dropdownList:
            case utils.fieldTypes.filterSelect:
            case utils.fieldTypes.select:
                await browser.waitUntil(
                    async () => {
                        const cellEditor = await $('.ag-popup-editor');
                        return Boolean(cellEditor);
                    },
                    { timeout: this.timeoutWaitFor },
                );
                await browser.keys([Key.Ctrl, 'a']);
                await browser.keys(Key.Backspace);
                await browser.keys(value);
                await browser.keys(Key.Enter);
                break;
            case utils.fieldTypes.reference:
                await browser.keys([Key.Ctrl, 'a']);
                await browser.keys(Key.Backspace);
                await browser.keys(value);
                if (!value) {
                    await browser.keys(Key.Enter);
                }
                break;
            case utils.fieldTypes.numeric:
            case utils.fieldTypes.text: {
                await browser.keys([Key.Ctrl, 'a']);
                await browser.keys(Key.Backspace);
                await utils.waitForElementToExist({
                    name: 'element input',
                    selector: `${this.cssSelector} input`,
                });
                await this.write({ content: value, cssSelector: 'input' });
                await browser.keys(Key.Enter);
                break;
            }
            case utils.fieldTypes.date:
                await this.writeDate(await this.generateDate(value));
                break;
            default:
                await this.write({ content: value });
                await browser.keys(Key.Enter);
                break;
        }
    };

    generateDate = async (dateString: string): Promise<string> => {
        if (!dateString) {
            return '';
        }
        await openDatePicker(this);
        const lang = await getUserLocale();
        const dateField = formatDateNth(dateString, lang);
        return changeDateWithFormat(dateField, lang);
    };

    generateDateVerify = async ({
        dateFormat,
        cssSelector = 'div',
        ignoreContext = false,
    }: {
        dateFormat: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }): Promise<boolean> => {
        const element = await browser.$(this.cssSelector);
        const lang = await getUserLocale();
        let selectorToUse;
        let text;
        if ((await element.getAttribute('class')).includes('e-nested-cell-editable')) {
            const isDayPickerOpen = await (await this.find('.rdp-root', ignoreContext)).isExisting();
            if (!isDayPickerOpen) {
                const dateField = await this.find(cssSelector, ignoreContext);
                await dateField.click();
            }
            await this.click('.rdp-selected', true);

            const dateFieldInput = await this.find('[data-component="date"] [data-element="input"]');
            text = await dateFieldInput.getValue();
            selectorToUse = dateFieldInput.selector;
        } else {
            const dateField = await this.find(cssSelector, ignoreContext);
            text = await dateField.getText();
            selectorToUse = dateField.selector;
        }
        const dateField = formatDateNth(dateFormat, lang);
        const toBe = changeDateWithFormat(dateField, lang);
        if (toBe === text) {
            return true;
        }
        throw new Error(`Expected value: "${toBe}", actual: "${text}".\nSelector: ${selectorToUse}`);
    };

    writeDate: utils.AsyncStringReturnVoid = async content => {
        try {
            const selector = '[data-component="date"] [data-element="input"]';
            const generatedSelector = this.getSelectorForOperation(selector);
            await scrollElementIntoMainView(generatedSelector);
            const dateField = await this.find(selector);
            await dateField.click();
            if (content) {
                await dateField.clearValue();
                const arrValue = [...content];
                for (let i = 0; i < arrValue.length; i += 1) {
                    await browser.keys(arrValue[i]);
                }
            } else {
                while ((await dateField.getValue()).length > 0) {
                    await browser.keys(Key.ArrowRight);
                    await browser.keys(Key.Backspace);
                }
            }
            await browser.pause(200);
            await browser.keys(Key.Enter);
        } catch (error) {
            throw new Error(`Error writing date: ${error}`);
        }
    };

    clickNestedTableField = async () => {
        const element = await this.get();
        await utils.waitForElementToExist({ name: 'field', selector: this.cssSelector });
        try {
            await scrollElementIntoMainView(this.cssSelector);
            await element.waitForClickable({ timeout: this.timeoutWaitFor });
            await element.moveTo();
            await element.click();
            await waitForPromises(500, 'waiting for click to complete');
        } catch (error) {
            console.log(error);
            try {
                await browser.execute(el => el.click(), element);
            } catch (err) {
                throw new Error(err);
            }
        }

        await waitForPromises(500, 'waiting for click to complete');
    };

    selectValue = async (value: string, columnName?: string): Promise<void> => {
        const element = await this.get();
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${columnName}" nested field.\nSelector: ${this.cssSelector}`,
        });
        const itemsSelector = `${utils.getDataTestIdSelector({ domSelector: 'ul', dataTestIdValue: 'e-ui-select-dropdown' })} li`;
        const items = await $$(itemsSelector);

        for (let i = 0; i < items.length; i += 1) {
            const item = items[i];
            const itemElt = await item.$(
                utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-ui-select-suggestion-value' }),
            );
            const itemVal = await itemElt.getText();
            if (itemVal === value) {
                const nthChildSelector = `${itemsSelector}:nth-child(${i + 1})`;
                await scrollElementIntoMainView(nthChildSelector);
                await item.moveTo({ xOffset: 0, yOffset: 0 }); // if this fails again because of the Lef Menu Item poping out the moveTo needs to be removed
                await item.waitForClickable();
                await item.click();
                await waitForPromises(500, 'select value');

                break;
            }
        }
        await $(itemsSelector).waitForExist({
            reverse: true,
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element still displayed.\nSelector: ${items}`,
        });
    };

    expectNestedProgressValue: utils.AsyncObjectStringStringBooleanReturnVoid = async ({
        toBe,
        cssSelector = 'div',
        ignoreContext = false,
    }) => {
        const selectorToUse = `${this.getSelectorForOperation(
            cssSelector,
            ignoreContext,
        )} [data-element="current-progress-label"]`;
        const trimmedToBe = toBe.replace('%', '').trim();
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeout: this.valueCheckTimeout,
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
        });
        let value = '';
        let trimmedResult = '';
        try {
            await browser.waitUntil(
                async () => {
                    const result = (await element.getText()) as unknown as string[];
                    value = Array.isArray(result) ? result[0] : result;
                    trimmedResult = trimEnd(value, '%').trim();
                    return (trimmedResult || '') === (trimmedToBe || '');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected value: ${trimmedToBe}, actual: ${trimmedResult}.\nSelector: ${selectorToUse}`);
        }
    };

    openLookupDialog: utils.AsyncStringReturnVoid = async columnName => {
        try {
            const element = await this.get();
            await element.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element could not be found: "${columnName}" nested field.\nSelector: ${this.cssSelector}`,
            });
            const lookupButton = await this.find(
                '.ag-popup-editor .e-reference-cell-editor [data-testid="e-ui-select-lookup-button"]',
                true,
            );
            await lookupButton.waitForDisplayed({
                reverse: false,
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Element is not displayed.\nSelector: ${lookupButton.selector}`,
            });
            await browser.waitUntil(() => lookupButton.isClickable(), {
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Element is not clickable.\nSelector: ${lookupButton.selector}`,
            });
            await lookupButton.click();
            await waitForPromises(500, 'open button lookup');
        } catch (error) {
            throw new Error(error);
        }
    };

    clickTunnelLink = async (columnName: string): Promise<void> => {
        try {
            const element = await this.get();
            await element.waitForExist({
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Expected element could not be found: "${columnName}" nested field.\nSelector: ${this.cssSelector}`,
            });
            const tunnelLinkButton = await $(
                utils.getDataTestIdSelector({
                    domSelector: 'div',
                    dataTestIdValue: 'e-ui-select-dropdown-create-new-item-link',
                }),
            );
            await tunnelLinkButton.waitForClickable({
                timeout: this.timeoutWaitFor,
            });
            await tunnelLinkButton.click();
            await waitForPromises(300, 'click tunnel link');
        } catch (error) {
            throw new Error(error);
        }
    };

    expectNestedError = async ({
        toBe,
        fieldType,
        rowNumber = null,
    }: {
        toBe: boolean;
        fieldType: utils.NestedFieldTypes;
        columnName: string;
        rowNumber?: number | null;
    }): Promise<void> => {
        try {
            if (rowNumber !== null) await waitForTableToBePopulated(this.tableSelector);
            await scrollToTableRow(this.cssSelector, rowNumber!);
            await waitForPromises(100, 'waiting for element to exist');
        } catch (error) {
            throw new Error(`Could not scroll to element: ${this.cssSelector}`);
        }
        await waitForPromises(100, 'element to be visible');

        const VALID_FIELD_TYPES = [
            utils.fieldTypes.checkbox,
            utils.fieldTypes.date,
            utils.fieldTypes.aggregate,
            utils.fieldTypes.count,
            utils.fieldTypes.dropdownList,
            utils.fieldTypes.filterSelect,
            utils.fieldTypes.icon,
            utils.fieldTypes.image,
            utils.fieldTypes.label,
            utils.fieldTypes.link,
            utils.fieldTypes.numeric,
            utils.fieldTypes.radio,
            utils.fieldTypes.reference,
            utils.fieldTypes.relativeDate,
            utils.fieldTypes.select,
            utils.fieldTypes.switch,
            utils.fieldTypes.text,
            utils.fieldTypes.progress,
        ] as const;

        if (VALID_FIELD_TYPES.includes(fieldType as (typeof VALID_FIELD_TYPES)[number])) {
            await this.expectCellError(toBe);
        } else {
            throw new Error(`Invalid fieldType type: ${fieldType}`);
        }
    };

    expectCellError: utils.AsyncBooleanReturnVoid = async reverse => {
        const element = await this.find(`[data-element="error"]`);
        const errorExists = await element.isExisting();

        if (reverse !== errorExists) {
            const expectedError = reverse ? 'errors' : 'no error';
            throw new Error(
                `Expected nested field to contain: ${expectedError}.\nSelector: ${this.cssSelector} ${element.selector}`,
            );
        }
    };

    async openDateRangePopup(identifier: string): Promise<void> {
        const dateFieldSelector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${identifier}`,
        });

        try {
            const popUp = await $('.e-popover-dialog');
            const dateTimeSelector = await $(this.cssSelector);
            const dateField = await $(dateFieldSelector);

            await dateTimeSelector.scrollIntoView();
            const isPopupVisible = await dateField.isDisplayedInViewport();

            if (isPopupVisible) {
                await popUp.scrollIntoView();
                await browser.execute(dateF => dateF.click(), dateField);
                await waitForPromises(500, 'Waiting for click');
                return;
            }

            await dateTimeSelector.waitForDisplayed();
            await browser.execute(dateT => dateT.click(), dateTimeSelector);
            await waitForPromises(500, 'Waiting for click');
            await popUp.scrollIntoView();
            await dateField.waitForDisplayed();
            await browser.execute(dateF => dateF.click(), dateField);
            await waitForPromises(500, 'Waiting for click');
        } catch (error) {
            throw new Error(
                `Expected element could not be found: "${identifier}".\nSelector: ${dateFieldSelector}\nError: ${error.message}`,
            );
        }
    }

    selectDateTimeRangePart = async ({
        startOrEnd,
        value,
        part,
    }: {
        startOrEnd: 'start' | 'end';
        value: string;
        part: 'month' | 'year' | 'day';
    }) => {
        await this.openDateRangePopup(startOrEnd);

        const context = await $(SELECTORS.datePickerContext(startOrEnd));

        const listSelector =
            part === 'day' ? `.e-calendar-grid-styled .e-calendar-date-cell` : `.e-${part}-selector option`;

        if (part === 'month' || part === 'year') {
            const monthOrYearSelector = await $(`${context.selector} .e-${part}-selector`);
            await monthOrYearSelector.moveTo();
            await monthOrYearSelector.click();
            await waitForPromises(500, 'waiting for month selector to be clicked');
        }

        const items = await $$(`${context.selector} ${listSelector}`);

        // eslint-disable-next-line no-restricted-syntax
        for (const item of items) {
            const itemText = await item.getText();
            if (itemText.trim() === value.trim()) {
                await item.moveTo();
                await item.click();
                await waitForPromises(500, 'waiting for day to be selected');
                return;
            }
        }

        const errorMessage =
            part === 'day'
                ? `Expected day "${value}" could not be found.`
                : `Expected option to be selected could not be found: "${value}"`;
        throw new Error(`${errorMessage}.\nSelector: ${listSelector}`);
    };

    async setHoursMin(identifier: string, targetTime: string) {
        const [hours, minutes] = this.extractTime(targetTime);

        await this.openDateRangePopup(identifier);

        await this.setTimeField(identifier, 'hours', hours);
        await this.setTimeField(identifier, 'minutes', minutes);
        await waitForPromises(500, 'Waiting for popup to open');
    }

    private extractTime(targetDate: string): [number, number] {
        const regex = /(\d{1,2})[,:.](\d{2})/;
        const match = targetDate.match(regex);

        if (!match) {
            throw new Error('Invalid time format. Expected HH:MM');
        }

        const hours = parseInt(match[1], 10);
        const minutes = parseInt(match[2], 10);

        // eslint-disable-next-line no-restricted-globals
        if (isNaN(hours) || hours < 1 || hours > 12) {
            throw new Error(`Invalid hour: "${hours}". Must be between 1 and 12.`);
        }

        // eslint-disable-next-line no-restricted-globals
        if (isNaN(minutes) || minutes < 0 || minutes > 59) {
            throw new Error(`Invalid minutes: "${minutes}". Must be between 0 and 59.`);
        }

        return [hours, minutes];
    }

    private async setTimeField(identifier: string, field: 'hours' | 'minutes', value: number) {
        const cssSelector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${identifier}-time-input-${field}`,
        });
        await this.timeState(`${cssSelector}`);
        const inputField = await $(cssSelector);
        await inputField.waitForExist();
        await inputField.waitForDisplayed();
        await inputField.click();
        await inputField.clearValue();
        await this.write({ content: value.toString(), cssSelector, ignoreContext: true }); // e.g., "08"
        await browser.keys(Key.Tab);
    }

    async timeState(selector: string, identifier?: string): Promise<void> {
        const isDisabled = await $(selector).getProperty('disabled');

        if (isDisabled) {
            const label = identifier ? ` "${identifier}" ` : ' ';
            throw new Error(`Expected element${label}to be enabled.\nSelector: ${selector}`);
        }
    }

    async toggleDateRangeButton(identifier: string, targetDate: string): Promise<void> {
        await this.openDateRangePopup(identifier);

        const parentElement = `.e-popover-dialog[data-type="${identifier}"] .e-time-field`;
        const selectorToUse = utils.getDataComponentSelector('div', 'button-toggle-group');
        const buttonSelector = `${parentElement} ${selectorToUse} button[aria-label="${targetDate}"]`;

        const errorMessage = `Expected option could not be found: "${targetDate}". \nSelector: ${parentElement} ${selectorToUse}`;

        const toggleButton = await $(buttonSelector);

        if (!(await toggleButton.isExisting())) {
            throw new Error(errorMessage);
        }

        await this.timeState(buttonSelector, targetDate); // Check if the button is enabled

        try {
            await toggleButton.waitForExist();
            await toggleButton.waitForDisplayed();
            await toggleButton.scrollIntoView();
            await this.jsClick({
                cssSelector: toggleButton.selector.toString(),
                ignoreContext: true,
                skipVisibilityCheck: true,
            });
            await waitForPromises(500, 'Waiting for popup to open');
        } catch (error) {
            throw new Error(errorMessage);
        }
    }

    async selectDateRangeCheckbox(identifier: string, targetString: string, state: string): Promise<void> {
        await this.openDateRangePopup(identifier);
        const checkboxSelector = utils.getDataTestIdSelector({
            domSelector: 'input',
            dataTestIdValue: `e-datetime-input-${identifier}-date-picker-checkbox`,
        });

        const checkboxLabelSelector = `//label[text()="${targetString}"]`;
        const checkboxLabel = await $(checkboxLabelSelector);

        try {
            await checkboxLabel.waitForExist();

            const checkboxElement = await $(`${checkboxSelector}`);
            await checkboxElement.waitForExist();

            const checkboxValue = await checkboxElement.getProperty('checked');
            const shouldSelect = state === 'ticks';
            const isSelected = checkboxValue;

            if ((shouldSelect && !isSelected) || (!shouldSelect && isSelected)) {
                await checkboxElement.scrollIntoView();
                await checkboxElement.click();
                await $(this.cssSelector).scrollIntoView();
                await waitForPromises(300, 'Waiting for checkbox click');
            }
        } catch (error) {
            throw new Error(
                `Expected element could not be found: "${targetString}". \nSelector: ${this.cssSelector}${checkboxLabelSelector}\nError: ${error.message}`,
            );
        }
    }

    expectDateTimeRangeValue = async (startEnd: 'start' | 'end' | 'start and end', expectedValue: string) => {
        const dateTimeElement =
            startEnd === 'start and end' ? await $(this.cssSelector) : await $(SELECTORS.dateTime(startEnd));
        await utils.waitForElementToBeDisplayed({
            name: 'dateTimeRangeInput',
            selector: dateTimeElement.selector.toString(),
        });

        const currentValue =
            startEnd === 'start and end'
                ? (await dateTimeElement.getText()).replace(/\r?\n/g, ' ')
                : await dateTimeElement.getValue();
        if (currentValue !== expectedValue) {
            throw new Error(
                `Expected value: "${expectedValue}", actual: "${currentValue}".\nSelector: ${dateTimeElement.selector.toString()}`,
            );
        }
        await waitForPromises(500, 'waiting for dateTimeRange');
    };

    leaveFocusDateTimeRangeField = async () => {
        await browser.keys(Key.Escape);
        await waitForPromises(500, 'Waiting for lose focus');
    };

    clickTimeZoneField = async (startOrEnd: 'start' | 'end') => {
        const timeZoneFieldOpenButton = await $(
            utils.getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: `e-time-component-open-timezone-${startOrEnd.toLowerCase().trim()}`,
            }),
        );

        if (await timeZoneFieldOpenButton.isExisting()) {
            await timeZoneFieldOpenButton.moveTo();
            await timeZoneFieldOpenButton.click();
            await waitForPromises(300, 'waiting for time zone field button to be clicked');
        }

        const timeZoneField = await $(SELECTORS.timeZoneInput(startOrEnd));
        await timeZoneField.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "time zone field".\nSelector: ${timeZoneField.selector}`,
        });
        await timeZoneField.moveTo();
        await timeZoneField.click();
        await waitForPromises(300, 'waiting for time zone field to be clicked');
    };

    expectTimeZoneValue = async (startOrEnd: 'start' | 'end', expectedValue: string) => {
        const timeZone = await $(SELECTORS.timeZoneInput(startOrEnd));
        await utils.waitForElementToBeDisplayed({
            name: 'time zone field',
            selector: timeZone.selector.toString(),
        });

        const currentValue = await timeZone.getValue();
        if (currentValue !== expectedValue) {
            throw new Error(
                `Expected value: "${expectedValue}", actual: "${currentValue}".\nSelector: ${timeZone.selector.toString()}`,
            );
        }
    };
}
