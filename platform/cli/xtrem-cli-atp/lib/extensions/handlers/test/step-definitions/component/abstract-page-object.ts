/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/naming-convention */
import AllureReporter from '@wdio/allure-reporter';
import * as fs from 'fs';
import { tmpdir } from 'os';
import * as path from 'path';
import { Key, Selector } from 'webdriverio';
import { runtimeParameters } from '../../../../../parameters';
import { getHash } from '../hash';
import { compareImages } from '../image-diff';
import * as utils from '../step-definitions-utils';
import { scrollElementIntoMainView } from '../step-definitions-utils';
import { waitForPromises, waitMillis } from './field/wait-util';
import { testGlobals } from './test-globals';

export interface waitForDisplayedAndGetElementOptions {
    parent?: any;
    reverse?: boolean;
    ignoreContext?: boolean;
    selector: string;
    interval?: number;
    timeout?: number;
    timeoutMsg?: (selector: Selector) => string;
}

export default abstract class AbstractPageObject {
    /**
     * Construct an AbstractPageObject
     * @param cssSelector the CSS selector of the node that this AbstractPageObject instance is bound to, all other
     * functions will be based on this selector, unless their arguments instruct differently (eg. ignoreContext )
     * @param n if provided the n-th matching element will be used as the base node.
     */
    constructor(
        public cssSelector: string,
        public n?: number,
    ) {
        if (n) {
            this.cssSelector = `${cssSelector.trim()}:nth-of-type(${n})`;
        }
    }

    // set to global timeout -2 seconds or 58 seconds. Is used by other objects in waitFor functions
    protected timeoutWaitFor: number = utils.atpEnv.timeoutWaitFor;

    protected timeoutWaitForLoading: number = utils.atpEnv.timeoutWaitForLoading;

    // set to global timeout /2 or 4 seconds. Use for value checking
    protected valueCheckTimeout: number = utils.atpEnv.timeout;

    protected getSelectorForOperation(selector?: string, ignoreContext = false) {
        if (!selector) {
            return this.cssSelector;
        }
        if (ignoreContext) {
            return selector;
        }
        return `${this.cssSelector} ${selector}`;
    }

    /**
     * Get the WDIO node instance using the page object selector.
     */
    get() {
        return browser.$(this.cssSelector);
    }

    // eslint-disable-next-line class-methods-use-this
    protected async takePuppeteerPageScreenshot(screenshotOptions?: any): Promise<Buffer | string> {
        const puppeteerBrowser = await browser.getPuppeteer();
        return browser.call(async () => {
            const pages = await puppeteerBrowser.pages();
            return pages[0].screenshot(screenshotOptions);
        });
    }

    async write({
        content,
        cssSelector = 'input',
        ignoreContext = false,
    }: {
        content: string;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        await this.expectToAppear({ cssSelector, ignoreContext });
        await utils.waitForElementToExist({ name: 'text', selector: cssSelector });
        await this.click(cssSelector, ignoreContext);
        const selector = ignoreContext ? cssSelector : this.getSelectorForOperation(cssSelector);
        await (await browser.$(selector)).setValue(content);
    }

    async clearInput({
        ignoreContext = false,
        fieldType = utils.fieldTypes.text,
        cssSelector = 'input',
    }: {
        ignoreContext?: boolean;
        fieldType?: utils.FieldTypes;
        cssSelector?: string;
    } = {}) {
        await this.expectToAppear({ cssSelector, ignoreContext });

        switch (fieldType) {
            case utils.fieldTypes.numeric:
                await this.click(cssSelector, ignoreContext);
                await browser.keys([Key.Ctrl, 'a']);
                await browser.keys(Key.Backspace);
                break;
            case utils.fieldTypes.multiReference: {
                await this.click(cssSelector, ignoreContext);
                await AbstractPageObject.press('Escape');
                await $('[data-testid="e-ui-select-dropdown"]').waitForDisplayed({ reverse: true });
                const elements = await this.findAll(
                    '.e-ui-select-label [data-component="pill"] button[data-element="close"]',
                );
                // eslint-disable-next-line no-restricted-syntax
                for (const element of elements) {
                    await element.waitForClickable();
                    await element.moveTo();
                    await browser.execute(`document.querySelector('${element.selector}').click()`);
                    await browser.execute(`document.querySelector('${cssSelector}').click()`);
                    await AbstractPageObject.press('Escape');
                    await $('[data-testid="e-ui-select-dropdown"]').waitForDisplayed({ reverse: true });
                    await waitForPromises(300, 'clear field');
                }
                await AbstractPageObject.press('Escape');
                await $('[data-testid="e-ui-select-dropdown"]').waitForDisplayed({ reverse: true });
                await waitForPromises(300, 'clear field');

                break;
            }
            case utils.fieldTypes.reference: {
                await this.click(cssSelector, ignoreContext);
                const closeBtn = await this.find(
                    utils.getDataTestIdSelector({ domSelector: 'button', dataTestIdValue: 'e-ui-select-close' }),
                );
                const isCloseBtnVisible = await closeBtn.isDisplayed();
                if (isCloseBtnVisible) {
                    await this.click(closeBtn.selector as string, true);
                    await waitForPromises(300, 'clear field');
                }
                break;
            }
            case utils.fieldTypes.multiDropdown: {
                await this.click(cssSelector, ignoreContext);
                const dropDownElementSelector = `${this.cssSelector} ${utils.getDataTestIdSelector({
                    domSelector: 'ul',
                    dataTestIdValue: 'e-ui-select-dropdown',
                })} li ${utils.getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-ui-select-suggestion' })}`;
                const dropDownElement = await browser.$(`${this.cssSelector} .e-ui-select-inline-dropdown`);
                await dropDownElement.waitForDisplayed();
                const options = await $$(dropDownElementSelector);
                // eslint-disable-next-line no-restricted-syntax
                for (const opt of options) {
                    const input = await opt.$('input');
                    const isChecked = await input.isSelected();
                    if (isChecked === true) {
                        try {
                            await utils.waitForElementToExist({ name: 'input', selector: input.selector.toString() });
                            await utils.waitForElementToBeDisplayed({ name: 'opt', selector: opt.selector.toString() });
                            await opt.waitForClickable();
                            await input.moveTo();
                            await input.click();
                        } catch (error) {
                            throw new Error(error);
                        }
                        await waitForPromises(1000, 'clear field');
                    }
                }
                break;
            }
            default:
                await utils.scrollElementIntoMainView(this.getSelectorForOperation(cssSelector, ignoreContext));
                while ((await (await this.find(cssSelector, ignoreContext)).getValue()) !== '') {
                    await this.click(cssSelector, ignoreContext);
                    await browser.keys(Key.Backspace);
                }
        }

        await waitForPromises(500, 'clear field');

        // Handle closing the list/dropdown if applicable
        if (
            fieldType !== utils.fieldTypes.richText &&
            fieldType !== utils.fieldTypes.numeric &&
            fieldType !== utils.fieldTypes.text &&
            fieldType !== utils.fieldTypes.select &&
            fieldType !== utils.fieldTypes.reference &&
            fieldType !== utils.fieldTypes.filterSelect
        ) {
            await this.closeList();
        }
    }

    // https://github.com/puppeteer/puppeteer/issues/3241#issuecomment-1171770074
    static async assertClipboardValue(value: string) {
        const puppeteerBrowser = await browser.getPuppeteer();
        const pages = await puppeteerBrowser.pages();
        const page = pages[0];
        const client = await page.target().createCDPSession();
        await client.send('Browser.grantPermissions', {
            origin: undefined,
            permissions: ['clipboardReadWrite', 'clipboardSanitizedWrite'],
        });
        await browser.waitUntil(
            async () => {
                const clipboardText = await page.evaluate(() => navigator.clipboard.readText());
                return (clipboardText ?? null) === value;
            },
            {
                timeout: 5000,
                timeoutMsg: `Expected clipboard value: "${value}", actual: "${await page.evaluate(() => navigator.clipboard.readText())}".`,
            },
        );
    }

    static async press(keys: string) {
        await browser.keys(
            keys.split('+').reduce((acc, key) => {
                if (Key[key as keyof typeof Key]) {
                    acc.push(Key[key as keyof typeof Key]);
                } else {
                    acc.push(key);
                }
                return acc;
            }, [] as string[]),
        );
    }

    static async isFocused(selector: string) {
        const element = await $(selector);
        await element.waitForExist();
        await browser.waitUntil(async () => {
            const isFocused = await element.isFocused();
            return isFocused;
        });
    }

    async expectToMatchSnapshot(cssSelector?: string, ignoreContext = false) {
        try {
            await browser.execute(
                'window.document.body.style.setProperty("-webkit-font-smoothing", "none", "important");window.document.querySelectorAll("input").forEach(i=>i.style.setProperty("caret-color", "transparent", "important"));',
            );
            const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
            const selector = await browser.$(selectorToUse);
            await selector.waitForExist();
            await scrollElementIntoMainView(selectorToUse);

            await waitMillis(1000, 'scroll for snapshot');

            const puppeteerBrowser = await browser.getPuppeteer();
            const elem = await browser.call(async () => {
                const pages = await puppeteerBrowser.pages();
                return pages[0].$(selectorToUse);
            });

            const snapshotsRootDir = path.resolve(path.dirname(testGlobals._CURRENT_SCENARIO_PATH!), 'snapshots');
            if (!fs.existsSync(snapshotsRootDir)) {
                fs.mkdirSync(snapshotsRootDir);
            }

            const snapshotDir = path.resolve(snapshotsRootDir, path.parse(testGlobals._CURRENT_SCENARIO_PATH!).name);
            if (!fs.existsSync(snapshotDir)) {
                fs.mkdirSync(snapshotDir);
            }

            const hash = getHash(`${testGlobals._CURRENT_SCENARIO}_${testGlobals._CURRENT_STEP_ID}`);
            const fileName = `${hash}.png`;
            const scenarioFileName = `${hash}.txt`;
            const tempPath = path.resolve(tmpdir(), fileName);
            const filePath = path.resolve(snapshotDir, fileName);
            const scenarioFilePath = path.resolve(snapshotDir, scenarioFileName);

            const screenshot = (await elem?.screenshot({
                captureBeyondViewport: false,
                encoding: 'base64',
                path: tempPath,
            })) as string;
            const image = Buffer.from(screenshot, 'base64');

            fs.writeFileSync(tempPath, image);

            if (process.env.XTREM_UPDATE_SNAPSHOTS !== 'true' && fs.existsSync(filePath)) {
                await compareImages(filePath, tempPath);
            } else {
                fs.copyFileSync(tempPath, filePath);
                if (testGlobals._CURRENT_SCENARIO_STEPS) {
                    fs.writeFileSync(scenarioFilePath, testGlobals._CURRENT_SCENARIO_STEPS);
                }
                fs.unlinkSync(tempPath);
            }
        } catch (error) {
            console.log(error);
            throw error;
        }
    }

    // eslint-disable-next-line class-methods-use-this
    generateScreenshotFilePath() {
        console.log('PATH', (browser as any)._CURRENT_SCENARIO_PATH);

        const snapshotsRootDir = path.resolve(path.dirname((browser as any)._CURRENT_SCENARIO_PATH), 'snapshots');
        if (!fs.existsSync(snapshotsRootDir)) {
            fs.mkdirSync(snapshotsRootDir);
        }

        const snapshotDir = path.resolve(snapshotsRootDir, path.parse((browser as any)._CURRENT_SCENARIO_PATH).name);
        if (!fs.existsSync(snapshotDir)) {
            fs.mkdirSync(snapshotDir);
        }

        const hash = getHash(`${(browser as any)._CURRENT_SCENARIO}_${(browser as any)._CURRENT_STEP_ID}`);
        const fileName = `${hash}.png`;
        return path.resolve(snapshotDir, fileName);
    }

    async scrollTo({
        selector,
        ignoreContext = false,
        scrollBlock = 'center',
    }: {
        selector?: string;
        ignoreContext?: boolean;
        scrollBlock?: string;
    } = {}) {
        const selectorToUse = this.getSelectorForOperation(selector, ignoreContext);
        await browser.execute(
            `const node = document.querySelector('${selectorToUse}'); node ? node.scrollIntoViewIfNeeded() : true;`,
        );
        if (await (await $(selectorToUse)).isExisting())
            await (await browser.$(selectorToUse)).scrollIntoView({ block: scrollBlock as ScrollLogicalPosition });
    }

    /**
     * Find and return the WDIO node instance of that matches the CSS selector
     *
     * @param selector the CSS selector of the child node
     * @param ignoreContext if the flag is set to true, the search for the CSS selector will be done on a global scope,
     * instead of searching within the current element.
     */
    find(selector: string, ignoreContext = false) {
        const generatedSelector = this.getSelectorForOperation(selector, ignoreContext);
        return browser.$(generatedSelector);
    }

    /**
     * Find and return the WDIO node instance of that matches the CSS selector.
     * If element could not be found, then throws an error.
     *
     * @param selector the CSS selector of the child node
     * @param ignoreContext if the flag is set to true, the search for the CSS selector will be done on a global scope,
     * instead of searching within the current element.
     */
    async findOrFail(selector: string, ignoreContext = false) {
        const element = await browser.$(this.getSelectorForOperation(selector, ignoreContext));
        if (!element) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });

            throw new Error(`Could not find any element with the following selector: '${selector}'`);
        }
        return element;
    }

    /**
     * Find and return an array of WDIO node instances of that matche the CSS selector
     *
     * @param selector the CSS selector of the child node
     * @param ignoreContext if the flag is set to true, the search for the CSS selector will be done on a global scope,
     * instead of searching within the current element.
     */
    findAll(selector: string, ignoreContext = false) {
        return $$(this.getSelectorForOperation(selector, ignoreContext));
    }

    /**
     * Expect a text content of a subnode, selected by a CSS selector. This function will try to find node by the
     * selector, extract the text and compare it with the expected value.
     *
     * @param toBe expected value
     * @param ignoreCase whether to ignore string casing
     * @param cssSelector CSS selector of the sub node
     * @param ignoreContext if the flag is set to true, the search for the CSS selector will be done on a global scope,
     * instead of searching within the current element.
     */
    async expectTextContains({
        toBe,
        ignoreCase = false,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        ignoreCase?: boolean;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({ timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}` });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let textContent = '';
        try {
            await element.moveTo();
            await browser.waitUntil(
                async () => {
                    textContent = (await element.getText()).trim();
                    return (
                        textContent.includes(toBe) ||
                        (ignoreCase && textContent.toLocaleLowerCase().includes(toBe.toLocaleLowerCase()))
                    );
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: "${toBe}", actual: "${textContent}".\nSelector: ${selectorToUse}`);
        }
    }

    /**
     * Expect a text content of a subnode, selected by a CSS selector. This function will try to find node by the
     * selector, extract the text and compare it with the expected value.
     *
     * @param toBe expected value
     * @param ignoreCase whether to ignore string casing
     * @param cssSelector CSS selector of the sub node
     * @param ignoreContext if the flag is set to true, the search for the CSS selector will be done on a global scope,
     * instead of searching within the current element.
     */
    async expectValueContains({
        toBe,
        ignoreCase = false,
        cssSelector,
        ignoreContext = false,
        multiLine = false,
        ignoreWhitespace = false,
    }: {
        toBe: string;
        ignoreCase?: boolean;
        cssSelector?: string;
        ignoreContext?: boolean;
        multiLine?: boolean;
        ignoreWhitespace?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
            timeout: this.timeoutWaitFor,
        });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
            timeout: this.timeoutWaitFor,
        });
        if (multiLine) this.attachTextValue('Expected value', toBe);
        let textContent = '';
        try {
            await element.moveTo();
            await browser.waitUntil(
                async () => {
                    textContent = (await element.getValue()).trim();
                    if (multiLine) textContent = textContent.replace(' \n', '\n'); // In multiline values any trailing spaces are stripped when saving the feature.
                    let valueToCheck = toBe;
                    if (ignoreWhitespace) {
                        textContent = textContent.replace(/\s+/g, ' ');
                        valueToCheck = toBe.replace(/\s+/g, ' ');
                    }
                    return (
                        textContent.includes(valueToCheck) ||
                        (ignoreCase && textContent.toLocaleLowerCase().includes(valueToCheck.toLocaleLowerCase()))
                    );
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            const errorDetails = `Expected value: "${toBe}", actual: "${textContent}".\nSelector: ${selectorToUse}`;
            if (multiLine) this.attachTextValue('Error details', errorDetails.replace('",', '"\n'));
            throw new Error(errorDetails);
        }
    }

    /**
     * Expect a text content of a subnode, selected by a CSS selector. This function will try to find node by the
     * selector, extract the text and compare it with the expected value.
     *
     * @param toBe expected value
     * @param ignoreCase whether to ignore string casing
     * @param cssSelector CSS selector of the sub node
     * @param ignoreContext if the flag is set to true, the search for the CSS selector will be done on a global scope,
     * instead of searching within the current element.
     */
    async expectTextContent({
        toBe,
        ignoreCase = false,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        ignoreCase?: boolean;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await $(selectorToUse);
        await utils.waitForElementToExist({ name: 'text', selector: element.selector.toString() });
        await this.expectTextContentWithElement({ element, selectorToUse, toBe, ignoreCase });
    }

    async expectTextContentWithElement({
        element,
        selectorToUse,
        toBe,
        ignoreCase,
    }: {
        element: WebdriverIO.Element;
        selectorToUse: string;
        toBe: string;
        ignoreCase: boolean;
    }) {
        let textContent = '';
        try {
            await browser.waitUntil(
                async () => {
                    await element.waitForExist({ timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}` });
                    const parentElement = await browser.$(this.cssSelector);
                    if (await parentElement.isExisting()) {
                        const elemAtt = await parentElement.getAttribute('class');
                        if (!elemAtt.includes('e-hidden')) {
                            await element.waitForDisplayed({
                                timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
                            });
                        }
                    } else {
                        await element.waitForDisplayed({
                            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
                            timeout: this.timeoutWaitFor,
                        });
                    }
                    textContent = (await element.getText()).trim();
                    await waitForPromises(500, 'get text');
                    return (
                        (textContent || '').replace(/…/g, '...') === (toBe || '') ||
                        (ignoreCase &&
                            (textContent || '').toLowerCase().replace(/…/g, '...') ===
                            (toBe || '').toLowerCase().replace(/"/g, ''))
                    );
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected value: "${toBe}", actual: "${textContent}".\nSelector: ${selectorToUse}`,
                },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: "${toBe}", actual: "${textContent}".\nSelector: ${selectorToUse}`);
        }
    }

    async waitForDisplayedAndGetElement({
        parent,
        reverse = false,
        ignoreContext = false,
        selector,
        interval,
        timeout,
        timeoutMsg,
    }: waitForDisplayedAndGetElementOptions): Promise<WebdriverIO.Element> {
        const getElement = () =>
            parent ? parent.$(selector) : $(ignoreContext ? selector : `${this.cssSelector} ${selector}`);
        const element = await getElement();
        await element.waitForDisplayed({
            reverse,
            interval,
            timeout,
            timeoutMsg: timeoutMsg ? timeoutMsg(element.selector) : undefined,
        });
        return getElement();
    }

    async expectCheckedValue(toBe: string, cssSelector = 'input') {
        const selectorToUse = this.getSelectorForOperation(cssSelector);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
        });
        let result = '';

        try {
            await browser.waitUntil(
                async () => {
                    result = String(await element.isSelected());
                    return result != null && result === toBe;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            throw new Error(`Expected value: ${toBe}, actual: ${result}.\nSelector: ${selectorToUse}`);
        }
    }

    // eslint-disable-next-line class-methods-use-this
    getRegExResult(expectedValue: string, actualValue: string) {
        const expectedRegexValue = expectedValue
            .replace(/\\/g, '\\\\')
            .replace(/\./g, '\\.')
            .replace(/\?/g, '\\?')
            .replace(/\+/g, '\\+')
            .replace(/\(/g, '\\(')
            .replace(/\)/g, '\\)')
            .replace(/\[/g, '\\[')
            .replace(/\]/g, '\\]')
            .replace(/\$/g, '\\$')
            .replace(/\^/g, '\\^')
            .replace(/\|/g, '\\|')
            .replace(/\{/g, '\\{')
            .replace(/\}/g, '\\}')
            .replace(/\*/g, '.*');

        const expectedRegex = new RegExp(expectedRegexValue);
        return expectedRegex.test(actualValue);
    }

    async expectTextContainsPattern({
        toBe,
        ignoreCase = false,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        ignoreCase?: boolean;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
            timeout: this.timeoutWaitFor,
        });
        await element.waitForDisplayed({
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
            timeout: this.timeoutWaitFor,
        });
        this.attachTextValue('Expected value', `Expected value:\n${toBe}`);
        let textContent = '';
        try {
            await element.moveTo();
            await browser.waitUntil(
                async () => {
                    textContent = (await element.getText()).trim().replace(' \n', '\n'); // In multiline values any trailing spaces are stripped when saving the feature.
                    return (
                        this.getRegExResult(toBe, textContent) ||
                        (ignoreCase && this.getRegExResult(toBe.toLocaleLowerCase(), textContent.toLocaleLowerCase()))
                    );
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            const errorDetails = `Expected value: "${toBe}"\n\nActual value: "${textContent}"\n\nSelector: ${selectorToUse}`;
            this.attachTextValue('Error details', errorDetails);
            throw new Error(errorDetails);
        }
    }

    /**
     * This will check if element is displayed or not (i.e. the display property is not 'none')
     *
     * @param domElement Element to check if it is displayed or not in DOM
     * @param reverse if set to true, it will return check for non visibility
     */

    async expectToBeDisplayed(selector: string, reverse = false) {
        const element = await browser.$(selector);
        try {
            await browser.waitUntil(
                async () => {
                    if (reverse && !(await element.isExisting())) {
                        return true;
                    }

                    if (!(await element.isExisting())) {
                        return false;
                    }
                    if (!(await element.isDisplayed())) {
                        await element.moveTo();
                    }
                    await browser.execute(elem => elem.scrollIntoView(), element);
                    await utils.scrollElementIntoMainView(selector);
                    const cssProperty = await element.getCSSProperty('display');
                    return (reverse && cssProperty.value === 'none') || (!reverse && cssProperty.value !== 'none');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${selector},
                ${error}`,
            );
        }
    }

    async expectNotToBeDisplayed(selector: string) {
        await this.expectToBeDisplayed(selector, true);
    }

    async expectToNotExist(selector: string) {
        const elementSelector = await browser.$(selector);
        return elementSelector.waitForExist({
            reverse: true,
            timeout: this.timeoutWaitFor,
        });
    }

    /**
     * This will check if element is enabled or not
     *
     * @param domElement Element to check if it is enabled or not in DOM
     * @param reverse if set to true, it will return check for disable element
     */
    async expectToBeEnabled(selector: string, reverse = false) {
        const element = await browser.$(selector);
        await browser.waitUntil(
            async () => {
                const disabledAttribute = await element.getAttribute('disabled');
                return reverse ? disabledAttribute !== null : disabledAttribute == null || disabledAttribute !== '';
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be ${reverse ? 'disabled' : 'enabled'}.\nSelector: ${selector}`,
            },
        );
    }

    async expectToBeEnabledClass(selector: string, reverse = false) {
        const element = await browser.$(selector);
        await browser.waitUntil(
            async () => {
                const disabledAttribute = await element.getAttribute('class');
                return reverse === disabledAttribute.includes('e-disabled');
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be ${reverse ? 'disabled' : 'enabled'}.\nSelector: ${selector}`,
            },
        );
    }

    async expectToBeNestedEditable(selector: string, reverse = false) {
        const element = await browser.$(selector);
        await browser.waitUntil(
            async () => {
                const editableAttribute = await element.getAttribute('class');
                return reverse !== editableAttribute.includes('e-nested-cell-editable');
            },
            {
                timeout: this.valueCheckTimeout,
                timeoutMsg: `Expected Element to be ${reverse ? 'readonly' : 'editable'}.\nSelector: ${selector}`,
            },
        );
    }

    async expectNotToBeEnabled(selector: string) {
        await this.expectToBeEnabled(selector, true);
    }

    async expectValue({
        toBe,
        cssSelector,
        ignoreContext = false,
        multiLine = false,
    }: {
        toBe: string;
        cssSelector?: string;
        ignoreContext?: boolean;
        multiLine?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        await waitForPromises(500, 'wait for result values to be loaded');
        let element = await $(selectorToUse);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
        });

        if (multiLine) this.attachTextValue('Expected value', toBe);

        let value = '';
        let result;
        try {
            await browser.waitUntil(
                async () => {
                    element = await $(selectorToUse);
                    result = (await element.getValue()) as unknown as string[];
                    value = Array.isArray(result) ? result[0] : result;
                    if (multiLine) value = value.replace(' \n', '\n'); // In multiline values any trailing spaces are stripped when saving the feature.
                    return (value || '') === (runtimeParameters.getStringOrParameter(toBe) || '');
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected value: "${toBe}", actual: "${value}", selector: ${selectorToUse}`,
                },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            const errorDetails = `Expected value: "${toBe}", actual: "${value}".\nSelector: ${selectorToUse}`;
            if (multiLine) this.attachTextValue('Error details', errorDetails.replace('",', '"\n'));
            throw new Error(errorDetails);
        }
    }

    async expectBackgroundColor({
        toBe,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string | undefined;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
        });
        await element.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let value;
        try {
            await browser.waitUntil(
                async () => {
                    const property = await element.getCSSProperty('background-color');
                    value = property && property.parsed && property.parsed.hex;
                    return value === toBe;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: ${toBe}, actual: ${value}.\nSelector: ${selectorToUse}`);
        }
    }

    async expectBorderColor({
        toBe,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string | undefined;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
        });
        await element.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let value;
        try {
            await browser.waitUntil(
                async () => {
                    const property = await element.getCSSProperty('border-color');
                    value = property && property.parsed && property.parsed.hex;
                    return value === toBe;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: ${toBe}, actual: ${value}.\nSelector: ${selectorToUse}`);
        }
    }

    async expectTextColor({
        toBe,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string | undefined;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = await browser.$(selectorToUse);
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
        });
        await element.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let value;
        try {
            await browser.waitUntil(
                async () => {
                    const property = await element.getCSSProperty('color');
                    value = property && property.parsed && property.parsed.hex;
                    return value === toBe;
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Expected value: ${toBe}, actual: ${value}, selector: ${selectorToUse}`,
                },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: ${toBe}, actual: ${value}.\nSelector: ${selectorToUse}`);
        }
    }

    /**
     * Expect a text content of a subnode, selected by a CSS selector. This function will try to find node by the
     * selector, extract the text and compare it with the expected value.
     *
     * @param toBe expected value
     * @param ignoreCase whether to ignore string casing
     * @param selector CSS selector of the sub node
     * @param ignoreContext if the flag is set to true, the search for the CSS selector will be done on a global scope,
     * instead of searching within the current element.
     */
    async expectTitleAttribute({
        toBe,
        ignoreCase = false,
        cssSelector,
        ignoreContext = false,
    }: {
        toBe: string;
        ignoreCase?: boolean;
        cssSelector?: string;
        ignoreContext?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const selector = await browser.$(selectorToUse);
        await selector.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selectorToUse}`,
        });
        await selector.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}`,
        });
        let title = '';
        try {
            await browser.waitUntil(
                async () => {
                    title = await selector.getAttribute('title');
                    return title === toBe || (ignoreCase && title.toLowerCase() === toBe.toLowerCase());
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected value: ${toBe}, actual: ${title}.\nSelector: ${selectorToUse}`);
        }
    }

    /**
     * Ensure that the element exists, enabled and visible and then simulates a mouse click event.
     *
     * @param cssSelector if provided the click event will be triggered on the first matched subnode.
     */
    async click(cssSelector?: string, ignoreContext = false) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const selector = await browser.$(selectorToUse);
        await selector.waitForDisplayed({ timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}` });
        await utils.scrollElementIntoMainView(selectorToUse);
        await selector.click();
    }

    async doubleClick(cssSelector?: string, ignoreContext = false) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const selector = await browser.$(selectorToUse);
        await selector.waitForDisplayed({ timeoutMsg: `Element not displayed.\nSelector: ${selectorToUse}` });
        await selector.waitForClickable();
        await selector.doubleClick();
    }

    // eslint-disable-next-line class-methods-use-this
    async loseFocus() {
        await browser.keys(Key.Tab);
    }

    async closeList() {
        if (
            this.cssSelector.includes(utils.getContextSelector(utils.ElementContext.MODAL)) ||
            (this.cssSelector.includes(utils.getNestedFieldType(utils.fieldTypes.dynamicSelect)) &&
                this.cssSelector.includes(utils.getNestedFieldType(utils.fieldTypes.dynamicPod)))
        ) {
            await browser.keys(Key.Tab);
        } else {
            await browser.keys(Key.Escape);
        }
    }

    async loseFocusForHelperText() {
        await waitForPromises(200, 'helper text (before change of focus)');
        await this.loseFocus();
    }

    /**
     * Expect a node to appear. It waits 10 seconds for the node to appear before throwing an error.
     *
     * @param selector if provided, it will wait for a subnode matched by the CSS selector.
     */
    async expectToAppear({
        cssSelector,
        ignoreContext = false,
        reverse = false,
    }: {
        cssSelector?: string;
        ignoreContext?: boolean;
        reverse?: boolean;
    } = {}) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        try {
            await browser.waitUntil(
                async () => {
                    const isDisplayed: any = await browser.execute(
                        `return document.querySelector('${selectorToUse}') !== null;`,
                    );
                    return (isDisplayed && !reverse) || (!isDisplayed && reverse);
                },
                {
                    timeout: this.valueCheckTimeout,
                    timeoutMsg: `Element expected to ${reverse ? 'dis' : ''}appear in ${this.valueCheckTimeout} ms: (selector: ${selectorToUse})`,
                },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Element expected to ${reverse ? 'dis' : ''}appear in ${this.valueCheckTimeout} ms: (selector: ${selectorToUse})`,
            );
        }
    }

    expectToDisappear(cssSelector?: string, ignoreContext = false): Promise<void> {
        return this.expectToAppear({ cssSelector, ignoreContext, reverse: true });
    }

    async expectToBeReady(cssSelector?: string, ignoreContext = false): Promise<void> {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        try {
            await browser.waitUntil(
                async () => {
                    const element = await this.find(selectorToUse, ignoreContext);
                    return (await element.isExisting()) && (await element.isDisplayed()) && element.isEnabled();
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Element expected to be ready in ${this.timeoutWaitFor} ms: (selector: ${selectorToUse})`);
        }
    }

    /**
     * Click a node using a simulated JavaScript event. For some frameworks, simple click events just don't work, in
     * such cases this helper function can be very handy
     *
     * @param selector the CSS selector of the element to which the click event is triggered to.
     */
    async jsClick({
        cssSelector,
        ignoreContext = false,
        skipVisibilityCheck = false,
    }: {
        cssSelector?: string;
        ignoreContext?: boolean;
        skipVisibilityCheck?: boolean;
    }) {
        const selectorToUse = this.getSelectorForOperation(cssSelector, ignoreContext);
        const element = (await browser.$(this.getSelectorForOperation(cssSelector, ignoreContext))) as any;
        if (!skipVisibilityCheck) {
            await element.waitForExist();
            await element.waitForVisible();
        }
        await browser.execute(`document.querySelector('${selectorToUse}').click()`);
    }

    async expectToBeReadOnly({
        selector,
        FieldType,
        reverse = false,
    }: {
        selector: string;
        FieldType?: utils.FieldTypes;
        reverse?: boolean;
    }) {
        const displayedSelector = `${FieldType ? utils.getElementTypeSelector(FieldType) : ''}${
            FieldType === utils.fieldTypes.checkbox ? ',.e-checkbox-field-read-only' : ''
        }`;

        const displayedElement = await browser.$(
            `${FieldType === utils.fieldTypes.dynamicSelect ? `${selector}` : `${selector} ${displayedSelector}`}`,
        );

        await displayedElement.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selector} ${displayedSelector}`,
        });
        await displayedElement.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element not displayed.\nSelector: ${selector} ${displayedSelector}`,
        });

        let selectorToUse = '';

        switch (FieldType) {
            case utils.fieldTypes.checkbox:
                selectorToUse = '.e-checkbox-field-read-only';
                break;
            case utils.fieldTypes.switch:
                selectorToUse = 'input[readonly]';
                break;
            case utils.fieldTypes.richText:
                selectorToUse = 'e-field-read-only';
                break;
            case utils.fieldTypes.image:
            case utils.fieldTypes.codeEditor:
            case utils.fieldTypes.graphiqlEditor:
            case utils.fieldTypes.dropdownList:
            case utils.fieldTypes.time:
            case utils.fieldTypes.dateTimeRange:
            case utils.fieldTypes.dynamicSelect:
                selectorToUse = 'e-read-only';
                break;
            default:
                selectorToUse = 'readonly';
        }

        try {
            await browser.waitUntil(
                async () => {
                    let readonlyState: boolean;
                    switch (FieldType) {
                        case utils.fieldTypes.image:
                        case utils.fieldTypes.codeEditor:
                        case utils.fieldTypes.graphiqlEditor:
                        case utils.fieldTypes.dropdownList:
                        case utils.fieldTypes.time:
                        case utils.fieldTypes.dateTimeRange:
                            readonlyState = (await (await browser.$(selector)).getAttribute('class')).includes(
                                selectorToUse,
                            );
                            break;
                        case utils.fieldTypes.richText:
                        case utils.fieldTypes.dynamicSelect:
                            readonlyState = (await displayedElement.getAttribute('class')).includes(selectorToUse);
                            break;
                        case utils.fieldTypes.checkbox:
                        case utils.fieldTypes.switch:
                            readonlyState = await (
                                await (await browser.$(selector)).$(`${selectorToUse}`)
                            ).isExisting();
                            break;
                        default:
                            readonlyState = (await displayedElement.getAttribute(selectorToUse)) !== null;
                    }
                    return readonlyState !== reverse;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Expected Element ${reverse ? 'not ' : ''}to be read-only.\nSelector: ${selector} ${selectorToUse}`,
            );
        }
    }

    async expectToBeMandatory(selector: string) {
        const selectorElement = await browser.$(selector);
        await selectorElement.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element does not exist.\nSelector: ${selector}`,
        });
        await selectorElement.waitForDisplayed({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Element not displayed.\nSelector: ${selector}`,
        });
        try {
            await browser.waitUntil(
                async () => {
                    const fieldLabel = await selectorElement.getText();
                    return fieldLabel.endsWith('*');
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected Element to be mandatory.\nSelector: ${selector}`);
        }
    }

    /**
     * Returns the CSS selector based on the selector type and selector. Given an invalid selector type,
     * the function throws a descriptive error.
     *
     * @param selectorType the type of selector to use ("class", "test id" or "css selector")
     * @param selector the CSS selector of the element.
     */
    // eslint-disable-next-line class-methods-use-this
    getCssSelector(selectorType: 'class' | 'test id' | 'css selector', selector: string) {
        switch (selectorType) {
            case 'class':
                return `.${selector}`;
            case 'test id':
                return `[data-testid~="${selector}"]`;
            case 'css selector':
                return selector;
            default:
                throw new Error(
                    `Expected selector to be "class", "test id" or "css selector".\nSelector Type: ${selectorType}`,
                );
        }
    }

    async dismissAllNotification() {
        const selectorToUse = utils.getDataTestIdSelector({
            domSelector: 'div',
            dataTestIdValue: 'xe-notification-card-preview',
        });
        const closeButtonSelector = `${selectorToUse} button[data-element="close"]`;
        if ((await this.findAll(selectorToUse, true)).length !== 0) {
            await browser.waitUntil(
                async () => {
                    const closeButtons = await this.findAll(closeButtonSelector, true);
                    await Promise.all(closeButtons.map(b => b.click()));
                    return (await this.findAll(selectorToUse, true)).length === 0;
                },
                // long timeout
                { timeout: this.timeoutWaitFor, interval: 2000 },
            );
        }
    }

    async validateCardSelection(selection: 'selected' | 'unselected') {
        const selectorToUse = `${this.cssSelector} [data-testid="e-card"] input`;
        const valued = selection === 'selected';

        if ((await this.findAll(selectorToUse, true)).length !== 0) {
            const checkBox = await this.findAll(selectorToUse, true);

            const results = await Promise.all(
                checkBox.map(b => {
                    try {
                        return b.isSelected(); // Replace null with 'false'
                    } catch (error) {
                        console.error(`Error getting attribute for checkbox: ${error}`);
                        return Promise.resolve(false); // Return 'false' if there's an error
                    }
                }),
            );

            const mismatch = results.filter(value => value !== valued);
            if (mismatch.length > 0) {
                throw new Error(`Expected to be all ${selection}.\nSelector: ${selectorToUse}`);
            }
        }
    }

    async findElementByTextContent({
        cssSelector,
        ignoreCase = false,
        text,
    }: {
        cssSelector: string;
        ignoreCase: boolean;
        text: string;
    }) {
        const elements = await this.findAll(cssSelector);
        for (let i = 0; i < elements.length; i += 1) {
            const textContent = await elements[i].getText();
            if (ignoreCase && textContent.toLowerCase() === text.toLowerCase()) {
                return elements[i];
            }

            if (!ignoreCase && textContent === text) {
                return elements[i];
            }
        }

        throw new Error(`Option with text content ${text} not found.`);
    }

    // eslint-disable-next-line class-methods-use-this
    setLoginState(state: boolean) {
        testGlobals.isLogged = state;
    }

    // eslint-disable-next-line class-methods-use-this
    getLoginState(): boolean {
        return testGlobals.isLogged || false;
    }

    // graphql
    attachTextValue(attachmentName: string, textContent: string) {
        if (attachmentName.toLowerCase().includes('password')) {
            // eslint-disable-next-line no-param-reassign
            textContent = '*'.repeat(textContent.length);
        }
        this.attachAllureFile(attachmentName, textContent);
    }

    // eslint-disable-next-line class-methods-use-this
    attachAllureFile(attachmentName: string, fileContent: string | object | Buffer, fileType = 'text/plain') {
        AllureReporter.addAttachment(attachmentName, fileContent, fileType);
    }

    // eslint-disable-next-line class-methods-use-this
    findLatestFile(foundFiles: string[]): string {
        let largest = 0;
        let latestFile = '';
        foundFiles.forEach(file => {
            const stats = fs.statSync(file);
            if (largest < stats.birthtimeMs) {
                largest = stats.birthtimeMs;
                latestFile = file;
            }
        });
        console.log('latestfile : ', latestFile);
        return latestFile;
    }

    async expectDropdownMenuElements(expectedElements: string[]) {
        const dropdownMenuSelector = '[data-component="action-popover"] li';
        const menuElements = await this.findAll(dropdownMenuSelector, true);
        let i: number = 0;
        // eslint-disable-next-line no-restricted-syntax
        for (const el of menuElements) {
            if (expectedElements[i] !== 'divider') {
                await this.expectTextContentWithElement({
                    element: el,
                    selectorToUse: el.selector.toString(),
                    toBe: expectedElements[i],
                    ignoreCase: false,
                });
            } else {
                const dataElement = await el.getAttribute('data-element');
                const isDivider = dataElement?.includes('divider');
                if (!isDivider) {
                    throw new Error(
                        `Expected a "divider" to be displayed between "${expectedElements[i - 1]}" and "${expectedElements[i + 1]}".\nSelector: ${el.selector.toString()}`,
                    );
                }
            }
            i += 1;
        }
    }
}
