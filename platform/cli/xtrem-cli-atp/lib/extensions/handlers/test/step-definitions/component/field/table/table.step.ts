import { DataTable, Then, When } from '@cucumber/cucumber';
import * as utils from '../../../step-definitions-utils';
import * as StaticStore from '../../static-store';
import { waitForPromises } from '../wait-util';
import { TableConfigObject } from './table-configuration-object';
import { SelectedRowDetails, getSelectedRow, getTableObject } from './table-nested-field/table-nested-field-utils';
import { TableObject } from './table-object';
import { scrollToTableColumn } from './tableUtils';

When(
    /^the user selects the "(.*)" (bound|labelled) table field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, context: utils.ElementContext) => {
        const table = new TableObject({
            identifier,
            lookupStrategy,
            context,
        });
        await table.dismissAllNotification();
        await table.expect.waitForTableStopLoading();
        await table.validate.checkStateofTable(identifier);
        StaticStore.storeObject(StaticStore.StoredKeys.TABLE, table);
    },
);

When(
    /^the user selects the "(.*)" (bound|labelled) summary table field on (the main page|a modal|a full width modal|the sidebar|the navigation panel|the detail panel)$/,
    async (identifier: string, lookupStrategy: utils.LookupStrategy, context: utils.ElementContext) => {
        const summaryTable = new TableObject({
            identifier,
            lookupStrategy,
            context,
            subtype: 'table-summary',
        });
        await utils.waitForElementToBeFound({
            name: `"${identifier}" summary table field`,
            selector: summaryTable.cssSelector,
        });
        StaticStore.storeObject(StaticStore.StoredKeys.TABLE, summaryTable);
    },
);

Then(
    /^the user (opens|closes) the filter of the "(.*)" (bound|labelled) column in the table field$/,
    async (state: 'opens' | 'closes', columnName: string, lookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
        if (state === 'opens') {
            await table.filter.openFilter(columnName, lookupStrategy);
        } else {
            await table.filter.closeFilter();
        }
    },
);

Then(/^the user searches "(.*)" in the filter of the table field$/, async (searchValue: string) => {
    const table = getTableObject();
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(searchValue);
    await table.expect.waitForTableStopLoading();
    await table.filter.searchFilter(storeValue);
    await waitForPromises(500, 'Filtering table');
});

Then(/^the search value of the filter in the table field is "(.*)"$/, async (expectedValue: string) => {
    const table = getTableObject();
    const storeValue = StaticStore.getUserdefinedKeyValueFromStore(expectedValue);
    await table.filter.expectCustomReferenceFilterValue(storeValue);
});

Then(
    /^the user (ticks|unticks) the item with text "(.*)" in the filter of the table field$/,
    async (tickState: utils.TickStateType, itemText: string) => {
        const table = getTableObject();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(itemText);
        await table.expect.waitForTableStopLoading();
        await table.filter.tickFilterItem(storeValue, tickState);
        await waitForPromises(500, 'Filtering table');
    },
);

When(
    /^the user filters the "([^"\n\r]*)" (bound|labelled) column in the table field with filter type "(.*)"$/,
    async (columnName: string, lookupStrategy: utils.LookupStrategy, filter: string) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
        await table.filter.filterColumn({ id: columnName, lookupStrategy, filter });
        await waitForPromises(500, 'Filtering table');
    },
);

When(
    /^the user filters the "([^"\n\r]*)" (bound|labelled) column in the table field with value "(.*)"$/,
    async (columnName: string, lookupStrategy: utils.LookupStrategy, value: string) => {
        const table = getTableObject();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await table.expect.waitForTableStopLoading();
        await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
        await table.filter.filterColumn({ id: columnName, lookupStrategy, value: storeValue });
        await waitForPromises(500, 'Filtering table');
    },
);

When(
    /^the user filters the "([^"]+)" (bound|labelled) column in the table field with filter "([^"]+)" and value "([^"]+)"$/,
    async (columnName: string, lookupStrategy: utils.LookupStrategy, filter: string, value: string) => {
        const table = getTableObject();
        const storeValue = StaticStore.getUserdefinedKeyValueFromStore(value);
        await table.expect.waitForTableStopLoading();
        await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
        await table.filter.filterColumn({ id: columnName, lookupStrategy, value: storeValue, filter });
        await waitForPromises(500, 'Filtering table');
    },
);

Then('the user clicks the remove all filters button in the table field', async () => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    await table.filter.clickClearFloatingFilterButton();
});

Then(/^the option menu of the table field is displayed$/, async () => {
    const table = getTableObject();
    await table.expect.waitForOptionMenuToBeDisplayed();
});

Then(/^the user clicks the option menu of the table field$/, async () => {
    const table = getTableObject();
    await table.tableClick.clickOptionMenu();
});

Then(/^the user clicks the "(.*)" value in the option menu of the table field$/, async (input: string) => {
    const table = getTableObject();
    await table.tableClick.selectOptionMenuValue(input);
});

Then(
    /^the user clicks the "(.*)" (bound|labelled) business action button of the table field$/,
    async (buttonName: string, lookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await table.action.clickBusinessActionButton(buttonName, lookupStrategy);
    },
);

Then(
    /^the user clicks the "(.*)" (bound|labelled) header action button of the table field$/,
    async (buttonName: string, lookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await table.dismissAllNotification();
        await table.action.clickHeaderActionButton(buttonName, lookupStrategy);
    },
);

Then(
    /^the (First|Next|Previous|Last) page button of the table field is (enabled|disabled)$/,
    async (buttonName: utils.TableButton, cssState: 'enabled' | 'disabled') => {
        const table = getTableObject();
        await table.expect.expectPageButtonToBeEnabled(buttonName, cssState === 'disabled');
    },
);

Then(/^the page number of the table field is "(.*)"$/, async (expectedPageNumber: string) => {
    const table = getTableObject();
    await table.expect.expectPageNumberToBe(expectedPageNumber);
});

Then(/^the summary row paging information of the table field is "(.*)"$/, async (expectedSummaryRow: string) => {
    const table = getTableObject();
    await table.expect.expectSummaryRowToBe(expectedSummaryRow);
});

When(/^the user clicks the (next|previous) page button of the table field$/, async (page: 'next' | 'previous') => {
    const table = getTableObject();
    await table.tableClick.stepToPage(page);
});

Then(/^the user selects the "(.*)" tab of the table field$/, async (tabName: string) => {
    const table = getTableObject();
    await table.filter.clickFilterTab(tabName);
});

Then(
    /^the "(.*)" (bound|labelled) bulk action button of the table field is (displayed|hidden)$/,
    async (buttonIdentifier: string, buttonLookupStrategy: utils.LookupStrategy, cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.action.expectActionButtonToBeDisplayed({
            id: buttonIdentifier,
            lookupStrategy: buttonLookupStrategy,
            reverse: cssState === 'hidden',
        });
    },
);

Then(
    /^the user clicks the "(.*)" (bound|labelled) bulk action button of the table field$/,
    async (buttonIdentifier: string, buttonLookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await table.action.clickBulkActionButton(buttonIdentifier, buttonLookupStrategy);
    },
);

Then(/^the user clicks on the "Clear content" action of the floating row in the table field$/, async () => {
    const table = getTableObject();
    await table.action.resetFloatingRow();
});

Then(/^the user clicks on the "Confirm" action of the floating row in the table field$/, async () => {
    const table = getTableObject();
    await table.action.confirmFloatingRow();
});

Then(
    /^the clear selection button on the bulk action bar of the table field is (displayed|hidden)$/,
    async (cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.expect.expectClearSelectionToBeDisplayed(cssState === 'hidden');
    },
);

Then(/^the user clicks the clear selection button on the bulk action bar of the table field$/, async () => {
    const table = getTableObject();
    await table.tableClick.clickClearSelection();
});

Then(/^the bulk action bar of the table field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const table = getTableObject();
    await table.expect.expectBulkActionBarToBeDisplayed(cssState === 'hidden');
});

Then(/^the bulk action bar of the table field has ([0-9]*) items$/, async (expectedNumberOfItems: number) => {
    const table = getTableObject();
    await table.expect.expectNumberOfItemsSelectedOnBulkActionBar(expectedNumberOfItems);
});

When(
    /^the user clicks the "(Calendar|Columns display|View mode|Filter)" button in the header of the table field$/,
    async (buttonName: utils.ButtonName) => {
        const table = getTableObject();
        await table.columns.clickOnHeaderButton(buttonName);
    },
);

When(/^the user selects the columns "(.*)" to display in the table field$/, async (columnIdentifier: string) => {
    const table = getTableObject();
    await table.columns.selectColumn(columnIdentifier);
});

Then(
    /^the "(.*)" (bound|labelled) column in the table field is (displayed|hidden)$/,
    async (
        columnIdentifiers: string,
        columnLookupStrategy: utils.LookupStrategy,
        displayed: utils.DisplayedOrHidden,
    ) => {
        const table = getTableObject();
        const reverse = displayed === utils.DisplayedOrHidden.HIDDEN;
        await table.expect.waitForTableStopLoading();
        await table.columns.expectColumnsToBeDisplayed({
            id: columnIdentifiers,
            lookupStrategy: columnLookupStrategy,
            reverse,
        });
    },
);

When(
    /^the user clicks the "(.*)" (bound|labelled) column of the table field$/,
    async (columnIdentifier: string, columnLookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        const headerSelector = table.filter.getHeaderSelector(columnIdentifier, columnLookupStrategy);
        const header = await table.filter.getHeader(columnIdentifier, columnLookupStrategy);
        await utils.scrollElementIntoMainView(`${headerSelector} div`);
        const $header = await header.$('div');
        await $header.click();
        await waitForPromises(500, 'click header');
    },
);

Then(/^the table field is (empty|not empty)$/, async (cssState: 'empty' | 'not empty') => {
    const table = getTableObject();
    await table.expect.expectTableToBeEmpty(cssState === 'not empty');
});

When(/^the user adds a new table row to the table field$/, async () => {
    const table = getTableObject();
    await table.rows.clickAddRowButton();
});

When(/^the user adds a new table row to the table field using the sidebar$/, async () => {
    const table = getTableObject();
    await table.rows.clickAddWithSidebar();
});

When(
    /^the user clicks the "(.*)" (bound|labelled) button of the table field$/,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (btnIdentifier: string, _LookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await table.dismissAllNotification();
        await table.action.clickButton(btnIdentifier);
        await waitForPromises(500, 'Wait for button click');
    },
);

When(/^the user (filters|unfilters) by errors the table field$/, async (filterState: 'filters' | 'unfilters') => {
    const table = getTableObject();
    if (filterState === 'filters') {
        await table.filter.filterByErrors();
    } else {
        await table.filter.unfilterByErrors();
    }
});

Then(/^a total of "(.*)" errors is found in the table field$/, async (expectedErrors: string) => {
    const table = getTableObject();
    await table.expect.expectNumberOfErrorsToBe(expectedErrors);
});

When(/^the user filters the mobile lookup table with "(.*)" query$/, async (content: string) => {
    const table = getTableObject();
    const cssSelector = '.e-table-field-mobile-search input';
    await table.write({ content, cssSelector });
    await waitForPromises(500, 'Wait for filter');
});

Then(/^the user (?:un)?selects all rows of the table field$/, async () => {
    const table = getTableObject();
    await table.action.enableSelectAll();
});

Then(
    /^the "(.*)" (bound|labelled) header action button of the table field is (displayed|hidden)$/,
    async (buttonName: string, buttonLookupStrategy: utils.LookupStrategy, cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.action.expectHeaderActionButtonToBeDisplayed({
            id: buttonName,
            lookupStrategy: buttonLookupStrategy,
            reverse: cssState === 'hidden',
        });
    },
);

Then(
    /^the "(.*)" (bound|labelled) button of the table field is (displayed|hidden)$/,
    async (buttonName: string, _buttonLookupStrategy: utils.LookupStrategy, cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.action.expectButtonToBeDisplayed(buttonName, cssState === 'hidden');
    },
);

Then(
    /^the "(.*)" (bound|labelled) header action button of the table field is (enabled|disabled)$/,
    async (buttonName: string, buttonLookupStrategy: utils.LookupStrategy, expectedValue: utils.EnabledState) => {
        const table = getTableObject();
        await table.action.expectHeaderActionButtonEnabledState({
            id: buttonName,
            lookupStrategy: buttonLookupStrategy,
            expectedState: expectedValue,
        });
    },
);

Then(
    /^the "(.*)" (bound|labelled) button of the table field is (enabled|disabled)$/,
    async (buttonName: string, _buttonLookupStrategy: utils.LookupStrategy, expectedValue: utils.EnabledState) => {
        const table = getTableObject();
        await table.action.expectButtonEnabledState(buttonName, expectedValue);
    },
);

Then(/^the value of the inline action tooltip in the table field is "(.*)"$/, async (value: string) => {
    const table = getTableObject();
    await table.expect.expectTooltipValue(value);
});

Then(
    /^the (group by|ungroup) option in the header menu of the "(.*)" (bound|labelled) column of the table field is (displayed|hidden)$/,
    async (
        groupOrUngroup: 'group by' | 'ungroup',
        columnIdentifier: string,
        columnLookupStrategy: utils.LookupStrategy,
        displayedOrHidden: 'displayed' | 'hidden',
    ) => {
        const table = getTableObject();
        if (groupOrUngroup === 'group by') {
            await table.expect.expectGroupByToBeDisplayed({
                id: columnIdentifier,
                lookupStrategy: columnLookupStrategy,
                reverse: displayedOrHidden === 'hidden',
            });
        } else {
            await table.expect.expectUnGroupToBeDisplayed({
                id: columnIdentifier,
                lookupStrategy: columnLookupStrategy,
                reverse: displayedOrHidden === 'hidden',
            });
        }
        await waitForPromises(500, 'Wait for group');
    },
);

Then(
    /^the user clicks the (?:(group by)(?: (year|month|day))?|(ungroup)) option in the header menu of the "(.*)" (bound|labelled) column of the table field$/,
    async (
        group: 'group by' | null,
        aggregation: utils.TimeUnits,
        ungroup: 'ungroup' | null,
        columnIdentifier: string,
        columnLookupStrategy: utils.LookupStrategy,
    ) => {
        const table = getTableObject();
        if (group) {
            await table.tableClick.clickOnGroupByOption({
                id: columnIdentifier,
                lookupStrategy: columnLookupStrategy,
                time: aggregation,
            });
        } else if (ungroup) {
            await table.tableClick.clickOnUnGroupOption(columnIdentifier, columnLookupStrategy);
        }
    },
);

Then(/^the value of the option menu of the table field is "(.*)"$/, async (value: string) => {
    const table = getTableObject();
    await table.expect.expectOptionMenuValue(value);
});

Then(/^the table field is (valid|invalid)$/, async (valid: 'valid' | 'invalid') => {
    const table = getTableObject();
    await table.validate.checkValidity(valid === 'valid');
});

When(
    /^the user clicks the "(.*)" (bound|labelled) action of the table field$/,
    async (actionId: string, actionLookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await table.clickFieldAction(actionLookupStrategy, actionId);
    },
);

When(
    /^the user clicks the "(.*)" (bound|labelled) add action of the table field$/,
    async (actionId: string, actionLookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await table.rows.clickAddItemButton(actionId, actionLookupStrategy);
    },
);

Then(/^the title of the table field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const table = getTableObject();
    await table.expectTitleToBeDisplayed(cssState === 'hidden');
});

Then(/^the helper text of the table field is "(.*)"$/, async (value: string) => {
    const table = getTableObject();
    await table.expectHelperText(value);
});

Then(
    /^the clear selected items link in the filter menu of the "(.*)" (bound|labelled) column of the table field is (enabled|disabled)$/,
    async (columnName: string, lookupStrategy: utils.LookupStrategy, cssState: 'enabled' | 'disabled') => {
        const table = getTableObject();
        await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
        await table.expect.expectClearSelectedItemsLinkToBeEnabled({
            id: columnName,
            lookupStrategy,
            reverse: cssState === 'disabled',
        });
    },
);

Then(
    /^the user clicks the clear selected items link in the filter menu of the "(.*)" (bound|labelled) column of the table field$/,
    async (columnName: string, lookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await scrollToTableColumn({ tableSelector: table.cssSelector, columnName, lookupStrategy });
        await table.tableClick.clickClearSelectedItems(columnName, lookupStrategy);
    },
);

Then(
    /^the floating filter value of the "([^"\n\r]*)" (bound|labelled) column of the table field is "(.*)"$/,
    async (columnIdentifier: string, columnLookupStrategy: utils.LookupStrategy, expectedValue: string) => {
        const table = getTableObject();
        await table.filter.expectFloatingFilterValue({
            id: columnIdentifier,
            lookupStrategy: columnLookupStrategy,
            value: expectedValue,
        });
    },
);

Then(
    /^the floating filter of the "([^"\n\r]*)" (bound|labelled) column of the table field is (enabled|disabled)$/,
    async (columnName: string, columnLookupStrategy: utils.LookupStrategy, cssState: 'enabled' | 'disabled') => {
        const table = getTableObject();
        await scrollToTableColumn({
            tableSelector: table.cssSelector,
            columnName,
            lookupStrategy: columnLookupStrategy,
        });
        await table.filter.expectFloatingFilterEnabled({
            id: columnName,
            lookupStrategy: columnLookupStrategy,
            reverse: cssState === 'disabled',
        });
    },
);

/* -------------------------------step definitions to keep ------------------------------*/
Then(
    /^the table column configuration with name "(.*)" on the sidebar is (ticked|unticked)$/,
    async (configName: string, ticked: string) => {
        const optionConfigItem = new TableConfigObject(configName);
        await optionConfigItem.tickCheck(ticked);
    },
);

Then(
    /^the user (ticks|unticks) the table column configuration with "(.*)" name on the sidebar$/,
    async (ticked: string, configName: string) => {
        const optionConfigItem = new TableConfigObject(configName);
        await optionConfigItem.tick(ticked);
    },
);

Then(
    /^the table column configuration on the sidebar are displayed in the following order "(.*)"$/,
    async (list: string) => {
        await TableConfigObject.checkListOrder(list);
    },
);

Then(
    /^the table column configuration with name "(.*)" on the sidebar is (locked|unlocked)$/,
    async (configName: string, lock: string) => {
        const optionConfigItem = new TableConfigObject(configName);
        await optionConfigItem.isLocked(lock);
    },
);

Then(/^the summary table field is (enabled|disabled)$/, async (cssState: 'enabled' | 'disabled') => {
    const table = getTableObject();
    await table.expectTableToBeEnabled(cssState === 'disabled');
});

Then(/^the title of the summary table field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const table = getTableObject();
    await table.expectTitleToBeDisplayed(cssState === 'hidden');
});

Then(/^the title of the summary table field is "(.*)"$/, async (expectedTitle: string) => {
    const table = getTableObject();
    await table.expectTitle(expectedTitle);
});

Then(/^the helper text of the summary table field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const table = getTableObject();
    await table.expectTableHelperTextToBeDisplayed(cssState === 'hidden');
});

Then(/^the helper text of the summary table field is "(.*)"$/, async (expectedHelperText: string) => {
    const table = getTableObject();
    await table.expectTableHelperText(expectedHelperText);
});

Then(
    /^the user checks if the "(.*)" (bound|labelled) column in the table field header is focused$/,
    async (columnIdentifier: string, columnLookupStrategy: utils.LookupStrategy) => {
        const table = getTableObject();
        await table.filter.getColumnHeader(columnIdentifier, columnLookupStrategy);
        await table.expectFocusToBeVisible();
    },
);

When(/^the user clicks the (csv|excel) export button of the table field$/, async (exportType: 'csv' | 'excel') => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    await table.tableClick.clickExportButton(exportType);
});

Then(
    /^the "(.*)" (bound|labelled) column in the table field contains (errors|no error)$/,
    async (columnIdentifiers: string, columnLookupStrategy: utils.LookupStrategy, errorState: string) => {
        const table = getTableObject();
        const reverse = errorState === 'errors';
        await table.expect.waitForTableStopLoading();
        await table.expect.expectColumnError({ id: columnIdentifiers, lookupStrategy: columnLookupStrategy, reverse });
    },
);

Then(
    /^the validation error tooltip containing text "(.*)" in the table field is (displayed|hidden)$/,
    async (value: string, cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        await table.expect.expectTooltipDisplayedError(value, cssState === 'displayed');
    },
);

Then(/^the user hovers the validation error icon of the table field$/, async () => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    await table.validate.hoverValidationErrorIcon();
});

Then(/^the table has "(.*)" (errors|error)$/, async (value: number, errorState: 'errors' | 'error') => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    if ((value > 0 && errorState === 'error') || (value <= 0 && errorState === 'errors')) {
        throw new Error(
            `Invalid combination of value "${value}" and errorState "${errorState}".\nSelector: ${table.cssSelector}`,
        );
    }
    await table.expect.expectTableError(value, errorState === 'errors');
});

Then(/^the table contains errors$/, async (value: string) => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    await table.validate.tableValidationError(value);
});

Then(/^the user closes the global validation error panel of the table field$/, async () => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    await table.validate.closeValidationPanel();
});

Then('the user adds a new row to the mobile table', async () => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    await table.tableClick.clickAddLineButtonMobile();
});

Then(/^the view dropdown of the table field is (displayed|hidden)$/, async (cssState: 'displayed' | 'hidden') => {
    const table = getTableObject();
    await table.tableClick.expectViewDropdownToBeDisplayed(cssState === 'hidden');
});

Then('the user opens the view dropdown menu of the table field', async () => {
    const table = getTableObject();
    await table.tableClick.openViewDropdown();
});

Then(/^the value of the view dropdown of the table field is "(.*)"$/, async (value: string) => {
    const table = getTableObject();
    await table.tableClick.expectViewOptionToBe(value);
});

Then(/^the user selects the "(.*)" option of the view dropdown of the table field$/, async (label: string) => {
    const table = getTableObject();
    await table.tableClick.selectViewDropdownOption(label);
});

Then(/^the user clicks the "(.*)" button of the view dropdown of the table field$/, async (label: string) => {
    const table = getTableObject();
    await table.tableClick.clickButtonInViewDropdown(label);
});

Then(
    /^the user clicks the "(.*)" (bound|labelled) multi action of the table field$/,
    async (actionIdentifier: string, actionLookupStrategy: utils.LookupStrategy) => {
        const table = <TableObject>StaticStore.getStoredObject(StaticStore.StoredKeys.TABLE);
        await table.rows.clickCustomActionButton(actionIdentifier, actionLookupStrategy);
    },
);

When(/^the user (?:un)?ticks the main checkbox of the selected row in the table field$/, async () => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    const { rowNumber } = StaticStore.getStoredObject<SelectedRowDetails>(StaticStore.StoredKeys.ROW);
    await table.rows.selectRow(rowNumber);
});

When(
    /^the selected row of the table field is (selected|unselected)$/,
    async (selectState: 'selected' | 'unselected') => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        if (selectState === 'selected') {
            await table.expect.expectToBeSelected(rowNumber);
        } else {
            await table.expect.expectNotToBeSelected(rowNumber);
        }
    },
);

When(
    /^the user (expands|collapses) the selected row of the table field$/,
    async (expandOrCollapse: 'expands' | 'collapses') => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        if (expandOrCollapse === 'expands') {
            await table.rows.expandRow(rowNumber);
        } else {
            await table.rows.collapseRow(rowNumber);
        }
        await waitForPromises(500, 'Wait for group');
    },
);

When(
    /^the user clicks the "(.*)" dropdown action of the selected row of the table field$/,
    async (actionName: string) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        await table.action.clickDropdownAction(actionName, rowNumber);
    },
);

Then(
    /^the "(.*)" dropdown action of the selected row in the table field is (enabled|disabled)$/,
    async (dropdownAction: string, expectedValue: utils.EnabledState) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        await table.expect.expectDropdownActionEnabledState({
            expectedDropdownActionText: dropdownAction,
            rowNumber,
            expectedState: expectedValue,
        });
    },
);

Then(
    /^the "(.*)" dropdown action of the selected row in the table field is (displayed|hidden)$/,
    async (dropdownAction: string, cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        await table.expect.expectDropdownActionToBeDisplayed({
            id: dropdownAction,
            rowNumber,
            reverse: cssState === 'hidden',
        });
    },
);

Then(
    /^the "(.*)" inline action button of the selected row in the table field is (displayed|hidden)$/,
    async (inlineAction: string, cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        await table.expect.expectInlineActionDisplayed({ id: inlineAction, rowNumber, reverse: cssState === 'hidden' });
    },
);

Then(
    /^the user hovers over the "(.*)" inline action button of the selected row in the table field$/,
    async (inlineAction: string) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        await table.action.hoverOverInlineAction(inlineAction, rowNumber);
    },
);

Then(
    /^the user clicks the "(.*)" inline action button of the selected row in the table field$/,
    async (buttonName: string) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        await table.action.clickInlineActionButton(buttonName, rowNumber);
    },
);

Then(
    /^the "([^"\n\r]*)" labelled nested (?:aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text|switch)? field of the selected row in the table field is (editable|readonly)$/,
    async (fieldName: string, cssState: 'editable' | 'readonly') => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        await table.rows.expectSelectedTableRowFieldEditable(fieldName, cssState === 'readonly');
    },
);

When(/^the user clicks the main validation error icon of the selected row in the table field$/, async () => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    const { rowNumber } = getSelectedRow();
    await table.tableClick.clickNestedIconError(rowNumber);
});

When(/^the user opens the more actions dropdown menu of the selected row of the table field$/, async () => {
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    const { rowNumber } = getSelectedRow();
    await table.action.openActionsDropdown(rowNumber);
});

Then(/^the dropdown action menu elements of the selected row are:$/, async (menu: DataTable) => {
    const dropdownElements: string[] = menu.raw().map(row => row[0]);
    const table = getTableObject();
    await table.expect.waitForTableStopLoading();
    await table.expect.expectDropdownMenuElements(dropdownElements);
});

Then(
    /^the user clicks the "(.*)" submenu option of the more actions dropdown of the selected row in the table field$/,
    async (menuName: string) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        await table.tableClick.clickMenuOption(menuName);
    },
);

Then(
    /^the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the summary table field is "(.*)"$/,
    async (
        columnName: string,
        columnLookupStrategy: utils.LookupStrategy,
        fieldType: utils.NestedFieldTypes,
        toBe: string,
    ) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const expectedValue = StaticStore.getUserdefinedKeyValueFromStore(toBe);
        const selectedRowIdentifier = StaticStore.getStoredObject<string>(StaticStore.StoredKeys.ROW);
        await table.rows.expectTableRowFieldValue({
            columnName,
            columnLookupStrategy,
            toBe: expectedValue,
            fieldType,
            rowID: selectedRowIdentifier,
        });
    },
);

Then(
    /^the user stores the value of the "([^"\n\r]*)" (bound|labelled) nested (aggregate|checkbox|date|relative date|dropdown-list|filter select|icon|image|label|link|numeric|progress|reference|select|text) field of the selected row in the summary table field with the key "([^"\n\r]*)"$/,
    async (
        columnName: string,
        columnLookupStrategy: utils.LookupStrategy,
        fieldType: utils.NestedFieldTypes,
        key: string,
    ) => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const selectedRowIdentifier = StaticStore.getStoredObject<string>(StaticStore.StoredKeys.ROW);
        const elementValue = await table.rows.getTableRowFieldValue({
            columnName,
            columnLookupStrategy,
            fieldType,
            rowID: selectedRowIdentifier,
        });
        StaticStore.storeObject<string>(key, elementValue);
        await utils.takeScreenshot();
    },
);

Then(
    /^the tunnel link in the "([^"\n\r]*)" (bound|labelled) nested reference field of the selected row in the table field is (displayed|hidden)$/,
    async (columnName: string, columnLookupStrategy: utils.LookupStrategy, cssState: 'displayed' | 'hidden') => {
        const table = getTableObject();
        await table.expect.waitForTableStopLoading();
        const { rowNumber } = getSelectedRow();
        await table.expect.expectTableTunnelLinkToBeDisplayed({
            id: columnName,
            lookupStrategy: columnLookupStrategy,
            rowNumber,
            reverse: cssState === 'hidden',
        });
    },
);
