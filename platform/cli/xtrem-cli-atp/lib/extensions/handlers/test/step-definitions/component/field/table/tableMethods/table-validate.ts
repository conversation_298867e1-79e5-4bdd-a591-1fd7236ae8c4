/* eslint-disable class-methods-use-this */

import {
    AsyncBooleanReturnVoid,
    AsyncStringReturnVoid,
    scrollElementIntoMainView,
    waitForElementToBeDisplayed,
    waitForElementToExist,
} from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import { waitForPromises } from '../../wait-util';
import { TableObject } from '../table-object';

export class TableValidate extends AbstractPageObject {
    private table: TableObject;

    constructor(table: TableObject) {
        super(table.cssSelector);
        this.table = table;
    }

    checkStateofTable: AsyncStringReturnVoid = async identifier => {
        await waitForPromises(300, 'wait for page to finish loading');
        await waitForElementToExist({ name: identifier, selector: this.cssSelector });
        await waitForElementToBeDisplayed({ name: identifier, selector: this.cssSelector });
        await scrollElementIntoMainView(this.cssSelector);
        await browser.execute(
            `const node = document.querySelector('${this.cssSelector}'); node ? node.scrollIntoViewIfNeeded() : true;`,
        );
    };

    checkValidity: AsyncBooleanReturnVoid = async expectedToBeValid => {
        const selectorToUse = 'span[type="error"]';
        try {
            if (expectedToBeValid) {
                await this.expectToDisappear(selectorToUse, true);
            } else {
                await this.expectToAppear({ cssSelector: selectorToUse, ignoreContext: true });
            }
        } catch (error) {
            throw new Error(
                `Expected Element to be ${expectedToBeValid ? '' : 'in'}valid.\nSelector: ${selectorToUse}`,
            );
        }
    };

    closeValidationPanel = async () => {
        const closeButtonSelector = `button[class~="e-button-validation-errors-close"]`;
        if (!(await (await browser.$(closeButtonSelector)).isExisting())) {
            throw new Error(`Expected element could not be found.\nSelector: ${closeButtonSelector}`);
        }
        await browser.waitUntil(
            async () => {
                const closeButtons = await this.findAll(closeButtonSelector, true);
                await Promise.all(closeButtons.map(b => b.click()));
                return (await this.findAll(closeButtonSelector, true)).length === 0;
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: 'Close button was not found after 2 seconds',
                interval: 2000,
            },
        );
    };

    hoverValidationErrorIcon = async () => {
        const validationErrorIcon = await browser.$('.e-xtrem-tab-item span[data-component="icon"]');
        if (!(await validationErrorIcon.isExisting())) {
            throw new Error(`Expected element could not be found. \nSelector: ${validationErrorIcon.selector}`);
        }
        await validationErrorIcon.waitForDisplayed();
        await validationErrorIcon.waitForClickable();
        await waitForPromises(500, 'Wait for displayed');
        await validationErrorIcon.moveTo();
        await waitForPromises(500, 'Wait for moved to');
    };

    tableValidationError: AsyncStringReturnVoid = async expectToastTextContent => {
        const actualMessage: string[] = [];
        const errorElement = await browser.$('.e-link-error-numbers button');

        if (!(await errorElement.isExisting())) {
            throw new Error(`Expected element could not be found: \nSelector: ${this.cssSelector}`);
        }
        await scrollElementIntoMainView('.e-link-error-numbers button');
        await errorElement.click();
        try {
            await browser.waitUntil(
                async () => {
                    const messages = await this.table.expect.getToastsMessages('.e-table-validation-global-errors');
                    await waitForPromises(500, 'Waiting for message');

                    if (messages.length > 0) {
                        const messageStr = messages.join(', ');
                        actualMessage.push(messageStr);
                    }

                    return messages.some(message => Boolean(message.includes(expectToastTextContent)));
                },
                {
                    timeout: this.timeoutWaitFor,
                    timeoutMsg: `Expected value "${expectToastTextContent}" was not found in the toast message after 2 seconds`,
                },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected value: "${expectToastTextContent}"\nactual: "${actualMessage.length > 0 ? actualMessage[0] : 'No messages found'}".\nSelector: ${this.cssSelector}`,
            );
        }
    };
}
