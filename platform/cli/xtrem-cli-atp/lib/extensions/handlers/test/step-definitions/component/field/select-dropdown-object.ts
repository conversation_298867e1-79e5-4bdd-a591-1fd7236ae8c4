import {
    getDataTestIdSelector,
    scrollElementIntoMainView,
    waitForElementToBeFound,
} from '../../step-definitions-utils';
import AbstractPageObject from '../abstract-page-object';
import { waitForPromises } from './wait-util';

export class SelectDropDownObject extends AbstractPageObject {
    selectOption = async (option: string, id?: string) => {
        const element = await this.get();
        await waitForElementToBeFound({ name: 'nested field', selector: element.selector as string });
        const listSelector = '';
        const selector = id
            ? `#${id} ${getDataTestIdSelector({ domSelector: 'ul', dataTestIdValue: 'e-ui-select-dropdown' })}`
            : getDataTestIdSelector({ domSelector: 'ul', dataTestIdValue: 'e-ui-select-dropdown' });

        const list = await this.find(selector, Boolean(id));

        try {
            await waitForElementToBeFound({ name: 'list of options', selector: list.selector as string });
        } catch {
            await (await $('body')).click();
            await scrollElementIntoMainView(this.cssSelector);
            await element.click();
            await waitForElementToBeFound({ name: 'list of options', selector: list.selector as string });
        }
        const generatedSelector = this.getSelectorForOperation(selector, Boolean(id));
        await scrollElementIntoMainView(generatedSelector);
        let currentOptions: WebdriverIO.Element[] = [];
        await waitForPromises(500, 'Wait options fully loaded');

        await browser.waitUntil(
            async () => {
                currentOptions = await browser.$$(
                    `${listSelector} li ${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-ui-select-suggestion-value' })}`,
                );

                if (currentOptions.length === 1) {
                    const textContent = (await currentOptions[0].getText()).trim();
                    return textContent !== 'Loading' && textContent !== 'No items found';
                }

                return currentOptions.length > 1;
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: 'Waiting for options to be loaded',
            },
        );
        await waitForPromises(500, 'Wait options fully loaded');

        const currentOptions_Array = await Promise.all(
            currentOptions.map(async opt => {
                const text = await opt.getText();
                return text.trim();
            }),
        );

        const index = currentOptions_Array.indexOf(option.trim().replace(/\s\s+/g, ' '));

        if (index === -1) {
            throw new Error(
                `Expected option to select could not be found: "${option}".\nSelector: ${this.cssSelector}`,
            );
        }
        await waitForPromises(800, 'selectMultiFieldOption');

        try {
            const parentLine = await (await currentOptions[index].parentElement()).parentElement();
            await parentLine.waitForClickable();
            await parentLine.moveTo();
            await parentLine.click();
        } catch (error) {
            throw new Error(error);
        }

        await waitForPromises(800, 'selectMultiFieldOption');
    };

    expectOptionsToBe = async (toBe: string) => {
        const toBe_Array = toBe.split('|').map(i => i.trim());

        const element = await this.get();
        await waitForElementToBeFound({ name: 'nested field', selector: element.selector as string });
        await element.scrollIntoView();

        const list = await this.find(
            getDataTestIdSelector({ domSelector: 'ul', dataTestIdValue: 'e-ui-select-dropdown' }),
        );
        await waitForElementToBeFound({ name: 'list of options', selector: list.selector as string });
        await list.scrollIntoView();

        const currentOptions = await this.findAll(
            `${list.selector} li ${getDataTestIdSelector({ domSelector: 'div', dataTestIdValue: 'e-ui-select-suggestion-value' })}`,
            true,
        );

        const currentOptions_Array = await Promise.all(
            currentOptions.map(async opt => {
                const text = await opt.getText();
                return text.trim();
            }),
        );

        const allElementsPresent = toBe_Array.every(el => currentOptions_Array.includes(el));
        if (!allElementsPresent) {
            throw new Error(`Expected options could not be found: "${toBe}".\nSelector: ${this.cssSelector}`);
        }
    };
}
