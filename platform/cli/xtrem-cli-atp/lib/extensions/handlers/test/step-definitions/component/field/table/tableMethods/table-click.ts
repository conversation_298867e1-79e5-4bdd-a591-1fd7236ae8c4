/* eslint-disable no-restricted-syntax */
/* eslint-disable class-methods-use-this */

import { kebabCase } from 'lodash';
import { ElementArray } from 'webdriverio';
import * as utils from '../../../../step-definitions-utils';
import {
    AsyncNumberReturnVoid,
    AsyncStringLookupStrategyReturnVoid,
    AsyncStringReturnVoid,
    expectElementToBeDisplayed,
    ExportType,
    LookupStrategy,
    Page,
    TimeUnits,
    waitForElementToBeDisplayed,
    waitForElementToExist,
} from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import { waitForPromises } from '../../wait-util';
import { TableObject } from '../table-object';
import { scrollToTableRow } from '../tableUtils';

const SELECTOR = {
    rowIndexSelector: (rowNumber: number) => `[row-index="${rowNumber - 1}"]`,
};

export class TableClick extends AbstractPageObject {
    private table: TableObject;

    constructor(table: TableObject) {
        super(table.cssSelector);
        this.table = table;
    }

    clickAddLineButtonMobile = async () => {
        const addLineButton = await this.find('button[data-testid="e-add-line-button"]');
        await addLineButton.click();
    };

    // To be removed later
    clickCard: AsyncNumberReturnVoid = async rowNumber => {
        await this.table.expect.waitForTableStopLoading();
        const rowContainer = await this.find('[data-testid="e-card"]');
        await waitForPromises(500, 'Wait for find');
        await waitForElementToExist({ name: 'row container', selector: rowContainer.selector.toString() });
        await waitForElementToBeDisplayed({ name: 'row container', selector: rowContainer.selector.toString() });
        const elements = await this.findAll('[data-testid="e-card"]');
        await waitForPromises(500, 'Wait for findAll');
        await elements[rowNumber - 1].click();
    };

    clickClearSelectedItems: AsyncStringLookupStrategyReturnVoid = async (columnName, lookupStrategy) => {
        await this.table.filter.openFilter(columnName, lookupStrategy);
        const clearSelectedItems = await this.find('[data-testid~="e-reference-custom-filter-clear-button"');
        if (!(await clearSelectedItems.isExisting())) {
            await this.table.filter.openFilter(columnName, lookupStrategy);
        }
        await clearSelectedItems.waitForClickable();
        await clearSelectedItems.click();
        await waitForPromises(500, 'Wait for click');
        await this.table.filter.closeFilter();
    };

    clickClearSelection = async () => {
        const clearButton = await this.find('.e-page-navigation-panel-bulk-actions-bar-clear-selection');
        await waitForElementToBeDisplayed({ name: 'clear button', selector: clearButton.selector.toString() });
        await clearButton.click();
    };

    clickExportButton = async (exportType: ExportType): Promise<void> => {
        const toggleButton = await this.find(
            '[data-component="multi-action-button"] > button[data-element="toggle-button"][data-testid="e-table-export"]',
        );

        await toggleButton.click();
        const exportButtons = await this.findAll('[data-element="additional-buttons"] > li > button', true);
        if (exportType === 'excel') {
            await exportButtons[0].click();
        } else {
            await exportButtons[1].click();
        }
    };

    clickMenuOption: AsyncStringReturnVoid = async menuName => {
        const popoverSubmenus = await this.findAll('button [data-element="action-popover-menu-item-inner-text"]', true);
        let submenu;
        for (const s of popoverSubmenus) {
            const submenuOption = await s.getText();
            if (submenuOption === menuName) {
                submenu = s;
                break;
            }
        }
        if (!submenu) {
            throw new Error(`Could not find menu option for row with text ${menuName}`);
        }
        await submenu.click();
    };

    clickNestedIconError: AsyncNumberReturnVoid = async rowNumber => {
        const selectorToUse = await this.find(
            `${SELECTOR.rowIndexSelector(rowNumber)} .e-table-field-validation-summary [data-element="error"]`,
        );
        await waitForPromises(500, 'waiting for element to exist');
        try {
            await scrollToTableRow(this.cssSelector, rowNumber);
            await this.expectToBeReady(selectorToUse.selector.toString(), true);
            await utils.scrollElementIntoMainView(
                this.getSelectorForOperation(selectorToUse.selector.toString(), true),
            );
            await this.click(selectorToUse.selector.toString(), true);
            await waitForPromises(500, 'waiting for click to complete');
        } catch (error) {
            throw new Error(
                `Expected element could not be found on row "${rowNumber}" \n cssSelector: ${selectorToUse.selector.toString()}`,
            );
        }
    };

    clickOnGroupByOption = async ({
        id,
        lookupStrategy,
        time,
    }: {
        id: string;
        lookupStrategy: LookupStrategy;
        time: TimeUnits;
    }): Promise<void> => {
        const groupByButton = await this.table.expect.expectGroupByToBeDisplayed({ id, lookupStrategy });

        await groupByButton.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${id}".\nSelector: ${groupByButton.selector.toString()}`,
        });
        if (time == null) {
            await groupByButton.click();
            await waitForPromises(900, 'Wait for click');
        } else {
            try {
                await groupByButton.moveTo();
                await (
                    await this.waitForDisplayedAndGetElement({
                        selector: `.e-table-field-group-by-${time}`,
                        ignoreContext: true,
                        timeoutMsg: s => `The following element could not be found: ${String(s)}.`,
                    })
                ).click();
                await waitForPromises(900, 'Wait for click');
            } catch (error) {
                throw new Error(error);
            }
        }
    };

    clickOnUnGroupOption: AsyncStringLookupStrategyReturnVoid = async (id, lookupStrategy) => {
        const unGroupButton = await this.table.expect.expectUnGroupToBeDisplayed({ id, lookupStrategy });
        try {
            await unGroupButton.waitForExist();
            await unGroupButton.scrollIntoView();
            await unGroupButton.moveTo();
            await unGroupButton.waitForClickable();
            await unGroupButton.click();
            await waitForPromises(200, 'wait ungroup button to be clicked');
        } catch (error) {
            throw new Error(error);
        }
    };

    clickOptionMenu = async () => {
        const optionMenu = await this.find('.e-option-item-menu');
        await optionMenu.click();
    };

    selectMenuItems = async (columnsToDisplay: string[], menuItems: ElementArray): Promise<void> => {
        // Select menu items
        for (const menuItem of menuItems) {
            const menuItemLabel = await menuItem.$('label');
            const labelText = await menuItemLabel.getText();
            if (columnsToDisplay.includes(labelText)) {
                await menuItemLabel.click();
            }
        }
    };

    selectOptionMenuValue: AsyncStringReturnVoid = async input => {
        await this.table.expect.waitForOptionMenuToBeDisplayed();
        const selectorToUse = `[data-testid~="e-ui-select-suggestion"][id*="${input}"] p`;
        const optionMenuItem = await this.find(selectorToUse);
        await optionMenuItem.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: "${input}".\nSelector: ${selectorToUse}`,
        });
        await optionMenuItem.click();
    };

    stepToPage = async (page: Page): Promise<void> => {
        const generatedSelector = this.getSelectorForOperation(`.ag-paging-button .ag-icon-${page}`);
        const button = await this.find(`.ag-paging-button .ag-icon-${page}`);
        await waitForElementToExist({ name: 'button', selector: button.selector.toString() });
        await utils.scrollElementIntoMainView(generatedSelector);
        await button.waitForClickable();
        await button.click();
        await this.table.expect.waitForTableStopLoading();
    };

    unselectAllMenuItems = async (menuItems: ElementArray): Promise<void> => {
        // Unselect all menu items
        for (const menuItem of menuItems) {
            const menuItemInput = await menuItem.$('input');
            const selectionState = await menuItemInput.isSelected();
            if (selectionState) {
                await menuItemInput.click();
            }
        }
    };

    expectViewDropdownToBeDisplayed = async (reverse: boolean = false): Promise<void> => {
        const selectorToUse = '[data-testid="e-table-view-selector"]';
        await expectElementToBeDisplayed({ selector: selectorToUse, reverse });
    };

    openViewDropdown = async (): Promise<void> => {
        const selectorToUse = '[data-testid="e-table-view-selector"][aria-label="Select view"]';
        const viewsDropdown = await this.find(selectorToUse);
        await viewsDropdown.click();
        await utils.waitForElementToBeDisplayed({
            name: 'list of views and actions',
            selector: '[data-testid="e-table-view-selector"][aria-expanded="true"]',
        });
    };

    expectViewOptionToBe = async (value: string) => {
        const selectorToUse = '[data-testid="e-table-view-selector"]';
        await this.expectValue({ toBe: value, cssSelector: selectorToUse, ignoreContext: true });
    };

    selectViewDropdownOption = async (label: string): Promise<void> => {
        try {
            if (label === 'Default view') {
                const defaultViewButtonSelector = '[data-testid="e-table-view-selector-item-default-view"]';
                const button = await this.find(defaultViewButtonSelector);
                await button.waitForClickable();
                await this.jsClick({ cssSelector: defaultViewButtonSelector, skipVisibilityCheck: true });
            } else {
                const optionName = kebabCase(label).toLowerCase();
                const optionSelector = `[data-testid="e-table-view-selector-item-${optionName}"][role="option"]`;
                await this.jsClick({ cssSelector: optionSelector, skipVisibilityCheck: true });
            }
        } catch (error) {
            throw new Error(`Could not find view option with the following text: ${label}`);
        }
    };

    clickButtonInViewDropdown = async (label: string): Promise<void> => {
        const buttonName = kebabCase(label).toLowerCase();
        const buttonSelector = `.e-table-view-selector-item-group-actions [data-testid="e-table-view-${buttonName}"]`;
        const dropdownButton = await this.find(buttonSelector);
        await dropdownButton.waitForClickable();
        await this.jsClick({ cssSelector: buttonSelector, skipVisibilityCheck: true });
    };
}
