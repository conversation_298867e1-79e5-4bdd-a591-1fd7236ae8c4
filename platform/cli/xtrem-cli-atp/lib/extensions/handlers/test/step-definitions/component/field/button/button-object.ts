import { ElementContext, LookupStrategy } from '../../../step-definitions-utils';
import { FieldObject } from '../field-object';

export class ButtonFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: LookupStrategy;
        context?: ElementContext;
    }) {
        super({ fieldType: 'button', identifier, lookupStrategy, context });
    }

    override async expectTitle(expectedTitle: string) {
        await this.expectTextContent({ toBe: expectedTitle, ignoreCase: false, cssSelector: 'label' });
    }

    async expectContent(expectedContent: string) {
        await this.expectTextContent({ toBe: expectedContent, ignoreCase: false, cssSelector: 'button' });
    }

    override async click() {
        await super.click('button');
    }
}
