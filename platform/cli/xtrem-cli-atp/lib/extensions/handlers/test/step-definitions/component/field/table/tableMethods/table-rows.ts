/* eslint-disable no-restricted-syntax */
/* eslint-disable class-methods-use-this */

import { camelCase } from 'lodash';
import {
    AsyncNumberReturnVoid,
    AsyncObjectStringNumberBooleanReturnVoid,
    AsyncStringBooleanReturnVoid,
    AsyncStringLookupStrategyReturnVoid,
    fieldTypes,
    getDataTestIdSelector,
    getLookupId,
    getMultipleDataTestIdSelector,
    getTableNestedFieldSelector,
    LookupStrategy,
    NestedFieldTypes,
    scrollElementIntoMainView,
    takeScreenshot,
    waitForElementToBeFound,
    waitForElementToExist,
} from '../../../../step-definitions-utils';
import AbstractPageObject from '../../../abstract-page-object';
import { waitForPromises } from '../../wait-util';
import { TableObject } from '../table-object';
import { scrollToTableRow, waitForGridCellsContent, waitForTableToBePopulated } from '../tableUtils';

const SELECTOR = {
    rowIndexSelector: (rowNumber: number) => `[row-index="${rowNumber - 1}"]`,
};

export class TableRows extends AbstractPageObject {
    private table: TableObject;

    constructor(table: TableObject) {
        super(table.cssSelector);
        this.table = table;
    }

    clickAddItemButton: AsyncStringLookupStrategyReturnVoid = async (actionIdentifier, actionLookupStrategy) => {
        const splitButtonToggle = await this.find(
            '.e-field-actions-wrapper [data-component="split-button"] [data-element="toggle-button"]',
        );
        await waitForElementToExist({
            name: 'splitButtonToggle',
            selector: splitButtonToggle.selector.toString(),
        });
        await splitButtonToggle.click();
        await waitForPromises(500, 'Waiting for split button');

        const testId =
            actionLookupStrategy === LookupStrategy.BIND
                ? `e-field-bind-${actionIdentifier}`
                : `e-field-label-${camelCase(actionIdentifier)}`;
        const addNewRowSidebarButton = await this.find(`[data-testid*=${testId}]`, true);
        await waitForElementToExist({
            name: 'addItemButton',
            selector: addNewRowSidebarButton.selector.toString(),
        });
        await addNewRowSidebarButton.click();
        await waitForPromises(500, 'Waiting for add new row click');
    };

    clickAddRowButton = async () => {
        const addNewRowPhantomButton = await this.find(
            getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: 'e-table-button-add-new-row-phantom',
            }),
        );
        await waitForElementToExist({
            name: 'addPhantomRowButton',
            selector: addNewRowPhantomButton.selector.toString(),
        });
        await addNewRowPhantomButton.click();
        await waitForPromises(300, 'Waiting for add new row click');
    };

    clickAddWithSidebar = async () => {
        const generatedSelector = this.getSelectorForOperation(
            '.e-field-actions-wrapper [data-component="split-button"] [data-element="toggle-button"]',
        );
        const splitButtonToggle = await this.find(
            '.e-field-actions-wrapper [data-component="split-button"] [data-element="toggle-button"]',
        );
        await waitForElementToExist({
            name: 'splitButtonToggle',
            selector: splitButtonToggle.selector.toString(),
        });
        await scrollElementIntoMainView(generatedSelector);
        await splitButtonToggle.click();
        await waitForPromises(500, 'Waiting for split button');
        const addNewRowSidebarButton = await this.find(
            getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: 'e-table-button-add-new-row-sidebar',
            }),
            true,
        );
        await waitForElementToExist({
            name: 'addSidebarButton',
            selector: addNewRowSidebarButton.selector.toString(),
        });
        await addNewRowSidebarButton.click();
        await waitForPromises(500, 'Waiting for add new row click');
    };

    clickCustomActionButton = async (actionIdentifier: string, actionLookupStrategy: LookupStrategy) => {
        const addLineMultiActionDropdown = await this.find(
            getDataTestIdSelector({
                domSelector: 'button',
                dataTestIdValue: 'e-table-button-add-new-row-multi-action',
            }),
        );
        await waitForElementToExist({
            name: 'addPhantomRowButton',
            selector: addLineMultiActionDropdown.selector.toString(),
        });
        await addLineMultiActionDropdown.click();
        await waitForPromises(300, 'Waiting for add new row click');

        await waitForElementToExist({
            name: 'Add line multi action dropdown',
            selector: '[data-component="multi-action-button"] [aria-expanded="true"]',
        });

        const getActionSelector = (actionId: string, strategy: LookupStrategy) => {
            const testId =
                actionId === 'Add line in panel'
                    ? 'e-table-button-add-new-row-sidebar'
                    : `e-field-${getLookupId(strategy, actionId)}`;

            return getMultipleDataTestIdSelector('', [testId]);
        };

        const selector = getActionSelector(actionIdentifier, actionLookupStrategy);

        const customActionButton = await this.find(selector);
        await waitForElementToExist({
            name: 'customActionButton',
            selector: customActionButton.selector.toString(),
        });
        await customActionButton.click();
        await waitForPromises(500, 'Waiting for custom action button click');
    };

    clickTableFieldAction = async (actionLookupStrategy: LookupStrategy, actionId: string): Promise<void> => {
        await waitForPromises(0, 'before click field action');
        const selectorToUse =
            actionLookupStrategy === LookupStrategy.BIND ? `bind-${actionId}` : `label-${camelCase(actionId)}`;
        await this.scrollTo();
        await this.click(`[data-testid~="e-header-action-${selectorToUse}"]`);
        await waitForPromises(0, 'click field action');
    };

    collapseRow: AsyncNumberReturnVoid = async rowNumber => {
        await scrollToTableRow(`${this.cssSelector} ${SELECTOR.rowIndexSelector(rowNumber)}`, rowNumber);

        const groupIcon = await this.waitForDisplayedAndGetElement({
            selector: `${SELECTOR.rowIndexSelector(rowNumber)} .ag-group-expanded > .ag-icon.ag-icon-tree-open`,
            timeoutMsg: selector => `Could not find a group row at index "${rowNumber}".\nSelector${selector}.`,
        });
        await groupIcon.waitForClickable();
        await groupIcon.click();
        await (
            await browser.$(
                `${SELECTOR.rowIndexSelector(rowNumber)} .ag-group-contracted > .ag-icon.ag-icon-tree-closed`,
            )
        ).waitForDisplayed();
    };

    expectSelectedTableRowFieldEditable: AsyncStringBooleanReturnVoid = async (fieldName, reverse = false) => {
        const rowCell = await this.find(`.e-nested-cell-label-${camelCase(fieldName)}`);
        await this.expectToBeNestedEditable(rowCell.selector.toString(), reverse);
    };

    expectTableRowFieldEditable: AsyncObjectStringNumberBooleanReturnVoid = async ({
        id,
        rowNumber,
        reverse = false,
    }) => {
        const field = camelCase(id);
        const cssSelector = `${SELECTOR.rowIndexSelector(rowNumber)} .e-nested-cell-label-${field}`;
        const rowCell = await this.find(cssSelector);
        await this.expectToBeNestedEditable(rowCell.selector.toString(), reverse);
    };

    expectTableRowFieldValue = async ({
        columnName,
        columnLookupStrategy,
        toBe,
        fieldType,
        rowID,
        ignoreContext = false,
    }: {
        columnName: string;
        columnLookupStrategy: LookupStrategy;
        toBe: string;
        fieldType: NestedFieldTypes;
        rowID: number | string;
        ignoreContext?: boolean;
    }): Promise<void> => {
        const selectorToUse = `[data-testid~="${rowID.toString()}"] [data-testid~="e-field-${
            columnLookupStrategy === LookupStrategy.BIND ? `bind-${columnName}` : `label-${camelCase(columnName)}`
        }"]`;

        await waitForElementToBeFound({ name: `"${columnName}" nested field`, selector: selectorToUse });

        switch (fieldType) {
            case fieldTypes.checkbox:
                await this.expectCheckedValue(toBe, selectorToUse);
                break;
            case fieldTypes.aggregate:
            case fieldTypes.date:
            case fieldTypes.dropdownList:
            case fieldTypes.filterSelect:
            case fieldTypes.icon:
            case fieldTypes.image:
            case fieldTypes.label:
            case fieldTypes.link:
            case fieldTypes.numeric:
            case fieldTypes.reference:
            case fieldTypes.relativeDate:
            case fieldTypes.select:
            case fieldTypes.text:
                await this.expectTextContent({ toBe, ignoreCase: false, cssSelector: selectorToUse, ignoreContext });
                break;
            case fieldTypes.progress:
                await this.table.expect.expectProgressValue({ toBe, cssSelector: selectorToUse, ignoreContext });
                break;
            default:
                throw new Error(`Invalid fieldType type: ${fieldType}`);
        }
    };

    expandRow: AsyncNumberReturnVoid = async rowNumber => {
        await scrollToTableRow(`${this.cssSelector} ${SELECTOR.rowIndexSelector(rowNumber)}`, rowNumber);

        const groupIcon = await this.waitForDisplayedAndGetElement({
            selector: `${SELECTOR.rowIndexSelector(rowNumber)} .ag-group-contracted > .ag-icon.ag-icon-tree-closed`,
            timeoutMsg: selector => `Expected element could not be found: row "${rowNumber}".\nSelector: ${selector}.`,
        });
        await groupIcon.waitForClickable();
        await groupIcon.click();
        await (
            await browser.$(`${SELECTOR.rowIndexSelector(rowNumber)} .ag-group-expanded > .ag-icon.ag-icon-tree-open`)
        ).waitForDisplayed();
    };

    /**
     * Find and return the index of the row that matches rowIdentifier and columnName
     * @param rowIdentifier the identifier of the row to search for
     * @param columnName the identifier of the column to search for
     * @param lookupStrategy the lookup strategy to use for the column
     */
    getRowIndex = async ({
        rowIdentifier,
        columnName,
        lookupStrategy,
    }: {
        rowIdentifier: string;
        columnName: string;
        lookupStrategy: LookupStrategy;
    }): Promise<number> => {
        await waitForTableToBePopulated(this.cssSelector);
        await waitForGridCellsContent(this.cssSelector);

        const fields = `div[class="ag-center-cols-container"] ${getTableNestedFieldSelector({ lookupStrategy, identifier: columnName })}`;
        await waitForElementToExist({ name: `fields for column "${columnName}"`, selector: fields });

        for (const field of await $$(fields)) {
            const currentVal = (await field.getText()).trim();
            const expectedVal = rowIdentifier.trim();
            if (currentVal === expectedVal) {
                const row = await field.parentElement();
                if ((await row.getAttribute('role')) && (await row.getAttribute('role')) === 'row') {
                    const rowIndex = await row.getAttribute('row-index');
                    return Number(rowIndex) + 1;
                }
            }
        }

        await takeScreenshot();
        throw new Error(
            `Expected element could not be found: row with text "${rowIdentifier}" in "${columnName}" column header.\nSelector:${fields}`,
        );
    };

    getUniqueID = async ({
        rowIdentifier,
        columnName,
        lookupStrategy,
    }: {
        rowIdentifier: string;
        columnName: string;
        lookupStrategy: LookupStrategy;
    }): Promise<string> => {
        const selectorToUse = `[data-testid~="e-field-${
            lookupStrategy === LookupStrategy.BIND ? `bind-${columnName}` : `label-${camelCase(columnName)}`
        }"]`;

        try {
            await (await $(selectorToUse)).waitForExist();
            await this.scrollTo({ selector: selectorToUse });
        } catch (error) {
            throw new Error(
                `The row with text "${rowIdentifier}" and column header "${columnName}" could not be found.\nSelector: ${selectorToUse}`,
            );
        }

        for (const row of await this.findAll('.e-table-summary-field-rows')) {
            const rowCell = row.$(selectorToUse);
            const rowText = await rowCell.getText();
            if (rowText === rowIdentifier) {
                const rowDataTestId = await row.getAttribute('data-testid');
                // The rowDataTestId is expected to be in the format: e-table-summary-field-row-<rowIndex>
                // Example: e-table-summary-field-row-388
                return rowDataTestId;
            }
        }

        await takeScreenshot();
        throw new Error(
            `The row with text "${rowIdentifier}" and column header "${columnName}" could not be found.\nSelector: ${selectorToUse}`,
        );
    };

    getTableRowFieldValue = async ({
        columnName,
        columnLookupStrategy,
        fieldType,
        rowID,
    }: {
        columnName: string;
        columnLookupStrategy: LookupStrategy;
        fieldType: NestedFieldTypes;
        rowID: number | string;
    }): Promise<string> => {
        const cssSelector = `[data-testid~="${rowID.toString()}"] [data-testid~="e-field-${
            columnLookupStrategy === LookupStrategy.BIND ? `bind-${columnName}` : `label-${camelCase(columnName)}`
        }"]`;

        await waitForElementToBeFound({ name: `"${columnName}" nested field`, selector: cssSelector });
        const element = await browser.$(cssSelector);
        const elementValue = fieldType === fieldTypes.progress ? await element.getValue() : await element.getText();
        return elementValue;
    };

    selectRow: AsyncNumberReturnVoid = async rowNumber => {
        const element = await this.find(
            `${SELECTOR.rowIndexSelector(rowNumber)} [col-id^="ag-Grid-ControlsColumn"] .ag-selection-checkbox input`,
        );
        await element.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected element could not be found: row "${rowNumber}".\nSelector: ${element.selector.toString()})`,
        });
        await this.jsClick({
            cssSelector: `${SELECTOR.rowIndexSelector(rowNumber)} [col-id^="ag-Grid-ControlsColumn"] .ag-selection-checkbox input`,
            ignoreContext: false,
            skipVisibilityCheck: true,
        });
        await waitForPromises(500, 'Waiting for click on row');
    };
}
