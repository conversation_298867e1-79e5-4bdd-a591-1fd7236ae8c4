import { Key } from 'webdriverio';
/* eslint-disable class-methods-use-this */
import { camelCase } from 'lodash';
import * as utils from '../../../step-definitions-utils';
import { getUserLocale, parseDateString } from '../date-utils';
import { FieldObject } from '../field-object';
import { waitForPromises } from '../wait-util';

export enum FormDesignerLocation {
    header = 'header',
    body = 'body',
    footer = 'footer',
}

export enum FormDesignerTableRelativeLocation {
    header = 'header',
    body = 'body',
    footer = 'footer',
    footerGroup = 'footerGroup',
}

export class FormDesignerFieldObject extends FieldObject {
    constructor({
        identifier,
        lookupStrategy,
        context,
    }: {
        identifier: string;
        lookupStrategy: utils.LookupStrategy;
        context?: utils.ElementContext;
    }) {
        super({ fieldType: 'form-designer', identifier, lookupStrategy, context });
    }

    private dialogSelector = 'div.carbon-portal:not([aria-hidden]) [data-state="open"] div[role="dialog"]';

    private leftPanelSelector = 'div.document-editor-left-panel';

    private rightPanelSelector = 'div.document-editor-right-panel';

    private dropdownFieldID = '';

    async selectFormDesignerText(location: FormDesignerLocation, lineNo?: number) {
        await this.click(`div.document-editor div#${location}${lineNo ? ` *:nth-child(${lineNo})` : ''}`);

        const keysStart: any = [Key.Home];
        const keysEnd: any = [Key.Shift, Key.End];

        if (!lineNo) {
            keysStart.unshift(Key.Ctrl);
            keysEnd.unshift(Key.Ctrl);
        }

        await browser.keys(keysStart);
        await browser.pause(100);
        await browser.keys(keysEnd);
        await browser.pause(100);
    }

    async clickFormDesignerToolbarButton(buttonName: string) {
        const selectorToUse = this.getSelectorForOperation('div.ck-toolbar');
        await utils.scrollElementIntoMainView(selectorToUse);
        await this.scrollTo({ selector: 'div.ck-toolbar' });
        const toolbarButtons = await this.findAll(
            'div.ck-toolbar[aria-label="Editor toolbar"] > div > button,div.ck-toolbar[aria-label="Editor toolbar"] > div > div.ck-dropdown > button',
        );

        let toolbarButton;

        // eslint-disable-next-line no-restricted-syntax
        for (const button of toolbarButtons) {
            if ((await button.$('span').getText()).toLowerCase() === buttonName.toLowerCase()) {
                toolbarButton = button;
                break;
            }
            if ((await button.getAttribute('data-cke-tooltip-text')).toLowerCase().includes(buttonName.toLowerCase())) {
                toolbarButton = button;
                break;
            }

            if ((await button.$('span').getText()).toLowerCase() === 'show more items') {
                try {
                    if ((await button.getAttribute('aria-expanded')) === 'false') await button.click();
                    await waitForPromises(200, 'click show more items button');
                    await this.clickFormDesignerToolbarOption(buttonName);
                    await waitForPromises(200, 'wait after clicking option from show more items');
                    return;
                } catch (error) {
                    break;
                }
            }
        }

        if (toolbarButton === undefined) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected element could not be found: "${buttonName}" button.\nSelector: ${this.cssSelector} div.ck-toolbar button`,
            );
        }

        if ((await toolbarButton.getAttribute('class')).includes('ck-disabled')) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected "${buttonName}" button to be enabled.\nSelector: ${this.cssSelector} div.ck-toolbar button`,
            );
        }

        await toolbarButton.click();

        await waitForPromises(200, 'click toolbar button');
    }

    async clickFormDesignerToolbarOption(optionName: string) {
        try {
            await browser.waitUntil(
                async () => {
                    return (await this.find('div.ck-dropdown__panel-visible')).isExisting();
                },
                { timeout: this.timeoutWaitFor },
            );
        } catch (error) {
            await browser.takeScreenshot();
            throw new Error(`Expected Toolbar Option menu to be displayed. No Menu displayed.`);
        }
        const optionList = await this.findAll('div.ck-dropdown__panel-visible button');
        let optionToSelect;

        // eslint-disable-next-line no-restricted-syntax
        for (const listItem of optionList) {
            if ((await listItem.$('.ck-button__label').getText()).toLowerCase() === optionName.toLowerCase()) {
                optionToSelect = listItem;
            }
        }
        if (optionToSelect === undefined) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected option to be selected could not be found: "${optionName}".\nSelector: ${this.cssSelector} div.ck-dropdown__panel-visible button`,
            );
        }

        await optionToSelect.click();

        await waitForPromises(500, 'click toolbar option');
    }

    async selectFormDesignerColor(fontOrBackground: 'font' | 'background', color: string) {
        await this.clickFormDesignerToolbarButton(fontOrBackground === 'font' ? 'Font Color' : 'Font Background Color');

        try {
            await this.clickFormDesignerToolbarOption(color);
        } catch (error) {
            if (error.message.includes('Expected option to be selected')) {
                throw new Error(
                    `Expected element could not be found: "${color}" color.\nSelector: ${this.cssSelector} div.ck-dropdown__panel-visible button`,
                );
            } else {
                throw error;
            }
        }
    }

    async addFormDesignerTable(rows: number, columns: number) {
        const selectorToUse = `button.ck-insert-table-dropdown-grid-box[data-row="${rows}"][data-column="${columns}"]`;
        if (rows > 10 || columns > 10) {
            throw new Error(
                `Expected maximum grid size is 10 rows X 10 columns, Actual is ${rows} rows X ${columns} columns.\nSelector: ${this.cssSelector} ${selectorToUse}`,
            );
        }

        if (rows < 1 || columns < 1) {
            throw new Error(
                `Expected minimum grid size is 1 rows X 1 columns, Actual is ${rows} rows X ${columns} columns.\nSelector: ${this.cssSelector} ${selectorToUse}`,
            );
        }

        await this.clickFormDesignerToolbarButton('Insert table');

        await (
            await this.waitForDisplayedAndGetElement({
                selector: selectorToUse,
                timeout: this.timeoutWaitFor,
                timeoutMsg: selector =>
                    `Expected element could not be found: table configuration with ${rows} Rows x ${columns} Columns. Selector: ${selector}`,
            })
        ).click();

        await waitForPromises(0, 'click insert table option');
    }

    async expectFormDesignerToolbarButtonState(buttonName: string, expectedStateDisabled = false) {
        await this.scrollTo({ selector: '[role="toolbar"]' });
        const showMoreItemsButtonSelector = "//button[.//*[contains(text(),'Show more items')]]";
        const showMoreItemsButton = await (await this.find('')).$(showMoreItemsButtonSelector);
        if (await showMoreItemsButton.isExisting()) {
            await showMoreItemsButton.click();
        }
        const toolbarButtonSelector = `//button[.//*[contains(text(),'${buttonName}')]]`;
        const toolbarButton = await (await this.find('')).$(toolbarButtonSelector);
        if (!(await toolbarButton.isExisting())) {
            await browser.takeScreenshot();
            throw new Error(
                `Expected element could not be found: "${buttonName}" button.\nSelector: ${this.cssSelector} ${toolbarButtonSelector}`,
            );
        }
        const isDisabled = (await toolbarButton.getAttribute('aria-disabled')) === 'true';
        if (isDisabled !== expectedStateDisabled) {
            const state = expectedStateDisabled ? 'disabled' : 'enabled';
            throw new Error(
                `Expected "${buttonName}" toolbar button to be ${state}.\nSelector: ${toolbarButton.selector}`,
            );
        }
    }

    async expectPanelToBeDisplayed(panelName: string, reverse = false) {
        const formDesignerLeftPanel = await this.find(this.leftPanelSelector);
        const formDesignerRightPanel = await this.find(this.rightPanelSelector);

        try {
            await browser.waitUntil(
                async () => {
                    if (reverse && (await this.getNamedPanel(panelName)) === undefined) {
                        return true;
                    }

                    return ((await this.getNamedPanel(panelName)) !== undefined) !== reverse;
                },
                { timeout: this.valueCheckTimeout },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Expected Element to be ${reverse ? 'hidden' : 'displayed'}.\nSelector: ${formDesignerLeftPanel.selector} | ${formDesignerRightPanel.selector}`,
            );
        }
    }

    async expectFormattingPanelButtonVisibility(buttonName: string, expectedDisplayed = true) {
        const formattingPanelButtonsSelector = `//*[@class='document-editor-right-panel']//button[.//*[contains(text(),'${buttonName}')]]`;
        const buttonFound = await (await (await this.find('')).$(formattingPanelButtonsSelector)).isExisting();
        if (buttonFound !== expectedDisplayed) {
            const visibility = expectedDisplayed ? 'displayed' : 'hidden';
            throw new Error(
                `Expected "${buttonName}" Formatting panel button to be ${visibility}. Selector ${this.cssSelector} ${formattingPanelButtonsSelector}`,
            );
        }
    }

    async getNamedPanel(panelName: string) {
        const formDesignerLeftPanel = await this.find(this.leftPanelSelector);
        const formDesignerRightPanel = await this.find(this.rightPanelSelector);
        const panelLabelSelector = 'span.document-editor-panel-header-label';
        if (await formDesignerLeftPanel.isExisting()) {
            if (
                (await (await formDesignerLeftPanel.$(panelLabelSelector)).getText()).toLowerCase() ===
                panelName.toLowerCase()
            ) {
                return formDesignerLeftPanel;
            }
        }
        if (await formDesignerRightPanel.isExisting()) {
            if (
                (await (await formDesignerRightPanel.$(panelLabelSelector)).getText()).toLowerCase() ===
                panelName.toLowerCase()
            ) {
                return formDesignerRightPanel;
            }
        }
        return undefined;
    }

    async closeFormDesignerPanel(panelName: string) {
        try {
            await this.expectPanelToBeDisplayed(panelName);
        } catch (error) {
            throw new Error(`Expected panel "${panelName}" to be displayed.`);
        }

        const panel = await this.getNamedPanel(panelName);
        await panel?.$('div.document-editor-panel-header button').click();

        await waitForPromises(0, 'click panel close button');
    }

    async searchNodeStepTree(value: string, context: utils.ElementContext) {
        const selectorToUse = `${utils.getContextSelector(context)} ${utils.getDataTestIdSelector({ domSelector: 'input', dataTestIdValue: 'e-node-browser-filter' })}`;

        try {
            await browser.waitUntil(
                async () => {
                    return (await browser.$(selectorToUse)).isExisting();
                },
                { timeout: this.timeoutWaitFor },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected node browser filter could not be found.\nSelector: ${selectorToUse}`);
        }
        await this.write({ content: value, cssSelector: selectorToUse, ignoreContext: true });

        await waitForPromises(200, 'write to search field');
    }

    async expectFormDesignerDialogTitle(expectedTitle: string) {
        const dialog = await $(this.dialogSelector);
        await utils.waitForElementToBeDisplayed({ name: 'Dialog', selector: dialog.selector as string });

        const currentTitle = await (
            await dialog.$('[data-component="heading"] [data-element="header-container"] [data-element="title"]')
        ).getText();

        if (currentTitle !== expectedTitle) {
            throw Error(`Expected value: ${expectedTitle}, actual: ${currentTitle}\nSelector: ${dialog.selector}`);
        }
    }

    async expectFormDesignerStepTitle(expectedTitle: string) {
        const dialog = await $(this.dialogSelector);
        await utils.waitForElementToBeDisplayed({ name: 'Dialog', selector: dialog.selector as string });

        const currentStep = await dialog.$('[data-element="form-content"] h2');
        const currentStepTitle = await currentStep.getText();
        if (currentStepTitle !== expectedTitle) {
            throw Error(
                `Expected value: ${expectedTitle}, actual: ${currentStepTitle}\nSelector: ${dialog.selector} h2`,
            );
        }
    }

    async clickFormDesignerDialogButton(buttonName: string) {
        const dialog = await $(this.dialogSelector);
        const buttons = await dialog.$$('[data-element="form-footer"] button');

        // eslint-disable-next-line no-restricted-syntax
        for (const button of buttons) {
            if (utils.formatString(await button.getText()) === utils.formatString(buttonName)) {
                await button.moveTo();
                await button.click();
                await waitForPromises(200, 'click dialog button');
                return;
            }
        }

        throw new Error(
            `Expected element could not be found: "${buttonName}" button.\nSelector: ${buttons[0].selector}`,
        );
    }

    async expectFormDesignerDialogButtonState(buttonName: string, disabled = false) {
        const dialog = await $(this.dialogSelector);
        const buttons = await dialog.$$('[data-element="form-footer"] button');

        // eslint-disable-next-line no-restricted-syntax
        for (const button of buttons) {
            if (utils.formatString(await button.getText()) === utils.formatString(buttonName)) {
                const expectedAttr = await button.getAttribute('disabled');
                if (disabled) {
                    if (expectedAttr !== null) return;
                    throw new Error(`Expected "${buttonName}" button to be disabled.\nSelector: ${button.selector}`);
                } else {
                    if (expectedAttr === null) return;
                    throw new Error(`Expected "${buttonName}" button to be enabled.\nSelector: ${button.selector}`);
                }
            }
        }

        throw new Error(
            `Expected element could not be found: "${buttonName}" button.\nSelector: ${buttons[0].selector}`,
        );
    }

    async clickFormDesignerDialogCloseButton() {
        const dialog = await $(this.dialogSelector);
        const browserURL = await browser.getUrl();
        const buttonSelector = utils.regex_handheld_settings(browserURL)
            ? '[data-component="navigation-bar"] [data-element="close"]'
            : 'span[type="close"]';
        const dialogCloseButton = await dialog.$(buttonSelector);
        await dialogCloseButton.moveTo();
        await dialogCloseButton.click();
        await waitForPromises(200, 'click dialog close button');
        await utils.waitForElementNotToBeDisplayed('Dialog', dialog.selector as string);
    }

    async selectFormDesignerCard(value: string) {
        const dialog = await $(this.dialogSelector);
        await utils.waitForElementToBeDisplayed({ name: 'Dialog', selector: dialog.selector as string });

        const cards = await dialog.$$('[data-element="form-content"] button.e-selection-card');

        // eslint-disable-next-line no-restricted-syntax
        for (const card of cards) {
            const cardTitle = await card.$('[data-testid="e-selection-card-title"]');
            if (cardTitle && (await cardTitle.getText()) === value) {
                await card.moveTo();
                await card.click();
                await waitForPromises(200, 'click card');
                return;
            }
        }

        throw new Error(`Expected element could not be found: "${value}" card.\nSelector: ${cards[0].selector}`);
    }

    async expectFormDesignerCardToBeSelected(value: string, unselected = false) {
        const dialog = await $(this.dialogSelector);
        await utils.waitForElementToBeDisplayed({ name: 'Dialog', selector: dialog.selector as string });

        const cards = await dialog.$$('[data-element="form-content"] button.e-selection-card');

        // eslint-disable-next-line no-restricted-syntax
        for (const card of cards) {
            const cardTitle = await card.$('[data-testid="e-selection-card-title"]');
            if (cardTitle && (await cardTitle.getText()) === value) {
                if (unselected) {
                    if ((await card.getAttribute('class')).includes('e-selection-card-selected')) {
                        throw new Error(`Expected "${value}" card to be unselected.\nSelector: ${card.selector}`);
                    }
                } else if (!(await card.getAttribute('class')).includes('e-selection-card-selected')) {
                    throw new Error(`Expected "${value}" card to be selected.\nSelector: ${card.selector}`);
                }
                return;
            }
        }

        throw new Error(`Expected element could not be found: "${value}" card.\nSelector: ${cards[0].selector}`);
    }

    async selectFormDesignerTableDataContainer({
        name,
        occurrence,
        containerType,
        location,
    }: {
        name: string;
        occurrence: string;
        containerType: 'table' | 'data container';
        location: FormDesignerLocation;
    }): Promise<WebdriverIO.Element> {
        const occurrenceStr = occurrence.replace(/\D/g, '');
        const occurrenceIndex = parseInt(occurrenceStr, 10) - 1;
        const selectorToUse = `div.document-editor div#${location} ${containerType === 'table' ? 'table' : 'section'}[data-context-object-type="${name}"]`;
        const tableDataContainer = await this.findAll(selectorToUse);
        const selectedTableDataContainer = tableDataContainer[occurrenceIndex];

        try {
            await selectedTableDataContainer.scrollIntoView();
            await selectedTableDataContainer.click();
        } catch (error) {
            throw new Error(
                `Expected ${containerType} "${name}" and occurrence "${occurrenceStr}", could not be found.\nSelector: ${selectorToUse}`,
            );
        }

        return selectedTableDataContainer;
    }

    async selectFormDesignerTableCellByHeader({
        formDesignerTable,
        columnHeader,
        relativeLocation,
        footerGroupNumber = 0,
    }: {
        formDesignerTable: WebdriverIO.Element;
        columnHeader: string;
        relativeLocation: 'header' | 'body' | 'footer' | 'footerGroup';
        footerGroupNumber?: number;
    }): Promise<WebdriverIO.Element> {
        // Convert relativeLocation to the corresponding class
        let tablePartClass;
        let columnIndex = -1;
        let groupErrorDesc = '';
        switch (relativeLocation) {
            case FormDesignerTableRelativeLocation.header:
                tablePartClass = '.query-table-head .query-table-row';
                break;
            case FormDesignerTableRelativeLocation.body:
                tablePartClass = '.query-table-body .query-table-row';
                break;
            case FormDesignerTableRelativeLocation.footer:
                tablePartClass = '.query-table-footer .query-table-row';
                break;
            case FormDesignerTableRelativeLocation.footerGroup:
                tablePartClass = `.query-table-body .query-table-row[data-footer-group="${footerGroupNumber - 1}"]`;
                groupErrorDesc = ` and group number "${footerGroupNumber}"`;
                break;
            default:
                throw new Error(`Unknown table part: ${relativeLocation}`);
        }

        const tableElement = await formDesignerTable.$(tablePartClass);
        await tableElement.waitForExist({
            timeout: this.timeoutWaitFor,
            timeoutMsg: `Expected column header "${columnHeader}"${groupErrorDesc} could not be found.\nSelector: ${tablePartClass}`,
        });

        // Find the index of the column with the specified header
        const headerCells = await formDesignerTable.$$('.query-table-head .query-table-cell p');
        await Promise.all(
            headerCells.map(async (cell, index) => {
                const text = await cell.getText();
                if (text.trim() === columnHeader.trim()) {
                    columnIndex = index;
                }
            }),
        );

        const selectorToUse = `${tablePartClass} .query-table-cell:nth-child(${columnIndex + 1})`;
        if (columnIndex === -1) {
            throw new Error(
                `Expected column header "${columnHeader}"${groupErrorDesc} could not be found.\nSelector: ${selectorToUse}`,
            );
        }

        return formDesignerTable.$(selectorToUse);
    }

    async insertParagraphBlock({
        parentContainer,
        paragraphPosition,
        containerType,
    }: {
        parentContainer: WebdriverIO.Element;
        paragraphPosition: 'before' | 'after';
        containerType: string;
    }) {
        if (parentContainer === undefined) {
            throw new Error(`No ${containerType} has been selected. Please select ${containerType} first`);
        }

        try {
            await browser.waitUntil(
                async () => {
                    return (await browser.$(parentContainer.selector)).isExisting();
                },
                { timeout: this.timeoutWaitFor },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected element could not be found.\nSelector: ${parentContainer.selector}`);
        }

        const insertParagraphButton = await parentContainer.$(
            `div.ck-widget__type-around div.ck-widget__type-around__button_${paragraphPosition}`,
        );

        const offsetsDivide = paragraphPosition === 'after' ? 3 : 2;

        await parentContainer.scrollIntoView();
        await parentContainer.click();
        await insertParagraphButton.scrollIntoView();
        await waitForPromises(200, `scroll insert paragraph ${paragraphPosition} block button into view`);

        await insertParagraphButton.moveTo({
            xOffset: (await insertParagraphButton.getSize()).height / offsetsDivide,
            yOffset: (await insertParagraphButton.getSize()).width / offsetsDivide,
        });
        await waitForPromises(200, `move to insert paragraph ${paragraphPosition} block button`);
        await insertParagraphButton.click();

        await waitForPromises(200, `click insert paragraph ${paragraphPosition} block`);
    }

    async selectUnbreakableContainer(parentContainer: WebdriverIO.Element) {
        const selectorToUse = `${parentContainer.selector as string} section.unbreakable-block`;

        try {
            await browser.waitUntil(
                async () => {
                    return (await browser.$(selectorToUse)).isExisting();
                },
                { timeout: this.timeoutWaitFor },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(`Expected element could not be found.\nSelector: ${selectorToUse}`);
        }

        return this.find(selectorToUse, true);
    }

    async clickTableFormatButtonByText(text: string) {
        const buttonsSelector = 'button span[data-element="main-text"]';
        const buttons = await browser.$$(buttonsSelector);
        let buttonFound = false;

        // eslint-disable-next-line no-restricted-syntax
        for (const span of buttons) {
            const buttonText = await span.getText();
            if (buttonText === text) {
                buttonFound = true;
                await (await span.parentElement()).click();
                break;
            }
        }

        if (!buttonFound) {
            throw new Error(`Expected button could not be found: "${text}".\nSelector: ${buttonsSelector}`);
        }
    }

    async clickToggleButton(groupLabel: string, buttonText: string) {
        // Locate all toggle button groups
        const toggleGroupsSelector = 'div[data-component="button-toggle-group"]:not([role="group"])';
        const toggleGroups = await $$(toggleGroupsSelector);

        let groupFound = false;
        const labelSelector = 'span[data-element="label"]';

        // eslint-disable-next-line no-restricted-syntax
        for (const group of toggleGroups) {
            const labelElement = await group.$(labelSelector);
            const labelText = await labelElement.getText();

            if (labelText.trim() === groupLabel.trim()) {
                groupFound = true;
                const buttonsSelector = '[data-element="button-toggle-button"]';
                const buttons = await group.$$(buttonsSelector);

                let buttonFound = false;

                // eslint-disable-next-line no-restricted-syntax
                for (const button of buttons) {
                    const text = await button.getText();

                    if (text.trim() === buttonText.trim()) {
                        buttonFound = true;
                        await button.click();
                        return; // Exit once the button is clicked
                    }
                }

                if (!buttonFound) {
                    throw new Error(
                        `Expected toggle button could not be found: "${buttonText}".\nSelector: ${toggleGroupsSelector} ${buttonsSelector}`,
                    );
                }

                return;
            }
        }

        if (!groupFound) {
            throw new Error(
                `Expected toggle button group could not be found: "${groupLabel}".\nSelector: ${toggleGroupsSelector} ${labelSelector}`,
            );
        }
    }

    async setColor(elementType: 'background' | 'border', colorHex: string) {
        const labelSelector = `.field-label`;
        const buttonSelector = `button[data-element="color-picker-cell"]`;
        const colorPickerSelector = 'div[data-component="dialog"][data-element="dialog"]';
        const colorInputSelector = `input[data-element="input"][value="${colorHex}"]`;
        const closeButtonSelector = 'button[data-element="close"]';

        // Find all labels and locate the one that matches the elementType
        const labels = await browser.$$(labelSelector);

        // eslint-disable-next-line no-restricted-syntax
        for (const label of labels) {
            const labelText = await label.getText();
            if (labelText.trim() === `${elementType.charAt(0).toUpperCase() + elementType.slice(1)} color`) {
                // Find the corresponding button within the same parent
                const parent = await label.parentElement();
                await (await parent.$(buttonSelector)).click();
                break;
            }
        }

        // Wait for the color picker dialog to appear
        await utils.waitForElementToBeDisplayed({ name: 'Color dialog', selector: colorPickerSelector });
        const colorPickerDialog = await browser.$(colorPickerSelector);

        try {
            await browser.waitUntil(
                async () => {
                    return (await colorPickerDialog.$(colorInputSelector)).isExisting();
                },
                { timeout: this.timeoutWaitFor },
            );
            await (await colorPickerDialog.$(colorInputSelector)).click();
        } catch (error) {
            throw new Error(
                `Expected element could not be found: "${colorHex}" color.\nSelector: ${colorInputSelector}`,
            );
        }
        await waitForPromises(200, 'color picker selection');

        // Click the close button to close the color picker dialog
        const closeButton = await $(closeButtonSelector);
        await closeButton.click();

        await waitForPromises(300, 'after color picker selection');
    }

    async setPanelFieldValue({
        elementName,
        fieldType,
        value,
        formDesignerPanel = this.rightPanelSelector,
    }: {
        elementName: string;
        fieldType: utils.FieldTypes;
        value?: string;
        formDesignerPanel?: string;
    }) {
        const panelField = await this.getPanelFieldByTitle({
            parentPanelSelector: formDesignerPanel,
            title: elementName,
            fieldType,
        });

        const formDesignerFieldSelector = panelField.selector as string;

        await this.expectToBeDisplayed(formDesignerFieldSelector);
        await this.expectToBeEnabled(formDesignerFieldSelector);

        await this.scrollTo({ selector: formDesignerFieldSelector, ignoreContext: true });

        if (!value) {
            await this.clearInput({ ignoreContext: true, fieldType, cssSelector: formDesignerFieldSelector });
        } else if (fieldType === utils.fieldTypes.dropdownList) {
            await this.selectPanelDropDownOption(formDesignerFieldSelector, value);
        } else {
            await this.write({ content: value, cssSelector: formDesignerFieldSelector, ignoreContext: true });
        }

        await waitForPromises(200, 'write to panel field');
    }

    async selectPanelDropDownOption(fieldSelector: string, value: string) {
        let optionFound = false;
        const selectorToUse = `div[data-element="select-list-wrapper"][data-floating-placement] [data-component^="option"]`;

        await this.click(`${fieldSelector} + span + span`, true);
        await waitForPromises(200, 'click on panel drop-down field');

        // eslint-disable-next-line no-restricted-syntax
        for (const option of await browser.$$(selectorToUse)) {
            const optionText = await option.getText();
            if (optionText.trim() === value.trim()) {
                await option.click();
                optionFound = true;
                break;
            }
        }

        if (!optionFound) {
            throw new Error(
                `Expected option to be selected could not be found: "${value}".\nSelector: ${selectorToUse}`,
            );
        }
    }

    async getPanelFieldByTitle({
        parentPanelSelector,
        title,
        fieldType,
    }: {
        parentPanelSelector: string;
        title: string;
        fieldType: utils.FieldTypes;
    }) {
        const selectorToUse = `${parentPanelSelector} label`;
        const panelLabels = await browser.$$(selectorToUse);

        // eslint-disable-next-line no-restricted-syntax
        for (const label of panelLabels) {
            await label.waitForDisplayed();
            const labelTextContent = await label.getText();

            if (labelTextContent === title) {
                return browser.$(`input[id="${await label.getAttribute('for')}"]`);
            }
        }

        throw new Error(
            `Expected ${fieldType} field could not be found: "${title}".\nSelector: ${selectorToUse} ${title}`,
        );
    }

    /* ------- Widget Editor Functions ------- */
    async getFormDesignerWidgetEditor() {
        await utils.waitForElementToBeDisplayed({ name: 'Dialog', selector: this.dialogSelector });

        const widgetTableSelector = `${this.dialogSelector} table[data-component="flat-table"]`;
        await utils.waitForElementToBeDisplayed({ name: 'Widget Editor', selector: widgetTableSelector });

        return browser.$(widgetTableSelector);
    }

    async getWidgetEditorRow(row: number) {
        const widgetTable = await this.getFormDesignerWidgetEditor();
        const widgetTableRows = await widgetTable.$$('tbody tr[data-element="flat-table-row"]');

        if (widgetTableRows.length < row) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Number of Widget Editor rows less than expected row number ${row}.\nSelector: ${widgetTable.selector} ${widgetTableRows[0].selector}`,
            );
        }

        const widgetRowTestId =
            (await widgetTableRows[row - 1].getAttribute('data-testid')) !== null
                ? `[data-testid="${await widgetTableRows[row - 1].getAttribute('data-testid')}"]`
                : '';
        const selectorToUse = `${widgetTableRows[row - 1].selector}${widgetRowTestId}`;

        return browser.$(`${widgetTable.selector} ${selectorToUse}`);
    }

    async getWidgetEditorField(columnName: string, row: number) {
        const widgetTableRow = await this.getWidgetEditorRow(row);
        const dataTestId = await widgetTableRow.getAttribute('data-testid');

        const selectors = [
            `e-widget-editor-content-${columnName.replaceAll(' ', '-').toLowerCase()}-${dataTestId}`,
            `e-widget-editor-filter-${columnName.toLowerCase().replace('filter ', '').replaceAll(' ', '-')}-${dataTestId}`,
            `e-condition-table-${columnName.charAt(0).toLowerCase() + columnName.replaceAll(' ', '').substring(1)}-column-${dataTestId}`,
            `e-condition-table-${columnName.charAt(0).toLowerCase() + columnName.replaceAll(' ', '').substring(1)}-${dataTestId}`,
            `e-widget-editor-filter-${columnName.toLowerCase().replace('operator', 'type')}-${dataTestId}`,
        ];

        const selectorToUse = selectors
            .map(selector =>
                utils.getDataTestIdSelector({
                    domSelector: 'input',
                    dataTestIdValue: selector,
                }),
            )
            .join(',');

        return browser.$(`${widgetTableRow.selector} ${selectorToUse}`);
    }

    async getWidgetEditorButton({
        buttonName,
        row,
        tableButton = false,
    }: {
        buttonName: string;
        row: number;
        tableButton: boolean;
    }) {
        const widgetTableRow = await this.getWidgetEditorRow(row);

        const selectorToUse = utils.getDataTestIdSelector({
            domSelector: 'button',
            dataTestIdValue: tableButton ? 'add-item-button' : `flat-table-${buttonName.toLowerCase()}-button`,
        });

        return browser.$(`${widgetTableRow.selector} ${selectorToUse}`);
    }

    async writeToWidgetEditorField({
        columnName,
        fieldType,
        row,
        value,
    }: {
        columnName: string;
        fieldType: utils.FieldTypes;
        row: number;
        value?: string;
    }) {
        const widgetTableField = await this.getWidgetEditorField(columnName, row);

        await this.expectWidgetTableFieldToExist({ selector: widgetTableField.selector as string, columnName, row });
        await this.expectToBeDisplayed(widgetTableField.selector as string);
        await this.expectToBeEnabled(widgetTableField.selector as string);

        await this.scrollTo({ selector: widgetTableField.selector as string, ignoreContext: true });

        if (!value) {
            await this.clearInput({ ignoreContext: true, fieldType, cssSelector: widgetTableField.selector as string });
        } else {
            await this.click(widgetTableField.selector as string, true);
            await waitForPromises(300, 'click on widget editor field');

            const treeDropdown = await $(
                utils.getDataTestIdSelector({
                    domSelector: 'div',
                    dataTestIdValue: 'e-tree-input-dropdown',
                }),
            );

            switch (fieldType) {
                case utils.fieldTypes.dropdownList:
                    if (await treeDropdown.isDisplayed()) {
                        await this.selectEditorDropdownFieldOption(value);
                    } else {
                        await this.write({
                            content: value,
                            cssSelector: widgetTableField.selector as string,
                            ignoreContext: true,
                        });
                        await waitForPromises(200, 'wait for dropdown list to open');
                        if (
                            (await (
                                await browser.$(`${this.dialogSelector} div[data-element="select-list-wrapper"]`)
                            ).getAttribute('data-floating-placement')) !== null
                        ) {
                            await this.click(
                                `${this.dialogSelector} div[data-element="select-list-wrapper"][data-floating-placement] [data-component^="option"][aria-selected="true"]`,
                                true,
                            );
                        }
                    }
                    break;
                case utils.fieldTypes.date:
                    await waitForPromises(200, 'wait for date picker to open');
                    await this.writeDate(value, widgetTableField.selector as string);
                    break;
                default:
                    await this.write({
                        content: value,
                        cssSelector: widgetTableField.selector as string,
                        ignoreContext: true,
                    });
                    break;
            }
        }
        await waitForPromises(300, 'write to widget editor field');
    }

    async writeDate(content: string, selector: string) {
        const lang = await getUserLocale();
        try {
            const dateField = await this.find(selector, true);
            await dateField.click();
            await dateField.clearValue();
            const arrValue = [...content];
            for (let i = 0; i < arrValue.length; i += 1) {
                await browser.keys(arrValue[i]);
            }
            await waitForPromises(300, 'wait for day picker');
            const isDayPickerOpen = await (await this.find(`.rdp-root`, true)).isExisting();
            if (isDayPickerOpen) {
                const { day, year, month } = parseDateString(content, lang);
                const daySelector = `.rdp-root [data-day="${year}-${month}-${day}"]`;
                // click the selected date so the date picker to closes
                await (await this.find(daySelector, true)).moveTo();
                await this.click(daySelector, true);
            }
        } catch (error) {
            throw new Error(`Error writing date: ${error}`);
        }
    }

    async openEditorDropdownField(columnName: string, row: number) {
        const widgetTableField = await this.getWidgetEditorField(columnName, row);
        await this.expectWidgetTableFieldToExist({ selector: widgetTableField.selector.toString(), columnName, row });
        await this.expectToBeDisplayed(widgetTableField.selector.toString());
        await this.expectToBeEnabled(widgetTableField.selector.toString());
        await this.scrollTo({ selector: widgetTableField.selector.toString(), ignoreContext: true });
        const dropdownField = await $(widgetTableField.selector);
        this.dropdownFieldID = await dropdownField.getAttribute('aria-controls');
        await dropdownField.moveTo();
        await dropdownField.click();
        await utils.waitForElementToBeDisplayed({
            name: 'Editor field dropdown',
            selector: `[id="${this.dropdownFieldID}"]`,
        });
    }

    async selectEditorDropdownFieldOption(value: string) {
        const option = await $(
            `#${this.dropdownFieldID} [data-testid="e-tree-view-container-label-${camelCase(value)}"]`,
        );

        await utils.waitForElementToExist({
            name: 'Editor dropdown field option',
            selector: option.selector.toString(),
        });

        await browser.waitUntil(
            async () => {
                const isClickable = await option.isClickable();
                if (!isClickable) {
                    await browser.execute(
                        `const element = arguments[0]; element.scrollBy(0, 50);`,
                        await option.parentElement(),
                    );
                }
                return isClickable;
            },
            {
                timeout: this.timeoutWaitFor,
                timeoutMsg: `Failed to scroll to the option: ${value}`,
            },
        );

        await option.moveTo();
        await option.click();
        await waitForPromises(300, 'select editor dropdown field option');
    }

    async expectWidgetTableFieldToExist({
        selector,
        columnName,
        row,
    }: {
        selector: string;
        columnName: string;
        row: number;
    }) {
        try {
            await browser.waitUntil(
                async () => {
                    return (await $(selector)).isExisting();
                },
                { timeout: this.timeoutWaitFor },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Expected element "${columnName}" and row "${row}" could not be found.\nSelector: ${selector}`,
            );
        }
    }

    async expectWidgetEditorFieldToBe({
        columnName,
        row,
        value,
    }: {
        columnName: string;
        fieldType: utils.FieldTypes;
        row: number;
        value: string;
    }) {
        const widgetTableField = await this.getWidgetEditorField(columnName, row);

        await this.expectWidgetTableFieldToExist({ selector: widgetTableField.selector as string, columnName, row });
        await this.expectToBeDisplayed(widgetTableField.selector as string);

        await this.expectValue({ toBe: value, cssSelector: widgetTableField.selector as string, ignoreContext: true });
    }

    async expectWidgetEditorFieldToBeDisabled({
        columnName,
        row,
        reverse = false,
    }: {
        columnName: string;
        fieldType: utils.FieldTypes;
        row: number;
        reverse?: boolean;
    }) {
        const widgetTableField = await this.getWidgetEditorField(columnName, row);

        await this.expectWidgetTableFieldToExist({ selector: widgetTableField.selector as string, columnName, row });
        await this.expectToBeEnabled(widgetTableField.selector as string, reverse);
    }

    async expectWidgetEditorFieldToBeDisplayed({
        columnName,
        row,
        reverse = false,
    }: {
        columnName: string;
        fieldType: utils.FieldTypes;
        row: number;
        reverse?: boolean;
    }) {
        const widgetTableField = await this.getWidgetEditorField(columnName, row);

        await this.expectToBeDisplayed(widgetTableField.selector as string, reverse);
    }

    async clickWidgetEditorButton({
        buttonName,
        row,
        tableButton,
    }: {
        buttonName: string;
        row: number;
        tableButton: boolean;
    }) {
        const widgetEditorButton = await this.getWidgetEditorButton({ buttonName, row, tableButton });

        try {
            await browser.waitUntil(
                async () => {
                    return (
                        (await (await $(widgetEditorButton.selector)).isExisting()) &&
                        (tableButton
                            ? (
                                  await (
                                      await $(`${widgetEditorButton.selector} span[data-element="main-text"]`)
                                  ).getText()
                              ).toLowerCase() === buttonName.toLowerCase()
                            : true)
                    );
                },
                { timeout: this.timeoutWaitFor },
            );
        } catch (error) {
            await this.takePuppeteerPageScreenshot({ captureBeyondViewport: false });
            throw new Error(
                `Expected element "${buttonName}" button${tableButton ? '' : ` and row "${row}"`} could not be found.\nSelector: ${widgetEditorButton.selector}`,
            );
        }

        await this.expectWidgetEditorButtonToBeDisplayed({ buttonName, row, tableButton });

        await widgetEditorButton.click();

        await waitForPromises(500, 'click widget editor button');
    }

    async expectWidgetEditorButtonToBeDisplayed({
        buttonName,
        row,
        tableButton,
        reverse = false,
    }: {
        buttonName: string;
        row: number;
        tableButton: boolean;
        reverse?: boolean;
    }) {
        const widgetEditorButton = await this.getWidgetEditorButton({ buttonName, row, tableButton });

        await this.expectToBeDisplayed(widgetEditorButton.selector as string, reverse);
    }

    async expectedValue(value: string, selectorToUse: WebdriverIO.Element) {
        const selector = selectorToUse.selector.toString();
        let elementTextValue;

        try {
            elementTextValue = await (await browser.$(selector)).getText();

            if (value !== elementTextValue) {
                throw new Error(`Expected value: "${value}", actual: "${elementTextValue}".\nSelector: ${selector}`);
            }
        } catch (error) {
            throw new Error(`Error while verifying expected value.\n${error}`);
        }
    }

    async writeTextValue(value: string, selectorToUse: WebdriverIO.Element) {
        const selector = selectorToUse.selector.toString();

        try {
            if (!(await browser.$(selector).isExisting())) {
                throw new Error(`Expected element could not be found.\nSelector: ${selector}`);
            }
            await selectorToUse.click();
            await this.clearText(selectorToUse);
            await selectorToUse.setValue(value);
        } catch (error) {
            throw new Error(`Error while writing text value.\n${error}`);
        }
    }

    async clearText(selectorToUse: WebdriverIO.Element) {
        try {
            const textContent = await selectorToUse.getText();
            let i: number = 0;
            if (textContent !== '') {
                while (i < textContent.length) {
                    await browser.keys([Key.Shift, Key.ArrowRight]);
                    await browser.keys(Key.Backspace);
                    i += 1;
                }
            }
        } catch (error) {
            throw new Error(`Error while clearing field.\n${error}`);
        }
    }

    async clickSwitch(name: string, rowId: number) {
        const selectorToUse = `[data-testid="e-widget-editor-filter-${name}-${rowId}"]`;
        const switchButton = await this.find(selectorToUse, true);
        await switchButton.click();
    }

    async dragRow(row: number, yStep: number) {
        const widgetTableRow = await this.getWidgetEditorRow(row);
        const origin = await widgetTableRow.$('span[type="drag"]');
        await origin.waitForExist();
        await origin.waitForClickable();
        const y = Number(yStep);
        await browser
            .action('pointer')
            .move({ duration: 0, origin })
            .down({ button: 0 }) // left button
            .pause(1000)
            .move({ duration: 100, origin, x: 0, y: 0 + y })
            .pause(1000)
            .up({ button: 0 })
            .perform();
    }
}
