{"name": "@sage/xtrem-cli-dev", "description": "Xtrem CLI dev", "version": "59.0.8", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-cli"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["build", "resources"], "dependencies": {"@sage/xtrem-cli-compile": "workspace:*", "@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-cli-main": "workspace:*", "@sage/xtrem-cli-transformers": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-i18n": "workspace:*", "@sage/xtrem-service": "workspace:*", "@sage/xtrem-shared": "workspace:*", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "chalk": "^4.0.0", "eslint": "^9.30.1", "glob": "^11.0.0", "graphql": "^16.11.0", "inquirer": "^12.0.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "mocha": "^11.0.0", "mocha-junit-reporter": "^2.0.0", "mocha-multi": "^1.1.3", "prettier": "^3.3.3", "thread-loader": "^4.0.2", "typescript": "~5.9.0", "yargs": "^18.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/xtrem-minify": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/eslint": "^9.6.1", "@types/inquirer": "^9.0.0", "@types/js-yaml": "^4.0.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/sinon": "^17.0.0", "@types/yargs": "^17.0.24", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "cross-env": "^10.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "sinon": "^21.0.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\" -\"build/lib/commands/utils/graphql-tests-entry-point.js\" -\"build/lib/commands/utils/transformers.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint lib test", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "test": "cross-env TZ=CET REPORTER=custom mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "pnpm test"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}}