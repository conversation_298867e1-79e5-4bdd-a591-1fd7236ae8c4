{"name": "@[VENDORNAME]/[PACKAGENAME]", "description": "A Sage Xtrem Service Package", "version": "59.0.8", "author": "[VENDORNAME]", "license": "UNLICENSED", "xtrem": {}, "keywords": ["xtrem-application-package"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/[PACKAGENAME].d.ts", "dependencies": {}, "devDependencies": {"@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-ui": "workspace:*", "@types/chai": "^4.3.6", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "c8": "^10.1.2", "chai": "^4.3.10", "mocha": "^11.0.0", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "xtrem compile", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "lint": "xtrem lint", "manage": "xtrem manage", "start": "xtrem start", "test": "xtrem test || echo 'no tests'", "test:ci": "xtrem test || echo 'no tests'", "xtrem": "xtrem"}}