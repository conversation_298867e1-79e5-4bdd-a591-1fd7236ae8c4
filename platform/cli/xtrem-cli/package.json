{"name": "@sage/xtrem-cli", "version": "59.0.8", "description": "", "main": "build/index.js", "types": "build/index.d.ts", "typings": "build/package-definition.d.ts", "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "build:watch": "tsc -b -v --watch .", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint lib test", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "prettier:check": "prettier --list-different \"{lib,test}/**/*.ts\"", "prettier:write": "prettier --write \"{lib,test}/**/*.ts\"", "start": "pnpm build && node ."}, "files": ["bin", "build/*.*", "build/lib"], "author": "Sage", "bin": {"xtrem": "./bin/xtrem"}, "license": "UNLICENSED", "dependencies": {"@sage/xtrem-cli-lib": "workspace:*", "@sage/xtrem-config": "workspace:*", "@sage/xtrem-core": "workspace:*", "@sage/xtrem-log": "workspace:*", "@sage/xtrem-shared": "workspace:*", "chalk": "^4.0.0", "find-up": "^7.0.0", "glob": "^11.0.0", "lodash": "^4.17.21", "newrelic": "^13.0.0", "yargs": "^18.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/eslint-plugin-xtrem": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "@sage/xtrem-minify": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@types/yargs": "^17.0.24", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "c8": {"reporter": ["json", "lcov", "text-summary"], "extension": [".ts"], "exclude": ["**/*.d.ts", "**/*-test.ts"]}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}