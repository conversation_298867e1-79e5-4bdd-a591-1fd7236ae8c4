{"name": "@sage/xtrem-cli-lib", "description": "Xtrem CLI lib", "version": "59.0.8", "author": "sage", "license": "UNLICENSED", "keywords": ["xtrem-cli"], "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "typings": "build/package-definition.d.ts", "files": ["bin", "build"], "dependencies": {"@sage/xtrem-core": "workspace:*", "@sage/xtrem-dts-bundle": "workspace:*", "chalk": "^4.0.0", "lodash": "^4.17.21", "socket.io": "^4.7.5", "typescript": "~5.9.0", "yargs": "^18.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@sage/xtrem-minify": "workspace:*", "@stylistic/eslint-plugin": "^5.0.0", "@types/lodash": "^4.14.198", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0"}, "scripts": {"build": "tsc -b -v . && pnpm dts-bundle", "build:binary": "pnpm clean && pnpm build && xtrem-minify -c -z \"build/**/*.js\"", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build", "dts-bundle": "xtrem-dts-bundle", "lint": "eslint lib", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\""}}