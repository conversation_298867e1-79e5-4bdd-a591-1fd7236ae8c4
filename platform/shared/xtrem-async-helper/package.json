{"name": "@sage/xtrem-async-helper", "description": "helper for migration to async/await", "version": "59.0.8", "license": "UNLICENSED", "author": "Sage", "publishConfig": {"registry": "https://pkgs.dev.azure.com/Sage-LiveServices/_packaging/Sage-ERP/npm/registry/"}, "repository": {"type": "git", "url": "git://github.com/Sage-ERP-X3/xtrem.git"}, "main": "build/index.js", "types": "build/index.d.ts", "bin": {"xtrem-tsc": "./bin/xtrem-tsc"}, "dependencies": {"@sage/xtrem-decimal": "workspace:*", "@sage/xtrem-shared": "workspace:*", "lodash": "^4.17.21"}, "devDependencies": {"@eslint/js": "^9.30.1", "@sage/eslint-config-xtrem": "^1.1.0", "@stylistic/eslint-plugin": "^5.0.0", "@types/chai": "^4.3.6", "@types/chai-as-promised": "^7.1.6", "@types/lodash": "^4.14.198", "@types/mocha": "^10.0.1", "@types/node": "^22.10.2", "@types/source-map-support": "^0.5.7", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "@typescript-eslint/utils": "^8.35.0", "c8": "^10.1.2", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "copyfiles": "^2.1.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-mocha": "^11.0.0", "eslint-plugin-n": "^17.20.0", "eslint-plugin-unicorn": "^60.0.0", "eslint-plugin-unused-imports": "^4.0.0", "globals": "^16.2.0", "mocha": "^11.0.0", "source-map-support": "^0.5.12", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0"}, "scripts": {"build": "tsc -b -v . && copyfiles lib/dynamic-import.js build", "build:binary": "echo 'Binary mode is not available for this package, falling back to normal build.' && pnpm build", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "clean": "rm -rf build junit-report*", "lint": "eslint lib test", "lint:filename": "eslint --no-config-lookup -c eslint-filename.config.mjs --report-unused-disable-directives-severity off \"**\"", "test": "cross-env TZ=CET mocha --recursive --exit \"test/**/*@(-|.)test.ts\"", "test:ci": "cross-env TZ=CET JUNIT_REPORT_PATH=junit-report-async-helper.xml JUNIT_REPORT_NAME='xtrem-async-helper' c8 --reporter=lcov --reporter=json --reporter=cobertura --reporter=text mocha --recursive --exit \"test/**/*@(-|.)test.ts\" --reporter mocha-jenkins-reporter"}, "gitHead": "f0406ede1639145fdb322ebdf823bc87abddf4b8"}